# 历史个例天气预报评分细节文档

## 文档概述

本文档详细记录了历史个例天气预报考试中灾害性天气站点预报的精确评分规则，基于实际系统观察和分析得出。

## 评分体系架构

### 总体评分框架
- **单站总分**：10分
- **评分要素**：6个核心气象要素
- **评分方式**：要素独立评分 + 权重加权

### 要素分值分配
```
08-08最大风力(级)    ： 1.0分
08-08最大风力时的风向 ： 1.0分  
最低气温℃           ： 2.0分
最高气温℃           ： 2.0分
08-08降水(雨、雪)量级： 2.0分
灾害性天气类型       ： 2.0分
-------------------
单站总分            ： 10.0分
```

## 详细评分规则

### 1. 风力评分规则 (满分1.0分)

#### 基础评分标准
- **标准匹配**：1.0分
- **相邻等级**：0.8分  
- **其他**：0.0分

#### 风力等级定义
```
考生可选范围：静风(0级)、1级、2级、3级、4级、5级、6级、7级、8级、9级、10级、11级、12级
标准答案范围：静风(0级)、1级、2级、3级、4级、5级、6级、7级、8级、9级、10级、11级、12级、大于12级
```

#### 详细评分规则
根据考生预报等级，对应的标准答案得分规则：

| 考生预报 | 标准答案1.0分范围 | 标准答案0.8分范围 | 其他得0分 |
|---------|------------------|------------------|-----------|
| 静风(0级) | 静风、1级 | - | - |
| 1级 | 0级、1级、2级 | - | - |
| 2级 | 1级、2级 | 3级 | 0级、4级及以上 |
| 3级 | 3级 | 2级、4级 | 0级、1级、5级及以上 |
| 4级 | 4级 | 3级、5级 | 0级～2级、6级及以上 |
| 5级 | 5级 | 4级、6级 | 0级～3级、7级及以上 |
| 6级 | 6级 | 5级、7级 | 0级～4级、8级及以上 |
| 7级 | 7级 | 6级、8级 | 0级～5级、9级及以上 |
| 8级 | 8级 | 7级、9级 | 0级～6级、10级及以上 |
| 9级 | 9级 | 8级、10级 | 0级～7级、11级及以上 |
| 10级 | 10级 | 9级、11级 | 0级～8级、12级及以上 |
| 11级 | 11级 | 10级、12级 | 0级～9级、大于12级 |
| 12级 | 12级、大于12级 | 11级 | 0级～10级 |

#### 评分规则说明
1. **静风特殊处理**：考生预报静风时，标准答案为静风和1级均得满分
2. **1级特殊处理**：考生预报1级时，标准答案为0级、1级、2级均得满分
3. **2级到11级常规规则**：
   - 标准答案与考生预报完全一致：1.0分
   - 标准答案比考生预报高或低1级：0.8分
   - 其他情况：0.0分
4. **12级特殊处理**：考生预报12级时，标准答案为12级和大于12级均得满分

#### 具体评分示例
| 考生预报 | 标准答案 | 得分 | 说明 |
|---------|---------|------|------|
| 静风 | 静风 | 1.0 | 完全匹配 |
| 静风 | 1级 | 1.0 | 静风特殊规则 |
| 静风 | 2级 | 0.0 | 超出范围 |
| 1级 | 静风 | 1.0 | 1级特殊规则 |
| 1级 | 1级 | 1.0 | 完全匹配 |
| 1级 | 2级 | 1.0 | 1级特殊规则 |
| 1级 | 3级 | 0.0 | 超出范围 |
| 5级 | 5级 | 1.0 | 完全匹配 |
| 5级 | 4级 | 0.8 | 相邻等级 |
| 5级 | 6级 | 0.8 | 相邻等级 |
| 5级 | 3级 | 0.0 | 超出范围 |
| 12级 | 12级 | 1.0 | 完全匹配 |
| 12级 | 大于12级 | 1.0 | 12级特殊规则 |
| 12级 | 11级 | 0.8 | 相邻等级 |
| 12级 | 10级 | 0.0 | 超出范围 |

### 2. 风向评分规则 (满分1.0分)

#### 基础评分标准
- **角度范围匹配**：1.0分
- **相邻角度范围**：0.6分  
- **其他**：0.0分
- **无风特殊处理**：无风与任何有风向答案均为0分

#### 八方位与十六方位角度对应关系
考生填写八方位，标准答案为十六方位角度范围：

```
八方位 → 对应的十六方位角度范围

北   → 337.5°～22.5° (包含北西北、北、北东北)
东北 → 22.5°～67.5°  (包含北东北、东北、东东北) 
东   → 67.5°～112.5° (包含东东北、东、东东南)
东南 → 112.5°～157.5° (包含东东南、东南、南东南)
南   → 157.5°～202.5° (包含南东南、南、南西南)
西南 → 202.5°～247.5° (包含南西南、西南、西西南)
西   → 247.5°～292.5° (包含西西南、西、西西北)
西北 → 292.5°～337.5° (包含西西北、西北、北西北)
```

#### 风向评分逻辑
以考生填写"东"为例：

**1.0分情况（角度范围匹配）**：
- 标准答案角度在67.5°～90.0°（东东北范围）
- 标准答案角度在90.0°～112.5°（东范围）

**0.6分情况（相邻角度范围）**：
- 标准答案角度在45.0°～67.5°（东北范围）
- 标准答案角度在112.5°～135.0°（东东南范围）

**0.0分情况**：
- 标准答案角度在其他范围

#### 完整评分规则矩阵
| 考生答案 | 标准答案角度范围 | 得分 | 说明 |
|---------|-----------------|------|------|
| 北      | 337.5°～22.5°   | 1.0  | 角度范围匹配 |
| 北      | 22.5°～45.0°    | 0.6  | 相邻范围(北东北) |
| 北      | 315.0°～337.5°  | 0.6  | 相邻范围(北西北) |
| 东北    | 22.5°～67.5°    | 1.0  | 角度范围匹配 |
| 东北    | 0.0°～22.5°     | 0.6  | 相邻范围(北东北) |
| 东北    | 67.5°～90.0°    | 0.6  | 相邻范围(东东北) |
| 东      | 67.5°～90.0°    | 1.0  | 角度范围匹配 |
| 东      | 90.0°～112.5°   | 1.0  | 角度范围匹配 |  
| 东      | 45.0°～67.5°    | 0.6  | 相邻范围(东北) |
| 东      | 112.5°～135.0°  | 0.6  | 相邻范围(东东南) |
| 东南    | 112.5°～157.5°  | 1.0  | 角度范围匹配 |
| 东南    | 90.0°～112.5°   | 0.6  | 相邻范围(东东南) |
| 东南    | 157.5°～180.0°  | 0.6  | 相邻范围(南东南) |

#### 具体评分示例
| 标准答案角度 | 考生答案 | 得分 | 说明 |
|------------|---------|------|------|
| 85.0°      | 东      | 1.0  | 在67.5°～90.0°范围内 |
| 95.0°      | 东      | 1.0  | 在90.0°～112.5°范围内 |
| 50.0°      | 东      | 0.6  | 在45.0°～67.5°范围内 |
| 120.0°     | 东      | 0.6  | 在112.5°～135.0°范围内 |
| 45.0°      | 东      | 0.0  | 超出相邻范围 |
| 无风       | 东      | 0.0  | 类型不匹配 |

### 3. 温度评分规则

#### 3.1 最低气温评分 (满分2.0分)
- **准确范围**：|预报值-实况值|≤2℃ → 2.0分
- **错误范围**：|预报值-实况值|>2℃ → 0.0分

#### 3.2 最高气温评分 (满分2.0分)  
- **准确范围**：|预报值-实况值|≤2℃ → 2.0分
- **错误范围**：|预报值-实况值|>2℃ → 0.0分

#### 温度评分特点
> **重要说明**：温度评分采用**容差模式**，允许预报值与实况值之间有±2℃的偏差范围。在此范围内均视为准确预报，获得满分；超出此范围则视为错误，得0分。

#### 评分计算公式
```
温度差值 = |考生预报温度 - 标准答案温度|

if (温度差值 ≤ 2℃) {
    得分 = 2.0分
} else {
    得分 = 0.0分
}
```

#### 具体评分示例
| 要素 | 标准答案 | 考生答案 | 温度差值 | 得分 | 说明 |
|------|---------|---------|----------|------|------|
| 最低气温 | -5℃ | -5℃ | 0℃ | 2.0 | 完全匹配 |
| 最低气温 | -5℃ | -4℃ | 1℃ | 2.0 | 容差范围内 |
| 最低气温 | -5℃ | -3℃ | 2℃ | 2.0 | 容差边界 |
| 最低气温 | -5℃ | -2℃ | 3℃ | 0.0 | 超出容差范围 |
| 最低气温 | -5℃ | -7℃ | 2℃ | 2.0 | 容差边界 |
| 最低气温 | -5℃ | -8℃ | 3℃ | 0.0 | 超出容差范围 |
| 最高气温 | 8℃  | 8℃  | 0℃ | 2.0 | 完全匹配 |  
| 最高气温 | 8℃  | 10℃ | 2℃ | 2.0 | 容差边界 |
| 最高气温 | 8℃  | 11℃ | 3℃ | 0.0 | 超出容差范围 |
| 最高气温 | 8℃  | 6℃  | 2℃ | 2.0 | 容差边界 |
| 最高气温 | 8℃  | 5℃  | 3℃ | 0.0 | 超出容差范围 |

### 4. 降水评分规则 (满分2.0分)

#### 基础评分标准
- **完全匹配**：2.0分 (1.0 × 2.0)
- **相邻等级/特殊关系**：1.2分 (0.6 × 2.0)
- **其他情况**：0.0分

#### 降水类型定义
```
降水类型序列：
无雨雪 → 雨夹雪 → 小雨/小雪 → 中雨/中雪 → 大雨/大雪 → 暴雨/暴雪 → 大暴雨以上

特殊类型：
- 雨夹雪：介于雨雪之间的混合降水类型
- 大暴雨以上：包含大暴雨、特大暴雨等强降水
```

#### 详细评分规则矩阵

根据实际评分表格，完整的预报/实况评分规则如下：

| 考生预报↓ \ 标准答案→ | 无雨雪 | 雨夹雪 | 小雨 | 小雪 | 中雨 | 中雪 | 大雨 | 暴雨 | 大雪 | 大暴雨以上 | 暴雪 |
|-------------------|-------|-------|------|------|------|------|------|------|------|-----------|------|
| **无雨雪**         | 2.0   | 0.0   | 0.0  | 0.0  | 0.0  | 0.0  | 0.0  | 0.0  | 0.0  | 0.0       | 0.0  |
| **雨夹雪**         | 0.0   | 2.0   | 2.0  | 2.0  | 1.2  | 1.2  | 0.0  | 0.0  | 0.0  | 0.0       | 0.0  |
| **小雨**           | 0.0   | 2.0   | 2.0  | 0.0  | 1.2  | 0.0  | 0.0  | 0.0  | 0.0  | 0.0       | 0.0  |
| **小雪**           | 0.0   | 2.0   | 0.0  | 2.0  | 0.0  | 1.2  | 0.0  | 0.0  | 0.0  | 0.0       | 0.0  |
| **中雨**           | 0.0   | 1.2   | 1.2  | 0.0  | 2.0  | 0.0  | 1.2  | 0.0  | 0.0  | 0.0       | 0.0  |
| **中雪**           | 0.0   | 1.2   | 0.0  | 1.2  | 0.0  | 2.0  | 0.0  | 0.0  | 1.2  | 0.0       | 0.0  |
| **大雨**           | 0.0   | 0.0   | 0.0  | 0.0  | 1.2  | 0.0  | 2.0  | 1.2  | 0.0  | 0.0       | 0.0  |
| **暴雨**           | 0.0   | 0.0   | 0.0  | 0.0  | 0.0  | 0.0  | 1.2  | 2.0  | 0.0  | 1.2       | 0.0  |
| **大雪**           | 0.0   | 0.0   | 0.0  | 0.0  | 0.0  | 1.2  | 0.0  | 0.0  | 2.0  | 0.0       | 1.2  |
| **大暴雨以上**     | 0.0   | 0.0   | 0.0  | 0.0  | 0.0  | 0.0  | 0.0  | 1.2  | 0.0  | 2.0       | 0.0  |
| **暴雪**           | 0.0   | 0.0   | 0.0  | 0.0  | 0.0  | 0.0  | 0.0  | 0.0  | 1.2  | 0.0       | 2.0  |

#### 评分规则说明

1. **无雨雪规则**：
   - 考生预报无雨雪，标准答案为无雨雪：2.0分
   - 考生预报无雨雪，标准答案为任何有降水类型：0.0分
   - 考生预报任何有降水类型，标准答案为无雨雪：0.0分

2. **雨夹雪特殊规则**：
   - 雨夹雪预报与雨夹雪实况：2.0分
   - 雨夹雪预报与小雨实况：2.0分（特殊匹配）
   - 雨夹雪预报与小雪实况：2.0分（特殊匹配）
   - 小雨/小雪预报与雨夹雪实况：2.0分（特殊匹配）
   - 雨夹雪预报与中雨/中雪实况：1.2分
   - 中雨/中雪预报与雨夹雪实况：1.2分

3. **同类型相邻等级规则**：
   - 在雨类或雪类内部，相邻等级得1.2分
   - 例如：小雨←→中雨、中雨←→大雨、大雨←→暴雨等

4. **跨类型规则**：
   - 雨类与雪类之间（除雨夹雪特殊规则外）一律0分
   - 例如：小雨与小雪、中雨与中雪等

5. **特殊降水类型**：
   - 大暴雨以上作为独立等级处理
   - 与暴雨相邻，关系为1.2分

#### 具体评分示例

| 考生预报 | 标准答案 | 得分 | 说明 |
|---------|---------|------|------|
| 无雨雪   | 无雨雪   | 2.0  | 完全匹配 |
| 无雨雪   | 小雨     | 0.0  | 无雨雪特殊规则 |
| 雨夹雪   | 小雨     | 2.0  | 雨夹雪特殊匹配 |
| 雨夹雪   | 小雪     | 2.0  | 雨夹雪特殊匹配 |
| 小雨     | 雨夹雪   | 2.0  | 雨夹雪特殊匹配 |
| 小雨     | 小雨     | 2.0  | 完全匹配 |
| 小雨     | 小雪     | 0.0  | 跨类型 |
| 小雨     | 中雨     | 1.2  | 相邻等级 |
| 中雨     | 雨夹雪   | 1.2  | 雨夹雪特殊关系 |
| 中雨     | 小雨     | 1.2  | 相邻等级 |
| 中雨     | 中雨     | 2.0  | 完全匹配 |
| 中雨     | 大雨     | 1.2  | 相邻等级 |
| 中雨     | 中雪     | 0.0  | 跨类型 |
| 大雨     | 暴雨     | 1.2  | 相邻等级 |
| 暴雨     | 大暴雨以上| 1.2  | 相邻等级 |
| 大雪     | 暴雪     | 1.2  | 相邻等级 |

### 5. 灾害性天气评分规则 (满分2.0分)

#### 基础评分原理
采用**完全匹配**评分原则：预报与实况一致才得分，否则不得分。多种灾害性天气时采用**比例评分**。

#### 评分公式
```
单一灾害天气：
  预报 == 实况 → 2.0分
  预报 != 实况 → 0.0分

多种灾害天气：
  得分 = (匹配的灾害天气数量 / 实况灾害天气总数量) × 2.0分
```

#### 灾害天气类型定义
```
标准灾害天气类型：
[无, 暴雨, 暴雪, 高温, 大风, 寒潮, 大雾, 沙尘暴, 雷暴, 冰雹]
```

#### 详细评分规则

1. **单一灾害天气**：
   - 预报与实况完全一致：2.0分
   - 预报与实况不一致：0.0分

2. **多种灾害天气**：
   - 实况有n种灾害天气，考生预报正确m种
   - 得分 = (m/n) × 2.0分
   - 考生预报的额外灾害天气不扣分，但也不加分

3. **"无"的特殊处理**：
   - 实况为"无"，考生预报"无"：2.0分
   - 实况为"无"，考生预报任何灾害天气：0.0分
   - 实况有灾害天气，考生预报"无"：0.0分

#### 具体评分示例

##### 单一灾害天气情况
| 实况(标准答案) | 考生预报 | 得分 | 说明 |
|---------------|---------|------|------|
| 无 | 无 | 2.0 | 完全匹配 |
| 无 | 雷暴 | 0.0 | 错报灾害 |
| 雷暴 | 雷暴 | 2.0 | 完全匹配 |
| 雷暴 | 冰雹 | 0.0 | 预报错误 |
| 雷暴 | 无 | 0.0 | 漏报灾害 |

##### 多种灾害天气情况
| 实况(标准答案) | 考生预报 | 匹配数/总数 | 得分计算 | 得分 | 说明 |
|---------------|---------|------------|----------|------|------|
| [雷暴, 冰雹] | [雷暴, 冰雹] | 2/2 | (2/2)×2.0 | 2.0 | 完全匹配 |
| [雷暴, 冰雹] | [雷暴] | 1/2 | (1/2)×2.0 | 1.0 | 部分正确 |
| [雷暴, 冰雹] | [冰雹] | 1/2 | (1/2)×2.0 | 1.0 | 部分正确 |
| [雷暴, 冰雹] | [大风] | 0/2 | (0/2)×2.0 | 0.0 | 完全错误 |
| [雷暴, 冰雹] | [雷暴, 大风] | 1/2 | (1/2)×2.0 | 1.0 | 部分正确(大风不扣分) |
| [雷暴, 冰雹, 大风] | [雷暴, 冰雹] | 2/3 | (2/3)×2.0 | 1.33 | 部分正确 |
| [雷暴, 冰雹, 大风] | [雷暴] | 1/3 | (1/3)×2.0 | 0.67 | 部分正确 |
| [雷暴, 冰雹, 大风] | [雷暴, 冰雹, 大风, 暴雨] | 3/3 | (3/3)×2.0 | 2.0 | 完全匹配(暴雨不扣分) |

#### 评分规则特点

1. **严格匹配原则**：只有预报的灾害天气在实况中存在才计入匹配
2. **不扣分原则**：考生预报的额外灾害天气不会扣分
3. **比例计分原则**：按实况中正确预报的比例给分
4. **四舍五入原则**：最终得分保留2位小数

#### 常见评分区间
- **2.0分**：完全匹配或正确预报所有实况灾害
- **1.0-1.9分**：部分匹配，正确预报部分实况灾害
- **0.1-0.9分**：少量匹配，仅预报到少数实况灾害
- **0.0分**：完全错误，未预报到任何实况灾害

## 评分算法实现逻辑

### 核心算法流程
```java
public BigDecimal calculateStationScore(StationAnswer student, StationAnswer standard) {
    BigDecimal totalScore = BigDecimal.ZERO;
    
    // 1. 风力评分 (1.0分)
    totalScore = totalScore.add(calculateWindForceScore(student.getWindForce(), standard.getWindForce()));
    
    // 2. 风向评分 (1.0分)
    totalScore = totalScore.add(calculateWindDirectionScore(student.getWindDirection(), standard.getWindDirectionAngleRange()));
    
    // 3. 最低气温评分 (2.0分) - 容差±2℃
    totalScore = totalScore.add(calculateTemperatureScore(student.getMinTemperature(), standard.getMinTemperature(), 2.0));
    
    // 4. 最高气温评分 (2.0分) - 容差±2℃  
    totalScore = totalScore.add(calculateTemperatureScore(student.getMaxTemperature(), standard.getMaxTemperature(), 2.0));
    
    // 5. 降水评分 (2.0分)
    totalScore = totalScore.add(calculatePrecipitationScore(student.getPrecipitation(), standard.getPrecipitation()));
    
    // 6. 灾害天气评分 (2.0分)
    totalScore = totalScore.add(calculateDisasterWeatherScore(student.getDisasterWeather(), standard.getDisasterWeather()));
    
    return totalScore;
}
```

### 关键评分方法实现

#### 风力评分方法
```java
private BigDecimal calculateWindForceScore(String studentLevel, String standardLevel) {
    int student = parseWindForceLevel(studentLevel);
    int standard = parseWindForceLevel(standardLevel);
    
    // 根据考生预报等级判断得分
    switch (student) {
        case 0: // 静风
            if (standard == 0 || standard == 1) return new BigDecimal("1.0");
            return BigDecimal.ZERO;
            
        case 1: // 1级
            if (standard == 0 || standard == 1 || standard == 2) return new BigDecimal("1.0");
            return BigDecimal.ZERO;
            
        case 2: // 2级
            if (standard == 1 || standard == 2) return new BigDecimal("1.0");
            if (standard == 3) return new BigDecimal("0.8");
            return BigDecimal.ZERO;
            
        case 12: // 12级
            if (standard == 12 || standard == 13) return new BigDecimal("1.0"); // 13表示大于12级
            if (standard == 11) return new BigDecimal("0.8");
            return BigDecimal.ZERO;
            
        default: // 3-11级常规处理
            if (standard == student) return new BigDecimal("1.0"); // 完全匹配
            if (standard == student - 1 || standard == student + 1) return new BigDecimal("0.8"); // 相邻等级
            return BigDecimal.ZERO;
    }
}

/**
 * 解析风力等级字符串为数字
 * @param windForceStr 风力等级字符串
 * @return 风力等级数字，大于12级返回13
 */
private int parseWindForceLevel(String windForceStr) {
    if (windForceStr == null) return -1;
    
    switch (windForceStr.toLowerCase()) {
        case "静风":
        case "0级":
            return 0;
        case "1级":
            return 1;
        case "2级":
            return 2;
        case "3级":
            return 3;
        case "4级":
            return 4;
        case "5级":
            return 5;
        case "6级":
            return 6;
        case "7级":
            return 7;
        case "8级":
            return 8;
        case "9级":
            return 9;
        case "10级":
            return 10;
        case "11级":
            return 11;
        case "12级":
            return 12;
        case "大于12级":
        case ">12级":
            return 13;
        default:
            // 尝试解析数字
            try {
                String numStr = windForceStr.replaceAll("[^0-9]", "");
                int level = Integer.parseInt(numStr);
                return Math.min(level, 12); // 最大12级
            } catch (NumberFormatException e) {
                return -1; // 解析失败
            }
    }
}
```

#### 风向评分方法
```java
private BigDecimal calculateWindDirectionScore(String studentDirection, String standardAngleRange) {
    if ("无风".equals(studentDirection) || "无风".equals(standardAngleRange)) {
        return "无风".equals(studentDirection) && "无风".equals(standardAngleRange) ? 
               new BigDecimal("1.0") : BigDecimal.ZERO;
    }
    
    // 解析标准答案的角度范围
    double[] angleRange = parseAngleRange(standardAngleRange);
    if (angleRange == null) return BigDecimal.ZERO;
    
    double startAngle = angleRange[0];
    double endAngle = angleRange[1];
    
    // 根据考生填写的八方位判断得分
    switch (studentDirection) {
        case "北":
            if (isInAngleRange(startAngle, endAngle, 337.5, 22.5)) return new BigDecimal("1.0");
            if (isInAngleRange(startAngle, endAngle, 22.5, 45.0) || 
                isInAngleRange(startAngle, endAngle, 315.0, 337.5)) return new BigDecimal("0.6");
            break;
            
        case "东北":
            if (isInAngleRange(startAngle, endAngle, 22.5, 67.5)) return new BigDecimal("1.0");
            if (isInAngleRange(startAngle, endAngle, 0.0, 22.5) || 
                isInAngleRange(startAngle, endAngle, 67.5, 90.0)) return new BigDecimal("0.6");
            break;
            
        case "东":
            if (isInAngleRange(startAngle, endAngle, 67.5, 90.0) ||
                isInAngleRange(startAngle, endAngle, 90.0, 112.5)) return new BigDecimal("1.0");
            if (isInAngleRange(startAngle, endAngle, 45.0, 67.5) || 
                isInAngleRange(startAngle, endAngle, 112.5, 135.0)) return new BigDecimal("0.6");
            break;
            
        case "东南":
            if (isInAngleRange(startAngle, endAngle, 112.5, 157.5)) return new BigDecimal("1.0");
            if (isInAngleRange(startAngle, endAngle, 90.0, 112.5) || 
                isInAngleRange(startAngle, endAngle, 157.5, 180.0)) return new BigDecimal("0.6");
            break;
            
        case "南":
            if (isInAngleRange(startAngle, endAngle, 157.5, 202.5)) return new BigDecimal("1.0");
            if (isInAngleRange(startAngle, endAngle, 135.0, 157.5) || 
                isInAngleRange(startAngle, endAngle, 202.5, 225.0)) return new BigDecimal("0.6");
            break;
            
        case "西南":
            if (isInAngleRange(startAngle, endAngle, 202.5, 247.5)) return new BigDecimal("1.0");
            if (isInAngleRange(startAngle, endAngle, 180.0, 202.5) || 
                isInAngleRange(startAngle, endAngle, 247.5, 270.0)) return new BigDecimal("0.6");
            break;
            
        case "西":
            if (isInAngleRange(startAngle, endAngle, 247.5, 292.5)) return new BigDecimal("1.0");
            if (isInAngleRange(startAngle, endAngle, 225.0, 247.5) || 
                isInAngleRange(startAngle, endAngle, 292.5, 315.0)) return new BigDecimal("0.6");
            break;
            
        case "西北":
            if (isInAngleRange(startAngle, endAngle, 292.5, 337.5)) return new BigDecimal("1.0");
            if (isInAngleRange(startAngle, endAngle, 270.0, 292.5) || 
                isInAngleRange(startAngle, endAngle, 337.5, 360.0)) return new BigDecimal("0.6");
            break;
    }
    
    return BigDecimal.ZERO;
}

/**
 * 解析角度范围字符串，如"67.5～90.0°"
 */
private double[] parseAngleRange(String angleRangeStr) {
    try {
        // 解析类似"67.5～90.0°"的字符串
        String cleanStr = angleRangeStr.replace("°", "").replace("～", "-");
        String[] parts = cleanStr.split("-");
        if (parts.length == 2) {
            return new double[]{Double.parseDouble(parts[0]), Double.parseDouble(parts[1])};
        }
    } catch (Exception e) {
        // 解析失败，返回null
    }
    return null;
}

/**
 * 判断角度范围是否在指定区间内
 */
private boolean isInAngleRange(double startAngle, double endAngle, double targetStart, double targetEnd) {
    // 处理跨越0度的特殊情况
    if (targetStart > targetEnd) {
        return (startAngle >= targetStart && startAngle <= 360.0) || 
               (startAngle >= 0.0 && startAngle <= targetEnd) ||
               (endAngle >= targetStart && endAngle <= 360.0) || 
               (endAngle >= 0.0 && endAngle <= targetEnd);
    }
    
    return (startAngle >= targetStart && startAngle <= targetEnd) || 
           (endAngle >= targetStart && endAngle <= targetEnd) ||
           (startAngle <= targetStart && endAngle >= targetEnd);
}
```

#### 温度容差评分方法
```java
private BigDecimal calculateTemperatureScore(Integer studentTemp, Integer standardTemp, double maxScore) {
    if (studentTemp == null || standardTemp == null) return BigDecimal.ZERO;
    
    // 计算温度差的绝对值
    int tempDifference = Math.abs(studentTemp - standardTemp);
    
    // 容差范围：≤2℃为准确，>2℃为错误
    return tempDifference <= 2 ? new BigDecimal(maxScore) : BigDecimal.ZERO;
}
```

#### 降水评分方法
```java
private BigDecimal calculatePrecipitationScore(String studentType, String standardType) {
    if (studentType == null || standardType == null) return BigDecimal.ZERO;
    
    // 使用评分矩阵查表法
    return getPrecipitationScoreFromMatrix(studentType, standardType);
}

/**
 * 根据评分矩阵获取降水评分
 */
private BigDecimal getPrecipitationScoreFromMatrix(String student, String standard) {
    // 创建评分矩阵映射
    Map<String, Map<String, Double>> scoreMatrix = createPrecipitationScoreMatrix();
    
    Map<String, Double> studentRow = scoreMatrix.get(student);
    if (studentRow == null) return BigDecimal.ZERO;
    
    Double score = studentRow.get(standard);
    return score != null ? new BigDecimal(score) : BigDecimal.ZERO;
}

/**
 * 创建降水评分矩阵
 */
private Map<String, Map<String, Double>> createPrecipitationScoreMatrix() {
    Map<String, Map<String, Double>> matrix = new HashMap<>();
    
    // 无雨雪预报
    Map<String, Double> noRainSnow = new HashMap<>();
    noRainSnow.put("无雨雪", 2.0);
    noRainSnow.put("雨夹雪", 0.0);
    noRainSnow.put("小雨", 0.0);
    noRainSnow.put("小雪", 0.0);
    noRainSnow.put("中雨", 0.0);
    noRainSnow.put("中雪", 0.0);
    noRainSnow.put("大雨", 0.0);
    noRainSnow.put("暴雨", 0.0);
    noRainSnow.put("大雪", 0.0);
    noRainSnow.put("大暴雨以上", 0.0);
    noRainSnow.put("暴雪", 0.0);
    matrix.put("无雨雪", noRainSnow);
    
    // 雨夹雪预报
    Map<String, Double> sleet = new HashMap<>();
    sleet.put("无雨雪", 0.0);
    sleet.put("雨夹雪", 2.0);
    sleet.put("小雨", 2.0);
    sleet.put("小雪", 2.0);
    sleet.put("中雨", 1.2);
    sleet.put("中雪", 1.2);
    sleet.put("大雨", 0.0);
    sleet.put("暴雨", 0.0);
    sleet.put("大雪", 0.0);
    sleet.put("大暴雨以上", 0.0);
    sleet.put("暴雪", 0.0);
    matrix.put("雨夹雪", sleet);
    
    // 小雨预报
    Map<String, Double> lightRain = new HashMap<>();
    lightRain.put("无雨雪", 0.0);
    lightRain.put("雨夹雪", 2.0);
    lightRain.put("小雨", 2.0);
    lightRain.put("小雪", 0.0);
    lightRain.put("中雨", 1.2);
    lightRain.put("中雪", 0.0);
    lightRain.put("大雨", 0.0);
    lightRain.put("暴雨", 0.0);
    lightRain.put("大雪", 0.0);
    lightRain.put("大暴雨以上", 0.0);
    lightRain.put("暴雪", 0.0);
    matrix.put("小雨", lightRain);
    
    // 小雪预报
    Map<String, Double> lightSnow = new HashMap<>();
    lightSnow.put("无雨雪", 0.0);
    lightSnow.put("雨夹雪", 2.0);
    lightSnow.put("小雨", 0.0);
    lightSnow.put("小雪", 2.0);
    lightSnow.put("中雨", 0.0);
    lightSnow.put("中雪", 1.2);
    lightSnow.put("大雨", 0.0);
    lightSnow.put("暴雨", 0.0);
    lightSnow.put("大雪", 0.0);
    lightSnow.put("大暴雨以上", 0.0);
    lightSnow.put("暴雪", 0.0);
    matrix.put("小雪", lightSnow);
    
    // 中雨预报
    Map<String, Double> moderateRain = new HashMap<>();
    moderateRain.put("无雨雪", 0.0);
    moderateRain.put("雨夹雪", 1.2);
    moderateRain.put("小雨", 1.2);
    moderateRain.put("小雪", 0.0);
    moderateRain.put("中雨", 2.0);
    moderateRain.put("中雪", 0.0);
    moderateRain.put("大雨", 1.2);
    moderateRain.put("暴雨", 0.0);
    moderateRain.put("大雪", 0.0);
    moderateRain.put("大暴雨以上", 0.0);
    moderateRain.put("暴雪", 0.0);
    matrix.put("中雨", moderateRain);
    
    // 中雪预报
    Map<String, Double> moderateSnow = new HashMap<>();
    moderateSnow.put("无雨雪", 0.0);
    moderateSnow.put("雨夹雪", 1.2);
    moderateSnow.put("小雨", 0.0);
    moderateSnow.put("小雪", 1.2);
    moderateSnow.put("中雨", 0.0);
    moderateSnow.put("中雪", 2.0);
    moderateSnow.put("大雨", 0.0);
    moderateSnow.put("暴雨", 0.0);
    moderateSnow.put("大雪", 1.2);
    moderateSnow.put("大暴雨以上", 0.0);
    moderateSnow.put("暴雪", 0.0);
    matrix.put("中雪", moderateSnow);
    
    // 大雨预报
    Map<String, Double> heavyRain = new HashMap<>();
    heavyRain.put("无雨雪", 0.0);
    heavyRain.put("雨夹雪", 0.0);
    heavyRain.put("小雨", 0.0);
    heavyRain.put("小雪", 0.0);
    heavyRain.put("中雨", 1.2);
    heavyRain.put("中雪", 0.0);
    heavyRain.put("大雨", 2.0);
    heavyRain.put("暴雨", 1.2);
    heavyRain.put("大雪", 0.0);
    heavyRain.put("大暴雨以上", 0.0);
    heavyRain.put("暴雪", 0.0);
    matrix.put("大雨", heavyRain);
    
    // 暴雨预报
    Map<String, Double> torrentialRain = new HashMap<>();
    torrentialRain.put("无雨雪", 0.0);
    torrentialRain.put("雨夹雪", 0.0);
    torrentialRain.put("小雨", 0.0);
    torrentialRain.put("小雪", 0.0);
    torrentialRain.put("中雨", 0.0);
    torrentialRain.put("中雪", 0.0);
    torrentialRain.put("大雨", 1.2);
    torrentialRain.put("暴雨", 2.0);
    torrentialRain.put("大雪", 0.0);
    torrentialRain.put("大暴雨以上", 1.2);
    torrentialRain.put("暴雪", 0.0);
    matrix.put("暴雨", torrentialRain);
    
    // 大雪预报
    Map<String, Double> heavySnow = new HashMap<>();
    heavySnow.put("无雨雪", 0.0);
    heavySnow.put("雨夹雪", 0.0);
    heavySnow.put("小雨", 0.0);
    heavySnow.put("小雪", 0.0);
    heavySnow.put("中雨", 0.0);
    heavySnow.put("中雪", 1.2);
    heavySnow.put("大雨", 0.0);
    heavySnow.put("暴雨", 0.0);
    heavySnow.put("大雪", 2.0);
    heavySnow.put("大暴雨以上", 0.0);
    heavySnow.put("暴雪", 1.2);
    matrix.put("大雪", heavySnow);
    
    // 大暴雨以上预报
    Map<String, Double> extremeRain = new HashMap<>();
    extremeRain.put("无雨雪", 0.0);
    extremeRain.put("雨夹雪", 0.0);
    extremeRain.put("小雨", 0.0);
    extremeRain.put("小雪", 0.0);
    extremeRain.put("中雨", 0.0);
    extremeRain.put("中雪", 0.0);
    extremeRain.put("大雨", 0.0);
    extremeRain.put("暴雨", 1.2);
    extremeRain.put("大雪", 0.0);
    extremeRain.put("大暴雨以上", 2.0);
    extremeRain.put("暴雪", 0.0);
    matrix.put("大暴雨以上", extremeRain);
    
    // 暴雪预报
    Map<String, Double> blizzard = new HashMap<>();
    blizzard.put("无雨雪", 0.0);
    blizzard.put("雨夹雪", 0.0);
    blizzard.put("小雨", 0.0);
    blizzard.put("小雪", 0.0);
    blizzard.put("中雨", 0.0);
    blizzard.put("中雪", 0.0);
    blizzard.put("大雨", 0.0);
    blizzard.put("暴雨", 0.0);
    blizzard.put("大雪", 1.2);
    blizzard.put("大暴雨以上", 0.0);
    blizzard.put("暴雪", 2.0);
    matrix.put("暴雪", blizzard);
    
    return matrix;
}
```

#### 灾害天气评分方法
```java
private BigDecimal calculateDisasterWeatherScore(List<String> studentForecast, List<String> standardActual) {
    // 处理空值情况
    Set<String> studentSet = new HashSet<>(studentForecast != null ? studentForecast : Collections.emptyList());
    Set<String> standardSet = new HashSet<>(standardActual != null ? standardActual : Collections.emptyList());
    
    // 处理"无"的特殊情况
    boolean studentHasNone = studentSet.contains("无") || studentSet.isEmpty();
    boolean standardHasNone = standardSet.contains("无") || standardSet.isEmpty();
    
    if (studentHasNone && standardHasNone) {
        return new BigDecimal("2.0"); // 都为"无"，完全匹配
    }
    
    if (studentHasNone || standardHasNone) {
        return BigDecimal.ZERO; // 一个为"无"，一个有灾害，不匹配
    }
    
    // 移除"无"选项，只处理具体的灾害天气
    studentSet.remove("无");
    standardSet.remove("无");
    
    // 计算匹配的灾害天气数量
    Set<String> correctMatches = new HashSet<>(studentSet);
    correctMatches.retainAll(standardSet); // 求交集，即正确预报的灾害天气
    
    // 按比例评分：(匹配数量 / 实况总数量) × 2.0
    if (standardSet.isEmpty()) {
        return new BigDecimal("2.0"); // 实况无灾害且预报也无灾害
    }
    
    double ratio = (double) correctMatches.size() / standardSet.size();
    return new BigDecimal(ratio * 2.0).setScale(2, RoundingMode.HALF_UP);
}

/**
 * 验证灾害天气类型是否有效
 */
private boolean isValidDisasterWeatherType(String weatherType) {
    Set<String> validTypes = Set.of(
        "无", "暴雨", "暴雪", "高温", "大风", "寒潮", 
        "大雾", "沙尘暴", "雷暴", "冰雹"
    );
    return validTypes.contains(weatherType);
}

/**
 * 过滤并验证灾害天气列表
 */
private List<String> filterValidDisasterWeather(List<String> weatherList) {
    if (weatherList == null) return Collections.emptyList();
    
    return weatherList.stream()
            .filter(this::isValidDisasterWeatherType)
            .collect(Collectors.toList());
}
```

## 评分系统配置

### 评分配置JSON结构
```json
{
  "totalScore": 10,
  "elementWeights": {
    "windForce": {
      "maxScore": 1.0,
      "scoringType": "level_based",
      "levels": {
        "exact": 1.0,
        "adjacent": 0.8,
        "near": 0.6,
        "distant": 0.2,
        "wrong": 0.0
      }
    },
    "windDirection": {
      "maxScore": 1.0,
      "scoringType": "angle_range_based",
      "levels": {
        "angleMatch": 1.0,
        "adjacentRange": 0.6,
        "wrong": 0.0
      },
      "angleRanges": {
        "北": ["337.5-22.5"],
        "东北": ["22.5-67.5"],
        "东": ["67.5-90.0", "90.0-112.5"],
        "东南": ["112.5-157.5"],
        "南": ["157.5-202.5"],
        "西南": ["202.5-247.5"],
        "西": ["247.5-292.5"],
        "西北": ["292.5-337.5"]
      }
    },
    "minTemperature": {
      "maxScore": 2.0,
      "scoringType": "tolerance_based",
      "toleranceEnabled": true,
      "toleranceRange": 2,
      "unit": "℃"
    },
    "maxTemperature": {
      "maxScore": 2.0, 
      "scoringType": "tolerance_based",
      "toleranceEnabled": true,
      "toleranceRange": 2,
      "unit": "℃"
    },
    "precipitation": {
      "maxScore": 2.0,
      "scoringType": "matrix_based",
      "scoreMatrix": {
        "无雨雪": {"无雨雪": 2.0, "雨夹雪": 0.0, "小雨": 0.0, "小雪": 0.0, "中雨": 0.0, "中雪": 0.0, "大雨": 0.0, "暴雨": 0.0, "大雪": 0.0, "大暴雨以上": 0.0, "暴雪": 0.0},
        "雨夹雪": {"无雨雪": 0.0, "雨夹雪": 2.0, "小雨": 2.0, "小雪": 2.0, "中雨": 1.2, "中雪": 1.2, "大雨": 0.0, "暴雨": 0.0, "大雪": 0.0, "大暴雨以上": 0.0, "暴雪": 0.0},
        "小雨": {"无雨雪": 0.0, "雨夹雪": 2.0, "小雨": 2.0, "小雪": 0.0, "中雨": 1.2, "中雪": 0.0, "大雨": 0.0, "暴雨": 0.0, "大雪": 0.0, "大暴雨以上": 0.0, "暴雪": 0.0},
        "小雪": {"无雨雪": 0.0, "雨夹雪": 2.0, "小雨": 0.0, "小雪": 2.0, "中雨": 0.0, "中雪": 1.2, "大雨": 0.0, "暴雨": 0.0, "大雪": 0.0, "大暴雨以上": 0.0, "暴雪": 0.0},
        "中雨": {"无雨雪": 0.0, "雨夹雪": 1.2, "小雨": 1.2, "小雪": 0.0, "中雨": 2.0, "中雪": 0.0, "大雨": 1.2, "暴雨": 0.0, "大雪": 0.0, "大暴雨以上": 0.0, "暴雪": 0.0},
        "中雪": {"无雨雪": 0.0, "雨夹雪": 1.2, "小雨": 0.0, "小雪": 1.2, "中雨": 0.0, "中雪": 2.0, "大雨": 0.0, "暴雨": 0.0, "大雪": 1.2, "大暴雨以上": 0.0, "暴雪": 0.0},
        "大雨": {"无雨雪": 0.0, "雨夹雪": 0.0, "小雨": 0.0, "小雪": 0.0, "中雨": 1.2, "中雪": 0.0, "大雨": 2.0, "暴雨": 1.2, "大雪": 0.0, "大暴雨以上": 0.0, "暴雪": 0.0},
        "暴雨": {"无雨雪": 0.0, "雨夹雪": 0.0, "小雨": 0.0, "小雪": 0.0, "中雨": 0.0, "中雪": 0.0, "大雨": 1.2, "暴雨": 2.0, "大雪": 0.0, "大暴雨以上": 1.2, "暴雪": 0.0},
        "大雪": {"无雨雪": 0.0, "雨夹雪": 0.0, "小雨": 0.0, "小雪": 0.0, "中雨": 0.0, "中雪": 1.2, "大雨": 0.0, "暴雨": 0.0, "大雪": 2.0, "大暴雨以上": 0.0, "暴雪": 1.2},
        "大暴雨以上": {"无雨雪": 0.0, "雨夹雪": 0.0, "小雨": 0.0, "小雪": 0.0, "中雨": 0.0, "中雪": 0.0, "大雨": 0.0, "暴雨": 1.2, "大雪": 0.0, "大暴雨以上": 2.0, "暴雪": 0.0},
        "暴雪": {"无雨雪": 0.0, "雨夹雪": 0.0, "小雨": 0.0, "小雪": 0.0, "中雨": 0.0, "中雪": 0.0, "大雨": 0.0, "暴雨": 0.0, "大雪": 1.2, "大暴雨以上": 0.0, "暴雪": 2.0}
      },
      "specialRules": {
        "sleetCrossType": "雨夹雪与小雨/小雪相互2.0分匹配",
        "sleetAdjacent": "雨夹雪与中雨/中雪相互1.2分匹配",
        "crossTypeZero": "雨类与雪类之间（除雨夹雪特殊规则）一律0分"
      }
    },
    "disasterWeather": {
      "maxScore": 2.0,
      "scoringType": "exact_match_with_proportion",
      "algorithm": "proportion_based",
      "validTypes": [
        "无", "暴雨", "暴雪", "高温", "大风", 
        "寒潮", "大雾", "沙尘暴", "雷暴", "冰雹"
      ],
      "rules": {
        "exactMatch": "预报与实况完全一致得满分",
        "proportionMatch": "多种灾害时按正确比例给分",
        "noExtraPenalty": "额外预报的灾害不扣分",
        "noneSpecialHandling": "无灾害情况特殊处理"
      }
    }
  }
}
```

## 质量保证和验证

### 评分准确性验证
1. **边界值测试**：验证各要素的临界评分情况
2. **组合测试**：验证不同要素组合的总分计算
3. **异常值处理**：验证空值、非法值的处理逻辑
4. **精度测试**：确保小数点精度处理正确

### 性能优化考虑
1. **缓存机制**：缓存等级关系映射表
2. **并行计算**：多站点评分并行处理  
3. **算法优化**：预计算相似度矩阵
4. **内存管理**：大批量评分时的内存控制

## 扩展和维护

### 评分规则调整
1. **配置化管理**：通过配置文件调整评分标准
2. **版本控制**：支持多套评分标准并存
3. **A/B测试**：支持新旧评分标准对比验证
4. **审计日志**：记录评分规则变更历史

### 新要素支持
1. **插件式架构**：支持新气象要素的评分插件
2. **评分策略扩展**：支持自定义评分算法
3. **权重动态调整**：支持根据题目难度调整权重
4. **多维度评分**：支持时间序列、空间分布等复合评分

# 强对流天气临近预报考试评分细节

## 概述

强对流天气临近预报考试包含两个题目，分别采用不同的评分规则和计算方法。

## 强对流天气临近预报第一题评分规则

### 总体评分框架
- **总分**：68分
- **站点评分**：44分（4个站点，每站点11分）
- **过程极端天气预报**：4分
- **预报理由简述**：20分

### 站点评分详细规则（每站点11分）

#### 评分要素分配
- **短时强降水类型正确**：3分
- **短时强降水强度等级正确**：1分
- **雷暴大风类型正确**：3分
- **雷暴大风强度等级正确**：1分
- **2cm以上大冰雹正确**：3分

#### 短时强降水评分规则

**类型判断标准**：
- 实况小时降水量≥20mm：有短时强降水
- 实况小时降水量<20mm：无短时强降水

**评分规则**：
1. **有短时强降水情况**：
   - 考生在对应强度等级框画"○"：类型分3分 + 强度分1分 = 4分
   - 考生在其他强度等级框画"○"：类型分3分 + 强度分0分 = 3分
   - 考生所有强降水框均画"×"：类型分0分 + 强度分0分 = 0分

2. **无短时强降水情况**：
   - 考生任意强降水框画"○"：类型分0分 + 强度分0分 = 0分
   - 考生所有强降水框均画"×"：类型分3分 + 强度分1分 = 4分

**强度等级划分**：
- 1小时强降水20≤R1＜40mm
- 1小时强降水40≤R1＜80mm
- 1小时强降水80≤R1mm

#### 雷暴大风评分规则

**类型判断标准**：
- 实况瞬时极大风≥8级：有雷暴大风
- 实况瞬时极大风<8级：无雷暴大风

**评分规则**：
1. **有雷暴大风情况**：
   - 考生在对应强度等级框画"○"：类型分3分 + 强度分1分 = 4分
   - 考生在其他强度等级框画"○"：类型分3分 + 强度分0分 = 3分
   - 考生所有大风框均画"×"：类型分0分 + 强度分0分 = 0分

2. **无雷暴大风情况**：
   - 考生任意大风框画"○"：类型分0分 + 强度分0分 = 0分
   - 考生所有大风框均画"×"：类型分3分 + 强度分1分 = 4分

#### 大冰雹评分规则

**判断标准**：
- 实况冰雹直径≥2cm：有大冰雹
- 实况冰雹直径<2cm或无冰雹：无大冰雹

**评分规则**：
1. **有大冰雹情况**：
   - 考生预报有大冰雹：3分
   - 考生预报无大冰雹：0分

2. **无大冰雹情况**：
   - 考生预报有大冰雹：0分
   - 考生预报无大冰雹：3分

### 过程极端天气预报评分（4分）

#### 评分原则
对4个站点中最强的短时强降水和雷暴大风天气预报进行评分。

#### 评分规则
- **最强短时强降水预报正确**：2分
- **最强雷暴大风预报正确**：2分

#### 特殊情况处理
若存在两个或多个站点同类天气强度完全一致的情况，则多站点分摊该类极端天气的2分。

**分摊计算公式**：
```
单站点得分 = 2分 / 最强强度站点数量
```

#### 评分示例

**示例1：单站点最强**
- 站点1：50mm/h强降水，站点2-4：30mm/h强降水
- 考生正确预报站点1的50mm/h强降水：得2分

**示例2：多站点并列最强**
- 站点1、站点2：50mm/h强降水，站点3-4：30mm/h强降水
- 考生正确预报站点1的50mm/h强降水：得1分（2分÷2站点）
- 考生正确预报站点2的50mm/h强降水：得1分（2分÷2站点）

### 预报理由简述评分（20分）

采用主观评分方式，根据预报理由的科学性、逻辑性和完整性进行评分。

#### 评分要点
1. **气象原理正确性**：5分
2. **分析逻辑清晰性**：5分
3. **预报依据充分性**：5分
4. **表达完整准确性**：5分

## 强对流天气临近预报第二题评分规则

### 总体评分框架
- **总分**：32分
- **评分方式**：TS评分 + 百分位排名赋分

### 评分流程

#### 第一步：落区文件生成
选手使用MICAPS4或SWAN绘制强对流灾害性天气落区，生成MICAPS第十四类文件（落区文件）。

#### 第二步：站点匹配判断
基于pandas.DataFrame进行站点级别的评分：
- **trueResult列**：站点真实发生灾害性天气为True，否则为False
- **result列**：站点在选手绘制落区内为True，否则为False

#### 第三步：TS评分计算

**实况判断标准**：
- **强降水**：测站小时雨强≥20mm
- **雷暴大风**：瞬时极大风≥8级

**TS评分矩阵**：

| 预报/实况 | 有（实况） | 无（实况） |
|-----------|------------|------------|
| 有（预报） | 正确A      | 空报B      |
| 无（预报） | 漏报C      | 不参与评分  |

**TS评分公式**：
```
TS = A / (A + B + C)
```

其中：
- A：正确预报数（预报有且实况有）
- B：空报数（预报有但实况无）
- C：漏报数（预报无但实况有）

#### 第四步：最终得分计算

**百分位排名赋分公式**：
```
选手最终得分 = 8 + 24 × (TSₙ - TSₘᵢₙ) / (TSₘₐₓ - TSₘᵢₙ)
```

**参数说明**：
- TSₙ：某考生的TS评分
- TSₘᵢₙ：所有考生中最低TS评分
- TSₘₐₓ：所有考生中最高TS评分

**得分特点**：
- **得分范围**：8分～32分
- **精度处理**：四舍五入保留一位小数
- **相对评分**：基于全体考生表现的相对排名

### 评分示例

#### TS评分计算示例

**场景**：某考生绘制强降水落区预报

**站点统计**：
- 总站点数：1000个
- 实况有强降水：100个站点
- 预报有强降水：120个站点
- 预报正确：80个站点（A=80）
- 空报：40个站点（B=40）
- 漏报：20个站点（C=20）

**TS计算**：
```
TS = 80 / (80 + 40 + 20) = 80 / 140 = 0.571
```

#### 最终得分计算示例

**假设全体考生TS分布**：
- TSₘₐₓ = 0.750（最高分考生）
- TSₘᵢₙ = 0.200（最低分考生）
- TSₙ = 0.571（该考生）

**最终得分计算**：
```
得分 = 8 + 24 × (0.571 - 0.200) / (0.750 - 0.200)
     = 8 + 24 × 0.371 / 0.550
     = 8 + 24 × 0.675
     = 8 + 16.2
     = 24.2分
```

### 评分算法实现

#### TS评分计算方法
```java
public double calculateTSScore(List<StationResult> stations) {
    int correctForecast = 0;  // A: 正确预报
    int falseAlarm = 0;       // B: 空报
    int missed = 0;           // C: 漏报

    for (StationResult station : stations) {
        boolean trueResult = station.isTrueResult();
        boolean forecast = station.isResult();

        if (forecast && trueResult) {
            correctForecast++;  // 预报有且实况有
        } else if (forecast && !trueResult) {
            falseAlarm++;       // 预报有但实况无
        } else if (!forecast && trueResult) {
            missed++;           // 预报无但实况有
        }
        // 预报无且实况无的情况不参与计算
    }

    int total = correctForecast + falseAlarm + missed;
    return total > 0 ? (double) correctForecast / total : 0.0;
}
```

#### 最终得分计算方法
```java
public double calculateFinalScore(double tsScore, double tsMin, double tsMax) {
    if (tsMax == tsMin) {
        return 20.0; // 所有考生TS相同时的默认分数
    }

    double normalizedScore = (tsScore - tsMin) / (tsMax - tsMin);
    double finalScore = 8.0 + 24.0 * normalizedScore;

    // 四舍五入保留一位小数
    return Math.round(finalScore * 10.0) / 10.0;
}
```

#### 批量评分处理方法
```java
public List<ExamResult> processAllStudents(List<StudentSubmission> submissions) {
    List<ExamResult> results = new ArrayList<>();
    List<Double> allTSScores = new ArrayList<>();

    // 第一轮：计算所有考生的TS分数
    for (StudentSubmission submission : submissions) {
        double tsScore = calculateTSScore(submission.getStationResults());
        allTSScores.add(tsScore);

        ExamResult result = new ExamResult();
        result.setStudentId(submission.getStudentId());
        result.setTsScore(tsScore);
        results.add(result);
    }

    // 计算TS分数的最值
    double tsMin = Collections.min(allTSScores);
    double tsMax = Collections.max(allTSScores);

    // 第二轮：计算最终得分
    for (ExamResult result : results) {
        double finalScore = calculateFinalScore(result.getTsScore(), tsMin, tsMax);
        result.setFinalScore(finalScore);
    }

    return results;
}
```

# 历史个例降水落区评分细节

## 概述

历史个例降水落区评分是基于CMA-MESO模式站点格式的24小时降水预报进行的TS评分，包括晴雨评分和降水分级评分两个部分。该评分方法用于评估参赛选手对前一天20时起报的降水预报准确性。

## 评分体系架构

### 总体评分框架
- **总分**：40分
- **评分类型**：晴雨TS评分 + 降水分级TS评分
- **评分方式**：技巧评分 = 基础分 + TS评分×0.7
- **权重分配**：各降水量级按权重加权求和

### 降水量级定义
```
晴雨：有降水/无降水
小雨：0.1—9.9毫米
中雨：10—24.9毫米
大雨：25—49.9毫米
暴雨：50—99.9毫米
大暴雨：≥100毫米
```

### 权重分配
```
晴雨：    0.1
小雨：    0.2
中雨：    0.2
大雨：    0.2
暴雨：    0.2
大暴雨：  0.1
```

## 详细评分规则

### 1. 晴雨TS评分

#### 评分原理
对区域内所有站点进行晴雨（有降水/无降水）的二分类评分。

#### TS评分矩阵

| 预报/实况 | 有降水（实况） | 无降水（实况） |
|-----------|----------------|----------------|
| 有降水（预报） | 正确A | 空报B |
| 无降水（预报） | 漏报C | 正确D |

#### TS评分公式
```
晴雨TS = (A + D) / (A + B + C + D)
```

**参数说明**：
- A：正确预报有降水的站点数
- B：空报降水的站点数（预报有但实况无）
- C：漏报降水的站点数（预报无但实况有）
- D：正确预报无降水的站点数

#### 评分示例

**场景**：某区域100个站点的晴雨预报评分

**统计结果**：
- 实况有降水：30个站点
- 实况无降水：70个站点
- 预报有降水：35个站点
- 预报无降水：65个站点

**混淆矩阵**：
- A（正确预报有降水）：25个站点
- B（空报）：10个站点（35-25）
- C（漏报）：5个站点（30-25）
- D（正确预报无降水）：60个站点（70-10）

**TS计算**：
```
晴雨TS = (25 + 60) / (25 + 10 + 5 + 60) = 85 / 100 = 0.85
```

### 2. 降水分级TS评分

#### 评分原理
以实况为准，当预报与实况的量级完全一致时才算正确，不一致时算错误或漏报。

#### TS评分矩阵（以小雨为例）

| 实况/预报 | 小雨（预报） | 小雨以外量级（预报） | 无雨（预报） |
|-----------|--------------|---------------------|--------------|
| 小雨（实况） | 正确A | 错误B | 漏报C |

#### TS评分公式
```
小雨TS = A / (A + B + C)
```

**参数说明**：
- A：实况小雨且预报小雨的站点数
- B：实况小雨但预报为小雨以外量级的站点数
- C：实况小雨但预报无雨的站点数

#### 各量级评分规则

**小雨（0.1—9.9毫米）评分**：
- 实况小雨 + 预报小雨 → 正确A
- 实况小雨 + 预报中雨/大雨/暴雨/大暴雨 → 错误B
- 实况小雨 + 预报无雨 → 漏报C

**中雨（10—24.9毫米）评分**：
- 实况中雨 + 预报中雨 → 正确A
- 实况中雨 + 预报小雨/大雨/暴雨/大暴雨 → 错误B
- 实况中雨 + 预报无雨 → 漏报C

**大雨（25—49.9毫米）评分**：
- 实况大雨 + 预报大雨 → 正确A
- 实况大雨 + 预报小雨/中雨/暴雨/大暴雨 → 错误B
- 实况大雨 + 预报无雨 → 漏报C

**暴雨（50—99.9毫米）评分**：
- 实况暴雨 + 预报暴雨 → 正确A
- 实况暴雨 + 预报小雨/中雨/大雨/大暴雨 → 错误B
- 实况暴雨 + 预报无雨 → 漏报C

**大暴雨（≥100毫米）评分**：
- 实况大暴雨 + 预报大暴雨 → 正确A
- 实况大暴雨 + 预报小雨/中雨/大雨/暴雨 → 错误B
- 实况大暴雨 + 预报无雨 → 漏报C

#### 评分示例

**场景**：某区域小雨TS评分计算

**统计结果**：
- 实况小雨站点：20个
- 预报小雨且实况小雨：15个（A=15）
- 预报中雨但实况小雨：3个（B=3）
- 预报无雨但实况小雨：2个（C=2）

**TS计算**：
```
小雨TS = 15 / (15 + 3 + 2) = 15 / 20 = 0.75
```

### 3. 基础分评定规则

#### 基础分标准
对于每个降水量级和晴雨的TS评分：

**基础分评定**：
- 参赛选手TS ≥ CMA-MESO模式TS → 基础分 = 0.3
- 参赛选手TS < CMA-MESO模式TS → 基础分 = 0.0

#### CMA-MESO模式基准
CMA-MESO模式作为业务模式基准，其TS评分作为参赛选手的比较标准。

### 4. 技巧评分计算

#### 技巧评分公式
```
技巧评分 = 基础分 + 参赛选手TS × 0.7
```

#### 各量级技巧评分计算

**晴雨技巧评分**：
```
晴雨技巧分 = 晴雨基础分 + 晴雨TS × 0.7
```

**小雨技巧评分**：
```
小雨技巧分 = 小雨基础分 + 小雨TS × 0.7
```

**中雨技巧评分**：
```
中雨技巧分 = 中雨基础分 + 中雨TS × 0.7
```

**大雨技巧评分**：
```
大雨技巧分 = 大雨基础分 + 大雨TS × 0.7
```

**暴雨技巧评分**：
```
暴雨技巧分 = 暴雨基础分 + 暴雨TS × 0.7
```

**大暴雨技巧评分**：
```
大暴雨技巧分 = 大暴雨基础分 + 大暴雨TS × 0.7
```

#### 技巧评分示例

**场景**：某参赛选手各量级TS评分与CMA-MESO对比

**TS评分对比**：
- 晴雨TS：选手0.85，CMA-MESO 0.80 → 基础分0.3
- 小雨TS：选手0.75，CMA-MESO 0.70 → 基础分0.3
- 中雨TS：选手0.60，CMA-MESO 0.65 → 基础分0.0
- 大雨TS：选手0.45，CMA-MESO 0.50 → 基础分0.0
- 暴雨TS：选手0.30，CMA-MESO 0.25 → 基础分0.3
- 大暴雨TS：选手0.20，CMA-MESO 0.15 → 基础分0.3

**技巧评分计算**：
- 晴雨技巧分 = 0.3 + 0.85 × 0.7 = 0.3 + 0.595 = 0.895
- 小雨技巧分 = 0.3 + 0.75 × 0.7 = 0.3 + 0.525 = 0.825
- 中雨技巧分 = 0.0 + 0.60 × 0.7 = 0.0 + 0.420 = 0.420
- 大雨技巧分 = 0.0 + 0.45 × 0.7 = 0.0 + 0.315 = 0.315
- 暴雨技巧分 = 0.3 + 0.30 × 0.7 = 0.3 + 0.210 = 0.510
- 大暴雨技巧分 = 0.3 + 0.20 × 0.7 = 0.3 + 0.140 = 0.440

### 5. 最终评分计算

#### 加权求和公式
```
最终评分 = (晴雨技巧分×0.1 + 小雨技巧分×0.2 + 中雨技巧分×0.2 +
           大雨技巧分×0.2 + 暴雨技巧分×0.2 + 大暴雨技巧分×0.1) × 40分
```

#### 最终评分示例

**使用上述技巧评分结果**：
```
加权求和 = 0.895×0.1 + 0.825×0.2 + 0.420×0.2 + 0.315×0.2 + 0.510×0.2 + 0.440×0.1
         = 0.0895 + 0.165 + 0.084 + 0.063 + 0.102 + 0.044
         = 0.5475

最终评分 = 0.5475 × 40 = 21.9分
```

## 评分算法实现

### 核心算法流程
```java
public double calculatePrecipitationScore(List<StationData> stations,
                                        Map<String, Double> cmaMesoTS) {
    // 1. 计算各量级TS评分
    Map<String, Double> studentTS = calculateAllLevelTS(stations);

    // 2. 计算基础分
    Map<String, Double> baseScores = calculateBaseScores(studentTS, cmaMesoTS);

    // 3. 计算技巧评分
    Map<String, Double> skillScores = calculateSkillScores(studentTS, baseScores);

    // 4. 加权求和计算最终评分
    return calculateWeightedFinalScore(skillScores);
}
```

### TS评分计算方法
```java
private Map<String, Double> calculateAllLevelTS(List<StationData> stations) {
    Map<String, Double> tsScores = new HashMap<>();

    // 晴雨TS评分
    tsScores.put("晴雨", calculateRainNoRainTS(stations));

    // 各量级TS评分
    tsScores.put("小雨", calculateLevelTS(stations, "小雨", 0.1, 9.9));
    tsScores.put("中雨", calculateLevelTS(stations, "中雨", 10.0, 24.9));
    tsScores.put("大雨", calculateLevelTS(stations, "大雨", 25.0, 49.9));
    tsScores.put("暴雨", calculateLevelTS(stations, "暴雨", 50.0, 99.9));
    tsScores.put("大暴雨", calculateLevelTS(stations, "大暴雨", 100.0, Double.MAX_VALUE));

    return tsScores;
}

private double calculateRainNoRainTS(List<StationData> stations) {
    int A = 0, B = 0, C = 0, D = 0;

    for (StationData station : stations) {
        boolean actualRain = station.getActualPrecipitation() >= 0.1;
        boolean forecastRain = station.getForecastPrecipitation() >= 0.1;

        if (forecastRain && actualRain) {
            A++; // 正确预报有降水
        } else if (forecastRain && !actualRain) {
            B++; // 空报
        } else if (!forecastRain && actualRain) {
            C++; // 漏报
        } else {
            D++; // 正确预报无降水
        }
    }

    int total = A + B + C + D;
    return total > 0 ? (double)(A + D) / total : 0.0;
}

private double calculateLevelTS(List<StationData> stations, String level,
                               double minValue, double maxValue) {
    int A = 0, B = 0, C = 0;

    for (StationData station : stations) {
        double actual = station.getActualPrecipitation();
        double forecast = station.getForecastPrecipitation();

        boolean actualInLevel = (actual >= minValue && actual <= maxValue);
        boolean forecastInLevel = (forecast >= minValue && forecast <= maxValue);

        if (actualInLevel) {
            if (forecastInLevel) {
                A++; // 正确预报该量级
            } else if (forecast >= 0.1) {
                B++; // 预报为其他量级
            } else {
                C++; // 漏报（预报无雨）
            }
        }
    }

    int total = A + B + C;
    return total > 0 ? (double)A / total : 0.0;
}
```

### 基础分计算方法
```java
private Map<String, Double> calculateBaseScores(Map<String, Double> studentTS,
                                               Map<String, Double> cmaMesoTS) {
    Map<String, Double> baseScores = new HashMap<>();

    for (String level : studentTS.keySet()) {
        double studentScore = studentTS.get(level);
        double cmaScore = cmaMesoTS.getOrDefault(level, 0.0);

        baseScores.put(level, studentScore >= cmaScore ? 0.3 : 0.0);
    }

    return baseScores;
}
```

### 技巧评分计算方法
```java
private Map<String, Double> calculateSkillScores(Map<String, Double> studentTS,
                                                 Map<String, Double> baseScores) {
    Map<String, Double> skillScores = new HashMap<>();

    for (String level : studentTS.keySet()) {
        double baseScore = baseScores.get(level);
        double tsScore = studentTS.get(level);

        skillScores.put(level, baseScore + tsScore * 0.7);
    }

    return skillScores;
}
```

### 最终评分计算方法
```java
private double calculateWeightedFinalScore(Map<String, Double> skillScores) {
    Map<String, Double> weights = Map.of(
        "晴雨", 0.1,
        "小雨", 0.2,
        "中雨", 0.2,
        "大雨", 0.2,
        "暴雨", 0.2,
        "大暴雨", 0.1
    );

    double weightedSum = 0.0;
    for (String level : skillScores.keySet()) {
        double skillScore = skillScores.get(level);
        double weight = weights.getOrDefault(level, 0.0);
        weightedSum += skillScore * weight;
    }

    return weightedSum * 40.0; // 乘以总分40分
}
```

## 评分特点和注意事项

### 评分特点
1. **严格量级匹配**：只有预报量级与实况量级完全一致才算正确
2. **基准对比评分**：以CMA-MESO模式为基准，体现预报技巧
3. **权重平衡设计**：各量级权重设计兼顾常见和极端天气
4. **技巧导向评分**：基础分+技巧分的设计鼓励超越业务模式

### 注意事项
1. **数据质量要求**：需要高质量的站点观测数据和模式预报数据
2. **时间匹配精度**：确保预报时效与实况观测时间的准确对应
3. **空间插值处理**：模式格点数据到站点的插值方法影响评分结果
4. **缺测数据处理**：需要制定明确的缺测数据处理规则

---

**文档版本**：v2.1
**最后更新**：2025-07-29
**维护人员**：天气预报模块开发团队
**审核状态**：已审核通过