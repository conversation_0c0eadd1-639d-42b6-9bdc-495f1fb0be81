# 历史个例评分系统 - 项目蓝图

## 项目概述

### 目标
开发一个前后端分离的历史个例考试评分系统，能够自动计算考生在天气预报历史个例考试中的得分，特别是针对灾害性天气（站点预报）的评分。

### 核心功能
- **自动评分引擎**：根据标准答案和考生答案计算各项气象要素得分
- **多维度评分**：支持风力、风向、温度、降水、灾害性天气等要素的综合评分
- **评分报告**：生成详细的评分分析报告和错误诊断
- **批量处理**：支持批量处理多个考生的答题结果
- **可视化展示**：通过图表展示评分结果和统计分析

## 技术架构

### 后端技术栈
- **框架**：Spring Boot + MyBatis Plus
- **数据库**：MySQL 8.0+
- **语言**：Java 8+
- **JSON处理**：Jackson/FastJSON

### 前端技术栈  
- **框架**：Vue.js + Element UI
- **构建工具**：Webpack
- **HTTP客户端**：Axios

### 核心模块
```
src/main/java/com/yf/exam/modules/
├── weather/               # 天气预报模块
│   ├── scoring/          # 评分引擎（新增）
│   │   ├── service/      # 评分服务
│   │   ├── algorithm/    # 评分算法
│   │   └── config/       # 评分配置
│   ├── entity/           # 实体类
│   ├── service/          # 业务服务
│   └── controller/       # 控制器
├── evaluation/           # 评估分析模块（新增）
└── report/              # 报告生成模块（新增）
```

## 数据架构

### 核心数据表
1. **el_weather_history_exam_answer** - 考生答案存储表
2. **el_qu** - 题目和标准答案表  
3. **el_weather_scoring_config** - 评分配置表（新增）
4. **el_weather_scoring_log** - 评分记录表（新增）
5. **el_weather_scoring_report** - 评分报告表（新增）

### 数据结构详解

#### 考生答案数据结构 (precipitation_answer/weather_answer)
```json
{
  "stations": [
    {
      "stationId": "站点ID",
      "stationName": "站点名称", 
      "windForce": "8级风力",
      "windDirection": "东北风",
      "minTemperature": -5,
      "maxTemperature": 8,
      "precipitation": "中雨",
      "disasterWeather": ["雷暴", "冰雹"]
    }
  ],
  "submitTime": "提交时间",
  "totalStations": 站点数量
}
```

#### 标准答案数据结构 (scenario_data)
```json
{
  "standardAnswer": {
    "stations": [
      {
        "stationId": "站点ID",
        "stationName": "站点名称",
        "windForce": "8级风力",
        "windDirection": "东北风", 
        "minTemperature": -5,
        "maxTemperature": 8,
        "precipitation": "中雨",
        "disasterWeather": ["雷暴", "冰雹"]
      }
    ]
  },
  "scoringConfig": {
    "totalScore": 100,
    "stationWeight": 10,
    "elementWeights": {
      "windForce": 1,
      "windDirection": 1,
      "minTemperature": 2,
      "maxTemperature": 2, 
      "precipitation": 2,
      "disasterWeather": 2
    }
  }
}
```

## 评分算法设计

### 评分标准参考
参考网站：https://www.heywhale.com/mw/project/682b3eba68145d8480869ba6
- **精确匹配原则**：各气象要素需要与标准答案精确匹配
- **权重分配**：不同要素具有不同的分值权重
- **站点综合评分**：每个站点独立评分，然后汇总

### 评分算法流程
1. **数据预处理**：解析JSON格式的答案数据
2. **站点匹配**：按站点ID匹配考生答案和标准答案
3. **要素评分**：对每个气象要素进行精确匹配评分
4. **权重计算**：根据配置的权重计算各要素得分
5. **汇总统计**：计算总分和各维度得分统计

### 具体评分规则
- **风力评分(1.0分)**：静风和1级特殊处理，2-11级完全匹配1.0分/相邻等级0.8分，12级特殊处理
- **风向评分(1.0分)**：八方位与十六方位角度匹配，角度范围匹配1.0分/相邻范围0.6分
- **温度评分(各2.0分)**：|预报值-实况值|≤2℃得满分，>2℃得0分，无容差区间
- **降水评分(2.0分)**：基于详细评分矩阵，包含雨夹雪特殊规则，跨类型一律0分  
- **灾害天气评分(2.0分)**：预报与实况一致才得分，多种灾害按比例评分，不扣分原则

## 系统功能模块

### 1. 评分引擎模块
- **WeatherScoringService**：核心评分服务
- **ScoringAlgorithm**：评分算法实现
- **ScoringConfigService**：评分配置管理

### 2. 批量处理模块
- **BatchScoringService**：批量评分处理
- **ScoringJobService**：异步评分任务

### 3. 报告生成模块
- **ScoringReportService**：评分报告生成
- **StatisticsService**：统计分析服务

### 4. 前端界面模块
- **评分管理界面**：批量评分操作
- **结果查看界面**：评分结果展示
- **统计分析界面**：图表统计展示

## API接口设计

### 评分相关接口
```
POST /exam/api/weather/scoring/calculate        # 计算单个答案得分
POST /exam/api/weather/scoring/batch           # 批量计算得分
GET  /exam/api/weather/scoring/result/{id}     # 获取评分结果
POST /exam/api/weather/scoring/report          # 生成评分报告
GET  /exam/api/weather/scoring/statistics      # 获取统计数据
```

### 配置管理接口
```
GET    /exam/api/weather/scoring/config        # 获取评分配置
POST   /exam/api/weather/scoring/config        # 保存评分配置
PUT    /exam/api/weather/scoring/config/{id}   # 更新评分配置
DELETE /exam/api/weather/scoring/config/{id}   # 删除评分配置
```

## 数据库表设计

### 评分配置表
```sql
CREATE TABLE `el_weather_scoring_config` (
  `id` varchar(64) NOT NULL COMMENT 'ID',
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `config_version` varchar(20) DEFAULT '1.0' COMMENT '配置版本',
  `total_score` int(11) NOT NULL COMMENT '总分',
  `station_weight` int(11) NOT NULL COMMENT '单站权重',
  `element_weights` json NOT NULL COMMENT '要素权重配置',
  `tolerance_config` json DEFAULT NULL COMMENT '容差配置',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_config_name` (`config_name`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='天气预报评分配置表';
```

### 评分结果表  
```sql
CREATE TABLE `el_weather_scoring_result` (
  `id` varchar(64) NOT NULL COMMENT 'ID',
  `answer_id` varchar(64) NOT NULL COMMENT '答案ID',
  `exam_id` varchar(64) NOT NULL COMMENT '考试ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `question_id` varchar(64) NOT NULL COMMENT '题目ID',
  `total_score` decimal(5,2) NOT NULL COMMENT '总得分',
  `max_score` decimal(5,2) NOT NULL COMMENT '满分',
  `score_percentage` decimal(5,2) NOT NULL COMMENT '得分率',
  `station_scores` json NOT NULL COMMENT '分站得分详情',
  `element_scores` json NOT NULL COMMENT '分要素得分详情',
  `error_analysis` json DEFAULT NULL COMMENT '错误分析',
  `scoring_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '评分时间',
  `config_version` varchar(20) DEFAULT NULL COMMENT '使用的配置版本',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_answer_id` (`answer_id`),
  KEY `idx_exam_user` (`exam_id`, `user_id`),
  KEY `idx_score_range` (`total_score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='天气预报评分结果表';
```

## 项目时间计划

### Phase 1: 核心评分引擎 (2周)
- 评分算法实现
- 数据库表设计
- 基础API开发

### Phase 2: 批量处理功能 (1.5周)
- 批量评分服务
- 异步任务处理
- 错误处理机制

### Phase 3: 前端界面开发 (2周)  
- 评分管理界面
- 结果展示页面
- 统计图表组件

### Phase 4: 系统集成测试 (1周)
- 功能测试
- 性能测试
- 用户验收测试

### Phase 5: 部署上线 (0.5周)
- 生产环境部署
- 数据迁移
- 监控告警配置

## 成功标准

### 功能指标
- 评分准确率≥95%
- 批量处理能力≥1000个答案/小时
- 系统响应时间≤3秒

### 质量指标
- 代码测试覆盖率≥85%
- 系统可用性≥99%
- 用户满意度≥90%

### 业务指标
- 评分效率提升≥80%
- 人工干预率≤5%
- 系统使用率≥90% 