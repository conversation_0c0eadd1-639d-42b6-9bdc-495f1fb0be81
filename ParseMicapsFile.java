import com.yf.exam.modules.weather.micaps.*;
import java.io.IOException;

/**
 * Parse MICAPS Type 1 file example program
 *
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
public class ParseMicapsFile {

    public static void main(String[] args) {
        // Create MICAPS data service instance
        MicapsDataService service = new MicapsDataService();

        // File path
        String filePath = "第一类.000";

        try {
            System.out.println("=== Starting MICAPS Type 1 File Parsing ===");
            System.out.println("File path: " + filePath);
            System.out.println();

            // Parse MICAPS file
            MicapsData data = service.parseMicapsFile(filePath);

            if (data instanceof MicapsType1Data) {
                MicapsType1Data type1Data = (MicapsType1Data) data;

                // Output basic information
                System.out.println("=== Basic File Information ===");
                System.out.println("Data type: Type " + type1Data.getDataType());
                System.out.println("Description: " + type1Data.getDescription());
                System.out.println("Observation time: " + type1Data.getYear() + "-" +
                                 type1Data.getMonth() + "-" +
                                 type1Data.getDay() + " " +
                                 type1Data.getHour() + ":00");
                System.out.println("Total stations: " + type1Data.getTotalStations());
                System.out.println("Actual stations: " + (type1Data.getStations() != null ? type1Data.getStations().size() : 0));
                System.out.println();

                // Output first 10 stations detailed information
                if (type1Data.getStations() != null && !type1Data.getStations().isEmpty()) {
                    System.out.println("=== First 10 Stations Details ===");
                    int count = 0;
                    for (MicapsStation station : type1Data.getStations()) {
                        if (count >= 10) break;

                        System.out.println("Station " + (count + 1) + ":");
                        System.out.println("  Station ID: " + station.getStationId());
                        System.out.println("  Longitude: " + station.getLongitude() + "°");
                        System.out.println("  Latitude: " + station.getLatitude() + "°");
                        System.out.println("  Elevation: " + station.getElevation() + "m");

                        if (station.getTemperature() != null) {
                            System.out.println("  Temperature: " + station.getTemperature() + "°C");
                        }
                        if (station.getPressure() != null) {
                            System.out.println("  Pressure: " + station.getPressure() + "hPa");
                        }
                        if (station.getWindDirection() != null) {
                            System.out.println("  Wind Direction: " + station.getWindDirection() + "°");
                        }
                        if (station.getWindSpeed() != null) {
                            System.out.println("  Wind Speed: " + station.getWindSpeed() + "m/s");
                        }
                        if (station.getPrecipitation6h() != null) {
                            System.out.println("  6h Precipitation: " + station.getPrecipitation6h() + "mm");
                        }
                        if (station.getVisibility() != null) {
                            System.out.println("  Visibility: " + station.getVisibility() + "km");
                        }

                        System.out.println();
                        count++;
                    }
                }

                // Statistics
                System.out.println("=== Data Statistics ===");
                if (type1Data.getStations() != null) {
                    long validTempCount = type1Data.getStations().stream()
                        .mapToLong(s -> s.getTemperature() != null ? 1 : 0)
                        .sum();
                    long validPressureCount = type1Data.getStations().stream()
                        .mapToLong(s -> s.getPressure() != null ? 1 : 0)
                        .sum();
                    long validWindCount = type1Data.getStations().stream()
                        .mapToLong(s -> s.getWindSpeed() != null ? 1 : 0)
                        .sum();
                    long validPrecipCount = type1Data.getStations().stream()
                        .mapToLong(s -> s.getPrecipitation6h() != null && s.getPrecipitation6h() > 0 ? 1 : 0)
                        .sum();

                    System.out.println("Stations with temperature data: " + validTempCount);
                    System.out.println("Stations with pressure data: " + validPressureCount);
                    System.out.println("Stations with wind data: " + validWindCount);
                    System.out.println("Stations with precipitation data: " + validPrecipCount);

                    // Temperature statistics
                    if (validTempCount > 0) {
                        double minTemp = type1Data.getStations().stream()
                            .filter(s -> s.getTemperature() != null)
                            .mapToDouble(MicapsStation::getTemperature)
                            .min().orElse(Double.NaN);
                        double maxTemp = type1Data.getStations().stream()
                            .filter(s -> s.getTemperature() != null)
                            .mapToDouble(MicapsStation::getTemperature)
                            .max().orElse(Double.NaN);
                        double avgTemp = type1Data.getStations().stream()
                            .filter(s -> s.getTemperature() != null)
                            .mapToDouble(MicapsStation::getTemperature)
                            .average().orElse(Double.NaN);

                        System.out.println("Temperature range: " + String.format("%.1f", minTemp) + "°C ~ " +
                                         String.format("%.1f", maxTemp) + "°C");
                        System.out.println("Average temperature: " + String.format("%.1f", avgTemp) + "°C");
                    }
                }

            } else {
                System.out.println("Parsed result is not Type 1 data: " + data.getClass().getSimpleName());
            }

            System.out.println("\n=== Parsing Complete ===");

        } catch (IOException e) {
            System.err.println("Failed to parse file: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("Program execution error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
