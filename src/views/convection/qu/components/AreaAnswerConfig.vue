<template>
  <div class="area-answer-config">
    <el-card class="config-card">
      <div slot="header" class="card-header">
        <span>区域答案配置</span>
        <el-button type="text" size="small" @click="addArea">
          <i class="el-icon-plus" /> 添加区域
        </el-button>
      </div>

      <div class="area-list">
        <div
          v-for="(area, index) in areas"
          :key="index"
          class="area-item"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="`区域${index + 1}名称`">
                <el-input
                  v-model="area.name"
                  placeholder="请输入区域名称"
                  @input="updateAreas"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="预报等级">
                <el-select
                  v-model="area.level"
                  placeholder="等级"
                  style="width: 100%"
                  @change="updateAreas"
                >
                  <el-option label="无" value="0" />
                  <el-option label="蓝色" value="1" />
                  <el-option label="黄色" value="2" />
                  <el-option label="橙色" value="3" />
                  <el-option label="红色" value="4" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="区域描述">
                <el-input
                  v-model="area.description"
                  placeholder="请输入区域描述"
                  @input="updateAreas"
                />
              </el-form-item>
            </el-col>
            <el-col :span="2">
              <el-form-item label=" ">
                <el-button
                  type="danger"
                  size="small"
                  icon="el-icon-delete"
                  @click="removeArea(index)"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="区域坐标">
                <div class="coordinates-input">
                  <el-input
                    v-model="area.coordinates"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入区域坐标，格式：经度1,纬度1;经度2,纬度2;..."
                    @input="updateAreas"
                  />
                  <div class="coordinate-help">
                    <small>
                      <i class="el-icon-info" />
                      坐标格式示例：116.3974,39.9093;116.4074,39.9193;116.4174,39.9093
                    </small>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <div v-if="areas.length === 0" class="empty-state">
        <i class="el-icon-map-location" />
        <p>暂无区域配置，请点击上方按钮添加区域</p>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'AreaAnswerConfig',
  props: {
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      areas: []
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.areas = newVal || []
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    addArea() {
      this.areas.push({
        name: '',
        level: '0',
        description: '',
        coordinates: ''
      })
      this.updateAreas()
    },

    removeArea(index) {
      this.areas.splice(index, 1)
      this.updateAreas()
    },

    updateAreas() {
      this.$emit('input', this.areas)
    }
  }
}
</script>

<style lang="scss" scoped>
.area-answer-config {
  .config-card {
    margin-bottom: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .area-item {
    padding: 15px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    margin-bottom: 15px;
    background-color: #fafafa;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .coordinates-input {
    .coordinate-help {
      margin-top: 8px;
      color: #909399;

      i {
        margin-right: 4px;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #909399;

    i {
      font-size: 48px;
      margin-bottom: 16px;
      display: block;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }
}
</style>
