<template>
  <div class="station-answer-config">
    <el-card class="config-card">
      <div slot="header" class="card-header">
        <span>站点答案配置</span>
        <el-button type="text" size="small" @click="addStation">
          <i class="el-icon-plus" /> 添加站点
        </el-button>
      </div>

      <div class="station-list">
        <div
          v-for="(station, index) in stations"
          :key="index"
          class="station-item"
        >
          <el-row :gutter="20">
            <el-col :span="5">
              <el-form-item :label="`站点${index + 1}名称`">
                <el-input
                  v-model="station.name"
                  placeholder="请输入站点名称"
                  @input="updateStations"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="经度">
                <el-input-number
                  v-model="station.longitude"
                  :precision="4"
                  :min="-180"
                  :max="180"
                  placeholder="经度"
                  style="width: 100%"
                  @change="updateStations"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="纬度">
                <el-input-number
                  v-model="station.latitude"
                  :precision="4"
                  :min="-90"
                  :max="90"
                  placeholder="纬度"
                  style="width: 100%"
                  @change="updateStations"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="强对流等级">
                <el-select
                  v-model="station.level"
                  placeholder="等级"
                  style="width: 100%"
                  @change="updateStations"
                >
                  <el-option label="无强对流" value="0" />
                  <el-option label="蓝色预警" value="1" />
                  <el-option label="黄色预警" value="2" />
                  <el-option label="橙色预警" value="3" />
                  <el-option label="红色预警" value="4" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label=" ">
                <el-button
                  type="danger"
                  size="small"
                  icon="el-icon-delete"
                  @click="removeStation(index)"
                >
                  删除
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <div v-if="stations.length === 0" class="empty-state">
        <i class="el-icon-location-outline" />
        <p>暂无站点配置，请点击上方按钮添加站点</p>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'StationAnswerConfig',
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      stations: []
    }
  },
  watch: {
    value: {
      handler(newVal) {
        // 将对象格式转换为数组格式用于编辑
        if (newVal && typeof newVal === 'object') {
          this.stations = Object.keys(newVal).map(key => ({
            id: key,
            name: newVal[key].name || key,
            longitude: newVal[key].longitude || null,
            latitude: newVal[key].latitude || null,
            level: newVal[key].level || '0'
          }))
        } else {
          this.stations = []
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    addStation() {
      // 确保stations是数组
      if (!Array.isArray(this.stations)) {
        this.stations = []
      }

      const newStationId = `station_${Date.now()}`
      this.stations.push({
        id: newStationId,
        name: '',
        longitude: null,
        latitude: null,
        level: '0'
      })
      this.updateStations()
    },

    removeStation(index) {
      if (Array.isArray(this.stations)) {
        this.stations.splice(index, 1)
        this.updateStations()
      }
    },

    updateStations() {
      // 将数组格式转换回对象格式
      const stationObject = {}
      this.stations.forEach(station => {
        if (station.id) {
          stationObject[station.id] = {
            name: station.name,
            longitude: station.longitude,
            latitude: station.latitude,
            level: station.level
          }
        }
      })
      this.$emit('input', stationObject)
    }
  }
}
</script>

<style lang="scss" scoped>
.station-answer-config {
  .config-card {
    margin-bottom: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .station-item {
    padding: 15px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    margin-bottom: 15px;
    background-color: #fafafa;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #909399;

    i {
      font-size: 48px;
      margin-bottom: 16px;
      display: block;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }
}
</style>
