<template>
  <div class="convection-qu-form">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button
          icon="el-icon-arrow-left"
          class="back-button"
          @click="handleBack"
        >
          返回列表
        </el-button>
        <div class="header-info">
          <h2>
            <i class="el-icon-edit-outline" />
            {{ isEdit ? '编辑试题' : '新建试题' }}
          </h2>
          <p v-if="isEdit" class="page-description">
            试题ID：{{ formData.id }} | 创建时间：{{ formatDateTime(formData.createTime) }}
          </p>
        </div>
      </div>
      <div class="header-right">
        <el-button
          :loading="savingDraft"
          icon="el-icon-document"
          @click="handleSaveDraft"
        >
          保存草稿
        </el-button>
        <el-button
          :loading="submitting"
          type="primary"
          icon="el-icon-check"
          @click="handleSubmit"
        >
          {{ isEdit ? '更新试题' : '创建试题' }}
        </el-button>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-container">
      <el-form
        ref="questionForm"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        size="medium"
        class="question-form"
      >
        <!-- 基本信息卡片 -->
        <el-card shadow="never" class="form-card">
          <div slot="header" class="card-header">
            <i class="el-icon-info" />
            <span>基本信息</span>
          </div>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="试题标题" prop="title">
                <el-input
                  v-model="formData.title"
                  placeholder="请输入试题标题（建议包含天气类型和时间）"
                  maxlength="100"
                  show-word-limit
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="难度等级" prop="level">
                <el-select
                  v-model="formData.level"
                  placeholder="请选择难度等级"
                  style="width: 100%"
                >
                  <el-option :value="1" label="简单">
                    <span>简单</span>
                    <span style="float: right; color: #8cc5ff;">适合初学者</span>
                  </el-option>
                  <el-option :value="2" label="中等">
                    <span>中等</span>
                    <span style="float: right; color: #ffb800;">有一定难度</span>
                  </el-option>
                  <el-option :value="3" label="困难">
                    <span>困难</span>
                    <span style="float: right; color: #f56c6c;">具有挑战性</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="预计用时" prop="estimatedTime">
                <el-input-number
                  v-model="formData.estimatedTime"
                  :min="10"
                  :max="300"
                  :step="10"
                  controls-position="right"
                  style="width: 150px"
                />
                <span style="margin-left: 8px; color: #666;">分钟</span>
                <div class="form-tip">
                  <i class="el-icon-time" />
                  <span>建议考生完成此题的时间</span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="试题描述" prop="analysis">
                <el-input
                  v-model="formData.analysis"
                  :rows="4"
                  type="textarea"
                  placeholder="请详细描述试题背景、天气情况、预报要求等（支持换行）"
                  maxlength="1000"
                  show-word-limit
                />
                <div class="form-tip">
                  <i class="el-icon-lightbulb" />
                  <span>建议包含：天气背景、预报时间、预报区域、特殊要求等信息</span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 标准答案配置卡片 -->
        <el-card shadow="never" class="form-card">
          <div slot="header" class="card-header">
            <i class="el-icon-star-on" />
            <span>标准答案配置</span>
            <el-tooltip content="标准答案用于自动评分和人工批卷参考" placement="top">
              <i class="el-icon-question" />
            </el-tooltip>
          </div>

          <!-- 标准预报依据 -->
          <el-row>
            <el-col :span="24">
              <el-form-item label="标准预报依据" prop="convectionStandardReasoning">
                <reasoning-input
                  v-model="formData.convectionStandardReasoning"
                  :max-words="2000"
                  :min-words="200"
                  :readonly="false"
                  placeholder="请输入标准预报依据，作为人工批卷的参考答案&#10;&#10;建议包含以下内容：&#10;1. 各类强对流天气的分级判断标准&#10;2. 关键气象要素的分析要点&#10;3. 预报逻辑和科学依据&#10;4. 注意事项和易错点提醒"
                />
                <div class="form-tip">
                  <i class="el-icon-warning" />
                  <span>标准预报依据将用于人工批卷时的对比参考，请尽可能详细准确</span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 站点预报标准答案 -->
          <el-row>
            <el-col :span="24">
              <el-form-item label="站点预报答案">
                <station-answer-config
                  v-model="formData.standardStationAnswer"
                />
              </el-form-item>
            </el-col>
          </el-row>

        </el-card>

        <!-- 文件资源配置卡片 -->
        <el-card shadow="never" class="form-card">
          <div slot="header" class="card-header">
            <i class="el-icon-folder-opened" />
            <span>文件资源配置</span>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="MICAPS资料">
                <file-upload
                  v-model="formData.micapsFiles"
                  :file-types="['dat', 'txt', 'grd']"
                  :max-size="50"
                  :max-count="10"
                  accept=".dat,.txt,.grd"
                  list-type="text"
                  @upload-success="handleMicapsUpload"
                  @remove="handleMicapsRemove"
                >
                  <el-button size="small" type="primary" icon="el-icon-upload">
                    上传MICAPS文件
                  </el-button>
                  <div slot="tip" class="upload-tip">
                    支持.dat/.txt/.grd格式，单个文件不超过50MB
                  </div>
                </file-upload>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="落区参考文件">
                <file-upload
                  v-model="formData.areaFiles"
                  :file-types="['png', 'jpg', 'jpeg', 'geojson']"
                  :max-size="20"
                  :max-count="5"
                  accept=".png,.jpg,.jpeg,.geojson"
                  list-type="picture-card"
                  @upload-success="handleAreaFileUpload"
                  @remove="handleAreaFileRemove"
                >
                  <i class="el-icon-plus" />
                  <div slot="tip" class="upload-tip">
                    支持图片或GeoJSON格式，仅管理员可见
                  </div>
                </file-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 高级设置卡片 -->
        <el-card v-if="showAdvanced" shadow="never" class="form-card">
          <div slot="header" class="card-header">
            <i class="el-icon-s-tools" />
            <span>高级设置</span>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="预计用时">
                <el-input-number
                  v-model="formData.estimatedTime"
                  :min="10"
                  :max="180"
                  :step="5"
                  placeholder="分钟"
                />
                <span class="input-suffix">分钟</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="试题标签">
                <el-input
                  v-model="formData.tags"
                  placeholder="多个标签用逗号分隔"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </el-form>
    </div>

    <!-- 高级设置切换 -->
    <div class="advanced-toggle">
      <el-button
        :class="{ 'expanded': showAdvanced }"
        type="text"
        icon="el-icon-arrow-down"
        @click="showAdvanced = !showAdvanced"
      >
        {{ showAdvanced ? '收起' : '展开' }}高级设置
      </el-button>
    </div>

  </div>
</template>

<script>
import { ConvectionCaseAPI } from '@/api/convection'
import { ReasoningInput } from '@/components/convection'
import FileUpload from '@/components/FileUpload'

export default {
  name: 'ConvectionQuForm',
  components: {
    ReasoningInput,
    FileUpload,
    StationAnswerConfig: () => import('./components/StationAnswerConfig')
  },
  data() {
    return {
      isEdit: false,
      submitting: false,
      savingDraft: false,
      showAdvanced: false,

      // 表单数据
      formData: {
        id: null,
        title: '',
        analysis: '',
        level: 2,

        convectionStandardReasoning: '',
        standardStationAnswer: {},

        micapsFiles: [],
        areaFiles: [],
        estimatedTime: 60,
        tags: '',
        createTime: null
      },

      // 表单验证规则
      formRules: {
        title: [
          { required: true, message: '请输入试题标题', trigger: 'blur' },
          { min: 5, max: 100, message: '标题长度在5到100个字符', trigger: 'blur' }
        ],
        level: [
          { required: true, message: '请选择难度等级', trigger: 'change' }
        ],

        convectionStandardReasoning: [
          { required: true, message: '请输入标准预报依据', trigger: 'blur' },
          { min: 100, message: '标准预报依据至少100个字符', trigger: 'blur' }
        ]
      }

    }
  },
  computed: {

  },
  created() {
    this.initData()
  },

  mounted() {
    // 保存原始数据用于对比
    this.originalData = JSON.stringify(this.formData)

    // 自动保存草稿
    this.autoSaveTimer = setInterval(() => {
      if (JSON.stringify(this.formData) !== this.originalData) {
        this.handleSaveDraft()
      }
    }, 60000) // 每分钟自动保存一次
  },

  beforeDestroy() {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer)
    }
  },
  methods: {
    // 初始化数据
    async initData() {
      const id = this.$route.params.id
      const copyData = this.$route.query.copy

      if (id) {
        // 编辑模式
        this.isEdit = true
        await this.loadQuestionData(id)
      } else if (copyData) {
        // 复制模式
        try {
          const data = JSON.parse(copyData)
          this.formData = { ...this.formData, ...data }
        } catch (error) {
          console.error('解析复制数据失败:', error)
        }
      }
    },

    // 加载试题数据
    async loadQuestionData(id) {
      try {
        const response = await ConvectionCaseAPI.getDetail(id)
        if (response.code === 0) {
          this.formData = { ...this.formData, ...response.data }
        } else {
          this.$message.error('加载试题数据失败：' + response.msg)
          this.handleBack()
        }
      } catch (error) {
        this.$message.error('加载试题数据失败：' + error.message)
        this.handleBack()
      }
    },

    // 返回列表
    handleBack() {
      this.$router.push('/convection/qu')
    },

    // 保存草稿
    async handleSaveDraft() {
      this.savingDraft = true
      try {
        // 保存到本地存储
        const draftKey = `convection_qu_draft_${this.formData.id || 'new'}`
        localStorage.setItem(draftKey, JSON.stringify(this.formData))
        this.$message.success('草稿已保存')
      } catch (error) {
        this.$message.error('保存草稿失败：' + error.message)
      } finally {
        this.savingDraft = false
      }
    },

    // 提交表单
    async handleSubmit() {
      // 表单验证
      const valid = await this.$refs.questionForm.validate().catch(() => false)
      if (!valid) return

      this.submitting = true
      try {
        const submitData = {
          ...this.formData,
          quType: 7 // 强对流试题类型
        }

        const response = await ConvectionCaseAPI.save(submitData)
        if (response.code === 0) {
          this.$message.success(this.isEdit ? '试题更新成功' : '试题创建成功')

          // 清除草稿
          const draftKey = `convection_qu_draft_${this.formData.id || 'new'}`
          localStorage.removeItem(draftKey)

          // 返回列表
          this.$router.push('/convection/qu')
        } else {
          this.$message.error('保存失败：' + response.msg)
        }
      } catch (error) {
        this.$message.error('保存失败：' + error.message)
      } finally {
        this.submitting = false
      }
    },

    // 文件上传处理
    handleMicapsUpload(file, fileList) {
      this.formData.micapsFiles = fileList
    },

    handleMicapsRemove(file, fileList) {
      this.formData.micapsFiles = fileList
    },

    handleAreaFileUpload(file, fileList) {
      this.formData.areaFiles = fileList
    },

    handleAreaFileRemove(file, fileList) {
      this.formData.areaFiles = fileList
    },

    formatDateTime(date) {
      if (!date) return '--'
      return new Date(date).toLocaleString()
    }
  },

  // 页面离开前提醒
  beforeRouteLeave(to, from, next) {
    const hasUnsavedChanges = JSON.stringify(this.formData) !== this.originalData

    if (hasUnsavedChanges) {
      this.$confirm('您有未保存的更改，确定要离开吗？', '确认离开', {
        confirmButtonText: '确定离开',
        cancelButtonText: '继续编辑',
        type: 'warning'
      }).then(() => {
        next()
      }).catch(() => {
        next(false)
      })
    } else {
      next()
    }
  }
}
</script>

<style scoped>
.convection-qu-form {
  padding: 20px;
  background: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.back-button {
  margin-top: 5px;
}

.header-info h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 10px;
}

.form-container {
  max-width: 1200px;
  margin: 0 auto;
}

.form-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.form-tip {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-top: 8px;
  color: #8c8c8c;
  font-size: 12px;
}

.standard-station-answer,
.standard-area-answer {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 15px;
  background: #fafafa;
}

.answer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-weight: 600;
  color: #303133;
}

.answer-preview {
  background: #ffffff;
  border-radius: 4px;
  padding: 10px;
}

.area-stats {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.stat-label {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #606266;
}

.color-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.stat-value {
  color: #303133;
  font-weight: 600;
}

.no-answer {
  color: #bfbfbf;
  font-size: 12px;
}

.no-answer-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #e6a23c;
  font-size: 13px;
  padding: 10px;
  background: #fdf6ec;
  border-radius: 4px;
}

.upload-tip {
  color: #8c8c8c;
  font-size: 12px;
  margin-top: 5px;
}

.input-suffix {
  margin-left: 8px;
  color: #8c8c8c;
}

.advanced-toggle {
  text-align: center;
  margin-top: 20px;
}

.advanced-toggle .el-button {
  font-size: 14px;
  transition: all 0.3s ease;
}

.advanced-toggle .el-button.expanded {
  transform: rotate(180deg);
}

.station-config-dialog,
.area-config-dialog {
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .header-left {
    flex-direction: column;
    gap: 10px;
  }

  .area-stats {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
