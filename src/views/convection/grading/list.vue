<template>
  <div class="convection-grading-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>
          <i class="el-icon-edit-outline" />
          强对流人工批卷
        </h2>
        <p class="page-description">管理强对流考试预报依据人工批卷任务，支持分配、批卷、复审等操作</p>
      </div>
      <div class="header-right">
        <el-button
          :disabled="selectedItems.length === 0"
          type="primary"
          size="medium"
          icon="el-icon-plus"
          @click="showBatchAssignDialog = true"
        >
          批量分配 ({{ selectedItems.length }})
        </el-button>
      </div>
    </div>

    <!-- 批卷统计卡片 -->
    <div class="stats-container">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon pending">
                <i class="el-icon-time" />
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ gradingStats.pending || 0 }}</div>
                <div class="stat-label">待批卷</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon in-progress">
                <i class="el-icon-edit" />
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ gradingStats.inProgress || 0 }}</div>
                <div class="stat-label">批卷中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon completed">
                <i class="el-icon-success" />
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ gradingStats.completed || 0 }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon review">
                <i class="el-icon-view" />
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ gradingStats.needsReview || 0 }}</div>
                <div class="stat-label">待复审</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索过滤区域 -->
    <div class="search-container">
      <el-card shadow="never" class="search-card">
        <el-form
          ref="searchForm"
          :model="searchForm"
          inline
          size="small"
          class="search-form"
        >
          <el-form-item label="考试标题" prop="examTitle">
            <el-input
              v-model="searchForm.examTitle"
              placeholder="请输入考试标题"
              style="width: 200px"
              clearable
            />
          </el-form-item>

          <el-form-item label="学生姓名" prop="studentName">
            <el-input
              v-model="searchForm.studentName"
              placeholder="请输入学生姓名"
              style="width: 150px"
              clearable
            />
          </el-form-item>

          <el-form-item label="批卷状态" prop="gradingStatus">
            <el-select
              v-model="searchForm.gradingStatus"
              placeholder="请选择状态"
              style="width: 120px"
              clearable
            >
              <el-option label="待分配" value="0" />
              <el-option label="已分配" value="1" />
              <el-option label="批卷中" value="2" />
              <el-option label="已完成" value="3" />
              <el-option label="待复审" value="4" />
            </el-select>
          </el-form-item>

          <el-form-item label="批卷教师" prop="graderName">
            <el-select
              v-model="searchForm.graderName"
              placeholder="请选择教师"
              style="width: 120px"
              clearable
              filterable
            >
              <el-option
                v-for="teacher in teacherList"
                :key="teacher.id"
                :label="teacher.realName"
                :value="teacher.realName"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="提交时间" prop="dateRange">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 240px"
              clearable
            />
          </el-form-item>

          <el-form-item>
            <el-button
              :loading="loading"
              type="primary"
              icon="el-icon-search"
              @click="handleSearch"
            >
              搜索
            </el-button>
            <el-button
              icon="el-icon-refresh"
              @click="handleReset"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button
          :disabled="selectedItems.length === 0"
          type="success"
          size="small"
          icon="el-icon-user"
          @click="showBatchAssignDialog = true"
        >
          批量分配 ({{ selectedItems.length }})
        </el-button>
        <el-button
          :disabled="selectedItems.length === 0"
          type="warning"
          size="small"
          icon="el-icon-refresh-right"
          @click="handleBatchReassign"
        >
          批量重新分配
        </el-button>
        <el-button
          type="info"
          size="small"
          icon="el-icon-download"
          @click="handleExport"
        >
          导出批卷结果
        </el-button>
      </div>

      <div class="toolbar-right">
        <el-button
          :loading="loading"
          size="small"
          icon="el-icon-refresh"
          @click="loadData"
        >
          刷新
        </el-button>

        <el-tooltip content="显示/隐藏列" placement="top">
          <el-dropdown trigger="click" @command="handleColumnCommand">
            <el-button size="small" icon="el-icon-setting" />
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="col in tableColumns"
                :key="col.prop"
                :command="col.prop"
              >
                <el-checkbox :value="col.visible" @change="toggleColumn(col.prop)">
                  {{ col.label }}
                </el-checkbox>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-tooltip>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        ref="dataTable"
        v-loading="loading"
        :data="tableData"
        border
        stripe
        height="600"
        class="data-table"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <!-- 选择列 -->
        <el-table-column
          type="selection"
          width="50"
          align="center"
        />

        <!-- 序号列 -->
        <el-table-column
          :index="getTableIndex"
          type="index"
          label="序号"
          width="60"
          align="center"
        />

        <!-- 考试信息 -->
        <el-table-column
          v-if="isColumnVisible('examInfo')"
          label="考试信息"
          min-width="200"
        >
          <template slot-scope="scope">
            <div class="exam-info-cell">
              <div class="exam-title">{{ scope.row.examTitle }}</div>
              <div class="exam-meta">
                <span class="exam-time">{{ formatDate(scope.row.examStartTime) }}</span>
                <el-tag size="mini" type="info">强对流</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 学生信息 -->
        <el-table-column
          v-if="isColumnVisible('studentInfo')"
          label="学生信息"
          width="150"
        >
          <template slot-scope="scope">
            <div class="student-info-cell">
              <div class="student-name">{{ scope.row.studentName }}</div>
              <div class="student-department">{{ scope.row.studentDepartment || '--' }}</div>
            </div>
          </template>
        </el-table-column>

        <!-- 提交时间 -->
        <el-table-column
          v-if="isColumnVisible('submitTime')"
          prop="submitTime"
          label="提交时间"
          width="160"
          align="center"
          sortable="custom"
        >
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.submitTime) }}
          </template>
        </el-table-column>

        <!-- 预报依据 -->
        <el-table-column
          v-if="isColumnVisible('reasoning')"
          label="预报依据"
          min-width="200"
        >
          <template slot-scope="scope">
            <div class="reasoning-cell">
              <div class="reasoning-preview">
                {{ getReasoningPreview(scope.row.forecastReasoning) }}
              </div>
              <div class="reasoning-meta">
                <span class="word-count">{{ scope.row.reasoningWordCount || 0 }}字</span>
                <el-button
                  type="text"
                  size="mini"
                  icon="el-icon-view"
                  @click="previewReasoning(scope.row)"
                >
                  预览
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 批卷状态 -->
        <el-table-column
          v-if="isColumnVisible('gradingStatus')"
          prop="gradingStatus"
          label="批卷状态"
          width="100"
          align="center"
          sortable="custom"
        >
          <template slot-scope="scope">
            <el-tag
              :type="getGradingStatusTagType(scope.row.gradingStatus)"
              size="small"
            >
              {{ getGradingStatusText(scope.row.gradingStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 批卷教师 -->
        <el-table-column
          v-if="isColumnVisible('graderName')"
          prop="graderName"
          label="批卷教师"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.graderName" class="grader-info">
              <div class="grader-name">{{ scope.row.graderName }}</div>
              <div class="assign-time">{{ formatDate(scope.row.assignTime) }}</div>
            </div>
            <span v-else class="no-grader">未分配</span>
          </template>
        </el-table-column>

        <!-- 评分情况 -->
        <el-table-column
          v-if="isColumnVisible('scoring')"
          label="评分情况"
          width="120"
          align="center"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.gradingStatus >= 3" class="scoring-info">
              <div class="reasoning-score">
                <span class="score-label">预报依据：</span>
                <span class="score-value">{{ scope.row.reasoningScore || 0 }}分</span>
              </div>
              <div class="grading-time">{{ formatDate(scope.row.gradingTime) }}</div>
            </div>
            <span v-else class="no-score">待评分</span>
          </template>
        </el-table-column>

        <!-- 优先级 -->
        <el-table-column
          v-if="isColumnVisible('priority')"
          prop="priority"
          label="优先级"
          width="80"
          align="center"
        >
          <template slot-scope="scope">
            <el-tag
              :type="getPriorityTagType(scope.row.priority)"
              size="mini"
            >
              {{ getPriorityText(scope.row.priority) }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column
          label="操作"
          width="200"
          align="center"
          fixed="right"
        >
          <template slot-scope="scope">
            <div class="action-buttons">
              <el-tooltip content="开始批卷" placement="top">
                <el-button
                  :disabled="!canStartGrading(scope.row)"
                  type="primary"
                  size="mini"
                  icon="el-icon-edit"
                  circle
                  @click="startGrading(scope.row)"
                />
              </el-tooltip>

              <el-tooltip content="查看详情" placement="top">
                <el-button
                  type="success"
                  size="mini"
                  icon="el-icon-view"
                  circle
                  @click="viewDetail(scope.row)"
                />
              </el-tooltip>

              <el-tooltip content="分配教师" placement="top">
                <el-button
                  :disabled="scope.row.gradingStatus >= 3"
                  type="warning"
                  size="mini"
                  icon="el-icon-user"
                  circle
                  @click="assignGrader(scope.row)"
                />
              </el-tooltip>

              <el-dropdown trigger="click" @command="command => handleRowDropdownCommand(command, scope.row)">
                <el-button size="mini" icon="el-icon-more" circle />
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    :disabled="scope.row.gradingStatus === 0"
                    command="reassign"
                    icon="el-icon-refresh-right"
                  >
                    重新分配
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="priority"
                    icon="el-icon-star-on"
                  >
                    设置优先级
                  </el-dropdown-item>
                  <el-dropdown-item
                    :disabled="scope.row.gradingStatus < 3"
                    command="review"
                    icon="el-icon-view"
                  >
                    申请复审
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="history"
                    icon="el-icon-time"
                  >
                    批卷历史
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="pagination.current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.size"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 批量分配对话框 -->
    <el-dialog
      :visible.sync="showBatchAssignDialog"
      :close-on-click-modal="false"
      title="批量分配批卷教师"
      width="600px"
      class="batch-assign-dialog"
    >
      <div class="assign-content">
        <div class="assign-info">
          <p>将为以下 <strong>{{ selectedItems.length }}</strong> 个批卷任务分配教师：</p>
          <ul class="task-list">
            <li v-for="item in selectedItems.slice(0, 5)" :key="item.id">
              {{ item.examTitle }} - {{ item.studentName }}
            </li>
            <li v-if="selectedItems.length > 5">
              ... 等{{ selectedItems.length - 5 }}个任务
            </li>
          </ul>
        </div>

        <el-form ref="assignForm" :model="assignForm" label-width="100px">
          <el-form-item label="分配方式" prop="assignType">
            <el-radio-group v-model="assignForm.assignType">
              <el-radio label="manual">手动分配</el-radio>
              <el-radio label="auto">自动分配</el-radio>
              <el-radio label="balance">负载均衡</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item
            v-if="assignForm.assignType === 'manual'"
            label="批卷教师"
            prop="graderIds"
          >
            <el-select
              v-model="assignForm.graderIds"
              multiple
              placeholder="请选择批卷教师"
              style="width: 100%"
            >
              <el-option
                v-for="teacher in teacherList"
                :key="teacher.id"
                :label="teacher.realName"
                :value="teacher.id"
              >
                <span>{{ teacher.realName }}</span>
                <span style="float: right; color: #8cc5ff;">
                  待批卷：{{ teacher.pendingCount || 0 }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item v-if="assignForm.assignType !== 'manual'" label="分配策略">
            <el-checkbox-group v-model="assignForm.strategies">
              <el-checkbox label="workload">考虑工作负载</el-checkbox>
              <el-checkbox label="specialty">考虑专业匹配</el-checkbox>
              <el-checkbox label="random">随机分配</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="优先级">
            <el-select v-model="assignForm.priority" placeholder="设置优先级">
              <el-option label="普通" value="normal" />
              <el-option label="重要" value="high" />
              <el-option label="紧急" value="urgent" />
            </el-select>
          </el-form-item>

          <el-form-item label="截止时间">
            <el-date-picker
              v-model="assignForm.deadline"
              type="datetime"
              placeholder="选择截止时间"
              style="width: 100%"
            />
          </el-form-item>
        </el-form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="showBatchAssignDialog = false">取消</el-button>
        <el-button
          :loading="assigning"
          type="primary"
          @click="handleBatchAssign"
        >
          确定分配
        </el-button>
      </span>
    </el-dialog>

    <!-- 预报依据预览对话框 -->
    <el-dialog
      :visible.sync="showReasoningDialog"
      title="预报依据预览"
      width="70%"
      class="reasoning-dialog"
    >
      <div v-if="currentReasoning" class="reasoning-preview-content">
        <div class="reasoning-meta-info">
          <div class="meta-item">
            <span class="meta-label">学生：</span>
            <span class="meta-value">{{ currentReasoning.studentName }}</span>
          </div>
          <div class="meta-item">
            <span class="meta-label">字数：</span>
            <span class="meta-value">{{ currentReasoning.wordCount }}字</span>
          </div>
          <div class="meta-item">
            <span class="meta-label">提交时间：</span>
            <span class="meta-value">{{ formatDateTime(currentReasoning.submitTime) }}</span>
          </div>
        </div>
        <div class="reasoning-content">
          <div class="reasoning-text">
            {{ currentReasoning.content }}
          </div>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="showReasoningDialog = false">关闭</el-button>
        <el-button
          v-if="currentReasoning && canStartGrading(currentReasoning)"
          type="primary"
          @click="startGradingFromPreview"
        >
          开始批卷
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { ConvectionGradingAPI } from '@/api/convection'

export default {
  name: 'ConvectionGradingList',
  data() {
    return {
      loading: false,
      assigning: false,
      tableData: [],
      selectedItems: [],
      teacherList: [],

      // 搜索表单
      searchForm: {
        examTitle: '',
        studentName: '',
        gradingStatus: '',
        graderName: '',
        dateRange: null
      },

      // 分页参数
      pagination: {
        current: 1,
        size: 20,
        total: 0
      },

      // 排序参数
      sortField: '',
      sortOrder: '',

      // 统计数据
      gradingStats: {
        pending: 0,
        inProgress: 0,
        completed: 0,
        needsReview: 0
      },

      // 表格列配置
      tableColumns: [
        { prop: 'examInfo', label: '考试信息', visible: true },
        { prop: 'studentInfo', label: '学生信息', visible: true },
        { prop: 'submitTime', label: '提交时间', visible: true },
        { prop: 'reasoning', label: '预报依据', visible: true },
        { prop: 'gradingStatus', label: '批卷状态', visible: true },
        { prop: 'graderName', label: '批卷教师', visible: true },
        { prop: 'scoring', label: '评分情况', visible: true },
        { prop: 'priority', label: '优先级', visible: false }
      ],

      // 对话框
      showBatchAssignDialog: false,
      showReasoningDialog: false,

      // 分配表单
      assignForm: {
        assignType: 'auto',
        graderIds: [],
        strategies: ['workload'],
        priority: 'normal',
        deadline: null
      },

      // 当前预览的预报依据
      currentReasoning: null
    }
  },
  created() {
    this.loadData()
    this.loadStats()
    this.loadTeachers()
  },
  methods: {
    // 加载数据
    async loadData() {
      this.loading = true
      try {
        const params = {
          current: this.pagination.current,
          size: this.pagination.size,
          params: {
            ...this.searchForm
          }
        }

        // 处理时间范围
        if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
          params.params.startTime = this.searchForm.dateRange[0]
          params.params.endTime = this.searchForm.dateRange[1]
        }

        // 处理排序
        if (this.sortField) {
          params.params.sortField = this.sortField
          params.params.sortOrder = this.sortOrder
        }

        const response = await ConvectionGradingAPI.getPendingList(params)
        if (response.code === 0) {
          this.tableData = response.data.records || []
          this.pagination.total = response.data.total || 0
        } else {
          this.$message.error('加载数据失败：' + response.msg)
        }
      } catch (error) {
        this.$message.error('加载数据失败：' + error.message)
      } finally {
        this.loading = false
      }
    },

    // 加载统计数据
    async loadStats() {
      try {
        const response = await ConvectionGradingAPI.getStatistics()
        if (response.code === 0) {
          this.gradingStats = response.data || {}
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },

    // 加载教师列表
    async loadTeachers() {
      try {
        const response = await ConvectionGradingAPI.getGraderList()
        if (response.code === 0) {
          this.teacherList = response.data || []
        }
      } catch (error) {
        console.error('加载教师列表失败:', error)
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.current = 1
      this.loadData()
    },

    // 重置搜索
    handleReset() {
      this.$refs.searchForm.resetFields()
      this.searchForm = {
        examTitle: '',
        studentName: '',
        gradingStatus: '',
        graderName: '',
        dateRange: null
      }
      this.pagination.current = 1
      this.loadData()
    },

    // 开始批卷
    startGrading(row) {
      this.$router.push(`/convection/grading/detail/${row.answerId}`)
    },

    // 查看详情
    viewDetail(row) {
      this.$router.push(`/convection/grading/detail/${row.answerId}`)
    },

    // 分配教师
    assignGrader(row) {
      this.selectedItems = [row]
      this.showBatchAssignDialog = true
    },

    // 行操作下拉菜单
    async handleRowDropdownCommand(command, row) {
      switch (command) {
        case 'reassign':
          await this.handleReassign(row)
          break
        case 'priority':
          await this.handleSetPriority(row)
          break
        case 'review':
          await this.handleRequestReview(row)
          break
        case 'history':
          this.viewGradingHistory(row)
          break
      }
    },

    // 重新分配
    async handleReassign(row) {
      this.$confirm('确定要重新分配这个批卷任务吗？', '确认重新分配', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          const response = await ConvectionGradingAPI.reassign(row.answerId)
          if (response.code === 0) {
            this.$message.success('重新分配成功')
            this.loadData()
            this.loadStats()
          } else {
            this.$message.error('重新分配失败：' + response.msg)
          }
        } catch (error) {
          this.$message.error('重新分配失败：' + error.message)
        }
      }).catch(() => {
        // 用户取消
      })
    },

    // 设置优先级
    async handleSetPriority(row) {
      this.$prompt('请选择优先级', '设置优先级', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'select',
        inputOptions: [
          { value: 'normal', label: '普通' },
          { value: 'high', label: '重要' },
          { value: 'urgent', label: '紧急' }
        ]
      }).then(async({ value }) => {
        try {
          const response = await ConvectionGradingAPI.setPriority(row.answerId, value)
          if (response.code === 0) {
            this.$message.success('优先级设置成功')
            this.loadData()
          } else {
            this.$message.error('设置失败：' + response.msg)
          }
        } catch (error) {
          this.$message.error('设置失败：' + error.message)
        }
      }).catch(() => {
        // 用户取消
      })
    },

    // 申请复审
    async handleRequestReview(row) {
      this.$prompt('请输入复审理由', '申请复审', {
        confirmButtonText: '提交申请',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputPlaceholder: '请详细说明需要复审的原因...'
      }).then(async({ value }) => {
        try {
          const response = await ConvectionGradingAPI.requestReview(row.answerId, value)
          if (response.code === 0) {
            this.$message.success('复审申请已提交')
            this.loadData()
            this.loadStats()
          } else {
            this.$message.error('申请失败：' + response.msg)
          }
        } catch (error) {
          this.$message.error('申请失败：' + error.message)
        }
      }).catch(() => {
        // 用户取消
      })
    },

    // 查看批卷历史
    viewGradingHistory(row) {
      this.$message.info('批卷历史功能开发中...')
    },

    // 批量分配
    async handleBatchAssign() {
      if (this.selectedItems.length === 0) {
        this.$message.warning('请选择要分配的任务')
        return
      }

      this.assigning = true
      try {
        const params = {
          answerIds: this.selectedItems.map(item => item.answerId),
          assignType: this.assignForm.assignType,
          graderIds: this.assignForm.graderIds,
          strategies: this.assignForm.strategies,
          priority: this.assignForm.priority,
          deadline: this.assignForm.deadline
        }

        const response = await ConvectionGradingAPI.batchAssign(params)
        if (response.code === 0) {
          this.$message.success('批量分配成功')
          this.showBatchAssignDialog = false
          this.loadData()
          this.loadStats()
          this.selectedItems = []
        } else {
          this.$message.error('批量分配失败：' + response.msg)
        }
      } catch (error) {
        this.$message.error('批量分配失败：' + error.message)
      } finally {
        this.assigning = false
      }
    },

    // 批量重新分配
    async handleBatchReassign() {
      if (this.selectedItems.length === 0) {
        this.$message.warning('请选择要重新分配的任务')
        return
      }

      this.$confirm(`确定要重新分配选中的${this.selectedItems.length}个批卷任务吗？`, '确认批量重新分配', {
        confirmButtonText: '确定重新分配',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          const answerIds = this.selectedItems.map(item => item.answerId)
          const response = await ConvectionGradingAPI.batchReassign(answerIds)
          if (response.code === 0) {
            this.$message.success('批量重新分配成功')
            this.loadData()
            this.loadStats()
            this.selectedItems = []
          } else {
            this.$message.error('批量重新分配失败：' + response.msg)
          }
        } catch (error) {
          this.$message.error('批量重新分配失败：' + error.message)
        }
      }).catch(() => {
        // 用户取消
      })
    },

    // 导出数据
    handleExport() {
      this.$message.info('导出功能开发中...')
    },

    // 预览预报依据
    previewReasoning(row) {
      this.currentReasoning = {
        answerId: row.answerId,
        studentName: row.studentName,
        content: row.forecastReasoning,
        wordCount: row.reasoningWordCount,
        submitTime: row.submitTime,
        gradingStatus: row.gradingStatus
      }
      this.showReasoningDialog = true
    },

    // 从预览开始批卷
    startGradingFromPreview() {
      this.showReasoningDialog = false
      this.$router.push(`/convection/grading/detail/${this.currentReasoning.answerId}`)
    },

    // 选择变化
    handleSelectionChange(selection) {
      this.selectedItems = selection
    },

    // 排序变化
    handleSortChange({ column, prop, order }) {
      this.sortField = prop
      this.sortOrder = order === 'ascending' ? 'asc' : 'desc'
      this.loadData()
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.current = 1
      this.loadData()
    },

    // 当前页变化
    handleCurrentChange(current) {
      this.pagination.current = current
      this.loadData()
    },

    // 表格索引
    getTableIndex(index) {
      return (this.pagination.current - 1) * this.pagination.size + index + 1
    },

    // 列显示控制
    isColumnVisible(prop) {
      const column = this.tableColumns.find(col => col.prop === prop)
      return column ? column.visible : true
    },

    toggleColumn(prop) {
      const column = this.tableColumns.find(col => col.prop === prop)
      if (column) {
        column.visible = !column.visible
      }
    },

    handleColumnCommand(command) {
      // 列控制命令处理
    },

    // 业务判断方法
    canStartGrading(row) {
      return row.gradingStatus >= 1 && row.gradingStatus < 3
    },

    getReasoningPreview(text) {
      if (!text) return '未填写预报依据'
      return text.length > 50 ? text.substring(0, 50) + '...' : text
    },

    // 工具方法
    getGradingStatusText(status) {
      const statusMap = {
        0: '待分配',
        1: '已分配',
        2: '批卷中',
        3: '已完成',
        4: '待复审'
      }
      return statusMap[status] || '未知'
    },

    getGradingStatusTagType(status) {
      const typeMap = {
        0: 'info',
        1: 'warning',
        2: 'primary',
        3: 'success',
        4: 'danger'
      }
      return typeMap[status] || 'info'
    },

    getPriorityText(priority) {
      const priorityMap = {
        'normal': '普通',
        'high': '重要',
        'urgent': '紧急'
      }
      return priorityMap[priority] || '普通'
    },

    getPriorityTagType(priority) {
      const typeMap = {
        'normal': 'info',
        'high': 'warning',
        'urgent': 'danger'
      }
      return typeMap[priority] || 'info'
    },

    formatDateTime(date) {
      if (!date) return '--'
      return new Date(date).toLocaleString()
    },

    formatDate(date) {
      if (!date) return '--'
      return new Date(date).toLocaleDateString()
    }
  }
}
</script>

<style scoped>
.convection-grading-list {
  padding: 20px;
  background: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.stats-container {
  margin-bottom: 20px;
}

.stat-card {
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: #ffffff;
}

.stat-icon.pending {
  background: linear-gradient(135deg, #ffd93d 0%, #ff6b6b 100%);
}

.stat-icon.in-progress {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.review {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #8c8c8c;
}

.search-container {
  margin-bottom: 20px;
}

.search-card {
  border-radius: 8px;
}

.search-form {
  margin: 0;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #ffffff;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.table-container {
  background: #ffffff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.data-table {
  width: 100%;
}

.exam-info-cell {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.exam-title {
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.exam-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.exam-time {
  font-size: 12px;
  color: #8c8c8c;
}

.student-info-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.student-name {
  font-weight: 600;
  color: #303133;
}

.student-department {
  font-size: 12px;
  color: #8c8c8c;
}

.reasoning-cell {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.reasoning-preview {
  color: #606266;
  line-height: 1.4;
  font-size: 13px;
}

.reasoning-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.word-count {
  font-size: 12px;
  color: #8c8c8c;
}

.grader-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  text-align: center;
}

.grader-name {
  font-weight: 600;
  color: #303133;
}

.assign-time {
  font-size: 12px;
  color: #8c8c8c;
}

.no-grader {
  color: #bfbfbf;
  font-size: 13px;
}

.scoring-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  text-align: center;
}

.reasoning-score {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
}

.score-label {
  color: #8c8c8c;
}

.score-value {
  color: #303133;
  font-weight: 600;
}

.grading-time {
  font-size: 12px;
  color: #8c8c8c;
}

.no-score {
  color: #bfbfbf;
  font-size: 13px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

.batch-assign-dialog {
  border-radius: 8px;
}

.assign-content {
  padding: 10px 0;
}

.assign-info {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.task-list {
  margin: 10px 0 0 0;
  padding-left: 20px;
}

.task-list li {
  margin-bottom: 5px;
  color: #606266;
}

.reasoning-dialog {
  border-radius: 8px;
}

.reasoning-preview-content {
  max-height: 500px;
  overflow-y: auto;
}

.reasoning-meta-info {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.meta-label {
  color: #8c8c8c;
  font-weight: 600;
}

.meta-value {
  color: #303133;
}

.reasoning-content {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 20px;
}

.reasoning-text {
  line-height: 1.6;
  color: #303133;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .stats-container .el-col {
    margin-bottom: 15px;
  }

  .exam-meta {
    flex-direction: column;
    gap: 4px;
    align-items: flex-start;
  }

  .reasoning-meta {
    flex-direction: column;
    gap: 5px;
    align-items: flex-start;
  }

  .action-buttons {
    flex-wrap: wrap;
    gap: 4px;
  }
}
</style>
