<template>
  <div class="grading-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button
          icon="el-icon-arrow-left"
          class="back-button"
          @click="goBack"
        >
          返回批卷列表
        </el-button>
        <div class="header-info">
          <h2>
            <i class="el-icon-edit-outline" />
            强对流预报依据批卷
          </h2>
          <div class="header-meta">
            <span class="meta-item">
              <i class="el-icon-user" />
              考生：{{ studentInfo.realName }}
            </span>
            <span class="meta-item">
              <i class="el-icon-document" />
              考试：{{ examInfo.title }}
            </span>
            <span class="meta-item">
              <i class="el-icon-time" />
              提交时间：{{ formatDateTime(answerInfo.submitTime) }}
            </span>
          </div>
        </div>
      </div>
      <div class="header-right">
        <div class="grading-status">
          <span class="status-label">批卷状态：</span>
          <el-tag :type="getStatusTagType(gradingStatus)" size="medium">
            {{ getStatusText(gradingStatus) }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 批卷进度指示器 -->
    <div class="grading-progress">
      <el-card shadow="never" class="progress-card">
        <el-steps :active="currentStep" finish-status="success" align-center>
          <el-step title="加载答案" description="获取学生答案">
            <i slot="icon" class="el-icon-document" />
          </el-step>
          <el-step title="答案对比" description="对比标准答案">
            <i slot="icon" class="el-icon-s-data" />
          </el-step>
          <el-step title="评分批卷" description="输入评分和评语">
            <i slot="icon" class="el-icon-edit" />
          </el-step>
          <el-step title="完成批卷" description="提交批卷结果">
            <i slot="icon" class="el-icon-success" />
          </el-step>
        </el-steps>
      </el-card>
    </div>

    <!-- 批卷内容区域 -->
    <div v-loading="loading" class="grading-content">
      <el-row :gutter="20">
        <!-- 左侧：答案对比 -->
        <el-col :span="14">
          <div class="comparison-section">
            <answer-comparison
              :student-name="studentInfo.realName"
              :submit-time="answerInfo.submitTime"
              :submit-status="answerInfo.answerStatus"
              :student-station-answer="answerInfo.stationAnswer"
              :student-area-answer="answerInfo.areaAnswer"
              :student-reasoning="answerInfo.forecastReasoning"
              :standard-station-answer="questionInfo.standardStationAnswer"
              :standard-area-answer="questionInfo.standardAreaAnswer"
              :standard-reasoning="questionInfo.convectionStandardReasoning"
              :station-score="scoreInfo.stationScore"
              :area-score="scoreInfo.areaScore"
              :reasoning-score="scoreInfo.reasoningScore"
              :total-score="scoreInfo.totalScore"
              :stations="stations"
            />
          </div>
        </el-col>

        <!-- 右侧：评分面板 -->
        <el-col :span="10">
          <div class="grading-section">
            <grading-panel
              :initial-data="gradingData"
              :readonly="isReadonly"
              :grading-status="gradingStatus"
              :show-advanced="true"
              :show-quick-grading="true"
              :grading-start-time="gradingStartTime"
              :grading-end-time="gradingEndTime"
              @score-change="handleScoreChange"
              @save-draft="handleSaveDraft"
              @submit-grading="handleSubmitGrading"
            />
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 批卷历史记录 -->
    <div v-if="gradingHistory.length > 0" class="grading-history">
      <el-card shadow="never" class="history-card">
        <div slot="header" class="card-header">
          <i class="el-icon-time" />
          <span>批卷历史</span>
        </div>
        <el-timeline>
          <el-timeline-item
            v-for="(record, index) in gradingHistory"
            :key="index"
            :timestamp="formatDateTime(record.createTime)"
            placement="top"
          >
            <div class="history-item">
              <div class="history-header">
                <span class="grader-name">{{ record.graderName }}</span>
                <el-tag :type="getHistoryTagType(record.action)" size="mini">
                  {{ getHistoryActionText(record.action) }}
                </el-tag>
              </div>
              <div class="history-content">
                <div v-if="record.scores" class="score-info">
                  <span>分级依据：{{ record.scores.basisScore }}分</span>
                  <span>极端天气：{{ record.scores.extremeScore }}分</span>
                  <span>总分：{{ record.scores.totalScore }}分</span>
                </div>
                <div v-if="record.comments" class="comment-info">
                  <p>{{ record.comments }}</p>
                </div>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>

    <!-- 批卷指南弹窗 -->
    <el-dialog
      :visible.sync="showGradingGuide"
      title="批卷指南"
      width="700px"
      class="grading-guide-dialog"
    >
      <div class="guide-content">
        <div class="guide-section">
          <h4>
            <i class="el-icon-star-on" />
            评分标准
          </h4>
          <el-table
            :data="gradingStandards"
            border
            size="small"
            class="standards-table"
          >
            <el-table-column prop="item" label="评分项目" width="120" />
            <el-table-column prop="fullScore" label="满分" width="80" align="center" />
            <el-table-column prop="criteria" label="评分标准" />
          </el-table>
        </div>

        <div class="guide-section">
          <h4>
            <i class="el-icon-info" />
            评分要点
          </h4>
          <ul class="grading-points">
            <li><strong>分级依据阐述（10分）</strong>：重点考查对强对流天气分级标准的理解和阐述</li>
            <li><strong>极端天气预报理由（10分）</strong>：重点考查预报逻辑的科学性和关键指标的识别</li>
            <li><strong>表述质量</strong>：考虑语言表达的清晰度、专业术语使用的准确性</li>
            <li><strong>创新性思考</strong>：对于有创新性见解的答案可适当加分</li>
          </ul>
        </div>

        <div class="guide-section">
          <h4>
            <i class="el-icon-warning" />
            注意事项
          </h4>
          <ul class="grading-notes">
            <li>批卷过程中保持客观公正，避免主观偏见</li>
            <li>参考标准答案，但不局限于标准答案</li>
            <li>对于有争议的答案，可标记为需要复审</li>
            <li>评语要具体明确，有助于学生改进</li>
          </ul>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="showGradingGuide = false">我知道了</el-button>
      </span>
    </el-dialog>

    <!-- 快速操作工具栏 -->
    <div v-if="!isReadonly" class="quick-toolbar">
      <el-tooltip content="批卷指南" placement="top">
        <el-button
          circle
          icon="el-icon-question"
          @click="showGradingGuide = true"
        />
      </el-tooltip>

      <el-tooltip content="全屏模式" placement="top">
        <el-button
          :icon="isFullscreen ? 'el-icon-copy-document' : 'el-icon-full-screen'"
          circle
          @click="toggleFullscreen"
        />
      </el-tooltip>

      <el-tooltip content="切换布局" placement="top">
        <el-button
          circle
          icon="el-icon-s-grid"
          @click="toggleLayout"
        />
      </el-tooltip>
    </div>
  </div>
</template>

<script>
import { ConvectionAnswerAPI, ConvectionGradingAPI } from '@/api/convection'
import { AnswerComparison, GradingPanel } from '@/components/convection'

export default {
  name: 'GradingDetail',
  components: {
    AnswerComparison,
    GradingPanel
  },
  data() {
    return {
      loading: false,
      answerId: '',
      showGradingGuide: false,
      isFullscreen: false,

      // 基础信息
      studentInfo: {},
      examInfo: {},
      questionInfo: {},
      answerInfo: {},
      scoreInfo: {},

      // 批卷数据
      gradingData: {
        reasoningGradingBasisScore: 0,
        reasoningExtremeScore: 0,
        gradingComments: '',
        improvementSuggestions: '',
        needsReview: false,
        isExcellent: false,
        hasTypicalError: false
      },

      gradingStatus: 0, // 0-未批卷，1-已批卷，2-已复审
      gradingStartTime: '',
      gradingEndTime: '',
      gradingHistory: [],

      // 站点数据
      stations: [
        { code: '54511', name: '北京', lon: 116.28, lat: 39.93 },
        { code: '54517', name: '密云', lon: 116.83, lat: 40.37 },
        { code: '54594', name: '天津', lon: 117.17, lat: 39.08 },
        { code: '54596', name: '塘沽', lon: 117.65, lat: 39.02 }
      ],

      // 评分标准
      gradingStandards: [
        {
          item: '分级依据阐述',
          fullScore: 10,
          criteria: '准确理解分级标准，阐述完整清晰，术语使用恰当'
        },
        {
          item: '极端天气预报理由',
          fullScore: 10,
          criteria: '预报依据科学合理，逻辑清晰，关键指标识别准确'
        }
      ]
    }
  },
  computed: {
    currentStep() {
      if (this.gradingStatus === 1) return 3
      if (this.gradingData.gradingComments) return 2
      if (this.answerInfo.id) return 1
      return 0
    },

    isReadonly() {
      return this.gradingStatus === 1 // 已批卷状态为只读
    }
  },
  async created() {
    this.answerId = this.$route.params.answerId
    if (!this.answerId) {
      this.$message.error('答案ID无效')
      this.goBack()
      return
    }

    await this.loadGradingData()
    this.gradingStartTime = new Date().toISOString()
  },
  methods: {
    // 加载批卷数据
    async loadGradingData() {
      this.loading = true
      try {
        const response = await ConvectionGradingAPI.getDetail(this.answerId)
        if (response.code === 0) {
          const data = response.data

          // 基础信息
          this.studentInfo = data.studentInfo || {}
          this.examInfo = data.examInfo || {}
          this.questionInfo = data.questionInfo || {}
          this.answerInfo = data.answerInfo || {}
          this.scoreInfo = data.scoreInfo || {}

          // 批卷信息
          if (data.gradingInfo) {
            this.gradingData = { ...this.gradingData, ...data.gradingInfo }
            this.gradingStatus = data.gradingInfo.gradingStatus || 0
            this.gradingStartTime = data.gradingInfo.gradingStartTime
            this.gradingEndTime = data.gradingInfo.gradingEndTime
          }

          // 批卷历史
          this.gradingHistory = data.gradingHistory || []
        } else {
          this.$message.error('加载批卷数据失败：' + response.msg)
          this.goBack()
        }
      } catch (error) {
        this.$message.error('加载批卷数据失败：' + error.message)
        this.goBack()
      } finally {
        this.loading = false
      }
    },

    // 评分变化处理
    handleScoreChange(scoreData) {
      this.gradingData = { ...this.gradingData, ...scoreData }
      this.scoreInfo.reasoningScore = scoreData.totalScore
      this.scoreInfo.totalScore = (this.scoreInfo.stationScore || 0) +
                                  (this.scoreInfo.areaScore || 0) +
                                  scoreData.totalScore
    },

    // 保存草稿
    async handleSaveDraft(formData) {
      try {
        this.gradingData = { ...this.gradingData, ...formData }

        const response = await ConvectionGradingAPI.saveDraft({
          answerId: this.answerId,
          ...this.gradingData
        })

        if (response.code !== 0) {
          throw new Error(response.msg)
        }

        return Promise.resolve()
      } catch (error) {
        return Promise.reject(error)
      }
    },

    // 提交批卷
    async handleSubmitGrading(formData) {
      try {
        this.gradingData = { ...this.gradingData, ...formData }

        const submitData = {
          answerId: this.answerId,
          reasoningGradingBasisScore: this.gradingData.reasoningGradingBasisScore,
          reasoningExtremeScore: this.gradingData.reasoningExtremeScore,
          gradingComments: this.gradingData.gradingComments,
          improvementSuggestions: this.gradingData.improvementSuggestions,
          needsReview: this.gradingData.needsReview,
          gradingStartTime: this.gradingStartTime,
          gradingEndTime: new Date().toISOString()
        }

        const response = await ConvectionGradingAPI.submitReasoningScore(submitData)
        if (response.code === 0) {
          this.gradingStatus = 1
          this.gradingEndTime = submitData.gradingEndTime

          // 添加到历史记录
          this.gradingHistory.unshift({
            action: 'submit',
            graderName: this.$store.getters.userInfo.realName,
            createTime: this.gradingEndTime,
            scores: {
              basisScore: this.gradingData.reasoningGradingBasisScore,
              extremeScore: this.gradingData.reasoningExtremeScore,
              totalScore: this.gradingData.reasoningGradingBasisScore + this.gradingData.reasoningExtremeScore
            },
            comments: this.gradingData.gradingComments
          })

          return Promise.resolve()
        } else {
          throw new Error(response.msg)
        }
      } catch (error) {
        return Promise.reject(error)
      }
    },

    // 返回列表
    goBack() {
      this.$router.push('/convection/grading')
    },

    // 切换全屏
    toggleFullscreen() {
      if (!this.isFullscreen) {
        if (document.documentElement.requestFullscreen) {
          document.documentElement.requestFullscreen()
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        }
      }
      this.isFullscreen = !this.isFullscreen
    },

    // 切换布局
    toggleLayout() {
      // 实现布局切换逻辑
      this.$message.info('布局切换功能开发中...')
    },

    // 工具方法
    getStatusText(status) {
      const statusMap = { 0: '待批卷', 1: '已批卷', 2: '已复审' }
      return statusMap[status] || '未知'
    },

    getStatusTagType(status) {
      const typeMap = { 0: 'warning', 1: 'success', 2: 'primary' }
      return typeMap[status] || 'info'
    },

    getHistoryActionText(action) {
      const actionMap = {
        'start': '开始批卷',
        'save': '保存草稿',
        'submit': '提交批卷',
        'review': '复审',
        'reassign': '重新分配'
      }
      return actionMap[action] || '未知操作'
    },

    getHistoryTagType(action) {
      const typeMap = {
        'start': 'info',
        'save': 'warning',
        'submit': 'success',
        'review': 'primary',
        'reassign': 'danger'
      }
      return typeMap[action] || 'info'
    },

    formatDateTime(date) {
      if (!date) return '--'
      return new Date(date).toLocaleString()
    }
  },

  beforeRouteLeave(to, from, next) {
    if (!this.isReadonly && this.gradingData.gradingComments) {
      this.$confirm('您有未提交的批卷内容，确定要离开吗？', '确认离开', {
        confirmButtonText: '确定离开',
        cancelButtonText: '继续批卷',
        type: 'warning'
      }).then(() => {
        next()
      }).catch(() => {
        next(false)
      })
    } else {
      next()
    }
  }
}
</script>

<style scoped>
.grading-detail {
  padding: 20px;
  background: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 25px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.back-button {
  margin-top: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  color: #ffffff;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.header-info h2 {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-meta {
  display: flex;
  gap: 25px;
  font-size: 14px;
  opacity: 0.9;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.grading-status {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}

.status-label {
  opacity: 0.9;
}

.grading-progress {
  margin-bottom: 20px;
}

.progress-card {
  border-radius: 12px;
  padding: 20px;
}

.grading-content {
  margin-bottom: 20px;
}

.comparison-section,
.grading-section {
  height: 100%;
}

.history-card {
  border-radius: 12px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.history-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #e9ecef;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.grader-name {
  font-weight: 600;
  color: #303133;
}

.history-content {
  color: #606266;
  font-size: 14px;
}

.score-info {
  display: flex;
  gap: 15px;
  margin-bottom: 8px;
  font-size: 13px;
}

.comment-info p {
  margin: 0;
  line-height: 1.5;
}

.grading-guide-dialog {
  border-radius: 8px;
}

.guide-content {
  max-height: 500px;
  overflow-y: auto;
}

.guide-section {
  margin-bottom: 25px;
}

.guide-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.standards-table {
  width: 100%;
  margin-bottom: 15px;
}

.grading-points,
.grading-notes {
  margin: 0;
  padding-left: 20px;
  line-height: 1.6;
  color: #606266;
}

.grading-points li,
.grading-notes li {
  margin-bottom: 8px;
}

.quick-toolbar {
  position: fixed;
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 1000;
}

.quick-toolbar .el-button {
  width: 40px;
  height: 40px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .grading-content .el-row {
    flex-direction: column;
  }

  .grading-content .el-col {
    width: 100% !important;
    margin-bottom: 20px;
  }

  .quick-toolbar {
    position: static;
    transform: none;
    flex-direction: row;
    justify-content: center;
    margin-top: 20px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 20px;
  }

  .header-left {
    flex-direction: column;
    gap: 15px;
  }

  .header-meta {
    flex-direction: column;
    gap: 10px;
  }

  .score-info {
    flex-direction: column;
    gap: 5px;
  }
}
</style>
