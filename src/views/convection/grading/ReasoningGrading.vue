<template>
  <div class="reasoning-grading-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>预报依据批卷</h2>
        <div class="header-desc">
          {{ taskInfo.examTitle }} - {{ taskInfo.description }}
        </div>
      </div>
      <div class="header-right">
        <div class="grading-stats">
          <span class="stat-item">
            已批改：{{ gradingProgress.graded }}/{{ gradingProgress.total }}
          </span>
          <el-progress 
            :percentage="getProgressPercentage()" 
            :show-text="false" 
            size="small"
            style="width: 120px; margin-left: 10px;"
          />
        </div>
      </div>
    </div>

    <!-- 批卷工具栏 -->
    <div class="toolbar-section">
      <div class="toolbar-left">
        <el-button-group>
          <el-button 
            :type="currentView === 'side-by-side' ? 'primary' : 'default'"
            @click="setView('side-by-side')"
            icon="el-icon-copy-document"
            size="small"
          >
            左右对比
          </el-button>
          <el-button 
            :type="currentView === 'tab' ? 'primary' : 'default'"
            @click="setView('tab')"
            icon="el-icon-folder"
            size="small"
          >
            标签切换
          </el-button>
        </el-button-group>
        
        <el-divider direction="vertical" />
        
        <el-button
          @click="showGradingGuide = !showGradingGuide"
          :type="showGradingGuide ? 'primary' : 'default'"
          icon="el-icon-question"
          size="small"
        >
          评分标准
        </el-button>
        
        <el-button
          @click="toggleAISuggestion"
          :type="showAISuggestion ? 'primary' : 'default'"
          icon="el-icon-magic-stick"
          size="small"
          :loading="aiAnalyzing"
        >
          AI建议
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <span class="current-paper">
          第 {{ currentIndex + 1 }} / {{ answerList.length }} 份
        </span>
        
        <el-button-group>
          <el-button 
            @click="previousAnswer"
            :disabled="currentIndex <= 0"
            icon="el-icon-arrow-left"
            size="small"
          >
            上一份
          </el-button>
          <el-button 
            @click="nextAnswer"
            :disabled="currentIndex >= answerList.length - 1"
            icon="el-icon-arrow-right"
            size="small"
          >
            下一份
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 评分标准面板 -->
    <div class="grading-guide-panel" v-if="showGradingGuide">
      <el-card shadow="never">
        <div slot="header" class="guide-header">
          <span>预报依据评分标准</span>
          <el-button type="text" @click="showGradingGuide = false">
            <i class="el-icon-close"></i>
          </el-button>
        </div>
        
        <div class="guide-content">
          <el-tabs v-model="activeGuideTab" type="card">
            <el-tab-pane label="评分维度" name="dimensions">
              <div class="guide-dimensions">
                <div 
                  v-for="dimension in gradingDimensions"
                  :key="dimension.key"
                  class="dimension-item"
                >
                  <div class="dimension-header">
                    <span class="dimension-name">{{ dimension.name }}</span>
                    <span class="dimension-weight">权重：{{ dimension.weight }}%</span>
                  </div>
                  <div class="dimension-desc">{{ dimension.description }}</div>
                  <div class="dimension-levels">
                    <div 
                      v-for="level in dimension.levels"
                      :key="level.score"
                      class="level-item"
                    >
                      <span class="level-score">{{ level.score }}分</span>
                      <span class="level-desc">{{ level.description }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="关键词库" name="keywords">
              <div class="guide-keywords">
                <div 
                  v-for="category in keywordCategories"
                  :key="category.name"
                  class="keyword-category"
                >
                  <h4>{{ category.name }}</h4>
                  <div class="keyword-tags">
                    <el-tag 
                      v-for="keyword in category.keywords"
                      :key="keyword"
                      size="small"
                      :type="category.type"
                      style="margin: 2px;"
                    >
                      {{ keyword }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="评分示例" name="examples">
              <div class="guide-examples">
                <div 
                  v-for="example in gradingExamples"
                  :key="example.id"
                  class="example-item"
                >
                  <div class="example-header">
                    <span class="example-score">得分：{{ example.score }}/20</span>
                    <el-tag :type="getExampleTagType(example.score)" size="small">
                      {{ getExampleLevel(example.score) }}
                    </el-tag>
                  </div>
                  <div class="example-content">{{ example.content }}</div>
                  <div class="example-comment">
                    <strong>评语：</strong>{{ example.comment }}
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-card>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左右对比视图 -->
      <div v-if="currentView === 'side-by-side'" class="side-by-side-view">
        <div class="content-panels">
          <!-- 标准答案面板 -->
          <div class="standard-panel">
            <div class="panel-header">
              <h3>标准答案</h3>
              <div class="panel-actions">
                <el-button 
                  type="text" 
                  size="small"
                  @click="highlightKeywords = !highlightKeywords"
                  :class="{ 'active': highlightKeywords }"
                >
                  <i class="el-icon-view"></i>
                  关键词高亮
                </el-button>
              </div>
            </div>
            <div class="panel-content">
              <div 
                class="standard-content"
                :class="{ 'highlight-keywords': highlightKeywords }"
                v-html="getHighlightedContent(standardAnswer.content)"
              ></div>
              
              <div class="standard-metadata">
                <div class="metadata-item">
                  <span class="label">关键要点：</span>
                  <div class="key-points">
                    <el-tag 
                      v-for="point in standardAnswer.keyPoints"
                      :key="point"
                      size="small"
                      type="success"
                    >
                      {{ point }}
                    </el-tag>
                  </div>
                </div>
                
                <div class="metadata-item">
                  <span class="label">评分要点：</span>
                  <ul class="scoring-points">
                    <li 
                      v-for="point in standardAnswer.scoringPoints"
                      :key="point.id"
                    >
                      <span class="point-text">{{ point.text }}</span>
                      <span class="point-score">({{ point.score }}分)</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 学生答案面板 -->
          <div class="student-panel">
            <div class="panel-header">
              <h3>学生答案</h3>
              <div class="panel-meta">
                <span class="student-info">
                  {{ currentAnswer.studentName }} - {{ currentAnswer.studentId }}
                </span>
                <span class="word-count">
                  字数：{{ currentAnswer.wordCount || 0 }}
                </span>
              </div>
            </div>
            <div class="panel-content">
              <div 
                class="student-content"
                :class="{ 'highlight-keywords': highlightKeywords }"
                v-html="getHighlightedContent(currentAnswer.content)"
              ></div>
              
              <!-- AI分析结果 -->
              <div class="ai-analysis" v-if="showAISuggestion && aiAnalysis">
                <div class="analysis-header">
                  <i class="el-icon-magic-stick"></i>
                  <span>AI分析建议</span>
                </div>
                
                <div class="analysis-content">
                  <div class="similarity-score">
                    <span class="label">相似度：</span>
                    <el-progress 
                      :percentage="aiAnalysis.similarity" 
                      :color="getSimilarityColor(aiAnalysis.similarity)"
                      :show-text="true"
                      :format="(percentage) => `${percentage}%`"
                    />
                  </div>
                  
                  <div class="matched-keywords">
                    <span class="label">匹配关键词：</span>
                    <div class="keyword-list">
                      <el-tag 
                        v-for="keyword in aiAnalysis.matchedKeywords"
                        :key="keyword"
                        size="mini"
                        type="success"
                      >
                        {{ keyword }}
                      </el-tag>
                    </div>
                  </div>
                  
                  <div class="missing-keywords">
                    <span class="label">缺失关键词：</span>
                    <div class="keyword-list">
                      <el-tag 
                        v-for="keyword in aiAnalysis.missingKeywords"
                        :key="keyword"
                        size="mini"
                        type="danger"
                      >
                        {{ keyword }}
                      </el-tag>
                    </div>
                  </div>
                  
                  <div class="suggested-score">
                    <span class="label">建议得分：</span>
                    <span class="score-value">{{ aiAnalysis.suggestedScore }}/20</span>
                    <span class="score-reason">{{ aiAnalysis.reason }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 标签切换视图 -->
      <div v-else class="tab-view">
        <el-tabs v-model="activeContentTab" type="border-card">
          <el-tab-pane label="标准答案" name="standard">
            <div class="tab-content">
              <div 
                class="content-text"
                :class="{ 'highlight-keywords': highlightKeywords }"
                v-html="getHighlightedContent(standardAnswer.content)"
              ></div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="学生答案" name="student">
            <div class="tab-content">
              <div class="student-info-bar">
                <span>{{ currentAnswer.studentName }} - {{ currentAnswer.studentId }}</span>
                <span>字数：{{ currentAnswer.wordCount || 0 }}</span>
              </div>
              <div 
                class="content-text"
                :class="{ 'highlight-keywords': highlightKeywords }"
                v-html="getHighlightedContent(currentAnswer.content)"
              ></div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 评分面板 -->
    <div class="grading-panel">
      <el-card shadow="never">
        <div slot="header" class="grading-header">
          <span>评分面板</span>
          <div class="header-actions">
            <el-button
              type="text"
              size="small"
              @click="resetGrading"
              icon="el-icon-refresh"
            >
              重置
            </el-button>
          </div>
        </div>
        
        <div class="grading-content">
          <el-form 
            ref="gradingForm"
            :model="gradingData"
            :rules="gradingRules"
            label-position="top"
          >
            <!-- 分项评分 -->
            <div class="scoring-dimensions">
              <h4>分项评分</h4>
              <el-row :gutter="20">
                <el-col 
                  v-for="dimension in gradingDimensions"
                  :key="dimension.key"
                  :span="6"
                >
                  <el-form-item 
                    :label="`${dimension.name} (${dimension.weight}%)`"
                    :prop="`scores.${dimension.key}`"
                  >
                    <el-input-number
                      v-model="gradingData.scores[dimension.key]"
                      :min="0"
                      :max="dimension.maxScore"
                      :step="0.5"
                      :precision="1"
                      size="small"
                      style="width: 100%;"
                      @change="calculateTotalScore"
                    />
                    <div class="dimension-hint">
                      满分：{{ dimension.maxScore }}分
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            
            <!-- 总分显示 -->
            <div class="total-score-section">
              <div class="total-score-display">
                <span class="label">总分：</span>
                <span class="score-value">{{ gradingData.totalScore }}/20</span>
                <span class="score-level">{{ getScoreLevel(gradingData.totalScore) }}</span>
              </div>
            </div>
            
            <!-- 评语输入 -->
            <el-form-item label="评语" prop="comment">
              <el-input
                v-model="gradingData.comment"
                type="textarea"
                :rows="4"
                placeholder="请输入详细的评语，包括答题亮点、不足之处和改进建议"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
            
            <!-- 快速评语模板 -->
            <div class="quick-comments">
              <span class="label">快速评语：</span>
              <div class="comment-templates">
                <el-tag 
                  v-for="template in commentTemplates"
                  :key="template.id"
                  size="small"
                  class="template-tag"
                  @click="insertTemplate(template.content)"
                >
                  {{ template.name }}
                </el-tag>
              </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="grading-actions">
              <el-button
                @click="saveGrading"
                :loading="saving"
                icon="el-icon-document"
              >
                保存评分
              </el-button>
              
              <el-button
                type="primary"
                @click="submitGrading"
                :loading="submitting"
                :disabled="!isGradingComplete"
                icon="el-icon-check"
              >
                提交评分
              </el-button>
              
              <el-button
                v-if="currentIndex < answerList.length - 1"
                type="success"
                @click="submitAndNext"
                :loading="submitting"
                :disabled="!isGradingComplete"
                icon="el-icon-right"
              >
                提交并下一份
              </el-button>
            </div>
          </el-form>
        </div>
      </el-card>
    </div>

    <!-- 批卷历史对话框 -->
    <el-dialog
      title="批卷历史"
      :visible.sync="historyDialogVisible"
      width="600px"
    >
      <div class="grading-history">
        <el-timeline>
          <el-timeline-item
            v-for="record in gradingHistory"
            :key="record.id"
            :timestamp="formatDateTime(record.createTime)"
            placement="top"
          >
            <div class="history-item">
              <div class="history-header">
                <span class="grader-name">{{ record.graderName }}</span>
                <span class="history-score">{{ record.totalScore }}/20</span>
              </div>
              <div class="history-comment">{{ record.comment }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { gradingApi } from '@/api/convection/grading'

export default {
  name: 'ReasoningGrading',
  
  data() {
    return {
      taskId: this.$route.params.taskId,
      
      // 任务信息
      taskInfo: {
        examTitle: '',
        description: '',
        deadline: null
      },
      
      // 批卷进度
      gradingProgress: {
        graded: 0,
        total: 0
      },
      
      // 答案列表
      answerList: [],
      currentIndex: 0,
      
      // 标准答案
      standardAnswer: {
        content: '',
        keyPoints: [],
        scoringPoints: []
      },
      
      // 当前答案
      currentAnswer: {
        id: '',
        studentName: '',
        studentId: '',
        content: '',
        wordCount: 0
      },
      
      // 视图设置
      currentView: 'side-by-side',
      showGradingGuide: false,
      showAISuggestion: false,
      highlightKeywords: true,
      activeGuideTab: 'dimensions',
      activeContentTab: 'standard',
      
      // AI分析
      aiAnalyzing: false,
      aiAnalysis: null,
      
      // 评分数据
      gradingData: {
        scores: {
          content: 0,
          logic: 0,
          professional: 0,
          completeness: 0
        },
        totalScore: 0,
        comment: ''
      },
      
      // 评分维度
      gradingDimensions: [
        {
          key: 'content',
          name: '内容准确性',
          weight: 30,
          maxScore: 6,
          description: '预报依据的科学性和准确性',
          levels: [
            { score: 6, description: '完全准确，依据充分' },
            { score: 4, description: '基本准确，有少量不足' },
            { score: 2, description: '部分准确，存在明显错误' },
            { score: 0, description: '严重错误或无依据' }
          ]
        },
        {
          key: 'logic',
          name: '逻辑条理',
          weight: 25,
          maxScore: 5,
          description: '论述的逻辑性和条理性',
          levels: [
            { score: 5, description: '逻辑清晰，条理分明' },
            { score: 4, description: '逻辑较好，条理清楚' },
            { score: 2, description: '逻辑一般，条理性不足' },
            { score: 0, description: '逻辑混乱，条理不清' }
          ]
        },
        {
          key: 'professional',
          name: '专业术语',
          weight: 25,
          maxScore: 5,
          description: '专业术语使用的准确性',
          levels: [
            { score: 5, description: '术语准确，使用恰当' },
            { score: 4, description: '术语基本准确' },
            { score: 2, description: '术语使用有误' },
            { score: 0, description: '术语错误或缺乏' }
          ]
        },
        {
          key: 'completeness',
          name: '完整性',
          weight: 20,
          maxScore: 4,
          description: '答案的完整性和全面性',
          levels: [
            { score: 4, description: '内容完整，覆盖全面' },
            { score: 3, description: '内容较完整' },
            { score: 2, description: '内容不够完整' },
            { score: 0, description: '内容严重不完整' }
          ]
        }
      ],
      
      // 关键词分类
      keywordCategories: [
        {
          name: '强对流指标',
          type: 'success',
          keywords: ['CAPE', 'CIN', '风切变', '抬升凝结高度', '自由对流高度', '垂直风切变']
        },
        {
          name: '天气系统',
          type: 'primary',
          keywords: ['低涡', '切变线', '冷锋', '暖锋', '低压槽', '高压脊', '副热带高压']
        },
        {
          name: '物理量场',
          type: 'warning',
          keywords: ['散度', '涡度', '垂直速度', '相当位温', '假相当位温', 'K指数', 'SI指数']
        },
        {
          name: '预报要素',
          type: 'danger',
          keywords: ['降水强度', '风速', '冰雹直径', '雷暴', '龙卷', '强降水', '雷暴大风']
        }
      ],
      
      // 评分示例
      gradingExamples: [
        {
          id: 1,
          score: 18,
          content: '根据数值预报产品分析，此次强对流天气过程主要由低涡切变线系统触发。从CAPE值分析，午后对流有效位能达到2500J/kg，为强对流发生提供了充足的不稳定能量。垂直风切变较强，0-6km风切变达到15m/s，有利于超级单体的形成和维持。结合雷达回波特征和地面观测，预计14-18时在目标区域将出现雷暴大风和冰雹天气，最大风速可达25m/s，冰雹直径2-3cm。',
          comment: '答案内容准确，逻辑清晰，专业术语使用恰当，分析全面深入，是一份优秀的预报依据。'
        },
        {
          id: 2,
          score: 12,
          content: '今天下午会有雷雨天气，主要是因为天气比较热，空气不稳定。从天气图上看有低压系统过境，会带来降水。预计会有大风和可能的冰雹。',
          comment: '基本预报思路正确，但分析过于简单，缺乏具体的物理量分析和数值依据，专业术语使用不够准确。'
        },
        {
          id: 3,
          score: 6,
          content: '根据卫星云图显示，有云系发展，可能会下雨。风比较大，可能有雷电。',
          comment: '内容过于简单，缺乏科学依据和专业分析，未体现强对流天气预报的专业水平。'
        }
      ],
      
      // 评语模板
      commentTemplates: [
        { id: 1, name: '分析准确', content: '预报依据分析准确，' },
        { id: 2, name: '逻辑清晰', content: '论述逻辑清晰，条理分明，' },
        { id: 3, name: '术语规范', content: '专业术语使用规范，' },
        { id: 4, name: '内容完整', content: '答题内容完整全面，' },
        { id: 5, name: '缺乏依据', content: '缺乏具体的数值预报依据，' },
        { id: 6, name: '分析不足', content: '对强对流机理分析不够深入，' },
        { id: 7, name: '建议加强', content: '建议加强对物理量场的分析。' }
      ],
      
      // 状态
      loading: false,
      saving: false,
      submitting: false,
      historyDialogVisible: false,
      gradingHistory: [],
      
      // 表单验证
      gradingRules: {
        comment: [
          { required: true, message: '请输入评语', trigger: 'blur' },
          { min: 10, message: '评语至少10个字符', trigger: 'blur' }
        ]
      }
    }
  },
  
  computed: {
    isGradingComplete() {
      return this.gradingData.totalScore > 0 && this.gradingData.comment.trim().length >= 10
    }
  },
  
  created() {
    this.loadTaskInfo()
    this.loadAnswerList()
    this.loadStandardAnswer()
  },
  
  methods: {
    // 加载任务信息
    async loadTaskInfo() {
      try {
        const response = await gradingApi.getGradingTask(this.taskId)
        this.taskInfo = response.data
        this.gradingProgress = {
          graded: response.data.gradedCount || 0,
          total: response.data.totalAnswers || 0
        }
      } catch (error) {
        console.error('加载任务信息失败:', error)
        this.$message.error('加载任务信息失败')
      }
    },
    
    // 加载答案列表
    async loadAnswerList() {
      this.loading = true
      try {
        const response = await gradingApi.getAnswerList(this.taskId)
        this.answerList = response.data || []
        
        if (this.answerList.length > 0) {
          this.loadCurrentAnswer()
        }
      } catch (error) {
        console.error('加载答案列表失败:', error)
        this.$message.error('加载答案列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 加载标准答案
    async loadStandardAnswer() {
      try {
        const response = await gradingApi.getStandardAnswer(this.taskId)
        this.standardAnswer = response.data
      } catch (error) {
        console.error('加载标准答案失败:', error)
      }
    },
    
    // 加载当前答案
    async loadCurrentAnswer() {
      if (this.answerList.length === 0) return
      
      const answer = this.answerList[this.currentIndex]
      this.currentAnswer = answer
      
      // 加载已有评分
      await this.loadExistingGrading(answer.id)
      
      // 清除AI分析
      this.aiAnalysis = null
    },
    
    // 加载已有评分
    async loadExistingGrading(answerId) {
      try {
        const response = await gradingApi.getGradingResult(answerId)
        if (response.data) {
          this.gradingData = {
            scores: response.data.scores || {
              content: 0,
              logic: 0,
              professional: 0,
              completeness: 0
            },
            totalScore: response.data.totalScore || 0,
            comment: response.data.comment || ''
          }
        } else {
          this.resetGrading()
        }
      } catch (error) {
        this.resetGrading()
      }
    },
    
    // 切换视图
    setView(view) {
      this.currentView = view
    },
    
    // 上一份答案
    previousAnswer() {
      if (this.currentIndex > 0) {
        this.currentIndex--
        this.loadCurrentAnswer()
      }
    },
    
    // 下一份答案
    nextAnswer() {
      if (this.currentIndex < this.answerList.length - 1) {
        this.currentIndex++
        this.loadCurrentAnswer()
      }
    },
    
    // 切换AI建议
    async toggleAISuggestion() {
      this.showAISuggestion = !this.showAISuggestion
      
      if (this.showAISuggestion && !this.aiAnalysis) {
        await this.analyzeWithAI()
      }
    },
    
    // AI分析
    async analyzeWithAI() {
      this.aiAnalyzing = true
      try {
        const response = await gradingApi.analyzeWithAI({
          standardAnswer: this.standardAnswer.content,
          studentAnswer: this.currentAnswer.content
        })
        
        this.aiAnalysis = response.data
      } catch (error) {
        console.error('AI分析失败:', error)
        this.$message.error('AI分析失败')
      } finally {
        this.aiAnalyzing = false
      }
    },
    
    // 获取高亮内容
    getHighlightedContent(content) {
      if (!this.highlightKeywords || !content) return content
      
      let highlightedContent = content
      
      // 高亮关键词
      this.keywordCategories.forEach(category => {
        category.keywords.forEach(keyword => {
          const regex = new RegExp(`(${keyword})`, 'gi')
          highlightedContent = highlightedContent.replace(
            regex, 
            `<mark class="keyword-highlight ${category.type}">$1</mark>`
          )
        })
      })
      
      return highlightedContent
    },
    
    // 计算总分
    calculateTotalScore() {
      const scores = this.gradingData.scores
      this.gradingData.totalScore = Number((
        scores.content + scores.logic + scores.professional + scores.completeness
      ).toFixed(1))
    },
    
    // 重置评分
    resetGrading() {
      this.gradingData = {
        scores: {
          content: 0,
          logic: 0,
          professional: 0,
          completeness: 0
        },
        totalScore: 0,
        comment: ''
      }
    },
    
    // 插入评语模板
    insertTemplate(template) {
      this.gradingData.comment += template
    },
    
    // 保存评分
    async saveGrading() {
      try {
        await this.$refs.gradingForm.validate()
        
        this.saving = true
        
        const data = {
          answerId: this.currentAnswer.id,
          scores: this.gradingData.scores,
          totalScore: this.gradingData.totalScore,
          comment: this.gradingData.comment,
          status: 'draft'
        }
        
        await gradingApi.saveGradingResult(data)
        this.$message.success('评分保存成功')
        
      } catch (error) {
        if (error !== false) {
          this.$message.error('保存失败')
          console.error(error)
        }
      } finally {
        this.saving = false
      }
    },
    
    // 提交评分
    async submitGrading() {
      try {
        await this.$refs.gradingForm.validate()
        
        this.submitting = true
        
        const data = {
          answerId: this.currentAnswer.id,
          scores: this.gradingData.scores,
          totalScore: this.gradingData.totalScore,
          comment: this.gradingData.comment,
          status: 'completed'
        }
        
        await gradingApi.submitGradingResult(data)
        this.$message.success('评分提交成功')
        
        // 更新进度
        this.gradingProgress.graded++
        
      } catch (error) {
        if (error !== false) {
          this.$message.error('提交失败')
          console.error(error)
        }
      } finally {
        this.submitting = false
      }
    },
    
    // 提交并下一份
    async submitAndNext() {
      await this.submitGrading()
      if (!this.submitting) {
        this.nextAnswer()
      }
    },
    
    // 工具方法
    getProgressPercentage() {
      if (this.gradingProgress.total === 0) return 0
      return Math.round(this.gradingProgress.graded / this.gradingProgress.total * 100)
    },
    
    getSimilarityColor(similarity) {
      if (similarity >= 80) return '#67C23A'
      if (similarity >= 60) return '#E6A23C'
      return '#F56C6C'
    },
    
    getScoreLevel(score) {
      if (score >= 18) return '优秀'
      if (score >= 15) return '良好'
      if (score >= 12) return '中等'
      if (score >= 9) return '及格'
      return '不及格'
    },
    
    getExampleTagType(score) {
      if (score >= 15) return 'success'
      if (score >= 10) return 'warning'
      return 'danger'
    },
    
    getExampleLevel(score) {
      if (score >= 15) return '优秀'
      if (score >= 10) return '良好'
      return '需改进'
    },
    
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      return new Date(dateTime).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.reasoning-grading-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #fff;
    border-bottom: 1px solid #e4e7ed;
    
    .header-left {
      h2 {
        margin: 0 0 5px 0;
        color: #303133;
        font-size: 20px;
        font-weight: bold;
      }
      
      .header-desc {
        color: #909399;
        font-size: 13px;
      }
    }
    
    .header-right {
      .grading-stats {
        display: flex;
        align-items: center;
        
        .stat-item {
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }
  
  .toolbar-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background: #fff;
    border-bottom: 1px solid #e4e7ed;
    
    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 15px;
    }
    
    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 15px;
      
      .current-paper {
        font-size: 14px;
        color: #606266;
      }
    }
  }
  
  .grading-guide-panel {
    margin: 10px 20px;
    
    .guide-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .guide-content {
      .guide-dimensions {
        .dimension-item {
          margin-bottom: 20px;
          padding: 15px;
          background: #f8f9fa;
          border-radius: 6px;
          
          .dimension-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            
            .dimension-name {
              font-weight: bold;
              color: #303133;
            }
            
            .dimension-weight {
              color: #909399;
              font-size: 12px;
            }
          }
          
          .dimension-desc {
            color: #606266;
            font-size: 13px;
            margin-bottom: 10px;
          }
          
          .dimension-levels {
            .level-item {
              display: flex;
              gap: 10px;
              margin-bottom: 5px;
              font-size: 12px;
              
              .level-score {
                color: #409eff;
                font-weight: bold;
                min-width: 35px;
              }
              
              .level-desc {
                color: #606266;
              }
            }
          }
        }
      }
      
      .guide-keywords {
        .keyword-category {
          margin-bottom: 20px;
          
          h4 {
            margin: 0 0 10px 0;
            color: #303133;
            font-size: 14px;
          }
          
          .keyword-tags {
            line-height: 1.8;
          }
        }
      }
      
      .guide-examples {
        .example-item {
          margin-bottom: 20px;
          padding: 15px;
          border: 1px solid #e4e7ed;
          border-radius: 6px;
          
          .example-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            
            .example-score {
              font-weight: bold;
              color: #303133;
            }
          }
          
          .example-content {
            color: #606266;
            line-height: 1.6;
            margin-bottom: 10px;
          }
          
          .example-comment {
            color: #909399;
            font-size: 13px;
          }
        }
      }
    }
  }
  
  .main-content {
    flex: 1;
    margin: 0 20px;
    overflow: hidden;
    
    .side-by-side-view {
      height: 100%;
      
      .content-panels {
        display: flex;
        height: 100%;
        gap: 15px;
        
        .standard-panel, .student-panel {
          flex: 1;
          background: #fff;
          border-radius: 8px;
          overflow: hidden;
          display: flex;
          flex-direction: column;
          
          .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e4e7ed;
            
            h3 {
              margin: 0;
              color: #303133;
              font-size: 16px;
            }
            
            .panel-meta {
              display: flex;
              gap: 15px;
              font-size: 13px;
              color: #909399;
            }
            
            .panel-actions {
              .el-button.active {
                color: #409eff;
              }
            }
          }
          
          .panel-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            
            .standard-content, .student-content {
              line-height: 1.8;
              color: #303133;
              
              &.highlight-keywords {
                ::v-deep .keyword-highlight {
                  padding: 2px 4px;
                  border-radius: 3px;
                  font-weight: bold;
                  
                  &.success {
                    background: #f0f9ff;
                    color: #67c23a;
                  }
                  
                  &.primary {
                    background: #ecf5ff;
                    color: #409eff;
                  }
                  
                  &.warning {
                    background: #fdf6ec;
                    color: #e6a23c;
                  }
                  
                  &.danger {
                    background: #fef0f0;
                    color: #f56c6c;
                  }
                }
              }
            }
            
            .standard-metadata {
              margin-top: 20px;
              padding-top: 20px;
              border-top: 1px solid #e4e7ed;
              
              .metadata-item {
                margin-bottom: 15px;
                
                .label {
                  font-weight: bold;
                  color: #303133;
                  margin-bottom: 8px;
                  display: block;
                }
                
                .key-points {
                  .el-tag {
                    margin: 2px 4px 2px 0;
                  }
                }
                
                .scoring-points {
                  margin: 0;
                  padding-left: 20px;
                  
                  li {
                    margin-bottom: 5px;
                    color: #606266;
                    
                    .point-score {
                      color: #409eff;
                      font-weight: bold;
                    }
                  }
                }
              }
            }
            
            .ai-analysis {
              margin-top: 20px;
              padding: 15px;
              background: #f0f9ff;
              border-radius: 6px;
              border: 1px solid #d9ecff;
              
              .analysis-header {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 15px;
                font-weight: bold;
                color: #409eff;
              }
              
              .analysis-content {
                .similarity-score, .matched-keywords, .missing-keywords, .suggested-score {
                  margin-bottom: 12px;
                  
                  .label {
                    font-weight: bold;
                    color: #303133;
                    margin-right: 8px;
                  }
                  
                  .keyword-list {
                    margin-top: 5px;
                    
                    .el-tag {
                      margin: 2px 4px 2px 0;
                    }
                  }
                  
                  .score-value {
                    font-weight: bold;
                    color: #409eff;
                    margin-right: 8px;
                  }
                  
                  .score-reason {
                    color: #606266;
                    font-size: 12px;
                  }
                }
              }
            }
          }
        }
      }
    }
    
    .tab-view {
      height: 100%;
      
      .tab-content {
        padding: 20px;
        height: calc(100% - 80px);
        overflow-y: auto;
        
        .student-info-bar {
          display: flex;
          justify-content: space-between;
          padding: 10px 0;
          border-bottom: 1px solid #e4e7ed;
          margin-bottom: 15px;
          font-size: 13px;
          color: #909399;
        }
        
        .content-text {
          line-height: 1.8;
          color: #303133;
        }
      }
    }
  }
  
  .grading-panel {
    margin: 15px 20px 20px;
    
    .grading-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .grading-content {
      .scoring-dimensions {
        margin-bottom: 20px;
        
        h4 {
          margin: 0 0 15px 0;
          color: #303133;
          font-size: 14px;
        }
        
        .dimension-hint {
          font-size: 11px;
          color: #909399;
          margin-top: 2px;
        }
      }
      
      .total-score-section {
        margin-bottom: 20px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 6px;
        text-align: center;
        
        .total-score-display {
          .label {
            font-size: 16px;
            color: #303133;
            margin-right: 10px;
          }
          
          .score-value {
            font-size: 24px;
            font-weight: bold;
            color: #409eff;
            margin-right: 10px;
          }
          
          .score-level {
            font-size: 14px;
            color: #67c23a;
            font-weight: bold;
          }
        }
      }
      
      .quick-comments {
        margin-bottom: 20px;
        
        .label {
          font-size: 13px;
          color: #303133;
          margin-right: 10px;
        }
        
        .comment-templates {
          margin-top: 8px;
          
          .template-tag {
            margin: 2px 4px 2px 0;
            cursor: pointer;
            transition: all 0.3s;
            
            &:hover {
              background: #409eff;
              color: #fff;
            }
          }
        }
      }
      
      .grading-actions {
        text-align: center;
        
        .el-button {
          margin: 0 8px;
        }
      }
    }
  }
  
  .grading-history {
    .history-item {
      .history-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        .grader-name {
          font-weight: bold;
          color: #303133;
        }
        
        .history-score {
          color: #409eff;
          font-weight: bold;
        }
      }
      
      .history-comment {
        color: #606266;
        line-height: 1.6;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .reasoning-grading-container {
    .main-content .side-by-side-view .content-panels {
      flex-direction: column;
      
      .standard-panel, .student-panel {
        height: 50%;
      }
    }
  }
}

@media (max-width: 768px) {
  .reasoning-grading-container {
    .page-header {
      flex-direction: column;
      gap: 10px;
      align-items: flex-start;
    }
    
    .toolbar-section {
      flex-direction: column;
      gap: 10px;
      align-items: flex-start;
    }
    
    .grading-content .scoring-dimensions .el-col {
      margin-bottom: 15px;
    }
  }
}
</style> 