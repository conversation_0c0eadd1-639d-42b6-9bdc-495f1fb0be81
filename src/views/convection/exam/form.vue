<template>
  <div class="convection-exam-form">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>{{ isEdit ? '编辑考试' : '新建考试' }}</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
      </div>

      <el-form ref="examForm" :model="examForm" :rules="examRules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="考试名称" prop="examName">
              <el-input v-model="examForm.examName" placeholder="请输入考试名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="考试类型" prop="examType">
              <el-select v-model="examForm.examType" placeholder="请选择考试类型" style="width: 100%">
                <el-option label="强对流预报" value="convection" />
                <el-option label="强对流分析" value="analysis" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="examForm.startTime"
                type="datetime"
                placeholder="选择开始时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="examForm.endTime"
                type="datetime"
                placeholder="选择结束时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="考试时长" prop="duration">
              <el-input-number
                v-model="examForm.duration"
                :min="30"
                :max="300"
                placeholder="分钟"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="考试状态" prop="status">
              <el-select v-model="examForm.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="未开始" value="0" />
                <el-option label="进行中" value="1" />
                <el-option label="已结束" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="考试说明" prop="description">
          <el-input
            v-model="examForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入考试说明"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm">{{ isEdit ? '更新' : '创建' }}</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { createConvectionExam, updateConvectionExam, getConvectionExamDetail } from '@/api/convection/exam'

export default {
  name: 'ConvectionExamForm',
  data() {
    return {
      examForm: {
        examName: '',
        examType: 'convection',
        startTime: '',
        endTime: '',
        duration: 120,
        status: '0',
        description: ''
      },
      examRules: {
        examName: [
          { required: true, message: '请输入考试名称', trigger: 'blur' }
        ],
        examType: [
          { required: true, message: '请选择考试类型', trigger: 'change' }
        ],
        startTime: [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择结束时间', trigger: 'change' }
        ],
        duration: [
          { required: true, message: '请输入考试时长', trigger: 'blur' }
        ]
      },
      loading: false
    }
  },
  computed: {
    isEdit() {
      return !!this.$route.params.id
    }
  },
  created() {
    if (this.isEdit) {
      this.loadExamDetail()
    }
  },
  methods: {
    async loadExamDetail() {
      try {
        this.loading = true
        const { data } = await getConvectionExamDetail(this.$route.params.id)
        this.examForm = { ...data }
      } catch (error) {
        this.$message.error('加载考试详情失败')
      } finally {
        this.loading = false
      }
    },

    submitForm() {
      this.$refs.examForm.validate(async(valid) => {
        if (valid) {
          try {
            this.loading = true
            if (this.isEdit) {
              await updateConvectionExam(this.$route.params.id, this.examForm)
              this.$message.success('更新成功')
            } else {
              await createConvectionExam(this.examForm)
              this.$message.success('创建成功')
            }
            this.goBack()
          } catch (error) {
            this.$message.error(this.isEdit ? '更新失败' : '创建失败')
          } finally {
            this.loading = false
          }
        }
      })
    },

    resetForm() {
      this.$refs.examForm.resetFields()
    },

    goBack() {
      this.$router.push('/convection/exam')
    }
  }
}
</script>

<style lang="scss" scoped>
.convection-exam-form {
  padding: 20px;

  .box-card {
    max-width: 800px;
    margin: 0 auto;
  }
}
</style>
