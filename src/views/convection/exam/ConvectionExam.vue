<template>
  <div class="convection-exam-container">
    <!-- 考试头部信息 -->
    <div class="exam-header">
      <div class="exam-info">
        <h2>{{ examData.title || '强对流天气临近预报考试' }}</h2>
        <div class="exam-meta">
          <span>考试时长：{{ examData.duration || 120 }}分钟</span>
          <span>总分：100分</span>
          <span>题目类型：强对流天气预报</span>
          <span>考试状态：{{ getExamStatusText() }}</span>
        </div>
      </div>
      
      <div class="exam-status">
        <div class="timer" :class="{ 'warning': remainingTime < 600, 'danger': remainingTime < 300 }">
          <i class="el-icon-time"></i>
          <span>剩余时间：{{ formattedRemainingTime }}</span>
        </div>
        <div class="progress">
          <span>答题进度：{{ overallProgress }}%</span>
          <el-progress 
            :percentage="overallProgress" 
            :show-text="false" 
            :color="getProgressColor()"
          />
        </div>
      </div>
    </div>
    
    <!-- 题目内容 -->
    <div class="exam-content">
      <div class="question-info">
        <h3>{{ questionData.title || '强对流天气预报题目' }}</h3>
        <div class="question-description" v-html="questionData.description"></div>
        
        <!-- MICAPS资料文件下载 -->
        <div class="micaps-files" v-if="questionData.micapsFiles && questionData.micapsFiles.length > 0">
          <h4>
            <i class="el-icon-folder"></i>
            气象资料文件：
          </h4>
          <div class="file-list">
            <el-button
              v-for="file in questionData.micapsFiles"
              :key="file.id"
              type="primary"
              plain
              size="small"
              icon="el-icon-download"
              @click="downloadFile(file)"
              class="file-btn"
            >
              {{ file.name }}
              <span class="file-size">({{ formatFileSize(file.size) }})</span>
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 第一部分：站点预报（68分） -->
      <div class="exam-section">
        <div class="section-header">
          <div class="section-title">
            <h3>第一部分：站点预报（68分）</h3>
            <div class="section-desc">请根据气象资料对指定站点进行强对流天气预报</div>
          </div>
          <div class="section-progress">
            <span>进度：{{ stationProgress }}%</span>
            <el-progress 
              :percentage="stationProgress" 
              :show-text="false" 
              size="small"
              :color="getProgressColor(stationProgress)"
            />
          </div>
        </div>
        
        <div class="section-content">
          <convection-station-table
            ref="stationTable"
            v-model="answerData.stationAnswer"
            :stations="questionData.stations || defaultStations"
            :readonly="examStatus === 'submitted'"
            @change="handleStationAnswerChange"
            @progress-change="handleStationProgressChange"
          />
        </div>
      </div>
      
      <!-- 预报依据阐述 -->
      <div class="exam-section">
        <div class="section-header">
          <div class="section-title">
            <h3>预报依据阐述（20分）</h3>
            <div class="section-desc">请详细阐述您的预报依据和判断理由</div>
          </div>
          <div class="section-progress">
            <span>字数：{{ reasoningWordCount }}/1500</span>
            <el-tag 
              :type="getReasoningTagType()" 
              size="mini"
            >
              {{ getReasoningStatus() }}
            </el-tag>
          </div>
        </div>
        
        <div class="section-content">
          <forecast-reasoning-textarea
            ref="reasoningTextarea"
            v-model="answerData.forecastReasoning"
            :readonly="examStatus === 'submitted'"
            :auto-save="true"
            @auto-save="handleAutoSave"
            @word-count-change="handleReasoningWordCountChange"
          />
        </div>
      </div>
      
      <!-- 第二部分：落区绘制（32分） -->
      <div class="exam-section">
        <div class="section-header">
          <div class="section-title">
            <h3>第二部分：落区绘制（32分）</h3>
            <div class="section-desc">请在地图上绘制0-1小时强对流天气落区预报</div>
          </div>
          <div class="section-progress">
            <span>进度：{{ areaProgress }}%</span>
            <el-progress 
              :percentage="areaProgress" 
              :show-text="false" 
              size="small"
              :color="getProgressColor(areaProgress)"
            />
          </div>
        </div>
        
        <div class="section-content">
          <convection-area-drawing
            ref="areaDrawing"
            :initial-data="answerData.areaAnswer"
            :region="questionData.forecastRegion || defaultRegion"
            :readonly="examStatus === 'submitted'"
            @data-change="handleAreaAnswerChange"
            @progress-change="handleAreaProgressChange"
          />
        </div>
      </div>
    </div>
    
    <!-- 考试操作区 -->
    <div class="exam-actions" v-if="examStatus !== 'submitted'">
      <div class="action-left">
        <el-button 
          @click="saveAnswerDraft" 
          :loading="saving"
          icon="el-icon-document"
        >
          保存草稿
        </el-button>
        <span v-if="lastSaveTime" class="save-time">
          最后保存：{{ formatDateTime(lastSaveTime) }}
        </span>
      </div>
      
      <div class="action-right">
        <el-button 
          @click="previewAnswer"
          icon="el-icon-view"
        >
          预览答案
        </el-button>
        <el-button 
          type="primary" 
          @click="submitExam"
          :disabled="!canSubmit"
          :loading="submitting"
          icon="el-icon-check"
        >
          提交考试
        </el-button>
      </div>
    </div>
    
    <!-- 已提交状态显示 -->
    <div class="submitted-status" v-if="examStatus === 'submitted'">
      <el-alert
        title="考试已提交"
        type="success"
        :closable="false"
        show-icon
      >
        <div slot="description">
          <p>您的考试已成功提交，感谢您的参与！</p>
          <p>提交时间：{{ formatDateTime(submitTime) }}</p>
          <p>总体完成度：{{ overallProgress }}%</p>
        </div>
      </el-alert>
      
      <div class="result-actions">
        <el-button 
          type="primary" 
          @click="viewResult"
          icon="el-icon-data-analysis"
        >
          查看考试结果
        </el-button>
        <el-button 
          @click="backToExamList"
          icon="el-icon-back"
        >
          返回考试列表
        </el-button>
      </div>
    </div>
    
    <!-- 提交确认对话框 -->
    <el-dialog
      title="确认提交考试"
      :visible.sync="submitDialogVisible"
      width="600px"
      :close-on-click-modal="false"
      :show-close="false"
    >
      <div class="submit-confirmation">
        <div class="warning-info">
          <i class="el-icon-warning" style="color: #E6A23C; font-size: 24px;"></i>
          <div class="warning-text">
            <h4>请确认您已完成所有题目</h4>
            <p>提交后将无法修改答案，请仔细检查您的答题内容。</p>
          </div>
        </div>
        
        <div class="progress-summary">
          <h4>答题进度汇总：</h4>
          <div class="progress-items">
            <div class="progress-item">
              <span class="item-label">站点预报：</span>
              <span class="item-value" :class="{ 'incomplete': stationProgress < 100 }">
                {{ stationProgress }}%
              </span>
            </div>
            <div class="progress-item">
              <span class="item-label">落区绘制：</span>
              <span class="item-value" :class="{ 'incomplete': areaProgress < 100 }">
                {{ areaProgress }}%
              </span>
            </div>
            <div class="progress-item">
              <span class="item-label">预报依据：</span>
              <span class="item-value" :class="{ 'incomplete': !isReasoningComplete }">
                {{ reasoningWordCount >= 300 ? '已完成' : `未完成 (${reasoningWordCount}/300字)` }}
              </span>
            </div>
            <div class="progress-item total">
              <span class="item-label">整体进度：</span>
              <span class="item-value">{{ overallProgress }}%</span>
            </div>
          </div>
        </div>
        
        <div class="time-info">
          <p>剩余考试时间：{{ formattedRemainingTime }}</p>
        </div>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="submitDialogVisible = false">继续答题</el-button>
        <el-button 
          type="primary" 
          @click="confirmSubmitExam"
          :loading="submitting"
        >
          确认提交
        </el-button>
      </div>
    </el-dialog>

    <!-- 答案预览对话框 -->
    <el-dialog
      title="答案预览"
      :visible.sync="previewDialogVisible"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="answer-preview">
        <div class="preview-section">
          <h4>站点预报答案</h4>
          <div class="station-summary">
            <div 
              v-for="(stationAnswer, stationId) in answerData.stationAnswer"
              :key="stationId"
              class="station-item"
            >
              <div class="station-name">{{ getStationName(stationId) }}：</div>
              <div class="station-selections">
                <span 
                  v-for="(value, type) in stationAnswer"
                  :key="type"
                  class="selection-item"
                >
                  {{ getWeatherTypeLabel(type) }}: {{ getOptionLabel(type, value) }}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="preview-section">
          <h4>预报依据阐述</h4>
          <div class="reasoning-preview">
            <div class="word-count">字数：{{ reasoningWordCount }}</div>
            <div class="reasoning-content">{{ answerData.forecastReasoning || '暂无内容' }}</div>
          </div>
        </div>
        
        <div class="preview-section">
          <h4>落区绘制情况</h4>
          <div class="area-summary">
            <div 
              v-for="(areas, type) in answerData.areaAnswer.convectionAreas || {}"
              :key="type"
              class="area-type-item"
              v-if="areas && areas.length > 0"
            >
              <span class="area-type">{{ getAreaTypeLabel(type) }}：</span>
              <span class="area-count">{{ areas.length }}个区域</span>
            </div>
            <div v-if="!hasAnyAreas" class="no-areas">暂无绘制区域</div>
          </div>
        </div>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="previewDialogVisible = false">关闭预览</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions, mapGetters } from 'vuex'
import ConvectionStationTable from '@/components/convection/ConvectionStationTable'
import ConvectionAreaDrawing from '@/components/convection/ConvectionAreaDrawing'
import ForecastReasoningTextarea from '@/components/convection/ForecastReasoningTextarea'

export default {
  name: 'ConvectionExam',
  
  components: {
    ConvectionStationTable,
    ConvectionAreaDrawing,
    ForecastReasoningTextarea
  },
  
  data() {
    return {
      examId: this.$route.params.examId,
      questionId: this.$route.params.questionId,
      
      submitDialogVisible: false,
      previewDialogVisible: false,
      
      // 默认数据
      defaultStations: [
        { id: 'station_001', name: '北京', code: '54511' },
        { id: 'station_002', name: '天津', code: '54527' },
        { id: 'station_003', name: '石家庄', code: '53698' },
        { id: 'station_004', name: '太原', code: '53772' }
      ],
      
      defaultRegion: {
        centerLon: 116.4,
        centerLat: 39.9,
        zoom: 8
      }
    }
  },
  
  computed: {
    ...mapState('convection', [
      'currentExam',
      'examStatus',
      'remainingTime',
      'currentQuestion',
      'questionData',
      'answerData',
      'stationProgress',
      'areaProgress',
      'overallProgress',
      'loading',
      'saving',
      'submitting',
      'lastSaveTime'
    ]),
    
    ...mapGetters('convection', [
      'isExamInProgress',
      'isExamSubmitted',
      'canSubmitExam',
      'formattedRemainingTime',
      'isStationAnswerComplete',
      'isAreaAnswerComplete',
      'isReasoningComplete'
    ]),
    
    examData() {
      return this.currentExam || {}
    },
    
    reasoningWordCount() {
      return this.answerData.reasoningWordCount || 0
    },
    
    submitTime() {
      return this.examData.submitTime
    },
    
    canSubmit() {
      return this.overallProgress >= 50 && !this.submitting
    },
    
    hasAnyAreas() {
      const areas = this.answerData.areaAnswer.convectionAreas || {}
      return Object.values(areas).some(typeAreas => typeAreas && typeAreas.length > 0)
    }
  },
  
  async created() {
    try {
      await this.loadExamData()
      await this.loadQuestionData()
      await this.loadExistingAnswer()
      
      if (this.examStatus === 'not_started') {
        await this.startExam(this.examId)
      }
    } catch (error) {
      console.error('初始化考试失败:', error)
      this.$message.error('初始化考试失败，请刷新页面重试')
    }
  },
  
  beforeDestroy() {
    this.stopExamTimer()
  },
  
  methods: {
    ...mapMutations('convection', [
      'SET_CURRENT_EXAM',
      'SET_QUESTION_DATA',
      'SET_ANSWER_DATA'
    ]),
    
    ...mapActions('convection', [
      'loadExamList',
      'startExam',
      'loadQuestionData',
      'loadExistingAnswer',
      'saveAnswerDraft',
      'submitExam',
      'stopExamTimer',
      'updateStationAnswer',
      'updateAreaAnswer',
      'updateForecastReasoning',
      'updateStationProgress',
      'updateAreaProgress'
    ]),
    
    // 加载考试数据
    async loadExamData() {
      // 这里应该调用API获取考试数据
      // 暂时使用模拟数据
      const examData = {
        id: this.examId,
        title: '强对流天气临近预报考试',
        duration: 120,
        totalScore: 100,
        status: 'in_progress'
      }
      this.SET_CURRENT_EXAM(examData)
    },
    
    // 处理站点答案变化
    handleStationAnswerChange(answerData) {
      this.updateStationAnswer(answerData)
      this.autoSaveAnswer()
    },
    
    // 处理站点进度变化
    handleStationProgressChange(progress) {
      this.updateStationProgress(progress)
    },
    
    // 处理落区答案变化
    handleAreaAnswerChange(answerData) {
      this.updateAreaAnswer(answerData)
      this.autoSaveAnswer()
    },
    
    // 处理落区进度变化
    handleAreaProgressChange(progress) {
      this.updateAreaProgress(progress)
    },
    
    // 处理预报依据字数变化
    handleReasoningWordCountChange(wordCount) {
      // 更新字数统计
    },
    
    // 处理自动保存
    async handleAutoSave(reasoningText) {
      this.updateForecastReasoning(reasoningText)
      await this.autoSaveAnswer()
    },
    
    // 自动保存答案
    async autoSaveAnswer() {
      try {
        await this.saveAnswerDraft({
          examId: this.examId,
          questionId: this.questionId
        })
      } catch (error) {
        console.error('自动保存失败:', error)
      }
    },
    
    // 手动保存草稿
    async saveAnswerDraft() {
      try {
        await this.saveAnswerDraft({
          examId: this.examId,
          questionId: this.questionId
        })
        this.$message.success('草稿保存成功')
      } catch (error) {
        this.$message.error('草稿保存失败')
      }
    },
    
    // 预览答案
    previewAnswer() {
      this.previewDialogVisible = true
    },
    
    // 提交考试
    submitExam() {
      this.submitDialogVisible = true
    },
    
    // 确认提交考试
    async confirmSubmitExam() {
      try {
        await this.submitExam({
          examId: this.examId,
          questionId: this.questionId
        })
        
        this.submitDialogVisible = false
        this.$message.success('考试提交成功')
        
      } catch (error) {
        this.$message.error('考试提交失败')
        console.error(error)
      }
    },
    
    // 查看考试结果
    viewResult() {
      this.$router.push({
        name: 'ConvectionExamResult',
        params: { examId: this.examId }
      })
    },
    
    // 返回考试列表
    backToExamList() {
      this.$router.push({ name: 'ConvectionExamList' })
    },
    
    // 下载文件
    downloadFile(file) {
      // 实现文件下载逻辑
      window.open(file.downloadUrl, '_blank')
    },
    
    // 获取考试状态文本
    getExamStatusText() {
      const statusMap = {
        'not_started': '未开始',
        'in_progress': '进行中',
        'submitted': '已提交'
      }
      return statusMap[this.examStatus] || '未知'
    },
    
    // 获取进度颜色
    getProgressColor(progress = this.overallProgress) {
      if (progress >= 80) return '#67C23A'
      if (progress >= 50) return '#E6A23C'
      return '#F56C6C'
    },
    
    // 获取预报依据标签类型
    getReasoningTagType() {
      if (this.reasoningWordCount >= 300) return 'success'
      if (this.reasoningWordCount >= 100) return 'warning'
      return 'danger'
    },
    
    // 获取预报依据状态
    getReasoningStatus() {
      if (this.reasoningWordCount >= 300) return '已完成'
      if (this.reasoningWordCount >= 100) return '进行中'
      return '未开始'
    },
    
    // 格式化文件大小
    formatFileSize(bytes) {
      if (!bytes) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    
    // 格式化日期时间
    formatDateTime(date) {
      if (!date) return '-'
      return new Date(date).toLocaleString('zh-CN')
    },
    
    // 获取站点名称
    getStationName(stationId) {
      const station = this.defaultStations.find(s => s.id === stationId)
      return station ? station.name : stationId
    },
    
    // 获取天气类型标签
    getWeatherTypeLabel(type) {
      const typeMap = {
        'shortTimeRainfall': '短时强降水',
        'thunderstormWind': '雷暴大风',
        'hail': '冰雹'
      }
      return typeMap[type] || type
    },
    
    // 获取选项标签
    getOptionLabel(type, value) {
      const optionMaps = {
        'shortTimeRainfall': {
          'level1': '20-40mm/h',
          'level2': '40-80mm/h',
          'level3': '≥80mm/h'
        },
        'thunderstormWind': {
          'moderate': '8-10级',
          'severe': '10-12级',
          'extreme': '≥12级/龙卷'
        },
        'hail': {
          'large': '≥2cm'
        }
      }
      return optionMaps[type]?.[value] || value
    },
    
    // 获取区域类型标签
    getAreaTypeLabel(type) {
      const typeMap = {
        'heavy-rainfall': '强降水',
        'thunderstorm-wind': '雷暴大风',
        'hail': '冰雹',
        'tornado': '龙卷'
      }
      return typeMap[type] || type
    }
  }
}
</script>

<style lang="scss" scoped>
.convection-exam-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  
  .exam-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 20px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    
    .exam-info {
      flex: 1;
      
      h2 {
        margin: 0 0 10px 0;
        color: #303133;
        font-size: 24px;
        font-weight: bold;
      }
      
      .exam-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        color: #606266;
        font-size: 14px;
        
        span {
          display: flex;
          align-items: center;
          
          &:not(:last-child)::after {
            content: '|';
            margin-left: 20px;
            color: #DCDFE6;
          }
        }
      }
    }
    
    .exam-status {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 15px;
      
      .timer {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 18px;
        font-weight: bold;
        color: #409EFF;
        
        &.warning {
          color: #E6A23C;
        }
        
        &.danger {
          color: #F56C6C;
          animation: pulse 1s infinite;
        }
        
        i {
          font-size: 20px;
        }
      }
      
      .progress {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 8px;
        
        span {
          font-size: 14px;
          color: #606266;
        }
        
        .el-progress {
          width: 200px;
        }
      }
    }
  }
  
  .exam-content {
    .question-info {
      margin-bottom: 30px;
      padding: 20px;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      
      h3 {
        margin: 0 0 15px 0;
        color: #303133;
        font-size: 20px;
        font-weight: bold;
      }
      
      .question-description {
        color: #606266;
        line-height: 1.6;
        margin-bottom: 20px;
      }
      
      .micaps-files {
        h4 {
          margin: 0 0 15px 0;
          color: #303133;
          font-size: 16px;
          display: flex;
          align-items: center;
          gap: 8px;
          
          i {
            color: #409EFF;
          }
        }
        
        .file-list {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          
          .file-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px 16px;
            
            .file-size {
              font-size: 11px;
              color: #909399;
              margin-top: 4px;
            }
          }
        }
      }
    }
    
    .exam-section {
      margin-bottom: 30px;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 20px 25px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid #E4E7ED;
        
        .section-title {
          flex: 1;
          
          h3 {
            margin: 0 0 5px 0;
            color: #303133;
            font-size: 18px;
            font-weight: bold;
          }
          
          .section-desc {
            color: #909399;
            font-size: 13px;
            line-height: 1.4;
          }
        }
        
        .section-progress {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 8px;
          
          span {
            font-size: 14px;
            color: #606266;
            white-space: nowrap;
          }
          
          .el-progress {
            width: 150px;
          }
        }
      }
      
      .section-content {
        padding: 25px;
      }
    }
  }
  
  .exam-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    padding: 20px 25px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    
    .action-left {
      display: flex;
      align-items: center;
      gap: 15px;
      
      .save-time {
        font-size: 12px;
        color: #909399;
      }
    }
    
    .action-right {
      display: flex;
      gap: 12px;
    }
  }
  
  .submitted-status {
    margin-top: 30px;
    
    .result-actions {
      display: flex;
      justify-content: center;
      gap: 15px;
      margin-top: 20px;
    }
  }
  
  .submit-confirmation {
    .warning-info {
      display: flex;
      align-items: flex-start;
      gap: 15px;
      margin-bottom: 25px;
      padding: 15px;
      background: #FEF7E6;
      border-radius: 8px;
      
      .warning-text {
        flex: 1;
        
        h4 {
          margin: 0 0 8px 0;
          color: #E6A23C;
          font-size: 16px;
        }
        
        p {
          margin: 0;
          color: #606266;
          line-height: 1.5;
        }
      }
    }
    
    .progress-summary {
      margin-bottom: 25px;
      
      h4 {
        margin: 0 0 15px 0;
        color: #303133;
        font-size: 16px;
      }
      
      .progress-items {
        .progress-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid #F0F0F0;
          
          &:last-child {
            border-bottom: none;
          }
          
          &.total {
            padding-top: 15px;
            border-top: 2px solid #E4E7ED;
            font-weight: bold;
          }
          
          .item-label {
            color: #606266;
          }
          
          .item-value {
            color: #303133;
            font-weight: bold;
            
            &.incomplete {
              color: #F56C6C;
            }
          }
        }
      }
    }
    
    .time-info {
      text-align: center;
      color: #909399;
      font-size: 14px;
      
      p {
        margin: 0;
      }
    }
  }
  
  .answer-preview {
    .preview-section {
      margin-bottom: 25px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      h4 {
        margin: 0 0 15px 0;
        color: #303133;
        font-size: 16px;
        font-weight: bold;
        padding-bottom: 8px;
        border-bottom: 2px solid #E1F0FF;
      }
      
      .station-summary {
        .station-item {
          margin-bottom: 15px;
          padding: 12px;
          background: #F8F9FA;
          border-radius: 6px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .station-name {
            font-weight: bold;
            color: #303133;
            margin-bottom: 8px;
          }
          
          .station-selections {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            
            .selection-item {
              padding: 4px 8px;
              background: #E1F0FF;
              color: #409EFF;
              border-radius: 4px;
              font-size: 12px;
            }
          }
        }
      }
      
      .reasoning-preview {
        .word-count {
          margin-bottom: 10px;
          color: #909399;
          font-size: 12px;
        }
        
        .reasoning-content {
          padding: 15px;
          background: #F8F9FA;
          border-radius: 6px;
          line-height: 1.6;
          color: #606266;
          white-space: pre-wrap;
          max-height: 200px;
          overflow-y: auto;
        }
      }
      
      .area-summary {
        .area-type-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 12px;
          background: #F8F9FA;
          border-radius: 4px;
          margin-bottom: 8px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .area-type {
            color: #303133;
            font-weight: bold;
          }
          
          .area-count {
            color: #409EFF;
          }
        }
        
        .no-areas {
          padding: 15px;
          text-align: center;
          color: #C0C4CC;
          background: #F8F9FA;
          border-radius: 6px;
        }
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .convection-exam-container {
    padding: 15px;
    
    .exam-header {
      flex-direction: column;
      gap: 20px;
      
      .exam-status {
        align-items: flex-start;
        width: 100%;
        
        .timer {
          font-size: 16px;
        }
        
        .progress .el-progress {
          width: 100%;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .convection-exam-container {
    .exam-header .exam-info .exam-meta {
      flex-direction: column;
      gap: 8px;
      
      span::after {
        display: none;
      }
    }
    
    .exam-section .section-header {
      flex-direction: column;
      gap: 15px;
      
      .section-progress {
        align-items: flex-start;
        width: 100%;
        
        .el-progress {
          width: 100%;
        }
      }
    }
    
    .exam-actions {
      flex-direction: column;
      gap: 15px;
      
      .action-left, .action-right {
        width: 100%;
        justify-content: center;
      }
    }
  }
}
</style> 