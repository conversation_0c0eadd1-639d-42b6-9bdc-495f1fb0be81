<template>
  <div class="student-exam-detail">
    <!-- 考试头部信息 -->
    <div class="exam-header">
      <div class="header-left">
        <div class="exam-info">
          <h2>{{ examInfo.title }}</h2>
          <div class="exam-meta">
            <span class="meta-item">
              <i class="el-icon-time" />
              预计用时：{{ examInfo.estimatedTime || 60 }}分钟
            </span>
            <span class="meta-item">
              <i class="el-icon-star-on" />
              难度：{{ getLevelText(examInfo.questionLevel) }}
            </span>
            <span class="meta-item">
              <i class="el-icon-user" />
              考生：{{ currentUser.realName }}
            </span>
          </div>
        </div>
      </div>
      <div class="header-right">
        <div class="timer-container">
          <div class="timer-label">剩余时间</div>
          <div :class="{ 'urgent': timeUrgent }" class="timer-display">
            <i class="el-icon-time" />
            <span>{{ formatTime(remainingTime) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 进度指示器 -->
    <div class="progress-indicator">
      <div class="progress-container">
        <el-steps :active="currentStep" finish-status="success" align-center>
          <el-step :description="`进度: ${stationProgress}%`" title="站点预报">
            <i slot="icon" class="el-icon-s-grid" />
          </el-step>
          <el-step :description="`进度: ${areaProgress}%`" title="落区绘制">
            <i slot="icon" class="el-icon-map-location" />
          </el-step>
          <el-step :description="isSubmitted ? '已提交' : '待提交'" title="提交考试">
            <i slot="icon" class="el-icon-check" />
          </el-step>
        </el-steps>
      </div>
      <div class="overall-progress">
        <span class="progress-text">总体完成度：{{ overallProgress }}%</span>
        <el-progress
          :percentage="overallProgress"
          :color="getProgressColor(overallProgress)"
          :stroke-width="8"
          class="progress-bar"
        />
      </div>
    </div>

    <!-- 考试内容区域 -->
    <div v-loading="loading" class="exam-content">
      <!-- 试题描述 -->
      <div v-if="questionInfo.analysis" class="question-description">
        <el-card shadow="never" class="description-card">
          <div slot="header" class="card-header">
            <i class="el-icon-document" />
            <span>试题背景</span>
          </div>
          <div class="description-content">
            {{ questionInfo.analysis }}
          </div>
        </el-card>
      </div>

      <!-- 站点预报表格 -->
      <div class="station-section">
        <station-table
          ref="stationTable"
          :stations="stations"
          :exam-id="examId"
          :question-id="questionInfo.id"
          :readonly="isSubmitted"
          @answer-change="handleStationAnswerChange"
          @progress-change="handleStationProgressChange"
        />
      </div>

      <!-- 落区绘制 -->
      <div class="area-section">
        <area-drawing
          ref="areaDrawing"
          :exam-id="examId"
          :question-id="questionInfo.id"
          :readonly="isSubmitted"
          :station-data="stations"
          @answer-change="handleAreaAnswerChange"
          @progress-change="handleAreaProgressChange"
        />
      </div>

      <!-- 操作按钮区域 -->
      <div v-if="!isSubmitted" class="action-section">
        <el-card shadow="never" class="action-card">
          <div class="action-buttons">
            <el-button
              :loading="saving"
              size="large"
              icon="el-icon-document"
              @click="handleSave"
            >
              保存答案
            </el-button>

            <el-button
              size="large"
              icon="el-icon-view"
              @click="handlePreview"
            >
              预览答案
            </el-button>

            <el-button
              :loading="submitting"
              :disabled="!canSubmit"
              type="primary"
              size="large"
              icon="el-icon-check"
              @click="handleSubmit"
            >
              提交考试
            </el-button>
          </div>

          <div class="submit-tips">
            <div v-if="!canSubmit" class="tip-item">
              <i class="el-icon-warning" />
              <span>请完成站点预报和落区绘制后再提交</span>
            </div>
            <div class="tip-item">
              <i class="el-icon-info" />
              <span>考试答案会自动保存，提交后无法修改</span>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 已提交状态 -->
      <div v-if="isSubmitted" class="submitted-section">
        <el-card shadow="never" class="submitted-card">
          <div class="submitted-content">
            <div class="submitted-icon">
              <i class="el-icon-success" />
            </div>
            <div class="submitted-text">
              <h3>考试已提交</h3>
              <p>提交时间：{{ formatDateTime(submitTime) }}</p>
              <p>感谢您的参与，请等待评分结果</p>
            </div>
            <div class="submitted-actions">
              <el-button
                type="primary"
                icon="el-icon-view"
                @click="viewResult"
              >
                查看结果
              </el-button>
              <el-button
                icon="el-icon-back"
                @click="goBack"
              >
                返回列表
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 答案预览对话框 -->
    <el-dialog
      :visible.sync="showPreviewDialog"
      :close-on-click-modal="false"
      title="答案预览"
      width="80%"
      class="preview-dialog"
    >
      <div class="preview-content">
        <div class="preview-section">
          <h4>站点预报答案</h4>
          <div v-if="previewData.stationAnswer" class="station-preview">
            <el-table
              :data="getStationPreviewData()"
              border
              size="small"
              max-height="300"
            >
              <el-table-column prop="stationName" label="站点" width="100" />
              <el-table-column prop="rainfall" label="短时强降水" width="120">
                <template slot-scope="scope">
                  <el-tag v-if="scope.row.rainfall" size="mini" type="primary">
                    {{ getRainfallLabel(scope.row.rainfall) }}
                  </el-tag>
                  <span v-else class="no-answer">未选择</span>
                </template>
              </el-table-column>
              <el-table-column prop="wind" label="雷暴大风" width="120">
                <template slot-scope="scope">
                  <el-tag v-if="scope.row.wind" size="mini" type="success">
                    {{ getWindLabel(scope.row.wind) }}
                  </el-tag>
                  <span v-else class="no-answer">未选择</span>
                </template>
              </el-table-column>
              <el-table-column prop="hail" label="冰雹" width="100">
                <template slot-scope="scope">
                  <el-tag v-if="scope.row.hail" size="mini" type="warning">
                    大冰雹
                  </el-tag>
                  <span v-else class="no-answer">未选择</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <div class="preview-section">
          <h4>预报依据</h4>
          <div class="reasoning-preview">
            <div class="reasoning-meta">
              <span>字数：{{ previewData.reasoningWordCount || 0 }}字</span>
            </div>
            <div class="reasoning-text">
              {{ previewData.forecastReasoning || '未填写预报依据' }}
            </div>
          </div>
        </div>

        <div class="preview-section">
          <h4>落区绘制答案</h4>
          <div class="area-preview">
            <div class="area-stats">
              <div v-for="type in convectionTypes" :key="type.value" class="stat-item">
                <div class="stat-label">
                  <div :style="{ backgroundColor: type.color }" class="color-dot" />
                  <span>{{ type.shortLabel }}：</span>
                </div>
                <span class="stat-value">
                  {{ getAreaCount(previewData.areaAnswer, type.value) }}个落区
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="showPreviewDialog = false">关闭预览</el-button>
        <el-button
          :loading="submitting"
          :disabled="!canSubmit"
          type="primary"
          @click="handleSubmitFromPreview"
        >
          确认提交
        </el-button>
      </span>
    </el-dialog>

    <!-- 离开页面确认 -->
    <div v-show="false" ref="leaveConfirm" />
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from 'vuex'
import { ConvectionExamAPI, ConvectionAnswerAPI } from '@/api/convection'
import { StationTable, AreaDrawing } from '@/components/convection'

export default {
  name: 'StudentExamDetail',
  components: {
    StationTable,
    AreaDrawing
  },
  data() {
    return {
      examId: '',
      loading: false,
      saving: false,
      submitting: false,
      showPreviewDialog: false,

      examInfo: {},
      questionInfo: {},
      currentUser: {},

      // 计时器相关
      examTimer: null,
      remainingTime: 0, // 秒

      // 站点数据
      stations: [
        { code: '54511', name: '北京', lon: 116.28, lat: 39.93 },
        { code: '54517', name: '密云', lon: 116.83, lat: 40.37 },
        { code: '54594', name: '天津', lon: 117.17, lat: 39.08 },
        { code: '54596', name: '塘沽', lon: 117.65, lat: 39.02 }
      ],

      // 预览数据
      previewData: {
        stationAnswer: {},
        areaAnswer: {},
        forecastReasoning: '',
        reasoningWordCount: 0
      },

      // 强对流类型配置
      convectionTypes: [
        { label: '短时强降水', shortLabel: '强降水', value: 'heavy_rainfall', color: '#1890ff' },
        { label: '雷暴大风', shortLabel: '雷暴风', value: 'thunderstorm_wind', color: '#52c41a' },
        { label: '冰雹', shortLabel: '冰雹', value: 'hail', color: '#722ed1' },
        { label: '龙卷', shortLabel: '龙卷', value: 'tornado', color: '#f5222d' }
      ]
    }
  },
  computed: {
    ...mapState('convection', {
      examAnswer: state => state.examAnswer,
      stationProgress: state => state.examAnswer.stationProgress,
      areaProgress: state => state.examAnswer.areaProgress,
      overallProgress: state => state.examAnswer.overallProgress
    }),

    currentStep() {
      if (this.overallProgress >= 100) return 2
      if (this.areaProgress > 0) return 1
      return 0
    },

    timeUrgent() {
      return this.remainingTime <= 600 // 剩余10分钟显示紧急状态
    },

    canSubmit() {
      return this.stationProgress >= 50 && this.areaProgress >= 30
    },

    isSubmitted() {
      return this.examAnswer.answerStatus === 1
    },

    submitTime() {
      return this.examAnswer.submitTime
    }
  },
  async created() {
    this.examId = this.$route.params.id
    if (!this.examId) {
      this.$message.error('考试ID无效')
      this.goBack()
      return
    }

    await this.initExamData()
    this.startTimer()

    // 监听页面关闭事件
    window.addEventListener('beforeunload', this.handleBeforeUnload)
  },
  methods: {
    ...mapMutations('convection', [
      'SET_CURRENT_EXAM',
      'SET_CURRENT_QUESTION',
      'UPDATE_PROGRESS'
    ]),

    ...mapActions('convection', [
      'initExam',
      'saveAnswer',
      'submitExam'
    ]),

    // 初始化考试数据
    async initExamData() {
      this.loading = true
      try {
        // 初始化考试状态
        await this.initExam({
          examId: this.examId,
          questionId: null // 将从考试信息中获取
        })

        // 获取考试信息
        const examResponse = await ConvectionExamAPI.getDetail(this.examId)
        if (examResponse.code === 0) {
          this.examInfo = examResponse.data
          this.calculateRemainingTime()
        }

        // 获取当前用户信息
        this.currentUser = this.$store.getters.userInfo || {}
      } catch (error) {
        this.$message.error('初始化考试失败：' + error.message)
        this.goBack()
      } finally {
        this.loading = false
      }
    },

    // 计算剩余时间
    calculateRemainingTime() {
      if (!this.examInfo.endTime) return

      const endTime = new Date(this.examInfo.endTime).getTime()
      const currentTime = new Date().getTime()
      const remaining = Math.max(0, Math.floor((endTime - currentTime) / 1000))

      this.remainingTime = remaining
    },

    // 启动计时器
    startTimer() {
      this.examTimer = setInterval(() => {
        if (this.remainingTime > 0) {
          this.remainingTime--
        } else {
          this.handleTimeUp()
        }
      }, 1000)
    },

    // 时间到处理
    handleTimeUp() {
      if (this.examTimer) {
        clearInterval(this.examTimer)
      }

      this.$alert('考试时间已结束，系统将自动提交您的答案。', '时间到', {
        confirmButtonText: '确定',
        type: 'warning',
        showClose: false,
        closeOnClickModal: false,
        closeOnPressEscape: false
      }).then(() => {
        this.handleSubmit(true) // 强制提交
      })
    },

    // 站点答案变化
    handleStationAnswerChange(data) {
      // 更新预览数据
      this.previewData.stationAnswer = data.stationAnswers
      this.previewData.forecastReasoning = data.forecastReasoning
      this.previewData.reasoningWordCount = data.forecastReasoning ? data.forecastReasoning.length : 0

      // 自动保存
      this.autoSave()
    },

    // 站点进度变化
    handleStationProgressChange(progress) {
      this.UPDATE_PROGRESS({ type: 'station', progress })
    },

    // 落区答案变化
    handleAreaAnswerChange(data) {
      this.previewData.areaAnswer = data
      this.autoSave()
    },

    // 落区进度变化
    handleAreaProgressChange(progress) {
      this.UPDATE_PROGRESS({ type: 'area', progress })
    },

    // 自动保存
    async autoSave() {
      if (this.isSubmitted) return

      try {
        await this.saveAnswer()
      } catch (error) {
        console.error('自动保存失败:', error)
      }
    },

    // 手动保存
    async handleSave() {
      if (this.isSubmitted) return

      this.saving = true
      try {
        await this.saveAnswer()
        this.$message.success('答案已保存')
      } catch (error) {
        this.$message.error('保存失败：' + error.message)
      } finally {
        this.saving = false
      }
    },

    // 预览答案
    handlePreview() {
      this.showPreviewDialog = true
    },

    // 提交考试
    async handleSubmit(forceSubmit = false) {
      if (this.isSubmitted) return

      if (!forceSubmit && !this.canSubmit) {
        this.$message.warning('请先完成站点预报和落区绘制')
        return
      }

      const confirmMessage = forceSubmit
        ? '时间已到，系统将自动提交您的答案。'
        : '确定要提交考试吗？提交后将无法修改。'

      try {
        await this.$confirm(confirmMessage, '确认提交', {
          confirmButtonText: '确定提交',
          cancelButtonText: forceSubmit ? '' : '取消',
          type: 'warning',
          showCancelButton: !forceSubmit
        })

        this.submitting = true
        await this.submitExam()
        this.$message.success('考试提交成功')

        // 停止计时器
        if (this.examTimer) {
          clearInterval(this.examTimer)
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('提交失败：' + error.message)
        }
      } finally {
        this.submitting = false
      }
    },

    // 从预览对话框提交
    async handleSubmitFromPreview() {
      this.showPreviewDialog = false
      await this.handleSubmit()
    },

    // 查看结果
    viewResult() {
      this.$router.push(`/student/convection/result/${this.examId}`)
    },

    // 返回列表
    goBack() {
      this.$router.push('/student/convection/exam-list')
    },

    // 页面离开前确认
    handleBeforeUnload(event) {
      if (!this.isSubmitted) {
        const message = '您还有未提交的考试答案，确定要离开吗？'
        event.returnValue = message
        return message
      }
    },

    // 获取站点预览数据
    getStationPreviewData() {
      return this.stations.map(station => ({
        stationName: station.name,
        stationCode: station.code,
        ...(this.previewData.stationAnswer[station.code] || {})
      }))
    },

    // 工具方法
    getLevelText(level) {
      const levelMap = { 1: '简单', 2: '中等', 3: '困难' }
      return levelMap[level] || '未知'
    },

    getProgressColor(progress) {
      if (progress >= 80) return '#67c23a'
      if (progress >= 50) return '#e6a23c'
      return '#f56c6c'
    },

    getAreaCount(areaAnswer, type) {
      return areaAnswer && areaAnswer[type] ? areaAnswer[type].length : 0
    },

    getRainfallLabel(value) {
      const labels = { 'level1': 'Level 1', 'level2': 'Level 2', 'level3': 'Level 3' }
      return labels[value] || value
    },

    getWindLabel(value) {
      const labels = { 'moderate': 'Moderate', 'severe': 'Severe', 'extreme': 'Extreme' }
      return labels[value] || value
    },

    formatTime(seconds) {
      if (seconds <= 0) return '00:00:00'

      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = seconds % 60

      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    },

    formatDateTime(date) {
      if (!date) return '--'
      return new Date(date).toLocaleString()
    }
  },

  beforeDestroy() {
    // 清理计时器
    if (this.examTimer) {
      clearInterval(this.examTimer)
    }

    // 移除事件监听
    window.removeEventListener('beforeunload', this.handleBeforeUnload)
  },

  beforeRouteLeave(to, from, next) {
    if (!this.isSubmitted) {
      this.$confirm('您还有未提交的考试答案，确定要离开吗？', '确认离开', {
        confirmButtonText: '确定离开',
        cancelButtonText: '继续答题',
        type: 'warning'
      }).then(() => {
        next()
      }).catch(() => {
        next(false)
      })
    } else {
      next()
    }
  }
}
</script>

<style scoped>
.student-exam-detail {
  padding: 20px;
  background: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.exam-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 25px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.exam-info h2 {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 600;
  line-height: 1.3;
}

.exam-meta {
  display: flex;
  gap: 25px;
  font-size: 14px;
  opacity: 0.9;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.timer-container {
  text-align: center;
}

.timer-label {
  font-size: 14px;
  opacity: 0.8;
  margin-bottom: 8px;
}

.timer-display {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 8px;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.timer-display.urgent {
  background: #f56c6c;
  border-color: #f56c6c;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.progress-indicator {
  background: #ffffff;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress-container {
  margin-bottom: 20px;
}

.overall-progress {
  display: flex;
  align-items: center;
  gap: 15px;
}

.progress-text {
  font-weight: 600;
  color: #303133;
  min-width: 120px;
}

.progress-bar {
  flex: 1;
}

.exam-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.description-card {
  border-radius: 12px;
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.description-content {
  line-height: 1.6;
  color: #606266;
  font-size: 14px;
}

.station-section,
.area-section {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-card {
  border-radius: 12px;
  overflow: hidden;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 20px;
}

.submit-tips {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #8c8c8c;
}

.submitted-card {
  border-radius: 12px;
  overflow: hidden;
}

.submitted-content {
  text-align: center;
  padding: 40px 20px;
}

.submitted-icon {
  font-size: 64px;
  color: #67c23a;
  margin-bottom: 20px;
}

.submitted-text h3 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 24px;
}

.submitted-text p {
  margin: 5px 0;
  color: #606266;
}

.submitted-actions {
  margin-top: 25px;
  display: flex;
  justify-content: center;
  gap: 15px;
}

.preview-dialog {
  border-radius: 8px;
}

.preview-content {
  max-height: 600px;
  overflow-y: auto;
}

.preview-section {
  margin-bottom: 25px;
}

.preview-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f0f0;
}

.station-preview {
  border-radius: 6px;
  overflow: hidden;
}

.no-answer {
  color: #bfbfbf;
  font-size: 12px;
}

.reasoning-preview {
  background: #f8f9fa;
  border-radius: 6px;
  overflow: hidden;
}

.reasoning-meta {
  padding: 10px 15px;
  background: #e9ecef;
  border-bottom: 1px solid #dee2e6;
  font-size: 12px;
  color: #6c757d;
}

.reasoning-text {
  padding: 15px;
  line-height: 1.6;
  color: #303133;
  min-height: 100px;
  max-height: 200px;
  overflow-y: auto;
}

.area-stats {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.stat-label {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #606266;
}

.color-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.stat-value {
  color: #303133;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .exam-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .exam-meta {
    flex-direction: column;
    gap: 10px;
    align-items: center;
  }

  .overall-progress {
    flex-direction: column;
    gap: 10px;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .area-stats {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
