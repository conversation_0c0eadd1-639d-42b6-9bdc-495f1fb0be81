<template>
  <div class="student-exam-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>
          <i class="el-icon-cloudy" />
          强对流天气预报考试
        </h2>
        <p class="page-description">参加强对流天气临近预报专项考试，提升预报技能</p>
      </div>
      <div class="header-right">
        <div class="header-stats">
          <div class="stat-item">
            <span class="stat-value">{{ examStats.total || 0 }}</span>
            <span class="stat-label">可参加考试</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ examStats.completed || 0 }}</span>
            <span class="stat-label">已完成</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ examStats.inProgress || 0 }}</span>
            <span class="stat-label">进行中</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-container">
      <el-card shadow="never" class="filter-card">
        <el-form inline size="small" class="filter-form">
          <el-form-item label="考试状态">
            <el-select
              v-model="filterForm.status"
              placeholder="全部状态"
              style="width: 120px"
              clearable
              @change="handleFilter"
            >
              <el-option label="未开始" value="pending" />
              <el-option label="进行中" value="active" />
              <el-option label="已完成" value="completed" />
              <el-option label="已截止" value="expired" />
            </el-select>
          </el-form-item>

          <el-form-item label="难度等级">
            <el-select
              v-model="filterForm.level"
              placeholder="全部难度"
              style="width: 120px"
              clearable
              @change="handleFilter"
            >
              <el-option label="简单" value="1" />
              <el-option label="中等" value="2" />
              <el-option label="困难" value="3" />
            </el-select>
          </el-form-item>

          <el-form-item label="搜索">
            <el-input
              v-model="filterForm.keyword"
              placeholder="输入考试标题搜索"
              style="width: 200px"
              clearable
              @keyup.enter.native="handleFilter"
              @clear="handleFilter"
            >
              <el-button
                slot="append"
                icon="el-icon-search"
                @click="handleFilter"
              />
            </el-input>
          </el-form-item>

          <el-form-item>
            <el-button
              :loading="loading"
              type="primary"
              icon="el-icon-refresh"
              @click="loadExamList"
            >
              刷新
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 考试列表 -->
    <div class="exam-container">
      <div v-loading="loading" class="exam-grid">
        <div
          v-for="exam in examList"
          :key="exam.id"
          :class="{ 'disabled': !exam.canParticipate }"
          class="exam-card"
        >
          <div class="card-header">
            <div class="header-top">
              <h3 class="exam-title">{{ exam.title }}</h3>
              <el-tag
                :type="getStatusTagType(exam.examStatus)"
                size="small"
                class="status-tag"
              >
                {{ getStatusText(exam.examStatus) }}
              </el-tag>
            </div>
            <div class="exam-meta">
              <span class="meta-item">
                <i class="el-icon-time" />
                预计用时：{{ exam.estimatedTime || 60 }}分钟
              </span>
              <span class="meta-item">
                <i class="el-icon-user" />
                参与人数：{{ exam.participantCount || 0 }}人
              </span>
            </div>
          </div>

          <div class="card-content">
            <div class="exam-info">
              <div class="info-row">
                <span class="info-label">考试时间：</span>
                <span class="info-value">
                  {{ formatDateRange(exam.startTime, exam.endTime) }}
                </span>
              </div>
              <div class="info-row">
                <span class="info-label">试题难度：</span>
                <span class="info-value">
                  <el-tag :type="getLevelTagType(exam.questionLevel)" size="mini">
                    {{ getLevelText(exam.questionLevel) }}
                  </el-tag>
                </span>
              </div>
              <div v-if="exam.description" class="info-row">
                <span class="info-label">考试说明：</span>
                <span class="info-value description">{{ exam.description }}</span>
              </div>
            </div>

            <!-- 答题进度 -->
            <div v-if="exam.userAnswer" class="progress-section">
              <div class="progress-header">
                <span class="progress-label">答题进度</span>
                <span class="progress-percentage">{{ exam.userAnswer.overallProgress || 0 }}%</span>
              </div>
              <el-progress
                :percentage="exam.userAnswer.overallProgress || 0"
                :stroke-width="8"
                :color="getProgressColor(exam.userAnswer.overallProgress)"
                class="progress-bar"
              />
              <div class="progress-details">
                <div class="detail-item">
                  <span>站点预报：{{ exam.userAnswer.stationProgress || 0 }}%</span>
                </div>
                <div class="detail-item">
                  <span>落区绘制：{{ exam.userAnswer.areaProgress || 0 }}%</span>
                </div>
              </div>
            </div>

            <!-- 考试结果 -->
            <div v-if="exam.examResult" class="result-section">
              <div class="result-header">
                <span class="result-label">考试成绩</span>
                <span class="result-score">{{ exam.examResult.totalScore }}分</span>
              </div>
              <div class="result-details">
                <div class="detail-item">
                  <span>站点预报：{{ exam.examResult.stationScore }}分</span>
                </div>
                <div class="detail-item">
                  <span>落区绘制：{{ exam.examResult.areaScore }}分</span>
                </div>
                <div class="detail-item">
                  <span>预报依据：{{ exam.examResult.reasoningScore }}分</span>
                </div>
              </div>
              <div class="result-grade">
                <el-tag :type="getGradeTagType(exam.examResult.totalScore)" size="small">
                  {{ getGradeText(exam.examResult.totalScore) }}
                </el-tag>
              </div>
            </div>
          </div>

          <div class="card-footer">
            <div class="footer-left">
              <span class="time-info">
                <i class="el-icon-calendar" />
                创建时间：{{ formatDate(exam.createTime) }}
              </span>
            </div>
            <div class="footer-right">
              <el-button
                v-if="exam.examStatus === 'completed' && exam.examResult"
                size="small"
                type="primary"
                icon="el-icon-view"
                plain
                @click="viewResult(exam)"
              >
                查看结果
              </el-button>

              <el-button
                v-else-if="exam.examStatus === 'active' && exam.userAnswer"
                size="small"
                type="warning"
                icon="el-icon-edit-outline"
                @click="continueExam(exam)"
              >
                继续答题
              </el-button>

              <el-button
                v-else-if="exam.examStatus === 'active' && exam.canParticipate"
                size="small"
                type="success"
                icon="el-icon-right"
                @click="startExam(exam)"
              >
                开始考试
              </el-button>

              <el-button
                v-else
                size="small"
                disabled
                icon="el-icon-lock"
              >
                {{ getDisabledReason(exam) }}
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && examList.length === 0" class="empty-state">
        <div class="empty-content">
          <i class="el-icon-document-remove" />
          <h3>暂无可参加的考试</h3>
          <p>当前没有符合条件的强对流考试，请稍后再试</p>
          <el-button type="primary" icon="el-icon-refresh" @click="loadExamList">
            重新加载
          </el-button>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="examList.length > 0" class="pagination-container">
      <el-pagination
        :current-page="pagination.current"
        :page-size="pagination.size"
        :total="pagination.total"
        layout="prev, pager, next, total"
        background
        @current-change="handlePageChange"
      />
    </div>

    <!-- 考试须知对话框 -->
    <el-dialog
      :visible.sync="showExamNotice"
      :close-on-click-modal="false"
      title="考试须知"
      width="600px"
      class="exam-notice-dialog"
    >
      <div class="notice-content">
        <div class="notice-section">
          <h4>
            <i class="el-icon-warning-outline" />
            考试说明
          </h4>
          <ul>
            <li>本次考试为强对流天气临近预报专项考试</li>
            <li>考试包含站点预报、落区绘制和预报依据三个部分</li>
            <li>考试时间限制为{{ selectedExam.estimatedTime || 60 }}分钟</li>
            <li>考试过程中系统会自动保存答案</li>
            <li>提交后不可修改，请仔细检查后再提交</li>
          </ul>
        </div>

        <div class="notice-section">
          <h4>
            <i class="el-icon-info" />
            技术要求
          </h4>
          <ul>
            <li>建议使用Chrome或Firefox浏览器</li>
            <li>确保网络连接稳定</li>
            <li>考试期间请勿刷新页面或关闭浏览器</li>
            <li>如遇技术问题请及时联系管理员</li>
          </ul>
        </div>

        <div class="notice-section">
          <h4>
            <i class="el-icon-success" />
            评分标准
          </h4>
          <ul>
            <li>站点预报（68分）：根据标准答案自动评分</li>
            <li>落区绘制（32分）：系统自动计算准确度</li>
            <li>预报依据（20分）：人工批卷评分</li>
            <li>总分120分，72分及以上为及格</li>
          </ul>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="showExamNotice = false">取消</el-button>
        <el-button type="primary" @click="confirmStartExam">
          我已阅读，开始考试
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { ConvectionExamAPI } from '@/api/convection'

export default {
  name: 'StudentExamList',
  data() {
    return {
      loading: false,
      examList: [],
      selectedExam: null,
      showExamNotice: false,

      // 筛选表单
      filterForm: {
        status: '',
        level: '',
        keyword: ''
      },

      // 分页
      pagination: {
        current: 1,
        size: 12,
        total: 0
      },

      // 统计数据
      examStats: {
        total: 0,
        completed: 0,
        inProgress: 0
      }
    }
  },
  created() {
    this.loadExamList()
    this.loadExamStats()
  },
  methods: {
    // 加载考试列表
    async loadExamList() {
      this.loading = true
      try {
        const params = {
          current: this.pagination.current,
          size: this.pagination.size,
          params: {
            examType: 'convection',
            ...this.filterForm
          }
        }

        const response = await ConvectionExamAPI.getMyExams(params)
        if (response.code === 0) {
          this.examList = response.data.records || []
          this.pagination.total = response.data.total || 0
        } else {
          this.$message.error('加载考试列表失败：' + response.msg)
        }
      } catch (error) {
        this.$message.error('加载考试列表失败：' + error.message)
      } finally {
        this.loading = false
      }
    },

    // 加载统计数据
    async loadExamStats() {
      try {
        const response = await ConvectionExamAPI.getStatistics()
        if (response.code === 0) {
          this.examStats = response.data || {}
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },

    // 筛选处理
    handleFilter() {
      this.pagination.current = 1
      this.loadExamList()
    },

    // 分页处理
    handlePageChange(page) {
      this.pagination.current = page
      this.loadExamList()
    },

    // 开始考试
    startExam(exam) {
      this.selectedExam = exam
      this.showExamNotice = true
    },

    // 确认开始考试
    async confirmStartExam() {
      try {
        const response = await ConvectionExamAPI.start(this.selectedExam.id)
        if (response.code === 0) {
          this.showExamNotice = false
          this.$router.push(`/student/convection/exam/${this.selectedExam.id}`)
        } else {
          this.$message.error('开始考试失败：' + response.msg)
        }
      } catch (error) {
        this.$message.error('开始考试失败：' + error.message)
      }
    },

    // 继续考试
    continueExam(exam) {
      this.$router.push(`/student/convection/exam/${exam.id}`)
    },

    // 查看结果
    viewResult(exam) {
      this.$router.push(`/student/convection/result/${exam.id}`)
    },

    // 工具方法
    getStatusText(status) {
      const statusMap = {
        'pending': '未开始',
        'active': '进行中',
        'completed': '已结束',
        'expired': '已截止'
      }
      return statusMap[status] || '未知'
    },

    getStatusTagType(status) {
      const typeMap = {
        'pending': 'info',
        'active': 'success',
        'completed': 'primary',
        'expired': 'danger'
      }
      return typeMap[status] || 'info'
    },

    getLevelText(level) {
      const levelMap = { 1: '简单', 2: '中等', 3: '困难' }
      return levelMap[level] || '未知'
    },

    getLevelTagType(level) {
      const typeMap = { 1: 'success', 2: 'warning', 3: 'danger' }
      return typeMap[level] || 'info'
    },

    getProgressColor(progress) {
      if (progress >= 80) return '#67c23a'
      if (progress >= 50) return '#e6a23c'
      return '#f56c6c'
    },

    getGradeText(score) {
      if (score >= 90) return '优秀'
      if (score >= 80) return '良好'
      if (score >= 72) return '及格'
      return '不及格'
    },

    getGradeTagType(score) {
      if (score >= 90) return 'success'
      if (score >= 80) return 'primary'
      if (score >= 72) return 'warning'
      return 'danger'
    },

    getDisabledReason(exam) {
      if (exam.examStatus === 'pending') return '未开始'
      if (exam.examStatus === 'expired') return '已截止'
      if (!exam.canParticipate) return '无权限'
      return '不可用'
    },

    formatDate(date) {
      if (!date) return '--'
      return new Date(date).toLocaleDateString()
    },

    formatDateRange(startTime, endTime) {
      if (!startTime || !endTime) return '--'
      const start = new Date(startTime).toLocaleString()
      const end = new Date(endTime).toLocaleString()
      return `${start} ~ ${end}`
    }
  }
}
</script>

<style scoped>
.student-exam-list {
  padding: 20px;
  background: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 25px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-description {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.header-stats {
  display: flex;
  gap: 30px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 24px;
  font-weight: 700;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
  margin-top: 4px;
  display: block;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-card {
  border-radius: 8px;
}

.filter-form {
  margin: 0;
}

.exam-container {
  margin-bottom: 20px;
}

.exam-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.exam-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

.exam-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.exam-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.card-header {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: #ffffff;
  padding: 20px;
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.exam-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.3;
  flex: 1;
  margin-right: 10px;
}

.status-tag {
  flex-shrink: 0;
}

.exam-meta {
  display: flex;
  gap: 20px;
  font-size: 14px;
  opacity: 0.9;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.card-content {
  padding: 20px;
}

.exam-info {
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-label {
  color: #8c8c8c;
  min-width: 80px;
  flex-shrink: 0;
}

.info-value {
  color: #303133;
}

.info-value.description {
  line-height: 1.4;
  max-height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.progress-section,
.result-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.progress-header,
.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.progress-label,
.result-label {
  font-weight: 600;
  color: #303133;
}

.progress-percentage,
.result-score {
  font-weight: 700;
  color: #1890ff;
}

.progress-bar {
  margin-bottom: 10px;
}

.progress-details,
.result-details {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #8c8c8c;
}

.result-grade {
  text-align: center;
  margin-top: 10px;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #fafafa;
  border-top: 1px solid #f0f0f0;
}

.time-info {
  font-size: 12px;
  color: #8c8c8c;
  display: flex;
  align-items: center;
  gap: 5px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.empty-content {
  text-align: center;
  color: #8c8c8c;
}

.empty-content i {
  font-size: 64px;
  margin-bottom: 20px;
  color: #dcdfe6;
}

.empty-content h3 {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 18px;
}

.empty-content p {
  margin: 0 0 20px 0;
  color: #8c8c8c;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

.exam-notice-dialog {
  border-radius: 8px;
}

.notice-content {
  max-height: 500px;
  overflow-y: auto;
}

.notice-section {
  margin-bottom: 25px;
}

.notice-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.notice-section ul {
  margin: 0;
  padding-left: 20px;
  line-height: 1.6;
  color: #606266;
}

.notice-section li {
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 20px;
  }

  .header-stats {
    gap: 20px;
  }

  .exam-grid {
    grid-template-columns: 1fr;
  }

  .card-footer {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .progress-details,
  .result-details {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
