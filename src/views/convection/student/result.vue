<template>
  <div class="student-exam-result">
    <!-- 结果头部 -->
    <div class="result-header">
      <div class="header-content">
        <div class="header-left">
          <el-button
            icon="el-icon-arrow-left"
            class="back-button"
            @click="goBack"
          >
            返回考试列表
          </el-button>
          <div class="header-info">
            <h2>考试结果</h2>
            <div class="exam-meta">
              <span class="exam-title">{{ examInfo.title }}</span>
              <span class="submit-time">提交时间：{{ formatDateTime(resultData.submitTime) }}</span>
            </div>
          </div>
        </div>
        <div class="header-right">
          <div class="result-summary">
            <div class="total-score">
              <span class="score-value">{{ resultData.totalScore || 0 }}</span>
              <span class="score-total">/ 120</span>
            </div>
            <div class="grade-badge">
              <el-tag
                :type="getGradeTagType(resultData.totalScore)"
                size="large"
                class="grade-tag"
              >
                {{ getGradeText(resultData.totalScore) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 成绩详情卡片 -->
    <div class="score-details">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card shadow="hover" class="score-card station">
            <div class="score-item">
              <div class="score-icon">
                <i class="el-icon-s-grid" />
              </div>
              <div class="score-info">
                <div class="score-title">站点预报</div>
                <div class="score-points">
                  <span class="current">{{ resultData.stationScore || 0 }}</span>
                  <span class="total">/ 68分</span>
                </div>
                <div class="score-rate">
                  得分率：{{ getScoreRate(resultData.stationScore, 68) }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card shadow="hover" class="score-card area">
            <div class="score-item">
              <div class="score-icon">
                <i class="el-icon-map-location" />
              </div>
              <div class="score-info">
                <div class="score-title">落区绘制</div>
                <div class="score-points">
                  <span class="current">{{ resultData.areaScore || 0 }}</span>
                  <span class="total">/ 32分</span>
                </div>
                <div class="score-rate">
                  得分率：{{ getScoreRate(resultData.areaScore, 32) }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card shadow="hover" class="score-card reasoning">
            <div class="score-item">
              <div class="score-icon">
                <i class="el-icon-edit-outline" />
              </div>
              <div class="score-info">
                <div class="score-title">预报依据</div>
                <div class="score-points">
                  <span class="current">{{ resultData.reasoningScore || 0 }}</span>
                  <span class="total">/ 20分</span>
                </div>
                <div class="score-rate">
                  得分率：{{ getScoreRate(resultData.reasoningScore, 20) }}%
                </div>
                <div v-if="gradingInfo.gradingStatus !== undefined" class="grading-status">
                  <el-tag
                    :type="getGradingStatusTagType(gradingInfo.gradingStatus)"
                    size="mini"
                  >
                    {{ getGradingStatusText(gradingInfo.gradingStatus) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <div v-loading="loading" class="result-content">
      <el-tabs v-model="activeTab" type="card" class="result-tabs">
        <!-- 答案回顾 -->
        <el-tab-pane label="答案回顾" name="review">
          <div class="review-content">
            <!-- 站点预报回顾 -->
            <div class="review-section">
              <div class="section-header">
                <h3>
                  <i class="el-icon-s-grid" />
                  站点预报回顾
                </h3>
                <div class="section-score">
                  得分：{{ resultData.stationScore || 0 }} / 68分
                </div>
              </div>

              <div class="station-review">
                <el-table
                  :data="getStationReviewData()"
                  border
                  size="medium"
                  class="review-table"
                >
                  <el-table-column prop="stationName" label="站点" width="100" align="center" />
                  <el-table-column label="短时强降水" width="150" align="center">
                    <template slot-scope="scope">
                      <div class="answer-cell">
                        <div class="my-answer">
                          <span class="answer-label">我的答案：</span>
                          <el-tag v-if="scope.row.myRainfall" size="mini" type="primary">
                            {{ getRainfallLabel(scope.row.myRainfall) }}
                          </el-tag>
                          <span v-else class="no-answer">未选择</span>
                        </div>
                        <div v-if="showStandardAnswer" class="standard-answer">
                          <span class="answer-label">标准答案：</span>
                          <el-tag v-if="scope.row.standardRainfall" size="mini" type="success">
                            {{ getRainfallLabel(scope.row.standardRainfall) }}
                          </el-tag>
                          <span v-else class="no-answer">无</span>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="雷暴大风" width="150" align="center">
                    <template slot-scope="scope">
                      <div class="answer-cell">
                        <div class="my-answer">
                          <span class="answer-label">我的答案：</span>
                          <el-tag v-if="scope.row.myWind" size="mini" type="primary">
                            {{ getWindLabel(scope.row.myWind) }}
                          </el-tag>
                          <span v-else class="no-answer">未选择</span>
                        </div>
                        <div v-if="showStandardAnswer" class="standard-answer">
                          <span class="answer-label">标准答案：</span>
                          <el-tag v-if="scope.row.standardWind" size="mini" type="success">
                            {{ getWindLabel(scope.row.standardWind) }}
                          </el-tag>
                          <span v-else class="no-answer">无</span>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="冰雹" width="120" align="center">
                    <template slot-scope="scope">
                      <div class="answer-cell">
                        <div class="my-answer">
                          <span class="answer-label">我的答案：</span>
                          <el-tag v-if="scope.row.myHail" size="mini" type="primary">
                            大冰雹
                          </el-tag>
                          <span v-else class="no-answer">未选择</span>
                        </div>
                        <div v-if="showStandardAnswer" class="standard-answer">
                          <span class="answer-label">标准答案：</span>
                          <el-tag v-if="scope.row.standardHail" size="mini" type="success">
                            大冰雹
                          </el-tag>
                          <span v-else class="no-answer">无</span>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="得分" width="80" align="center">
                    <template slot-scope="scope">
                      <div class="station-score">
                        <span class="score-value">{{ scope.row.score || 0 }}</span>
                        <span class="score-unit">分</span>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <!-- 预报依据回顾 -->
              <div class="reasoning-review">
                <div class="reasoning-header">
                  <h4>预报依据阐述</h4>
                  <div class="reasoning-meta">
                    <span>字数：{{ answerData.reasoningWordCount || 0 }}字</span>
                    <span>建议字数：100-1500字</span>
                  </div>
                </div>
                <div class="reasoning-content">
                  <div class="my-reasoning">
                    <div class="reasoning-label">我的预报依据：</div>
                    <div class="reasoning-text">
                      {{ answerData.forecastReasoning || '未填写预报依据' }}
                    </div>
                  </div>

                  <div v-if="showStandardAnswer && questionInfo.convectionStandardReasoning" class="standard-reasoning">
                    <div class="reasoning-label">标准预报依据：</div>
                    <div class="reasoning-text standard">
                      {{ questionInfo.convectionStandardReasoning }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 落区绘制回顾 -->
            <div class="review-section">
              <div class="section-header">
                <h3>
                  <i class="el-icon-map-location" />
                  落区绘制回顾
                </h3>
                <div class="section-score">
                  得分：{{ resultData.areaScore || 0 }} / 32分
                </div>
              </div>

              <div class="area-review">
                <div class="area-stats">
                  <div v-for="type in convectionTypes" :key="type.value" class="area-stat-item">
                    <div class="stat-label">
                      <div :style="{ backgroundColor: type.color }" class="color-dot" />
                      <span>{{ type.shortLabel }}：</span>
                    </div>
                    <div class="stat-content">
                      <span class="my-count">我绘制了{{ getAreaCount(answerData.areaAnswer, type.value) }}个落区</span>
                      <span v-if="showStandardAnswer" class="standard-count">
                        (标准答案{{ getAreaCount(questionInfo.standardAreaAnswer, type.value) }}个)
                      </span>
                    </div>
                  </div>
                </div>

                <div class="area-map-placeholder">
                  <p>落区绘制地图回顾</p>
                  <p class="placeholder-text">（此处可集成地图组件展示绘制结果）</p>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 评分详情 -->
        <el-tab-pane label="评分详情" name="scoring">
          <div class="scoring-content">
            <!-- 自动评分部分 -->
            <div class="scoring-section">
              <div class="section-header">
                <h3>
                  <i class="el-icon-cpu" />
                  自动评分详情
                </h3>
                <div class="section-score">
                  总分：{{ (resultData.stationScore || 0) + (resultData.areaScore || 0) }} / 100分
                </div>
              </div>

              <div class="auto-scoring-details">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-card shadow="never" class="scoring-detail-card">
                      <div slot="header">站点预报评分详情</div>
                      <div class="scoring-breakdown">
                        <div class="breakdown-item">
                          <span class="item-label">基础站点得分：</span>
                          <span class="item-value">{{ scoreDetails.basicStationScore || 0 }}分</span>
                        </div>
                        <div class="breakdown-item">
                          <span class="item-label">极端天气预报：</span>
                          <span class="item-value">{{ scoreDetails.extremeWeatherScore || 0 }}分</span>
                        </div>
                        <div class="breakdown-item total">
                          <span class="item-label">站点预报小计：</span>
                          <span class="item-value">{{ resultData.stationScore || 0 }}分</span>
                        </div>
                      </div>
                    </el-card>
                  </el-col>

                  <el-col :span="12">
                    <el-card shadow="never" class="scoring-detail-card">
                      <div slot="header">落区绘制评分详情</div>
                      <div class="scoring-breakdown">
                        <div v-for="type in convectionTypes" :key="type.value" class="breakdown-item">
                          <span class="item-label">{{ type.shortLabel }}：</span>
                          <span class="item-value">{{ getAreaTypeScore(type.value) }}分</span>
                        </div>
                        <div class="breakdown-item total">
                          <span class="item-label">落区绘制小计：</span>
                          <span class="item-value">{{ resultData.areaScore || 0 }}分</span>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                </el-row>
              </div>
            </div>

            <!-- 人工评分部分 -->
            <div v-if="gradingInfo.gradingStatus > 0" class="scoring-section">
              <div class="section-header">
                <h3>
                  <i class="el-icon-user" />
                  人工评分详情
                </h3>
                <div class="section-score">
                  得分：{{ resultData.reasoningScore || 0 }} / 20分
                </div>
              </div>

              <div class="manual-scoring-details">
                <el-card shadow="never" class="scoring-detail-card">
                  <div slot="header">
                    <span>预报依据评分</span>
                    <el-tag
                      :type="getGradingStatusTagType(gradingInfo.gradingStatus)"
                      size="small"
                      style="float: right;"
                    >
                      {{ getGradingStatusText(gradingInfo.gradingStatus) }}
                    </el-tag>
                  </div>

                  <div class="scoring-breakdown">
                    <div class="breakdown-item">
                      <span class="item-label">分级依据阐述：</span>
                      <span class="item-value">{{ gradingInfo.reasoningGradingBasisScore || 0 }}分 / 10分</span>
                    </div>
                    <div class="breakdown-item">
                      <span class="item-label">极端天气预报理由：</span>
                      <span class="item-value">{{ gradingInfo.reasoningExtremeScore || 0 }}分 / 10分</span>
                    </div>
                    <div class="breakdown-item total">
                      <span class="item-label">预报依据小计：</span>
                      <span class="item-value">{{ resultData.reasoningScore || 0 }}分</span>
                    </div>
                  </div>

                  <div class="grading-info">
                    <div class="grader-info">
                      <span class="info-label">批卷教师：</span>
                      <span class="info-value">{{ gradingInfo.graderName || '--' }}</span>
                    </div>
                    <div class="grading-time">
                      <span class="info-label">批卷时间：</span>
                      <span class="info-value">{{ formatDateTime(gradingInfo.gradingTime) }}</span>
                    </div>
                  </div>
                </el-card>
              </div>
            </div>

            <!-- 未批卷提示 -->
            <div v-else class="scoring-section">
              <div class="pending-grading">
                <div class="pending-icon">
                  <i class="el-icon-time" />
                </div>
                <div class="pending-text">
                  <h4>预报依据批卷中</h4>
                  <p>您的预报依据正在等待教师批卷，批卷完成后您将看到详细的评分和评语。</p>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 教师评语 -->
        <el-tab-pane v-if="gradingInfo.gradingStatus > 0" label="教师评语" name="comments">
          <div class="comments-content">
            <div class="comments-section">
              <div class="section-header">
                <h3>
                  <i class="el-icon-chat-line-round" />
                  批卷评语
                </h3>
                <div class="grader-info">
                  <span>批卷教师：{{ gradingInfo.graderName }}</span>
                  <span>批卷时间：{{ formatDateTime(gradingInfo.gradingTime) }}</span>
                </div>
              </div>

              <div class="comments-body">
                <div v-if="gradingInfo.gradingComments" class="comment-item">
                  <div class="comment-label">
                    <i class="el-icon-chat-dot-round" />
                    评语：
                  </div>
                  <div class="comment-content">
                    {{ gradingInfo.gradingComments }}
                  </div>
                </div>

                <div v-if="gradingInfo.improvementSuggestions" class="comment-item">
                  <div class="comment-label">
                    <i class="el-icon-edit" />
                    改进建议：
                  </div>
                  <div class="comment-content suggestions">
                    {{ gradingInfo.improvementSuggestions }}
                  </div>
                </div>

                <div v-if="!gradingInfo.gradingComments && !gradingInfo.improvementSuggestions" class="comment-empty">
                  <i class="el-icon-info" />
                  <span>教师未留下评语</span>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 成绩证书 -->
        <el-tab-pane v-if="resultData.totalScore >= 72" label="成绩证书" name="certificate">
          <div class="certificate-content">
            <div class="certificate-container">
              <div class="certificate">
                <div class="certificate-header">
                  <h2>强对流天气临近预报考试</h2>
                  <p>成绩证书</p>
                </div>

                <div class="certificate-body">
                  <div class="student-info">
                    <p>兹证明</p>
                    <h3>{{ studentInfo.realName || '学员' }}</h3>
                    <p>在强对流天气临近预报专项考试中</p>
                  </div>

                  <div class="score-info">
                    <div class="score-display">
                      <span class="score-number">{{ resultData.totalScore }}</span>
                      <span class="score-text">分</span>
                    </div>
                    <div class="grade-display">
                      <el-tag :type="getGradeTagType(resultData.totalScore)" size="large">
                        {{ getGradeText(resultData.totalScore) }}
                      </el-tag>
                    </div>
                  </div>

                  <div class="certificate-details">
                    <p>考试时间：{{ formatDate(examInfo.startTime) }}</p>
                    <p>有效期：长期有效</p>
                  </div>
                </div>

                <div class="certificate-footer">
                  <div class="seal">
                    <p>气象培训中心</p>
                    <p>{{ formatDate(new Date()) }}</p>
                  </div>
                </div>
              </div>

              <div class="certificate-actions">
                <el-button type="primary" icon="el-icon-download" @click="downloadCertificate">
                  下载证书
                </el-button>
                <el-button icon="el-icon-printer" @click="printCertificate">
                  打印证书
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 操作按钮 -->
    <div class="result-actions">
      <el-button icon="el-icon-back" size="large" @click="goBack">
        返回列表
      </el-button>
      <el-button
        :icon="showStandardAnswer ? 'el-icon-view' : 'el-icon-hide'"
        type="primary"
        size="large"
        @click="toggleStandardAnswer"
      >
        {{ showStandardAnswer ? '隐藏' : '查看' }}标准答案
      </el-button>
      <el-button type="success" icon="el-icon-share" size="large" @click="shareResult">
        分享成绩
      </el-button>
    </div>
  </div>
</template>

<script>
import { ConvectionAnswerAPI, ConvectionExamAPI } from '@/api/convection'

export default {
  name: 'StudentExamResult',
  data() {
    return {
      loading: false,
      examId: '',
      activeTab: 'review',
      showStandardAnswer: false,

      // 数据
      examInfo: {},
      questionInfo: {},
      answerData: {},
      resultData: {},
      gradingInfo: {},
      scoreDetails: {},
      studentInfo: {},

      // 站点数据
      stations: [
        { code: '54511', name: '北京', lon: 116.28, lat: 39.93 },
        { code: '54517', name: '密云', lon: 116.83, lat: 40.37 },
        { code: '54594', name: '天津', lon: 117.17, lat: 39.08 },
        { code: '54596', name: '塘沽', lon: 117.65, lat: 39.02 }
      ],

      // 强对流类型配置
      convectionTypes: [
        { label: '短时强降水', shortLabel: '强降水', value: 'heavy_rainfall', color: '#1890ff' },
        { label: '雷暴大风', shortLabel: '雷暴风', value: 'thunderstorm_wind', color: '#52c41a' },
        { label: '冰雹', shortLabel: '冰雹', value: 'hail', color: '#722ed1' },
        { label: '龙卷', shortLabel: '龙卷', value: 'tornado', color: '#f5222d' }
      ]
    }
  },
  async created() {
    this.examId = this.$route.params.examId
    if (!this.examId) {
      this.$message.error('考试ID无效')
      this.goBack()
      return
    }

    await this.loadResultData()
  },
  methods: {
    // 加载结果数据
    async loadResultData() {
      this.loading = true
      try {
        const response = await ConvectionExamAPI.getResult(this.examId)
        if (response.code === 0) {
          const data = response.data

          this.examInfo = data.examInfo || {}
          this.questionInfo = data.questionInfo || {}
          this.answerData = data.answerData || {}
          this.resultData = data.resultData || {}
          this.gradingInfo = data.gradingInfo || {}
          this.scoreDetails = data.scoreDetails || {}
          this.studentInfo = data.studentInfo || {}
        } else {
          this.$message.error('加载考试结果失败：' + response.msg)
          this.goBack()
        }
      } catch (error) {
        this.$message.error('加载考试结果失败：' + error.message)
        this.goBack()
      } finally {
        this.loading = false
      }
    },

    // 获取站点回顾数据
    getStationReviewData() {
      return this.stations.map(station => {
        const myAnswer = this.answerData.stationAnswer ? this.answerData.stationAnswer[station.code] : {}
        const standardAnswer = this.questionInfo.standardStationAnswer ? this.questionInfo.standardStationAnswer[station.code] : {}

        return {
          stationName: station.name,
          stationCode: station.code,
          myRainfall: myAnswer.rainfall,
          myWind: myAnswer.wind,
          myHail: myAnswer.hail,
          standardRainfall: standardAnswer.rainfall,
          standardWind: standardAnswer.wind,
          standardHail: standardAnswer.hail,
          score: this.getStationScore(station.code)
        }
      })
    },

    // 获取单个站点得分
    getStationScore(stationCode) {
      // 从评分详情中获取具体站点得分
      if (this.scoreDetails.stationScores && this.scoreDetails.stationScores[stationCode]) {
        return this.scoreDetails.stationScores[stationCode]
      }
      return 0
    },

    // 获取落区类型得分
    getAreaTypeScore(type) {
      if (this.scoreDetails.areaScores && this.scoreDetails.areaScores[type]) {
        return this.scoreDetails.areaScores[type]
      }
      return 0
    },

    // 返回列表
    goBack() {
      this.$router.push('/student/convection/exam-list')
    },

    // 切换标准答案显示
    toggleStandardAnswer() {
      this.showStandardAnswer = !this.showStandardAnswer
    },

    // 分享成绩
    shareResult() {
      const shareText = `我在强对流天气临近预报考试中获得了${this.resultData.totalScore}分的成绩！`

      if (navigator.share) {
        navigator.share({
          title: '考试成绩分享',
          text: shareText,
          url: window.location.href
        })
      } else {
        // 复制到剪贴板
        this.$copyText(shareText).then(() => {
          this.$message.success('成绩信息已复制到剪贴板')
        }).catch(() => {
          this.$message.error('分享失败')
        })
      }
    },

    // 下载证书
    downloadCertificate() {
      this.$message.info('证书下载功能开发中...')
    },

    // 打印证书
    printCertificate() {
      window.print()
    },

    // 工具方法
    getGradeText(score) {
      if (score >= 90) return '优秀'
      if (score >= 80) return '良好'
      if (score >= 72) return '及格'
      return '不及格'
    },

    getGradeTagType(score) {
      if (score >= 90) return 'success'
      if (score >= 80) return 'primary'
      if (score >= 72) return 'warning'
      return 'danger'
    },

    getGradingStatusText(status) {
      const statusMap = { 0: '待批卷', 1: '已批卷', 2: '已复审' }
      return statusMap[status] || '未知'
    },

    getGradingStatusTagType(status) {
      const typeMap = { 0: 'warning', 1: 'success', 2: 'primary' }
      return typeMap[status] || 'info'
    },

    getScoreRate(score, total) {
      if (!total) return 0
      return Math.round((score || 0) / total * 100)
    },

    getAreaCount(areaAnswer, type) {
      return areaAnswer && areaAnswer[type] ? areaAnswer[type].length : 0
    },

    getRainfallLabel(value) {
      const labels = { 'level1': 'Level 1', 'level2': 'Level 2', 'level3': 'Level 3' }
      return labels[value] || value
    },

    getWindLabel(value) {
      const labels = { 'moderate': 'Moderate', 'severe': 'Severe', 'extreme': 'Extreme' }
      return labels[value] || value
    },

    formatDateTime(date) {
      if (!date) return '--'
      return new Date(date).toLocaleString()
    },

    formatDate(date) {
      if (!date) return '--'
      return new Date(date).toLocaleDateString()
    }
  }
}
</script>

<style scoped>
.student-exam-result {
  padding: 20px;
  background: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.result-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 0;
  margin-bottom: 20px;
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  overflow: hidden;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 25px;
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.back-button {
  margin-top: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  color: #ffffff;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.header-info h2 {
  margin: 0 0 12px 0;
  font-size: 28px;
  font-weight: 600;
}

.exam-meta {
  display: flex;
  flex-direction: column;
  gap: 6px;
  font-size: 14px;
  opacity: 0.9;
}

.exam-title {
  font-weight: 600;
  font-size: 16px;
}

.result-summary {
  text-align: center;
}

.total-score {
  margin-bottom: 10px;
}

.score-value {
  font-size: 48px;
  font-weight: 700;
  line-height: 1;
}

.score-total {
  font-size: 24px;
  opacity: 0.8;
  margin-left: 5px;
}

.grade-tag {
  font-size: 16px;
  padding: 8px 16px;
}

.score-details {
  margin-bottom: 20px;
}

.score-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  height: 120px;
}

.score-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.score-card.station {
  border-left: 4px solid #1890ff;
}

.score-card.area {
  border-left: 4px solid #52c41a;
}

.score-card.reasoning {
  border-left: 4px solid #722ed1;
}

.score-item {
  display: flex;
  align-items: center;
  padding: 20px;
  height: 100%;
}

.score-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: #ffffff;
}

.score-card.station .score-icon {
  background: #1890ff;
}

.score-card.area .score-icon {
  background: #52c41a;
}

.score-card.reasoning .score-icon {
  background: #722ed1;
}

.score-info {
  flex: 1;
}

.score-title {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.score-points {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 4px;
}

.score-points .current {
  color: #1890ff;
}

.score-points .total {
  font-size: 16px;
  color: #8c8c8c;
  font-weight: 400;
}

.score-rate {
  font-size: 12px;
  color: #606266;
}

.grading-status {
  margin-top: 8px;
}

.result-content {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.result-tabs {
  padding: 20px;
}

.review-section {
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
  border-bottom: 2px solid #f0f0f0;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.section-score {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
}

.review-table {
  width: 100%;
  margin-bottom: 20px;
}

.answer-cell {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.my-answer,
.standard-answer {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
}

.answer-label {
  color: #8c8c8c;
  min-width: 60px;
}

.no-answer {
  color: #bfbfbf;
  font-size: 12px;
}

.station-score {
  text-align: center;
}

.score-value {
  font-weight: 600;
  color: #303133;
}

.score-unit {
  font-size: 12px;
  color: #8c8c8c;
}

.reasoning-review {
  margin-top: 20px;
}

.reasoning-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.reasoning-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.reasoning-meta {
  display: flex;
  gap: 15px;
  font-size: 13px;
  color: #8c8c8c;
}

.reasoning-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.my-reasoning,
.standard-reasoning {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #e9ecef;
}

.standard-reasoning {
  background: #f6ffed;
  border-color: #b7eb8f;
}

.reasoning-label {
  font-weight: 600;
  color: #303133;
  margin-bottom: 10px;
  font-size: 14px;
}

.reasoning-text {
  line-height: 1.6;
  color: #606266;
}

.reasoning-text.standard {
  color: #52c41a;
}

.area-stats {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.area-stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.color-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}

.my-count {
  color: #303133;
  font-weight: 600;
}

.standard-count {
  color: #8c8c8c;
}

.area-map-placeholder {
  background: #f5f5f5;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  color: #8c8c8c;
  border: 2px dashed #d9d9d9;
}

.placeholder-text {
  font-size: 12px;
  margin-top: 10px;
}

.scoring-section {
  margin-bottom: 30px;
}

.auto-scoring-details,
.manual-scoring-details {
  margin-top: 20px;
}

.scoring-detail-card {
  border-radius: 8px;
}

.scoring-breakdown {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.breakdown-item.total {
  border-bottom: none;
  padding-top: 12px;
  border-top: 2px solid #1890ff;
  font-weight: 600;
  color: #1890ff;
}

.item-label {
  color: #606266;
}

.item-value {
  font-weight: 600;
  color: #303133;
}

.grading-info {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  font-size: 13px;
  color: #8c8c8c;
}

.pending-grading {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 30px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: left;
}

.pending-icon {
  font-size: 48px;
  color: #e6a23c;
}

.pending-text h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 18px;
}

.pending-text p {
  margin: 0;
  color: #606266;
  line-height: 1.5;
}

.comments-section {
  margin-bottom: 20px;
}

.grader-info {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #8c8c8c;
}

.comments-body {
  margin-top: 20px;
}

.comment-item {
  margin-bottom: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e9ecef;
}

.comment-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
}

.comment-content {
  line-height: 1.6;
  color: #606266;
}

.comment-content.suggestions {
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
  padding: 15px;
  color: #d46b08;
}

.comment-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 40px;
  color: #8c8c8c;
  background: #f8f9fa;
  border-radius: 8px;
}

.certificate-content {
  padding: 20px 0;
}

.certificate-container {
  max-width: 800px;
  margin: 0 auto;
}

.certificate {
  background: #ffffff;
  border: 2px solid #d4af37;
  border-radius: 12px;
  padding: 40px;
  margin-bottom: 20px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(212, 175, 55, 0.2);
}

.certificate-header h2 {
  margin: 0 0 10px 0;
  color: #d4af37;
  font-size: 32px;
  font-weight: 700;
}

.certificate-header p {
  margin: 0 0 30px 0;
  color: #8c8c8c;
  font-size: 18px;
}

.student-info {
  margin-bottom: 30px;
}

.student-info p {
  margin: 10px 0;
  color: #606266;
  font-size: 18px;
}

.student-info h3 {
  margin: 20px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 700;
}

.score-display {
  margin-bottom: 15px;
}

.score-number {
  font-size: 64px;
  font-weight: 700;
  color: #d4af37;
  line-height: 1;
}

.score-text {
  font-size: 24px;
  color: #8c8c8c;
  margin-left: 10px;
}

.certificate-details {
  margin-top: 30px;
}

.certificate-details p {
  margin: 8px 0;
  color: #8c8c8c;
  font-size: 14px;
}

.certificate-footer {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.seal p {
  margin: 5px 0;
  color: #606266;
  font-size: 16px;
  font-weight: 600;
}

.certificate-actions {
  text-align: center;
}

.result-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  padding: 20px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
  }

  .header-left {
    flex-direction: column;
    gap: 15px;
  }

  .score-details .el-col {
    margin-bottom: 15px;
  }

  .section-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .answer-cell {
    font-size: 11px;
  }

  .area-stat-item {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .grading-info {
    flex-direction: column;
    gap: 8px;
  }

  .result-actions {
    flex-direction: column;
    align-items: center;
  }
}

/* 打印样式 */
@media print {
  .result-header,
  .result-actions,
  .result-tabs .el-tabs__header {
    display: none !important;
  }

  .certificate {
    border: 3px solid #000;
    page-break-inside: avoid;
  }
}
</style>
