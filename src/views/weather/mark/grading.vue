<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>历史个例批卷管理</h2>
      <p>管理历史个例考试的自动评分和批卷任务</p>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :inline="true" :model="queryForm" class="filter-form">
        <el-form-item label="考试名称">
          <el-input
            v-model="queryForm.examTitle"
            placeholder="请输入考试名称"
            style="width: 200px;"
            clearable
          />
        </el-form-item>

        <el-form-item label="考生姓名">
          <el-input
            v-model="queryForm.userName"
            placeholder="请输入考生姓名"
            style="width: 200px;"
            clearable
          />
        </el-form-item>

        <el-form-item label="评分状态">
          <el-select v-model="queryForm.gradingStatus" placeholder="请选择状态" clearable>
            <el-option :value="0" label="未评分" />
            <el-option :value="1" label="已评分" />
            <el-option :value="2" label="评分中" />
            <el-option :value="3" label="评分失败" />
          </el-select>
        </el-form-item>

        <el-form-item label="提交时间">
          <el-date-picker
            v-model="queryForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <i class="el-icon-search" />
            查询
          </el-button>
          <el-button @click="resetSearch">
            <i class="el-icon-refresh" />
            重置
          </el-button>
          <el-button
            :disabled="selectedAnswers.length === 0"
            type="success"
            @click="handleBatchGrading"
          >
            <i class="el-icon-s-promotion" />
            批量评分 ({{ selectedAnswers.length }})
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 答案列表 -->
    <el-card class="table-card">
      <div slot="header" class="card-header">
        <span>答案列表</span>
        <div class="header-actions">
          <el-button size="small" @click="refreshList">
            <i class="el-icon-refresh" />
            刷新
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="answerList"
        stripe
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column label="考试信息" min-width="200">
          <template v-slot="scope">
            <div class="exam-info">
              <div class="exam-title">{{ scope.row.examTitle }}</div>
              <div class="exam-meta">
                <el-tag size="mini" type="primary">历史个例</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="考生信息" width="150">
          <template v-slot="scope">
            <div class="user-info">
              <div class="user-name">{{ scope.row.userName }}</div>
              <div class="user-nick">{{ scope.row.userNick }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="提交时间" width="160" align="center">
          <template v-slot="scope">
            {{ scope.row.submitTime }}
          </template>
        </el-table-column>

        <el-table-column label="评分状态" width="120" align="center">
          <template v-slot="scope">
            <el-tag
              :type="getStatusType(scope.row.gradingStatus)"
              size="small"
            >
              {{ getStatusText(scope.row.gradingStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="得分" width="100" align="center">
          <template v-slot="scope">
            <span v-if="scope.row.gradingStatus === 1" class="score-text">
              {{ scope.row.totalScore || 0 }}分
            </span>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template v-slot="scope">
            <el-button
              :loading="scope.row.grading"
              :disabled="scope.row.gradingStatus === 2"
              size="mini"
              type="primary"
              @click="handleGrading(scope.row)"
            >
              {{ scope.row.gradingStatus === 1 ? '重新评分' : '开始评分' }}
            </el-button>

            <el-button
              v-if="scope.row.gradingStatus === 1"
              size="mini"
              type="info"
              @click="handleViewResult(scope.row)"
            >
              查看结果
            </el-button>

            <el-button
              size="mini"
              type="warning"
              @click="handleViewAnswer(scope.row)"
            >
              查看答案
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.size"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 批量评分对话框 -->
    <el-dialog
      :visible.sync="batchDialogVisible"
      :close-on-click-modal="false"
      title="批量评分"
      width="600px"
    >
      <div class="batch-content">
        <el-alert
          :closable="false"
          type="info"
          title="批量评分说明"
          show-icon
        >
          <p>将对选中的 <strong>{{ selectedAnswers.length }}</strong> 个答案进行批量评分</p>
          <p>评分完成后将自动更新评分状态和得分信息</p>
        </el-alert>

        <el-form :model="batchForm" class="batch-form" style="margin-top: 20px;">
          <el-form-item label="任务名称">
            <el-input
              v-model="batchForm.batchName"
              placeholder="请输入任务名称"
            />
          </el-form-item>

          <el-alert
            :closable="false"
            type="info"
            title="评分说明"
            show-icon
            style="margin-top: 16px;"
          >
            <p>历史个例考试采用固定的评分规则：</p>
            <ul style="margin: 8px 0; padding-left: 20px;">
              <li>风力等级：权重 1.0，完全匹配得分</li>
              <li>风向：权重 1.0，完全匹配得分</li>
              <li>温度：权重 4.0，容差 ±2℃</li>
              <li>降水：权重 2.0，按等级匹配</li>
              <li>灾害天气：权重 2.0，完全匹配得分</li>
            </ul>
          </el-alert>
        </el-form>

        <!-- 进度显示 -->
        <div v-if="batchProcessing" class="progress-section">
          <el-progress
            :percentage="batchProgress.percentage"
            :status="batchProgress.status"
          />
          <p class="progress-text">{{ batchProgress.text }}</p>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button :disabled="batchProcessing" @click="batchDialogVisible = false">
          取消
        </el-button>
        <el-button
          :loading="batchProcessing"
          type="primary"
          @click="confirmBatchGrading"
        >
          确认评分
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getAnswerList,
  calculateScore,
  batchCalculateScore,
  getBatchTaskStatus
} from '@/api/weather/grading'

export default {
  name: 'WeatherGradingManagement',
  data() {
    return {
      loading: false,
      answerList: [],
      selectedAnswers: [],

      // 查询条件
      queryForm: {
        examTitle: '',
        userName: '',
        gradingStatus: '',
        dateRange: []
      },

      // 分页
      pagination: {
        current: 1,
        size: 20,
        total: 0
      },

      // 批量评分
      batchDialogVisible: false,
      batchProcessing: false,
      batchForm: {
        batchName: ''
      },
      batchProgress: {
        percentage: 0,
        status: '',
        text: ''
      }
    }
  },

  created() {
    this.loadAnswerList()
  },

  methods: {
    // 加载答案列表
    async loadAnswerList() {
      this.loading = true
      try {
        const params = {
          current: this.pagination.current,
          size: this.pagination.size,
          params: {
            examTitle: this.queryForm.examTitle,
            userName: this.queryForm.userName,
            gradingStatus: this.queryForm.gradingStatus,
            startTime: this.queryForm.dateRange && this.queryForm.dateRange[0],
            endTime: this.queryForm.dateRange && this.queryForm.dateRange[1],
            examType: 'weather' // 只查询历史个例考试
          }
        }

        const response = await getAnswerList(params)
        if (response.code === 0) {
          this.answerList = response.data.records || []
          this.pagination.total = response.data.total || 0
        } else {
          this.$message.error('获取答案列表失败')
        }
      } catch (error) {
        console.error('获取答案列表错误:', error)
        this.$message.error('获取答案列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.current = 1
      this.loadAnswerList()
    },

    // 重置搜索
    resetSearch() {
      this.queryForm = {
        examTitle: '',
        userName: '',
        gradingStatus: '',
        dateRange: []
      }
      this.pagination.current = 1
      this.loadAnswerList()
    },

    // 刷新列表
    refreshList() {
      this.loadAnswerList()
    },

    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.current = 1
      this.loadAnswerList()
    },

    // 当前页改变
    handleCurrentChange(current) {
      this.pagination.current = current
      this.loadAnswerList()
    },

    // 选择改变
    handleSelectionChange(selection) {
      this.selectedAnswers = selection
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        0: 'warning', // 未评分
        1: 'success', // 已评分
        2: 'primary', // 评分中
        3: 'danger' // 评分失败
      }
      return statusMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '未评分',
        1: '已评分',
        2: '评分中',
        3: '评分失败'
      }
      return statusMap[status] || '未知'
    },

    // 单个评分
    async handleGrading(row) {
      this.$set(row, 'grading', true)
      try {
        const response = await calculateScore(row.id)

        if (response.code === 0) {
          this.$message.success('评分完成')
          this.loadAnswerList() // 刷新列表
        } else {
          this.$message.error(response.message || '评分失败')
        }
      } catch (error) {
        console.error('评分错误:', error)
        this.$message.error('评分失败')
      } finally {
        this.$set(row, 'grading', false)
      }
    },

    // 查看评分结果
    handleViewResult(row) {
      this.$router.push({
        path: `/weather/mark/result/${row.id}`
      })
    },

    // 查看答案
    handleViewAnswer(row) {
      this.$router.push({
        path: `/weather/mark/answer/${row.id}`
      })
    },

    // 批量评分
    handleBatchGrading() {
      if (this.selectedAnswers.length === 0) {
        this.$message.warning('请选择要评分的答案')
        return
      }

      this.batchForm.batchName = `批量评分_${new Date().toLocaleString()}`
      this.batchDialogVisible = true
    },

    // 确认批量评分
    async confirmBatchGrading() {
      if (!this.batchForm.batchName) {
        this.$message.warning('请输入任务名称')
        return
      }

      this.batchProcessing = true
      this.batchProgress = {
        percentage: 0,
        status: 'active',
        text: '正在提交批量评分任务...'
      }

      try {
        const answerIds = this.selectedAnswers.map(item => item.id)
        const response = await batchCalculateScore(
          answerIds,
          null,
          this.batchForm.batchName
        )

        if (response.code === 0) {
          const taskId = response.data
          this.batchProgress.text = '批量评分任务已提交，正在处理...'

          // 轮询任务状态
          await this.pollBatchTaskStatus(taskId)
        } else {
          throw new Error(response.message || '提交批量评分任务失败')
        }
      } catch (error) {
        console.error('批量评分错误:', error)
        this.$message.error(error.message || '批量评分失败')
        this.batchProgress.status = 'exception'
        this.batchProgress.text = '批量评分失败'
      } finally {
        setTimeout(() => {
          this.batchProcessing = false
          this.batchDialogVisible = false
          this.loadAnswerList() // 刷新列表
        }, 2000)
      }
    },

    // 轮询批量任务状态
    async pollBatchTaskStatus(taskId) {
      const maxAttempts = 60 // 最多轮询60次（5分钟）
      let attempts = 0

      const poll = async() => {
        try {
          const response = await getBatchTaskStatus(taskId)
          if (response.code === 0) {
            const task = response.data
            const progress = Math.min(90, (task.processedCount / task.totalCount) * 100)

            this.batchProgress.percentage = progress
            this.batchProgress.text = `已处理 ${task.processedCount}/${task.totalCount} 个答案`

            if (task.status === 'completed') {
              this.batchProgress.percentage = 100
              this.batchProgress.status = 'success'
              this.batchProgress.text = `批量评分完成！成功：${task.successCount}，失败：${task.failCount}`
              return
            } else if (task.status === 'failed') {
              this.batchProgress.status = 'exception'
              this.batchProgress.text = '批量评分任务失败'
              return
            }
          }

          attempts++
          if (attempts < maxAttempts) {
            setTimeout(poll, 5000) // 5秒后再次轮询
          } else {
            this.batchProgress.status = 'exception'
            this.batchProgress.text = '任务超时，请稍后查看结果'
          }
        } catch (error) {
          console.error('轮询任务状态错误:', error)
          this.batchProgress.status = 'exception'
          this.batchProgress.text = '获取任务状态失败'
        }
      }

      setTimeout(poll, 2000) // 2秒后开始轮询
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  margin: 0;
}

.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.exam-info {
  line-height: 1.4;
}

.exam-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.exam-meta {
  display: flex;
  gap: 4px;
}

.user-info {
  line-height: 1.4;
}

.user-name {
  font-weight: 500;
  color: #303133;
}

.user-nick {
  color: #909399;
  font-size: 12px;
}

.score-text {
  font-weight: 500;
  color: #67C23A;
}

.text-muted {
  color: #C0C4CC;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.batch-content {
  padding: 0 20px;
}

.batch-form {
  margin-top: 20px;
}

.progress-section {
  margin-top: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.progress-text {
  margin-top: 10px;
  text-align: center;
  color: #606266;
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
}
</style>
