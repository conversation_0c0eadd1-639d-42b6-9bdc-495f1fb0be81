<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-button size="small" icon="el-icon-arrow-left" @click="goBack">
        返回
      </el-button>
      <h2>评分结果详情</h2>
    </div>

    <div v-loading="loading">
      <!-- 基本信息 -->
      <el-card class="info-card">
        <div slot="header" class="card-header">
          <span>基本信息</span>
        </div>

        <el-row :gutter="20">
          <el-col :span="6">
            <div class="info-item">
              <span class="label">考试名称：</span>
              <span class="value">{{ resultData.examTitle }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">考生姓名：</span>
              <span class="value">{{ resultData.userName }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">提交时间：</span>
              <span class="value">{{ resultData.submitTime }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">评分时间：</span>
              <span class="value">{{ resultData.scoringTime }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 得分概览 -->
      <el-card class="score-overview">
        <div slot="header" class="card-header">
          <span>得分概览</span>
          <el-button size="small" type="primary" @click="handleRegrade">
            重新评分
          </el-button>
        </div>

        <el-row :gutter="20" class="score-summary">
          <el-col :span="4">
            <div class="score-item total-score">
              <div class="score-value">{{ resultData.totalScore || 0 }}</div>
              <div class="score-label">总得分</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="score-item">
              <div class="score-value">{{ resultData.maxScore || 100 }}</div>
              <div class="score-label">满分</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="score-item">
              <div class="score-value">{{ getScorePercentage() }}%</div>
              <div class="score-label">得分率</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="score-item">
              <div class="score-value">{{ resultData.stationCount || 0 }}</div>
              <div class="score-label">站点数量</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="score-item">
              <div class="score-value">{{ getCorrectStations() }}</div>
              <div class="score-label">正确站点</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="score-item">
              <div class="score-value">{{ getAccuracyRate() }}%</div>
              <div class="score-label">准确率</div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 分站得分详情 -->
      <el-card class="station-scores">
        <div slot="header" class="card-header">
          <span>分站得分详情</span>
          <div class="header-actions">
            <el-button size="small" @click="exportResults">
              <i class="el-icon-download" />
              导出结果
            </el-button>
          </div>
        </div>

        <el-table
          :data="stationScoreList"
          :summary-method="getSummaries"
          border
          stripe
          show-summary
        >
          <el-table-column prop="stationName" label="站点名称" width="120" />

          <el-table-column label="风力" width="100" align="center">
            <template v-slot="scope">
              <div class="score-cell">
                <div class="score-value">{{ scope.row.windForceScore || 0 }}</div>
                <div :class="getScoreStatus(scope.row.windForceScore, 1)" class="score-status">
                  {{ getScoreStatusText(scope.row.windForceScore, 1) }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="风向" width="100" align="center">
            <template v-slot="scope">
              <div class="score-cell">
                <div class="score-value">{{ scope.row.windDirectionScore || 0 }}</div>
                <div :class="getScoreStatus(scope.row.windDirectionScore, 1)" class="score-status">
                  {{ getScoreStatusText(scope.row.windDirectionScore, 1) }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="温度" width="100" align="center">
            <template v-slot="scope">
              <div class="score-cell">
                <div class="score-value">{{ scope.row.temperatureScore || 0 }}</div>
                <div :class="getScoreStatus(scope.row.temperatureScore, 4)" class="score-status">
                  {{ getScoreStatusText(scope.row.temperatureScore, 4) }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="降水" width="100" align="center">
            <template v-slot="scope">
              <div class="score-cell">
                <div class="score-value">{{ scope.row.precipitationScore || 0 }}</div>
                <div :class="getScoreStatus(scope.row.precipitationScore, 2)" class="score-status">
                  {{ getScoreStatusText(scope.row.precipitationScore, 2) }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="灾害天气" width="120" align="center">
            <template v-slot="scope">
              <div class="score-cell">
                <div class="score-value">{{ scope.row.disasterWeatherScore || 0 }}</div>
                <div :class="getScoreStatus(scope.row.disasterWeatherScore, 2)" class="score-status">
                  {{ getScoreStatusText(scope.row.disasterWeatherScore, 2) }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="小计" width="100" align="center">
            <template v-slot="scope">
              <div class="total-score-cell">
                {{ scope.row.totalScore || 0 }}
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="120" align="center" fixed="right">
            <template v-slot="scope">
              <el-button
                size="mini"
                type="info"
                @click="handleViewDetail(scope.row)"
              >
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 错误分析 -->
      <el-card v-if="errorAnalysis && errorAnalysis.length > 0" class="error-analysis">
        <div slot="header" class="card-header">
          <span>错误分析</span>
        </div>

        <div class="error-list">
          <div
            v-for="(error, index) in errorAnalysis"
            :key="index"
            class="error-item"
          >
            <div class="error-header">
              <el-tag :type="getErrorType(error.severity)" size="small">
                {{ error.severity }}
              </el-tag>
              <span class="error-station">{{ error.stationName }}</span>
              <span class="error-element">{{ error.element }}</span>
            </div>
            <div class="error-content">
              <p class="error-description">{{ error.description }}</p>
              <div class="error-details">
                <span class="predicted">预报值：{{ error.predictedValue }}</span>
                <span class="actual">实际值：{{ error.actualValue }}</span>
                <span class="difference">差值：{{ error.difference }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 站点详情对话框 -->
    <el-dialog
      :visible.sync="detailDialogVisible"
      title="站点评分详情"
      width="800px"
    >
      <div v-if="selectedStation">
        <h4>{{ selectedStation.stationName }} 评分详情</h4>
        <!-- 这里可以添加更详细的站点评分信息 -->
        <el-descriptions :column="2" border>
          <el-descriptions-item label="风力得分">
            {{ selectedStation.windForceScore || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="风向得分">
            {{ selectedStation.windDirectionScore || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="温度得分">
            {{ selectedStation.temperatureScore || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="降水得分">
            {{ selectedStation.precipitationScore || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="灾害天气得分">
            {{ selectedStation.disasterWeatherScore || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="站点总分">
            {{ selectedStation.totalScore || 0 }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getScoringResult,
  recalculateScore,
  exportScoringResult
} from '@/api/weather/grading'

export default {
  name: 'WeatherGradingResult',
  data() {
    return {
      loading: false,
      answerId: '',
      resultData: {},
      stationScoreList: [],
      errorAnalysis: [],
      detailDialogVisible: false,
      selectedStation: null
    }
  },

  created() {
    this.answerId = this.$route.params.id
    this.loadResultData()
  },

  methods: {
    // 返回
    goBack() {
      this.$router.go(-1)
    },

    // 加载评分结果数据
    async loadResultData() {
      this.loading = true
      try {
        const response = await getScoringResult(this.answerId)

        if (response.code === 0) {
          this.resultData = response.data.result || {}
          this.stationScoreList = response.data.stationScores || []
          this.errorAnalysis = response.data.errorAnalysis || []
        } else {
          this.$message.error('获取评分结果失败')
        }
      } catch (error) {
        console.error('获取评分结果错误:', error)
        this.$message.error('获取评分结果失败')
      } finally {
        this.loading = false
      }
    },

    // 获取得分百分比
    getScorePercentage() {
      if (!this.resultData.totalScore || !this.resultData.maxScore) return 0
      return Math.round((this.resultData.totalScore / this.resultData.maxScore) * 100)
    },

    // 获取正确站点数
    getCorrectStations() {
      return this.stationScoreList.filter(station =>
        (station.totalScore || 0) >= (station.maxScore || 10) * 0.8
      ).length
    },

    // 获取准确率
    getAccuracyRate() {
      if (this.stationScoreList.length === 0) return 0
      const correctCount = this.getCorrectStations()
      return Math.round((correctCount / this.stationScoreList.length) * 100)
    },

    // 获取得分状态
    getScoreStatus(score, maxScore) {
      const percentage = (score || 0) / maxScore
      if (percentage >= 0.9) return 'excellent'
      if (percentage >= 0.7) return 'good'
      if (percentage >= 0.5) return 'fair'
      return 'poor'
    },

    // 获取得分状态文本
    getScoreStatusText(score, maxScore) {
      const percentage = (score || 0) / maxScore
      if (percentage >= 0.9) return '优秀'
      if (percentage >= 0.7) return '良好'
      if (percentage >= 0.5) return '一般'
      return '较差'
    },

    // 获取错误类型
    getErrorType(severity) {
      const typeMap = {
        'high': 'danger',
        'medium': 'warning',
        'low': 'info'
      }
      return typeMap[severity] || 'info'
    },

    // 表格汇总方法
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }

        const values = data.map(item => {
          switch (column.property) {
            case 'windForceScore':
              return Number(item.windForceScore) || 0
            case 'windDirectionScore':
              return Number(item.windDirectionScore) || 0
            case 'temperatureScore':
              return Number(item.temperatureScore) || 0
            case 'precipitationScore':
              return Number(item.precipitationScore) || 0
            case 'disasterWeatherScore':
              return Number(item.disasterWeatherScore) || 0
            case 'totalScore':
              return Number(item.totalScore) || 0
            default:
              return 0
          }
        })

        if (!values.every(value => isNaN(value))) {
          const sum = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
          sums[index] = sum.toFixed(1)
        } else {
          sums[index] = '-'
        }
      })

      return sums
    },

    // 查看站点详情
    handleViewDetail(station) {
      this.selectedStation = station
      this.detailDialogVisible = true
    },

    // 重新评分
    async handleRegrade() {
      this.$confirm('确定要重新评分吗？这将覆盖当前的评分结果。', '确认重新评分', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          const response = await recalculateScore(this.answerId)

          if (response.code === 0) {
            this.$message.success('重新评分完成')
            this.loadResultData() // 重新加载数据
          } else {
            this.$message.error(response.message || '重新评分失败')
          }
        } catch (error) {
          console.error('重新评分错误:', error)
          this.$message.error('重新评分失败')
        }
      })
    },

    // 导出结果
    async exportResults() {
      try {
        const response = await exportScoringResult(this.answerId)

        // 创建下载链接
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `评分结果_${this.resultData.userName}_${new Date().toLocaleDateString()}.xlsx`
        link.click()
        window.URL.revokeObjectURL(url)

        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出错误:', error)
        this.$message.error('导出失败')
      }
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 12px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.info-card, .score-overview, .station-scores, .error-analysis {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.info-item {
  margin-bottom: 12px;
}

.info-item .label {
  color: #909399;
  font-size: 14px;
}

.info-item .value {
  color: #303133;
  font-weight: 500;
}

.score-summary {
  padding: 20px 0;
}

.score-item {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background-color: #f8f9fa;
  transition: all 0.3s;
}

.score-item:hover {
  background-color: #e9ecef;
}

.total-score {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.score-value {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
}

.score-label {
  font-size: 14px;
  opacity: 0.8;
}

.score-cell {
  text-align: center;
}

.score-cell .score-value {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
}

.score-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

.score-status.excellent {
  background-color: #f0f9ff;
  color: #1890ff;
}

.score-status.good {
  background-color: #f6ffed;
  color: #52c41a;
}

.score-status.fair {
  background-color: #fff7e6;
  color: #fa8c16;
}

.score-status.poor {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.total-score-cell {
  font-size: 16px;
  font-weight: bold;
  color: #1890ff;
}

.error-list {
  max-height: 400px;
  overflow-y: auto;
}

.error-item {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  margin-bottom: 12px;
  overflow: hidden;
}

.error-header {
  background-color: #fafafa;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.error-station {
  font-weight: 500;
  color: #303133;
}

.error-element {
  color: #606266;
  font-size: 14px;
}

.error-content {
  padding: 16px;
}

.error-description {
  margin: 0 0 12px 0;
  color: #303133;
  line-height: 1.5;
}

.error-details {
  display: flex;
  gap: 16px;
  font-size: 14px;
}

.error-details span {
  padding: 4px 8px;
  border-radius: 4px;
  background-color: #f5f5f5;
}

.predicted {
  color: #1890ff;
}

.actual {
  color: #52c41a;
}

.difference {
  color: #ff4d4f;
}
</style>
