<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-button size="small" icon="el-icon-arrow-left" @click="goBack">
        返回
      </el-button>
      <h2>答案详情</h2>
    </div>

    <div v-loading="loading">
      <!-- 基本信息 -->
      <el-card class="info-card">
        <div slot="header" class="card-header">
          <span>考试信息</span>
          <div class="header-actions">
            <el-button size="small" type="primary" @click="handleGrading">
              {{ answerData.gradingStatus === 1 ? '重新评分' : '开始评分' }}
            </el-button>
          </div>
        </div>

        <el-row :gutter="20">
          <el-col :span="6">
            <div class="info-item">
              <span class="label">考试名称：</span>
              <span class="value">{{ answerData.examTitle }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">考生姓名：</span>
              <span class="value">{{ answerData.userName }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">提交时间：</span>
              <span class="value">{{ answerData.submitTime }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">评分状态：</span>
              <el-tag :type="getStatusType(answerData.gradingStatus)" size="small">
                {{ getStatusText(answerData.gradingStatus) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 题目信息 -->
      <el-card class="question-card">
        <div slot="header" class="card-header">
          <span>题目信息</span>
        </div>

        <div class="question-content">
          <h4>{{ questionData.content }}</h4>
          <div v-if="questionData.image" class="question-image">
            <el-image
              :src="questionData.image"
              style="max-width: 100%; max-height: 300px;"
              fit="contain"
            />
          </div>

          <!-- 数据文件下载 -->
          <div v-if="questionData.dataFiles && questionData.dataFiles.length > 0" class="data-files">
            <h5>相关数据文件：</h5>
            <div class="file-list">
              <el-button
                v-for="file in questionData.dataFiles"
                :key="file.id"
                size="small"
                type="info"
                @click="downloadFile(file)"
              >
                <i class="el-icon-download" />
                {{ file.fileName }}
              </el-button>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 答案对比 -->
      <el-card class="answer-comparison">
        <div slot="header" class="card-header">
          <span>答案对比</span>
          <div class="header-actions">
            <el-radio-group v-model="viewMode" size="small">
              <el-radio-button label="table">表格视图</el-radio-button>
              <el-radio-button label="json">JSON视图</el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <!-- 表格视图 -->
        <div v-if="viewMode === 'table'" class="table-view">
          <el-tabs v-model="activeTab" type="card">
            <el-tab-pane label="学生答案" name="student">
              <weather-table-editor
                :table-data="studentAnswerData"
                :read-only="true"
              />
            </el-tab-pane>

            <el-tab-pane label="标准答案" name="standard">
              <weather-table-editor
                :table-data="standardAnswerData"
                :read-only="true"
              />
            </el-tab-pane>

            <el-tab-pane label="对比视图" name="comparison">
              <div class="comparison-view">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <h5>学生答案</h5>
                    <weather-table-editor
                      :table-data="studentAnswerData"
                      :read-only="true"
                    />
                  </el-col>
                  <el-col :span="12">
                    <h5>标准答案</h5>
                    <weather-table-editor
                      :table-data="standardAnswerData"
                      :read-only="true"
                    />
                  </el-col>
                </el-row>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- JSON视图 -->
        <div v-if="viewMode === 'json'" class="json-view">
          <el-row :gutter="20">
            <el-col :span="12">
              <h5>学生答案</h5>
              <pre class="json-content">{{ formatJson(answerData.answerData) }}</pre>
            </el-col>
            <el-col :span="12">
              <h5>标准答案</h5>
              <pre class="json-content">{{ formatJson(questionData.scenarioData) }}</pre>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 评分结果（如果已评分） -->
      <el-card v-if="answerData.gradingStatus === 1" class="scoring-result">
        <div slot="header" class="card-header">
          <span>评分结果</span>
          <el-button size="small" type="info" @click="viewDetailResult">
            查看详细结果
          </el-button>
        </div>

        <el-row :gutter="20">
          <el-col :span="6">
            <div class="result-item">
              <div class="result-value">{{ answerData.totalScore || 0 }}</div>
              <div class="result-label">总得分</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="result-item">
              <div class="result-value">{{ getScorePercentage() }}%</div>
              <div class="result-label">得分率</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="result-item">
              <div class="result-value">{{ answerData.scoringTime }}</div>
              <div class="result-label">评分时间</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="result-item">
              <div class="result-value">{{ getGradingLevel() }}</div>
              <div class="result-label">评分等级</div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getAnswerDetail, calculateScore } from '@/api/weather/grading'
import { post } from '@/utils/request'
import WeatherTableEditor from '@/components/weather/WeatherTableEditor'

export default {
  name: 'WeatherAnswerDetail',
  components: {
    WeatherTableEditor
  },
  data() {
    return {
      loading: false,
      answerId: '',
      answerData: {},
      questionData: {},
      studentAnswerData: {},
      standardAnswerData: {},
      viewMode: 'table',
      activeTab: 'student'
    }
  },

  created() {
    this.answerId = this.$route.params.id
    this.loadAnswerData()
  },

  methods: {
    // 返回
    goBack() {
      this.$router.go(-1)
    },

    // 加载答案数据
    async loadAnswerData() {
      this.loading = true
      try {
        const response = await getAnswerDetail(this.answerId)

        if (response.code === 0) {
          this.answerData = response.data.answer || {}
          this.questionData = response.data.question || {}

          // 解析答案数据
          if (this.answerData.answerData) {
            this.studentAnswerData = typeof this.answerData.answerData === 'string'
              ? JSON.parse(this.answerData.answerData)
              : this.answerData.answerData
          }

          // 解析标准答案数据
          if (this.questionData.scenarioData) {
            this.standardAnswerData = typeof this.questionData.scenarioData === 'string'
              ? JSON.parse(this.questionData.scenarioData)
              : this.questionData.scenarioData
          }
        } else {
          this.$message.error('获取答案数据失败')
        }
      } catch (error) {
        console.error('获取答案数据错误:', error)
        this.$message.error('获取答案数据失败')
      } finally {
        this.loading = false
      }
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        0: 'warning', // 未评分
        1: 'success', // 已评分
        2: 'primary', // 评分中
        3: 'danger' // 评分失败
      }
      return statusMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '未评分',
        1: '已评分',
        2: '评分中',
        3: '评分失败'
      }
      return statusMap[status] || '未知'
    },

    // 格式化JSON
    formatJson(data) {
      if (!data) return ''
      try {
        const parsed = typeof data === 'string' ? JSON.parse(data) : data
        return JSON.stringify(parsed, null, 2)
      } catch (error) {
        return data.toString()
      }
    },

    // 获取得分百分比
    getScorePercentage() {
      if (!this.answerData.totalScore || !this.answerData.maxScore) return 0
      return Math.round((this.answerData.totalScore / (this.answerData.maxScore || 100)) * 100)
    },

    // 获取评分等级
    getGradingLevel() {
      const percentage = this.getScorePercentage()
      if (percentage >= 90) return '优秀'
      if (percentage >= 80) return '良好'
      if (percentage >= 70) return '中等'
      if (percentage >= 60) return '及格'
      return '不及格'
    },

    // 下载文件
    async downloadFile(file) {
      try {
        const response = await post('/exam/api/weather/case/download', {
          fileId: file.id
        }, {
          responseType: 'blob'
        })

        // 创建下载链接
        const blob = new Blob([response])
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = file.fileName
        link.click()
        window.URL.revokeObjectURL(url)

        this.$message.success('下载成功')
      } catch (error) {
        console.error('下载文件错误:', error)
        this.$message.error('下载失败')
      }
    },

    // 开始评分
    async handleGrading() {
      try {
        const response = await calculateScore(this.answerId)

        if (response.code === 0) {
          this.$message.success('评分完成')
          this.loadAnswerData() // 重新加载数据
        } else {
          this.$message.error(response.message || '评分失败')
        }
      } catch (error) {
        console.error('评分错误:', error)
        this.$message.error('评分失败')
      }
    },

    // 查看详细结果
    viewDetailResult() {
      this.$router.push({
        path: `/weather/mark/result/${this.answerId}`
      })
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 12px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.info-card, .question-card, .answer-comparison, .scoring-result {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.info-item {
  margin-bottom: 12px;
}

.info-item .label {
  color: #909399;
  font-size: 14px;
}

.info-item .value {
  color: #303133;
  font-weight: 500;
}

.question-content h4 {
  margin: 0 0 16px 0;
  color: #303133;
  line-height: 1.5;
}

.question-image {
  margin: 16px 0;
  text-align: center;
}

.data-files {
  margin-top: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.data-files h5 {
  margin: 0 0 12px 0;
  color: #303133;
}

.file-list {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.table-view {
  min-height: 400px;
}

.json-view {
  padding: 16px 0;
}

.json-view h5 {
  margin: 0 0 12px 0;
  color: #303133;
}

.json-content {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #24292e;
  overflow-x: auto;
  max-height: 400px;
  overflow-y: auto;
}

.result-item {
  text-align: center;
  padding: 16px;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.result-value {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 8px;
}

.result-label {
  font-size: 14px;
  color: #909399;
}

.comparison-view h5 {
  margin: 0 0 16px 0;
  color: #303133;
  text-align: center;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
}
</style>
