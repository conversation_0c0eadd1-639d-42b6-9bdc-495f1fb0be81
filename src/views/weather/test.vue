<template>
  <div>
    <h2>天气预报题目测试页面</h2>

    <el-button @click="testQuApi">测试题目API</el-button>
    <el-button @click="testCreateQu">创建测试题目</el-button>
    <el-button @click="testRepoApi">测试题库API</el-button>
    <el-button @click="showWeatherTable">显示天气预报表格</el-button>

    <div style="margin-top: 20px;">
      <h3>API响应结果：</h3>
      <pre>{{ apiResult }}</pre>
    </div>

    <div style="margin-top: 20px;">
      <h3>题目列表：</h3>
      <el-table :data="quList" border>
        <el-table-column prop="id" label="ID" width="200" />
        <el-table-column prop="quType" label="题目类型" width="100" />
        <el-table-column prop="content" label="内容" />
        <el-table-column prop="weatherConfigId" label="天气配置ID" width="150" />
      </el-table>
    </div>

    <!-- 天气预报表格演示 -->
    <div v-if="showTable" style="margin-top: 20px;">
      <h3>天气预报表格演示</h3>
      <weather-forecast-table
        :question="weatherQuestion"
        :initial-answers="initialAnswers"
        @answer-change="onAnswerChange"
        @submit="onSubmitAnswers"
      />
    </div>
  </div>
</template>

<script>
import { post } from '@/utils/request'
import WeatherForecastTable from '@/components/WeatherForecastTable'

export default {
  name: 'WeatherTest',
  components: {
    WeatherForecastTable
  },
  data() {
    return {
      apiResult: '',
      quList: [],
      showTable: false,
      weatherQuestion: {
        id: 'weather_case_001',
        title: '2035年9月9日08时历史个例天气预报',
        content: '根据提供的气象观测资料，对指定站点进行24小时天气预报。请仔细分析气象数据，预报各站点的风力、风向、气温、降水和灾害性天气情况。',
        forecastDate: '2035年9月9日',
        forecastTime: '08时',
        dataFileName: '2035090908.dat',
        stations: '["站点1", "站点2", "站点3", "站点4", "站点5", "站点6"]',
        totalScore: 100.00
      },
      initialAnswers: {}
    }
  },
  methods: {
    async testQuApi() {
      try {
        const params = {
          current: 1,
          size: 20,
          params: {
            quType: 6
          }
        }

        console.log('发送请求参数:', params)

        const response = await post('/exam/api/qu/qu/paging', params)

        console.log('API响应:', response)
        this.apiResult = JSON.stringify(response, null, 2)

        if (response.code === 0 && response.data && response.data.records) {
          this.quList = response.data.records
        }
      } catch (error) {
        console.error('API调用失败:', error)
        this.apiResult = '错误: ' + error.message
      }
    },

    async testCreateQu() {
      try {
        // 首先创建一个题库
        const repoData = {
          title: '天气预报测试题库',
          remark: '用于测试天气预报功能的题库'
        }

        const repoResponse = await post('/exam/api/repo/save', repoData)
        console.log('题库创建响应:', repoResponse)

        let repoId = 'default_repo'
        if (repoResponse.code === 0 && repoResponse.data && repoResponse.data.id) {
          repoId = repoResponse.data.id
        }

        // 创建天气预报题目
        const quData = {
          quType: 6,
          level: 2,
          content: '测试天气预报表格题：请根据给定的气象数据填写24小时天气预报表格',
          remark: '这是一个测试题目',
          analysis: '根据温度、湿度、气压等要素进行分析',
          weatherConfigId: 'test_config_001',
          answerList: [
            {
              content: '天气预报表格答案',
              isRight: true,
              analysis: '这是正确的天气预报填写方式'
            }
          ],
          repoIds: [repoId]
        }

        console.log('创建题目参数:', quData)

        const response = await post('/exam/api/qu/qu/save', quData)

        console.log('创建题目响应:', response)
        this.apiResult = '创建结果: ' + JSON.stringify(response, null, 2)

        if (response.code === 0) {
          this.$message.success('题目创建成功！')
          // 重新查询列表
          this.testQuApi()
        } else {
          this.$message.error('题目创建失败: ' + response.message)
        }
      } catch (error) {
        console.error('创建题目失败:', error)
        this.apiResult = '创建错误: ' + error.message
        this.$message.error('创建题目失败: ' + error.message)
      }
    },

    async testRepoApi() {
      try {
        const params = {
          current: 1,
          size: 20,
          params: {}
        }

        console.log('发送题库请求参数:', params)

        const response = await post('/exam/api/repo/paging', params)

        console.log('题库API响应:', response)
        this.apiResult = '题库列表: ' + JSON.stringify(response, null, 2)

        if (response.code === 0 && response.data && response.data.records) {
          console.log('找到题库:', response.data.records.length, '个')
        }
      } catch (error) {
        console.error('题库API调用失败:', error)
        this.apiResult = '题库API错误: ' + error.message
      }
    },

    showWeatherTable() {
      this.showTable = !this.showTable
    },

    onAnswerChange(data) {
      console.log('答案变更:', data)
    },

    onSubmitAnswers(answers) {
      console.log('提交答案:', answers)
      this.$message.success('答案提交成功！')
      this.apiResult = '提交的答案: ' + JSON.stringify(answers, null, 2)
    }
  }
}
</script>

<style scoped>
pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}
</style>
