<template>
  <div class="weather-table-test">
    <el-card class="test-card">
      <div slot="header" class="card-header">
        <span>天气预报表格组件测试</span>
        <el-button type="primary" size="small" @click="loadTestData">加载测试数据</el-button>
      </div>

      <div class="test-content">
        <h3>题目内容</h3>
        <div class="question-content">
          <p>根据以下气象资料，请预报2035年9月9日08时未来24小时内指定站点的灾害性天气和气象要素：</p>

          <div class="background-info">
            <h4>【背景资料】</h4>
            <p>时间：2035年9月9日08时</p>
            <p>地点：华东地区某观测站</p>

            <h4>【当前天气实况】</h4>
            <ul>
              <li>气温：26℃</li>
              <li>风向：东南风</li>
              <li>风力：3-4级</li>
              <li>天气：多云</li>
              <li>气压：1012hPa</li>
              <li>相对湿度：75%</li>
            </ul>

            <h4>【预报要点】</h4>
            <ol>
              <li>受台风外围影响，风力将逐渐加大</li>
              <li>午后有雷阵雨天气过程</li>
              <li>气温变化不大</li>
              <li>需要关注强对流天气的发生可能</li>
            </ol>
          </div>
        </div>

        <h3>答题表格</h3>
        <weather-table-editor
          :table-config="tableConfig"
          :initial-data="initialData"
          @cell-change="onCellChange"
          @save="onSave"
        />

        <div class="test-actions">
          <el-button type="success" @click="validateAnswer">验证答案</el-button>
          <el-button type="warning" @click="clearData">清空数据</el-button>
          <el-button type="info" @click="showData">查看数据</el-button>
        </div>

        <div v-if="currentData" class="data-display">
          <h3>当前数据</h3>
          <pre>{{ JSON.stringify(currentData, null, 2) }}</pre>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import WeatherTableEditor from '@/components/weather/WeatherTableEditor'

export default {
  name: 'WeatherTableTest',
  components: {
    WeatherTableEditor
  },
  data() {
    return {
      tableConfig: {
        title: '未来24小时内指定站点的灾害性天气和气象要素预报',
        description: '请根据给定的站点信息，预报未来24小时内的最大风力、风向、气温、降水和灾害性天气类型',
        rows: 6,
        timeInterval: 4
      },
      initialData: {},
      currentData: null
    }
  },
  methods: {
    loadTestData() {
      // 加载测试数据
      this.initialData = {
        cellData: {
          row_0: {
            forecastTime: '2035年09月09日08时',
            stationInfo: '上海58367',
            maxWindForce: '3-4',
            windDirection: '东南',
            minTemp: 24,
            maxTemp: 28,
            precipitation: '无',
            disasterWeather: ['无']
          },
          row_1: {
            forecastTime: '2035年09月09日12时',
            stationInfo: '上海58367',
            maxWindForce: '5-6',
            windDirection: '东',
            minTemp: 26,
            maxTemp: 30,
            precipitation: '小雨',
            disasterWeather: ['雷暴']
          },
          row_2: {
            forecastTime: '2035年09月09日16时',
            stationInfo: '上海58367',
            maxWindForce: '7-8',
            windDirection: '东北',
            minTemp: 25,
            maxTemp: 28,
            precipitation: '中雨',
            disasterWeather: ['雷暴', '大风']
          }
        },
        answerStatus: false
      }

      this.$message.success('测试数据加载成功')
    },

    onCellChange(changeData) {
      console.log('单元格数据变化:', changeData)
    },

    onSave(data) {
      this.currentData = data
      console.log('保存数据:', data)
      this.$message.success('数据保存成功')
    },

    validateAnswer() {
      if (!this.currentData) {
        this.$message.warning('请先填写答案数据')
        return
      }

      // 简单的验证逻辑
      const cellData = this.currentData.cellData
      let validCount = 0
      let totalCount = 0

      Object.keys(cellData).forEach(rowKey => {
        const row = cellData[rowKey]
        totalCount++

        if (row.maxWindForce && row.windDirection && row.precipitation) {
          validCount++
        }
      })

      if (validCount === totalCount) {
        this.$message.success(`答案验证通过！完成度：${validCount}/${totalCount}`)
      } else {
        this.$message.warning(`答案不完整，完成度：${validCount}/${totalCount}`)
      }
    },

    clearData() {
      this.initialData = {}
      this.currentData = null
      this.$message.info('数据已清空')
    },

    showData() {
      if (this.currentData) {
        this.$alert(JSON.stringify(this.currentData, null, 2), '当前数据', {
          confirmButtonText: '确定'
        })
      } else {
        this.$message.warning('暂无数据')
      }
    }
  }
}
</script>

<style scoped lang="scss">
.weather-table-test {
  padding: 20px;

  .test-card {
    max-width: 1200px;
    margin: 0 auto;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .test-content {
    h3 {
      margin: 20px 0 15px 0;
      color: #303133;
      border-bottom: 2px solid #409eff;
      padding-bottom: 8px;
    }

    .question-content {
      background: #f5f7fa;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;

      .background-info {
        margin-top: 15px;

        h4 {
          color: #606266;
          margin: 15px 0 8px 0;
        }

        p, li {
          color: #606266;
          line-height: 1.6;
        }

        ul, ol {
          margin: 8px 0;
          padding-left: 20px;
        }
      }
    }

    .test-actions {
      margin: 20px 0;
      text-align: center;

      .el-button {
        margin: 0 10px;
      }
    }

    .data-display {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 15px;
      margin-top: 20px;

      pre {
        background: #fff;
        padding: 15px;
        border-radius: 4px;
        border: 1px solid #ddd;
        overflow-x: auto;
        font-size: 12px;
        line-height: 1.4;
      }
    }
  }
}
</style>
