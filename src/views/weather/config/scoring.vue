<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>评分配置管理</h2>
      <p>管理历史个例考试的评分规则和权重配置</p>
    </div>

    <!-- 操作按钮 -->
    <div class="toolbar">
      <el-button type="primary" @click="handleAdd">
        <i class="el-icon-plus" />
        新增配置
      </el-button>
      <el-button @click="refreshList">
        <i class="el-icon-refresh" />
        刷新
      </el-button>
    </div>

    <!-- 配置列表 -->
    <el-card class="table-card">
      <el-table v-loading="loading" :data="configList" stripe border>
        <el-table-column prop="configName" label="配置名称" min-width="200" />

        <el-table-column label="总分" width="100" align="center">
          <template v-slot="scope">
            {{ scope.row.totalScore }}
          </template>
        </el-table-column>

        <el-table-column label="要素权重" min-width="300">
          <template v-slot="scope">
            <div class="weight-display">
              <el-tag size="mini" type="primary">风力: {{ getWeight(scope.row, 'windForce') }}</el-tag>
              <el-tag size="mini" type="success">风向: {{ getWeight(scope.row, 'windDirection') }}</el-tag>
              <el-tag size="mini" type="warning">温度: {{ getWeight(scope.row, 'temperature') }}</el-tag>
              <el-tag size="mini" type="info">降水: {{ getWeight(scope.row, 'precipitation') }}</el-tag>
              <el-tag size="mini" type="danger">灾害: {{ getWeight(scope.row, 'disasterWeather') }}</el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100" align="center">
          <template v-slot="scope">
            <el-tag :type="scope.row.isActive ? 'success' : 'info'" size="small">
              {{ scope.row.isActive ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="160" align="center">
          <template v-slot="scope">
            {{ scope.row.createTime }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template v-slot="scope">
            <el-button size="mini" type="primary" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button
              v-if="!scope.row.isActive"
              size="mini"
              type="success"
              @click="handleActivate(scope.row)"
            >
              启用
            </el-button>
            <el-button
              :disabled="scope.row.isActive"
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 配置表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      width="800px"
    >
      <el-form
        ref="configForm"
        :model="configForm"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="配置名称" prop="configName">
          <el-input
            v-model="configForm.configName"
            placeholder="请输入配置名称"
          />
        </el-form-item>

        <el-form-item label="总分" prop="totalScore">
          <el-input-number
            v-model="configForm.totalScore"
            :min="1"
            :max="1000"
            placeholder="总分"
          />
        </el-form-item>

        <el-form-item label="要素权重" prop="elementWeights">
          <div class="weight-config">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="weight-item">
                  <label>风力等级权重：</label>
                  <el-input-number
                    v-model="configForm.elementWeights.windForce"
                    :min="0"
                    :max="100"
                    :step="0.1"
                  />
                </div>
              </el-col>
              <el-col :span="12">
                <div class="weight-item">
                  <label>风向权重：</label>
                  <el-input-number
                    v-model="configForm.elementWeights.windDirection"
                    :min="0"
                    :max="100"
                    :step="0.1"
                  />
                </div>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <div class="weight-item">
                  <label>温度权重：</label>
                  <el-input-number
                    v-model="configForm.elementWeights.temperature"
                    :min="0"
                    :max="100"
                    :step="0.1"
                  />
                </div>
              </el-col>
              <el-col :span="12">
                <div class="weight-item">
                  <label>降水权重：</label>
                  <el-input-number
                    v-model="configForm.elementWeights.precipitation"
                    :min="0"
                    :max="100"
                    :step="0.1"
                  />
                </div>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <div class="weight-item">
                  <label>灾害天气权重：</label>
                  <el-input-number
                    v-model="configForm.elementWeights.disasterWeather"
                    :min="0"
                    :max="100"
                    :step="0.1"
                  />
                </div>
              </el-col>
              <el-col :span="12">
                <div class="weight-summary">
                  <span>权重总和：{{ getTotalWeight() }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-form-item>

        <el-form-item label="容差配置" prop="toleranceConfig">
          <div class="tolerance-config">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="tolerance-item">
                  <label>温度容差（℃）：</label>
                  <el-input-number
                    v-model="configForm.toleranceConfig.temperature"
                    :min="0"
                    :max="10"
                    :step="0.1"
                  />
                </div>
              </el-col>
              <el-col :span="12">
                <div class="tolerance-item">
                  <label>降水容差（mm）：</label>
                  <el-input-number
                    v-model="configForm.toleranceConfig.precipitation"
                    :min="0"
                    :max="50"
                    :step="0.1"
                  />
                </div>
              </el-col>
            </el-row>
          </div>
        </el-form-item>

        <el-form-item label="配置描述">
          <el-input
            v-model="configForm.description"
            :rows="3"
            type="textarea"
            placeholder="请输入配置描述"
          />
        </el-form-item>

        <el-form-item label="是否启用">
          <el-switch
            v-model="configForm.isActive"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button :loading="saving" type="primary" @click="handleSave">
          保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { post } from '@/utils/request'

export default {
  name: 'WeatherScoringConfig',
  data() {
    return {
      loading: false,
      saving: false,
      configList: [],
      dialogVisible: false,
      dialogTitle: '',
      isEdit: false,

      // 表单数据
      configForm: {
        id: '',
        configName: '',
        totalScore: 100,
        elementWeights: {
          windForce: 1.0,
          windDirection: 1.0,
          temperature: 4.0,
          precipitation: 2.0,
          disasterWeather: 2.0
        },
        toleranceConfig: {
          temperature: 2.0,
          precipitation: 5.0
        },
        description: '',
        isActive: false
      },

      // 表单验证规则
      formRules: {
        configName: [
          { required: true, message: '请输入配置名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        totalScore: [
          { required: true, message: '请输入总分', trigger: 'blur' }
        ]
      }
    }
  },

  created() {
    this.loadConfigList()
  },

  methods: {
    // 加载配置列表
    async loadConfigList() {
      this.loading = true
      try {
        const response = await post('/exam/api/weather/scoring/config/list')
        if (response.code === 0) {
          this.configList = response.data || []
        } else {
          this.$message.error('获取配置列表失败')
        }
      } catch (error) {
        console.error('获取配置列表错误:', error)
        this.$message.error('获取配置列表失败')
      } finally {
        this.loading = false
      }
    },

    // 刷新列表
    refreshList() {
      this.loadConfigList()
    },

    // 获取权重值
    getWeight(config, element) {
      try {
        const weights = typeof config.elementWeights === 'string'
          ? JSON.parse(config.elementWeights)
          : config.elementWeights
        return weights[element] || 0
      } catch (error) {
        return 0
      }
    },

    // 计算权重总和
    getTotalWeight() {
      const weights = this.configForm.elementWeights
      return (weights.windForce + weights.windDirection + weights.temperature +
              weights.precipitation + weights.disasterWeather).toFixed(1)
    },

    // 新增配置
    handleAdd() {
      this.isEdit = false
      this.dialogTitle = '新增评分配置'
      this.configForm = {
        id: '',
        configName: '',
        totalScore: 100,
        elementWeights: {
          windForce: 1.0,
          windDirection: 1.0,
          temperature: 4.0,
          precipitation: 2.0,
          disasterWeather: 2.0
        },
        toleranceConfig: {
          temperature: 2.0,
          precipitation: 5.0
        },
        description: '',
        isActive: false
      }
      this.dialogVisible = true
    },

    // 编辑配置
    handleEdit(config) {
      this.isEdit = true
      this.dialogTitle = '编辑评分配置'

      // 解析JSON字段
      let elementWeights = {}
      let toleranceConfig = {}

      try {
        elementWeights = typeof config.elementWeights === 'string'
          ? JSON.parse(config.elementWeights)
          : config.elementWeights || {}
      } catch (error) {
        elementWeights = {}
      }

      try {
        toleranceConfig = typeof config.toleranceConfig === 'string'
          ? JSON.parse(config.toleranceConfig)
          : config.toleranceConfig || {}
      } catch (error) {
        toleranceConfig = {}
      }

      this.configForm = {
        id: config.id,
        configName: config.configName,
        totalScore: config.totalScore,
        elementWeights: {
          windForce: elementWeights.windForce || 1.0,
          windDirection: elementWeights.windDirection || 1.0,
          temperature: elementWeights.temperature || 4.0,
          precipitation: elementWeights.precipitation || 2.0,
          disasterWeather: elementWeights.disasterWeather || 2.0
        },
        toleranceConfig: {
          temperature: toleranceConfig.temperature || 2.0,
          precipitation: toleranceConfig.precipitation || 5.0
        },
        description: config.description || '',
        isActive: config.isActive
      }

      this.dialogVisible = true
    },

    // 保存配置
    async handleSave() {
      this.$refs.configForm.validate(async(valid) => {
        if (!valid) return

        this.saving = true
        try {
          const response = await post('/exam/api/weather/scoring/config/save', this.configForm)
          if (response.code === 0) {
            this.$message.success('保存成功')
            this.dialogVisible = false
            this.loadConfigList()
          } else {
            this.$message.error(response.message || '保存失败')
          }
        } catch (error) {
          console.error('保存配置错误:', error)
          this.$message.error('保存失败')
        } finally {
          this.saving = false
        }
      })
    },

    // 启用配置
    async handleActivate(config) {
      this.$confirm('启用此配置将禁用其他所有配置，确定要继续吗？', '确认启用', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          const response = await post('/exam/api/weather/scoring/config/activate', {
            id: config.id
          })

          if (response.code === 0) {
            this.$message.success('启用成功')
            this.loadConfigList()
          } else {
            this.$message.error(response.message || '启用失败')
          }
        } catch (error) {
          console.error('启用配置错误:', error)
          this.$message.error('启用失败')
        }
      })
    },

    // 删除配置
    async handleDelete(config) {
      this.$confirm('确定要删除这个配置吗？删除后不可恢复。', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          const response = await post('/exam/api/weather/scoring/config/delete', {
            id: config.id
          })

          if (response.code === 0) {
            this.$message.success('删除成功')
            this.loadConfigList()
          } else {
            this.$message.error(response.message || '删除失败')
          }
        } catch (error) {
          console.error('删除配置错误:', error)
          this.$message.error('删除失败')
        }
      })
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.toolbar {
  margin-bottom: 20px;
  display: flex;
  gap: 12px;
}

.table-card {
  margin-bottom: 20px;
}

.weight-display {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.weight-config {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  background-color: #fafafa;
}

.weight-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.weight-item label {
  width: 120px;
  color: #606266;
  font-size: 14px;
}

.weight-summary {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  font-weight: 500;
  color: #1890ff;
}

.tolerance-config {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  background-color: #f0f9ff;
}

.tolerance-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.tolerance-item label {
  width: 120px;
  color: #606266;
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
}
</style>
