<template>
  <div class="weather-exam-result-container">
    <!-- 页面头部 -->
    <div class="result-header">
      <div class="header-content">
        <div class="header-icon">
          <i class="el-icon-trophy" />
        </div>
        <div class="header-text">
          <h1 class="page-title">考试结果</h1>
          <p class="page-subtitle">{{ examData.title }}</p>
        </div>
      </div>
      <div class="back-button">
        <el-button type="primary" plain @click="goBack">
          <i class="el-icon-back" />
          返回
        </el-button>
      </div>
    </div>

    <!-- 考试信息卡片 -->
    <div v-loading="loading" class="exam-info-section">
      <div class="info-card">
        <div class="score-display">
          <div class="score-circle">
            <div class="score-number">{{ userScore }}</div>
            <div class="score-label">分</div>
          </div>
          <div class="score-details">
            <div class="detail-item">
              <span class="label">总分：</span>
              <span class="value">{{ examData.totalScore || 100 }}分</span>
            </div>
            <div class="detail-item">
              <span class="label">用时：</span>
              <span class="value">{{ formatTime(userTime) }}</span>
            </div>
            <div class="detail-item">
              <span class="label">提交时间：</span>
              <span class="value">{{ formatDateTime(submitTime) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 题目详情 -->
    <div v-if="questionData" class="question-section">
      <div class="question-card">
        <div class="question-header">
          <h3>题目详情</h3>
          <el-tag type="success" size="small">已完成</el-tag>
        </div>

        <div class="question-content">
          <div class="question-title">{{ questionData.title }}</div>
          <div v-if="questionData.content" class="question-description">
            {{ questionData.content }}
          </div>

          <!-- 天气预报表格 -->
          <div v-if="forecastData" class="forecast-table-section">
            <h4>天气预报表格</h4>
            <WeatherForecastTable
              :forecast-data="forecastData"
              :stations="stations"
              :readonly="true"
              :show-correct-answers="true"
              :user-answers="userAnswers"
            />
          </div>

          <!-- 正确答案显示 -->
          <div v-if="questionData.answerList" class="correct-answers-section">
            <h4>正确答案</h4>
            <div class="answers-grid">
              <div
                v-for="answer in questionData.answerList"
                :key="answer.id"
                :class="['answer-item', { 'correct-answer': answer.isRight }]"
              >
                <div class="answer-content">{{ answer.content }}</div>
                <div v-if="answer.isRight" class="correct-badge">
                  <i class="el-icon-check" />
                  正确答案
                </div>
                <div v-if="answer.analysis" class="answer-analysis">
                  <strong>解析：</strong>{{ answer.analysis }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && !questionData" class="empty-state">
      <div class="empty-icon">
        <i class="el-icon-warning" />
      </div>
      <div class="empty-title">无法获取考试结果</div>
      <div class="empty-description">请确认您已参加并提交了该考试</div>
    </div>
  </div>
</template>

<script>
import WeatherForecastTable from '@/components/WeatherForecastTable'
import {
  getWeatherExamInfo,
  getWeatherCaseResultDetail,
  getWeatherExamAnswer
} from '@/api/weather/weather'

export default {
  name: 'WeatherExamResult',
  components: {
    WeatherForecastTable
  },
  data() {
    return {
      loading: false,
      examId: '',
      examData: {},
      questionData: null,
      userScore: 0,
      userTime: 0,
      submitTime: '',
      forecastData: null,
      stations: [],
      userAnswers: {}
    }
  },
  created() {
    this.examId = this.$route.params.id
    this.loadExamResult()
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },

    // 加载考试结果
    async loadExamResult() {
      this.loading = true
      try {
        // 1. 获取考试基本信息
        await this.loadExamInfo()

        // 2. 获取用户答案信息
        await this.loadUserAnswer()

        // 3. 获取题目详情（包含正确答案）
        await this.loadQuestionDetail()
      } catch (error) {
        console.error('加载考试结果失败:', error)
        this.$message.error('加载考试结果失败')
      } finally {
        this.loading = false
      }
    },

    // 加载考试信息
    async loadExamInfo() {
      const response = await getWeatherExamInfo({ id: this.examId })
      if (response.code === 0) {
        this.examData = response.data
      } else {
        throw new Error(response.msg || '获取考试信息失败')
      }
    },

    // 加载用户答案
    async loadUserAnswer() {
      const response = await getWeatherExamAnswer({ examId: this.examId })
      if (response.code === 0 && response.data) {
        const answerData = response.data
        this.userScore = answerData.totalScore || 0
        this.userTime = answerData.userTime || 0
        this.submitTime = answerData.submitTime || ''

        // 解析用户答案数据
        if (answerData.answerData) {
          try {
            this.userAnswers = JSON.parse(answerData.answerData)
          } catch (e) {
            console.warn('解析用户答案数据失败:', e)
          }
        }
      }
    },

    // 加载题目详情（需要验证参与状态）
    async loadQuestionDetail() {
      if (!this.examData.questionId) return

      const response = await getWeatherCaseResultDetail({
        examId: this.examId,
        questionId: this.examData.questionId
      })

      if (response.code === 0) {
        this.questionData = response.data

        // 解析预报数据
        if (this.questionData.scenarioData) {
          try {
            const scenarioData = JSON.parse(this.questionData.scenarioData)
            this.forecastData = scenarioData.forecastData || null
            this.stations = scenarioData.stations || []
          } catch (e) {
            console.warn('解析场景数据失败:', e)
          }
        }
      } else {
        throw new Error(response.msg || '获取题目详情失败')
      }
    },

    // 格式化时间（分钟）
    formatTime(minutes) {
      if (!minutes) return '0分钟'
      const hours = Math.floor(minutes / 60)
      const mins = minutes % 60
      if (hours > 0) {
        return `${hours}小时${mins}分钟`
      }
      return `${mins}分钟`
    },

    // 格式化日期时间
    formatDateTime(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.weather-exam-result-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 0;
}

/* 页面头部 */
.result-header {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  color: white;
  padding: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 48px;
  margin-right: 20px;
  opacity: 0.9;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

.back-button .el-button {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
}

.back-button .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

/* 考试信息区域 */
.exam-info-section {
  padding: 30px 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.info-card {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.score-display {
  display: flex;
  align-items: center;
  gap: 40px;
}

.score-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #67c23a, #85ce61);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 25px rgba(103, 194, 58, 0.3);
}

.score-number {
  font-size: 36px;
  font-weight: 700;
  line-height: 1;
}

.score-label {
  font-size: 14px;
  opacity: 0.9;
}

.score-details {
  flex: 1;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 16px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item .label {
  color: #909399;
  margin-right: 8px;
  min-width: 80px;
}

.detail-item .value {
  color: #303133;
  font-weight: 600;
}

/* 题目区域 */
.question-section {
  padding: 0 40px 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.question-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.question-header h3 {
  margin: 0;
  color: #303133;
  font-size: 20px;
}

.question-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
}

.question-description {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 24px;
}

.forecast-table-section,
.correct-answers-section {
  margin-top: 24px;
}

.forecast-table-section h4,
.correct-answers-section h4 {
  color: #303133;
  font-size: 16px;
  margin-bottom: 16px;
}

.answers-grid {
  display: grid;
  gap: 16px;
}

.answer-item {
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.answer-item.correct-answer {
  border-color: #67c23a;
  background: #f0f9ff;
}

.answer-content {
  font-size: 14px;
  color: #303133;
  margin-bottom: 8px;
}

.correct-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #67c23a;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 8px;
}

.answer-analysis {
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 40px;
  color: #909399;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #606266;
}

.empty-description {
  font-size: 14px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .result-header {
    padding: 20px;
    flex-direction: column;
    gap: 20px;
  }

  .exam-info-section,
  .question-section {
    padding-left: 20px;
    padding-right: 20px;
  }

  .score-display {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .info-card {
    padding: 24px;
  }
}
</style>
