<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>历史个例考试管理</h2>
      <p>管理历史个例天气预报考试，包括考试发布、时间设置和参与人员管理</p>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-container">
      <el-row :gutter="20">
        <el-col :span="18">
          <el-input
            v-model="listQuery.title"
            placeholder="请输入考试标题"
            style="width: 200px;"
            class="filter-item"
            clearable
          />
          <el-select v-model="listQuery.state" placeholder="考试状态" clearable class="filter-item">
            <el-option :value="0" label="未发布" />
            <el-option :value="1" label="已发布" />
            <el-option :value="2" label="已结束" />
          </el-select>
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            class="filter-item"
            @change="handleDateChange"
          />
          <el-button type="primary" icon="el-icon-search" @click="getList">查询</el-button>
          <el-button type="default" @click="resetQuery">重置</el-button>
        </el-col>
        <el-col :span="6" style="text-align: right;">
          <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增考试</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="examList"
      border
      style="width: 100%"
    >
      <el-table-column prop="title" label="考试标题" min-width="200" />

      <el-table-column label="关联题目" width="150" align="center">
        <template v-slot="scope">
          <span v-if="scope.row.questionTitle">{{ scope.row.questionTitle }}</span>
          <el-tag v-else type="warning" size="mini">未关联</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="考试时长(分钟)" width="120" align="center">
        <template v-slot="scope">
          {{ scope.row.duration || scope.row.totalTime || '-' }}
        </template>
      </el-table-column>

      <el-table-column label="总分" width="100" align="center">
        <template v-slot="scope">
          {{ scope.row.totalScore || 100 }}
        </template>
      </el-table-column>

      <el-table-column label="考试状态" width="100" align="center">
        <template v-slot="scope">
          <el-tag :type="getStatusType(scope.row.state)" size="mini">
            {{ getStatusText(scope.row.state) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="监控鼠标" width="100" align="center">
        <template v-slot="scope">
          <el-tag :type="scope.row.monitorMouse ? 'success' : 'info'" size="mini">
            {{ scope.row.monitorMouse ? '开启' : '关闭' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="显示结果" width="100" align="center">
        <template v-slot="scope">
          <el-tag :type="scope.row.showResult ? 'success' : 'info'" size="mini">
            {{ scope.row.showResult ? '显示' : '不显示' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="startTime" label="开始时间" width="150" align="center" />

      <el-table-column prop="endTime" label="结束时间" width="150" align="center" />

      <el-table-column label="参与人数" width="100" align="center">
        <template v-slot="scope">
          {{ scope.row.joinCount || 0 }}
        </template>
      </el-table-column>

      <el-table-column prop="createTime" label="创建时间" width="150" align="center" />

      <el-table-column label="操作" width="200" align="center" fixed="right">
        <template v-slot="scope">
          <el-button size="mini" type="text" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button v-if="scope.row.state === 0" size="mini" type="text" @click="handlePublish(scope.row)">发布</el-button>
          <el-button v-if="scope.row.state === 1" size="mini" type="text" @click="handleEnd(scope.row)">结束</el-button>
          <el-button size="mini" type="text" @click="handleResults(scope.row)">成绩</el-button>
          <el-button size="mini" type="text" style="color: #f56c6c;" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="examForm"
        :model="examForm"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="考试标题" prop="title">
          <el-input v-model="examForm.title" placeholder="请输入考试标题" />
        </el-form-item>

        <el-form-item label="关联题目" prop="questionId">
          <el-select v-model="examForm.questionId" placeholder="请选择题目" style="width: 100%;">
            <el-option
              v-for="question in questionList"
              :key="question.id"
              :label="question.title"
              :value="question.id"
            />
          </el-select>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="考试时长" prop="duration" label-width="80px">
              <div style="display: flex; align-items: center;">
                <el-input-number
                  v-model="examForm.duration"
                  :min="10"
                  :max="300"
                  style="width: 120px;"
                />
                <span style="margin-left: 8px;">分钟</span>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="总分" prop="totalScore" label-width="60px">
              <div style="display: flex; align-items: center;">
                <el-input-number
                  v-model="examForm.totalScore"
                  :min="1"
                  :max="1000"
                  :precision="1"
                  style="width: 130px;"
                />
                <span style="margin-left: 8px;">分</span>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="及格分数" prop="qualifyScore" label-width="80px">
              <div style="display: flex; align-items: center;">
                <el-input-number
                  v-model="examForm.qualifyScore"
                  :min="0"
                  :max="examForm.totalScore || 100"
                  :precision="1"
                  style="width: 130px;"
                />
                <span style="margin-left: 8px;">分</span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="examForm.startTime"
                type="datetime"
                placeholder="选择开始时间"
                style="width: 100%;"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="examForm.endTime"
                type="datetime"
                placeholder="选择结束时间"
                style="width: 100%;"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="考试说明">
          <el-input
            v-model="examForm.description"
            :rows="3"
            type="textarea"
            placeholder="请输入考试说明"
          />
        </el-form-item>

        <!-- 新增功能选项 -->
        <el-form-item label="是否监控鼠标">
          <el-checkbox v-model="examForm.monitorMouse">
            开启鼠标监控（考生离开页面超过60秒将自动交卷）
          </el-checkbox>
        </el-form-item>

        <el-form-item label="交卷后显示结果">
          <el-checkbox v-model="examForm.showResult">
            考生交卷后显示考试结果
          </el-checkbox>
        </el-form-item>

        <!-- 权限配置 -->
        <el-form-item label="开放类型" prop="openType">
          <el-radio-group v-model="examForm.openType">
            <el-radio v-if="isSaRole" :label="1">完全公开</el-radio>
            <el-radio :label="2">部门开放</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="examForm.openType === 1" label="">
          <el-alert
            :closable="false"
            title="完全公开：任何人都可以进行考试！"
            type="warning"
          />
        </el-form-item>

        <el-form-item v-if="examForm.openType === 2" label="选择部门" prop="departIds">
          <el-input
            v-model="filterText"
            placeholder="输入关键字进行过滤"
            style="margin-bottom: 10px;"
          />
          <el-tree
            ref="tree"
            :data="treeData"
            :default-checked-keys="examForm.departIds"
            :props="defaultProps"
            :filter-node-method="filterNode"
            empty-text="暂无数据"
            default-expand-all
            show-checkbox
            node-key="id"
            @check-change="handleCheckChange"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { post } from '@/utils/request'
import { getWeatherExamList, saveWeatherExam, getWeatherExamDetail, deleteWeatherExam, getWeatherCaseList, updateWeatherExamState } from '@/api/weather/weather'
import { fetchTree } from '@/api/sys/depart/depart'
import Pagination from '@/components/Pagination'
import { mapGetters } from 'vuex'

export default {
  name: 'WeatherExamManage',
  components: {
    Pagination
  },
  computed: {
    ...mapGetters([
      'roles'
    ]),
    isSaRole() {
      return this.roles.includes('sa')
    }
  },
  data() {
    return {
      loading: false,
      examList: [],
      questionList: [],
      treeData: [],
      filterText: '',
      total: 0,
      listQuery: {
        page: 1,
        limit: 10,
        title: '',
        state: ''
      },
      dateRange: [],
      dialogVisible: false,
      dialogTitle: '',
      isEdit: false,
      examForm: {
        id: '',
        title: '',
        questionId: '',
        duration: 60,
        totalScore: 100,
        qualifyScore: 60,
        startTime: '',
        endTime: '',
        description: '',
        monitorMouse: false, // 新增：是否监控鼠标
        showResult: false, // 新增：交卷后显示结果
        state: 0,
        openType: 2,
        departIds: []
      },
      defaultProps: {
        children: 'children',
        label: 'deptName'
      },
      formRules: {
        title: [
          { required: true, message: '请输入考试标题', trigger: 'blur' }
        ],
        questionId: [
          { required: true, message: '请选择关联题目', trigger: 'change' }
        ],
        duration: [
          { required: true, message: '请输入考试时长', trigger: 'blur' }
        ],
        totalScore: [
          { required: true, message: '请输入总分', trigger: 'blur' }
        ],
        qualifyScore: [
          { required: true, message: '请输入及格分数', trigger: 'blur' }
        ],
        startTime: [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择结束时间', trigger: 'change' }
        ],
        openType: [
          { required: true, message: '请选择开放类型', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree && this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getList()
    this.getQuestionList()
    this.getDepartTree()
  },
  methods: {
    getList() {
      this.loading = true
      const params = {
        current: this.listQuery.page,
        size: this.listQuery.limit,
        params: {
          title: this.listQuery.title,
          state: this.listQuery.state,
          startTime: this.listQuery.startTime,
          endTime: this.listQuery.endTime,
          examType: 'weather' // 只查询历史个例考试
        }
      }

      getWeatherExamList(params).then(response => {
        this.examList = response.data.records || []
        this.total = response.data.total || 0
        this.loading = false
      }).catch(error => {
        console.error('获取考试列表失败:', error)
        this.$message.error('获取考试列表失败')
        this.loading = false
      })
    },

    getQuestionList() {
      // 获取历史个例题目列表
      const params = {
        current: 1,
        size: 1000,
        params: {
          quType: 6 // 天气预报表格题
        }
      }

      getWeatherCaseList(params).then(response => {
        this.questionList = response.data.records || []
      }).catch(error => {
        console.error('获取题目列表失败:', error)
      })
    },

    getDepartTree() {
      // 获取部门树数据
      fetchTree({}).then(response => {
        this.treeData = response.data || []
      }).catch(error => {
        console.error('获取部门树失败:', error)
      })
    },

    resetQuery() {
      this.listQuery = {
        page: 1,
        limit: 10,
        title: '',
        state: ''
      }
      this.dateRange = []
      this.getList()
    },

    handleDateChange(dates) {
      if (dates && dates.length === 2) {
        this.listQuery.startTime = dates[0]
        this.listQuery.endTime = dates[1]
      } else {
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    },

    getStatusType(state) {
      const types = {
        0: 'info',
        1: 'success',
        2: 'warning'
      }
      return types[state] || 'info'
    },

    getStatusText(state) {
      const texts = {
        0: '未发布',
        1: '已发布',
        2: '已结束'
      }
      return texts[state] || '未知'
    },

    handleAdd() {
      this.dialogTitle = '新增历史个例考试'
      this.isEdit = false
      this.dialogVisible = true
      this.resetForm()
    },

    handleEdit(row) {
      this.dialogTitle = '编辑历史个例考试'
      this.isEdit = true

      // 获取考试详情，包括部门信息
      getWeatherExamDetail({ id: row.id }).then(response => {
        this.examForm = { ...response.data }

        // 字段映射：后端字段 -> 前端字段
        if (this.examForm.totalTime) {
          this.examForm.duration = this.examForm.totalTime
        }

        // 确保新增字段有默认值
        if (this.examForm.monitorMouse === undefined) {
          this.examForm.monitorMouse = false
        }
        if (this.examForm.showResult === undefined) {
          this.examForm.showResult = false
        }

        // 确保departIds是数组
        if (!Array.isArray(this.examForm.departIds)) {
          this.examForm.departIds = this.examForm.departIds ? [this.examForm.departIds] : []
        }

        this.dialogVisible = true
      }).catch(error => {
        console.error('获取考试详情失败:', error)
        this.$message.error('获取考试详情失败')
      })
    },

    handlePublish(row) {
      this.$confirm('确定要发布这个考试吗？发布后学生就可以参加考试了。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const data = {
          ids: [row.id],
          state: 1 // 1=已发布
        }
        updateWeatherExamState(data).then(() => {
          this.$message.success('考试发布成功')
          this.getList()
        }).catch(error => {
          console.error('发布失败:', error)
          this.$message.error('发布失败')
        })
      })
    },

    handleEnd(row) {
      this.$confirm('确定要结束这个考试吗？结束后学生将无法继续参加考试。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const data = {
          ids: [row.id],
          state: 2 // 2=已结束
        }
        updateWeatherExamState(data).then(() => {
          this.$message.success('考试已结束')
          this.getList()
        }).catch(error => {
          console.error('结束失败:', error)
          this.$message.error('结束失败')
        })
      })
    },

    handleResults(row) {
      // 跳转到成绩管理页面
      this.$router.push(`/weather/mark?examId=${row.id}`)
    },

    handleDelete(row) {
      this.$confirm('确定要删除这个考试吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // TODO: 调用删除API
        this.$message.success('删除成功')
        this.getList()
      })
    },

    resetForm() {
      this.examForm = {
        id: '',
        title: '',
        questionId: '',
        duration: 60,
        totalScore: 100,
        qualifyScore: 60,
        startTime: '',
        endTime: '',
        description: '',
        monitorMouse: false, // 新增字段重置
        showResult: false, // 新增字段重置
        state: 0,
        openType: 2,
        departIds: []
      }
      if (this.$refs.examForm) {
        this.$refs.examForm.resetFields()
      }
    },

    handleCheckChange() {
      // 部门选择变化处理
      this.examForm.departIds = []
      const nodes = this.$refs.tree.getCheckedNodes()
      nodes.forEach(item => {
        this.examForm.departIds.push(item.id)
      })
    },

    filterNode(value, data) {
      if (!value) return true
      return data.deptName.indexOf(value) !== -1
    },

    handleAdd() {
      this.dialogTitle = '新增历史个例考试'
      this.isEdit = false
      this.dialogVisible = true
      this.resetForm()
    },

    handleResults(row) {
      // 跳转到成绩管理页面
      this.$router.push(`/weather/mark?examId=${row.id}`)
    },

    handleDelete(row) {
      this.$confirm('确定要删除这个考试吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // TODO: 调用删除API
        this.$message.success('删除成功')
        this.getList()
      })
    },

    submitForm() {
      this.$refs.examForm.validate((valid) => {
        if (valid) {
          // 验证部门选择
          if (this.examForm.openType === 2 && (!this.examForm.departIds || this.examForm.departIds.length === 0)) {
            this.$message.error('请选择至少一个部门')
            return
          }

          const examData = {
            ...this.examForm,
            totalTime: this.examForm.duration, // 将duration映射为totalTime
            totalScore: this.examForm.totalScore // 确保总分字段正确传递
          }

          console.log('=== 前端提交数据 ===')
          console.log('examForm.duration:', this.examForm.duration)
          console.log('examForm.totalScore:', this.examForm.totalScore)
          console.log('examData.totalTime:', examData.totalTime)
          console.log('examData.totalScore:', examData.totalScore)
          console.log('examData.questionId:', examData.questionId)

          saveWeatherExam(examData).then(() => {
            this.$message.success(this.isEdit ? '修改成功' : '新增成功')
            this.dialogVisible = false
            this.getList()
          }).catch(error => {
            console.error('保存失败:', error)
            this.$message.error('保存失败')
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.page-header {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 18px;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-item {
  margin-right: 10px;
}
</style>
