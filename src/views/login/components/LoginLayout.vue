<template>

  <div class="login-container">

    <div class="login-box">

      <div class="logo-box">
        <div>
          <a class="logo-title" />
        </div>
      </div>

      <app-main />

    </div>

  </div>

</template>

<script>
import { mapGetters } from 'vuex'
import AppMain from '@/layout/components/AppMain'

export default {
  name: 'LoginLayout',
  components: { AppMain },
  computed: {
    ...mapGetters([
      'siteData'
    ])
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/login.scss";

</style>

