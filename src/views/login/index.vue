<template>
  <div class="weather-login-container">
    <!-- 动态天气背景 -->
    <div class="weather-background">
      <div class="cloud cloud1" />
      <div class="cloud cloud2" />
      <div class="cloud cloud3" />
      <div class="rain-drops">
        <div v-for="n in 50" :key="n" class="drop" :style="getDropStyle(n)" />
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="login-main-content">
      <!-- 系统标题区域 -->
      <div class="system-header">
        <div class="weather-icon">
          <img src="@/assets/logo.png">
        </div>
        <h1 class="system-title">预报能手</h1>
        <h1 class="system-title">在线考试系统</h1>
        <p class="system-subtitle">Meteorological Examination System</p>
        <div class="system-features">
          <span class="feature-indicator online-exam" @click="showFeatureInfo('exam')">
            <i class="el-icon-edit-outline" />
            <span>在线考试</span>
          </span>
          <span class="feature-indicator auto-grading" @click="showFeatureInfo('grading')">
            <i class="el-icon-cpu" />
            <span>智能评分</span>
          </span>
          <span class="feature-indicator smart-marking" @click="showFeatureInfo('marking')">
            <i class="el-icon-magic-stick" />
            <span>智能批卷</span>
          </span>
          <span class="feature-indicator question-bank" @click="showFeatureInfo('library')">
            <i class="el-icon-document" />
            <span>题库管理</span>
          </span>
          <span class="feature-indicator data-analysis" @click="showFeatureInfo('analysis')">
            <i class="el-icon-data-analysis" />
            <span>成绩分析</span>
          </span>
        </div>
      </div>

      <!-- 登录表单区域 -->
      <div class="login-form-container">
        <div class="form-header">
          <h2>用户登录</h2>
          <p>请输入您的账号信息</p>
        </div>

        <el-form ref="postForm" :model="postForm" :rules="loginRules" class="weather-form">
          <el-form-item prop="username">
            <div class="input-wrapper">
              <i class="el-icon-user input-icon" />
              <el-input
                v-model="postForm.username"
                placeholder="请输入用户名"
                class="weather-input"
                clearable
              />
            </div>
          </el-form-item>

          <el-form-item prop="password">
            <div class="input-wrapper">
              <i class="el-icon-lock input-icon" />
              <el-input
                v-model="postForm.password"
                type="password"
                placeholder="请输入密码"
                class="weather-input"
                show-password
                clearable
              />
            </div>
          </el-form-item>

          <el-form-item>
            <el-button
              :loading="loading"
              type="primary"
              class="login-button"
              @click.native.prevent="accountLogin"
            >
              <span v-if="!loading">立即登录</span>
              <span v-else>登录中...</span>
            </el-button>
          </el-form-item>
        </el-form>

      </div>
    </div>

    <!-- 底部信息 -->
    <div class="footer-info">
      <p>© 2024 气象考试系统 - 专业的气象人才培养平台</p>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'WeatherLogin',
  data() {
    return {
      loading: false,
      isDemo: this.$demo,
      postForm: {
        username: '',
        password: ''
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', message: '用户名不能为空' }],
        password: [{ required: true, trigger: 'blur', message: '密码不能为空' }]
      }
    }
  },
  computed: {
    ...mapGetters([
      'siteData'
    ])
  },
  mounted() {
    this.initWeatherAnimation()
  },
  methods: {
    // 初始化天气动画
    initWeatherAnimation() {
      // 添加页面加载动画
      setTimeout(() => {
        const container = document.querySelector('.weather-login-container')
        if (container) {
          container.classList.add('loaded')
        }
      }, 100)
    },

    // 生成雨滴样式
    getDropStyle(index) {
      const delay = Math.random() * 2
      const duration = 1 + Math.random() * 2
      const left = Math.random() * 100
      return {
        left: `${left}%`,
        animationDelay: `${delay}s`,
        animationDuration: `${duration}s`
      }
    },

    loginBack() {
      // 添加成功登录动画
      this.$message.success('登录成功！')

      // 跳转到后台
      this.$router.push({ path: '/admin/dashboard' })

      setTimeout(() => {
        this.loading = false
      }, 1800)
    },

    accountLogin() {
      this.$refs.postForm.validate(valid => {
        if (valid) {
          this.loading = true
          this.$store.dispatch('user/login', this.postForm)
            .then(() => {
              this.loginBack()
            })
            .catch(() => {
              this.loading = false
              this.$message.error('登录失败，请检查用户名和密码')
            })
        }
      })
    },

    // 显示功能特色信息
    showFeatureInfo(type) {
      const messages = {
        exam: '在线考试：支持多种题型的在线考试系统，实时监考，防作弊技术，考试过程全程记录',
        grading: '智能评分：基于AI技术的自动评分系统，支持客观题即时评分和主观题智能评判',
        marking: '智能批卷：高效的批卷系统，支持批量处理，智能识别答案要点，提高批卷效率和准确性',
        library: '题库管理：丰富的气象专业题库，支持多种题型，智能组卷，难度自适应调节',
        analysis: '成绩分析：全面的考试数据分析，包括成绩分布、知识点掌握情况、学习建议等'
      }

      this.$message({
        message: messages[type],
        type: 'info',
        duration: 4000,
        showClose: true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.weather-login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s ease;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
    z-index: 0;
  }

  &.loaded {
    opacity: 1;
    transform: translateY(0);
  }
}

// 天气背景动画
.weather-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

// 云朵动画
.cloud {
  position: absolute;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50px;
  opacity: 0.7;
  animation: float 20s infinite linear;

  &::before,
  &::after {
    content: '';
    position: absolute;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50px;
  }

  &.cloud1 {
    width: 80px;
    height: 40px;
    top: 20%;
    left: -100px;
    animation-duration: 25s;

    &::before {
      width: 50px;
      height: 50px;
      top: -25px;
      left: 10px;
    }

    &::after {
      width: 60px;
      height: 40px;
      top: -15px;
      right: 10px;
    }
  }

  &.cloud2 {
    width: 60px;
    height: 30px;
    top: 40%;
    left: -80px;
    animation-duration: 30s;
    animation-delay: -10s;

    &::before {
      width: 40px;
      height: 40px;
      top: -20px;
      left: 8px;
    }

    &::after {
      width: 50px;
      height: 30px;
      top: -10px;
      right: 8px;
    }
  }

  &.cloud3 {
    width: 100px;
    height: 50px;
    top: 60%;
    left: -120px;
    animation-duration: 35s;
    animation-delay: -20s;

    &::before {
      width: 60px;
      height: 60px;
      top: -30px;
      left: 15px;
    }

    &::after {
      width: 70px;
      height: 50px;
      top: -20px;
      right: 15px;
    }
  }
}

@keyframes float {
  0% {
    transform: translateX(-100px);
  }
  100% {
    transform: translateX(calc(100vw + 100px));
  }
}

// 雨滴动画
.rain-drops {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.drop {
  position: absolute;
  width: 2px;
  height: 20px;
  background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.6));
  animation: fall linear infinite;
  border-radius: 0 0 50% 50%;
}

@keyframes fall {
  0% {
    transform: translateY(-100vh);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}

// 主要内容区域
.login-main-content {
  display: flex;
  max-width: 1200px;
  width: 90%;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  overflow: hidden;
  z-index: 2;
  animation: slideUp 0.8s ease 0.3s both;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 系统标题区域
.system-header {
  flex: 1;
  padding: 60px 40px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
  }

  .weather-icon {
    margin-bottom: 20px;
    z-index: 1;
    position: relative;
  }

  .weather-main-icon {
    font-size: 80px;
    color: #fff;
    animation: pulse 2s infinite;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
  }

  .system-title {
    font-size: 42px;
    font-weight: 700;
    margin: 0 0 10px 0;
    z-index: 1;
    position: relative;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    letter-spacing: 2px;
  }

  .system-subtitle {
    font-size: 16px;
    opacity: 0.9;
    margin: 0 0 40px 0;
    z-index: 1;
    position: relative;
    font-weight: 300;
    letter-spacing: 1px;
  }

     .system-features {
     display: flex;
     flex-wrap: wrap;
     gap: 20px;
     justify-content: center;
     z-index: 1;
     position: relative;

     .feature-indicator {
       display: flex;
       flex-direction: column;
       align-items: center;
       gap: 8px;
       padding: 12px 8px;
       background: rgba(255, 255, 255, 0.9);
       border-radius: 12px;
       backdrop-filter: blur(10px);
       border: 1px solid rgba(255, 255, 255, 0.3);
       box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
       transition: all 0.3s ease;
       cursor: pointer;
       min-width: 80px;

       &:hover {
         background: rgba(255, 255, 255, 1);
         transform: translateY(-2px);
         box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
       }

       i {
         font-size: 22px;
         animation: pulse 2s infinite;
       }

       span {
         font-size: 11px;
         color: #2c3e50;
         font-weight: 600;
         text-align: center;
         line-height: 1.2;
       }

       &.online-exam i {
         color: #f59e0b;
       }

       &.auto-grading i {
         color: #059669;
       }

       &.smart-marking i {
         color: #dc2626;
       }

       &.question-bank i {
         color: #2563eb;
       }

       &.data-analysis i {
         color: #7c3aed;
       }
     }
   }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

// 登录表单区域
.login-form-container {
  flex: 1;
  padding: 60px 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;

  .form-header {
    text-align: center;
    margin-bottom: 40px;

    h2 {
      font-size: 28px;
      color: #2c3e50;
      margin: 0 0 10px 0;
      font-weight: 600;
    }

    p {
      color: #7f8c8d;
      margin: 0;
      font-size: 14px;
    }
  }

  .weather-form {
    .el-form-item {
      margin-bottom: 25px;

      .input-wrapper {
        position: relative;
        display: flex;
        align-items: center;

        .input-icon {
          position: absolute;
          left: 15px;
          color: #bdc3c7;
          font-size: 18px;
          z-index: 3;
        }

        .weather-input {
          ::v-deep .el-input__inner {
            height: 50px;
            padding-left: 50px;
            border: 2px solid #e8ecf0;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;

            &:focus {
              border-color: #4facfe;
              background: #fff;
              box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
            }

            &::placeholder {
              color: #bdc3c7;
            }
          }
        }
      }
    }

    .login-button {
      width: 100%;
      height: 50px;
      font-size: 16px;
      font-weight: 600;
      border-radius: 12px;
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      border: none;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }

}

// 底部信息
.footer-info {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;

  p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-size: 14px;
    text-align: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-main-content {
    flex-direction: column;
    width: 95%;
    margin: 20px;
  }

  .system-header {
    padding: 40px 20px;

    .system-title {
      font-size: 32px;
    }

         .system-features {
       gap: 15px;

       .feature-indicator {
         padding: 10px;
         background: rgba(255, 255, 255, 0.95);

         i {
           font-size: 20px;
         }

         span {
           font-size: 10px;
           color: #2c3e50;
         }
       }
     }
  }

     .login-form-container {
     padding: 40px 20px;
   }
}

// 版本信息样式
.version-info {
  margin-top: 20px;
  text-align: center;
  font-size: 12px;
  color: #bdc3c7;

  span {
    margin: 0 5px;
  }
}

// 增强的动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.weather-form .el-form-item {
  animation: fadeInUp 0.6s ease forwards;

  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
}

 // 鼠标悬停效果增强
 .system-features .feature-indicator {
   position: relative;
   overflow: hidden;

   &::before {
     content: '';
     position: absolute;
     top: 0;
     left: -100%;
     width: 100%;
     height: 100%;
     background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
     transition: left 0.5s;
   }

   &:hover::before {
     left: 100%;
   }
 }

// 输入框聚焦动画
.weather-input ::v-deep .el-input__inner {
  position: relative;

  &:focus {
    animation: inputGlow 0.3s ease;
  }
}

@keyframes inputGlow {
  0% {
    box-shadow: 0 0 0 0 rgba(79, 172, 254, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(79, 172, 254, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(79, 172, 254, 0);
  }
}
</style>
