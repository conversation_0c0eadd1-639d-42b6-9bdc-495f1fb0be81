<template>
  <div class="exam-container">
    <el-row :gutter="24">
      <!-- 顶部信息栏 -->
      <el-col :span="24">
        <div class="exam-header">
          <div class="timer-box">
            <i class="el-icon-time" />
            <span class="timer-text">距离考试结束还有：</span>
            <exam-timer v-model="paperData.leftSeconds" @timeout="doHandler()" />
          </div>
        </div>
      </el-col>

      <!-- 左侧答题卡 -->
      <el-col :span="5" :xs="24">
        <div class="answer-card">
          <div class="card-header">
            <i class="el-icon-s-order" />
            <span>答题卡</span>
          </div>

          <div class="card-legend">
            <el-tag type="info" effect="plain">未作答</el-tag>
            <el-tag type="success" effect="plain">已作答</el-tag>
          </div>

          <template v-for="(list, type) in questionTypes">
            <div v-if="paperData[list] && paperData[list].length > 0" :key="type" class="question-section">
              <div class="section-title">
                <i class="el-icon-collection-tag" />
                {{ type }}
              </div>
              <div class="number-list">
                <el-tag
                  v-for="item in paperData[list]"
                  :key="item.id"
                  :type="cardItemClass(item.answered, item.quId)"
                  effect="plain"
                  class="number-tag"
                  @click="handSave(item)"
                >
                  {{ item.sort + 1 }}
                </el-tag>
              </div>
            </div>
          </template>

          <!-- 添加交卷按钮区域 -->
          <div class="submit-area">
            <el-button
              :loading="loading"
              class="submit-btn"
              type="danger"
              icon="el-icon-position"
              @click="handHandExam()"
            >
              {{ handleText }}
            </el-button>
            <div v-if="countNotAnswered() > 0" class="submit-tip">
              还有 {{ countNotAnswered() }} 题未作答
            </div>
          </div>
        </div>
      </el-col>

      <!-- 右侧题目内容 -->
      <el-col :span="19" :xs="24">
        <div class="question-content">
          <div class="question-box">
            <div class="question-header">
              <span class="question-number">{{ quData.sort + 1 }}</span>
              <span class="question-type">
                {{ getQuestionType(quData.quType) }}
              </span>
            </div>

            <div class="question-body">
              <div v-show="quData.quType !== 4" class="question-text">{{ quData.content }}</div>

              <div v-if="quData.image" class="question-image">
                <el-image :src="quData.image" fit="contain" />
              </div>

              <!-- 选择题选项 -->
              <div v-if="quData.quType === 1 || quData.quType === 3" class="options-list">
                <el-radio-group v-model="radioValue">
                  <el-radio
                    v-for="item in quData.answerList"
                    :key="item.id"
                    :label="item.id"
                    class="option-item"
                  >
                    {{ item.abc }}.{{ item.content }}
                    <div v-if="item.image" class="option-image">
                      <el-image :src="item.image" fit="contain" />
                    </div>
                  </el-radio>
                </el-radio-group>
              </div>

              <!-- 多选题选项 -->
              <div v-if="quData.quType === 2" class="options-list">
                <el-checkbox-group v-model="multiValue">
                  <el-checkbox
                    v-for="item in quData.answerList"
                    :key="item.id"
                    :label="item.id"
                    class="option-item"
                  >
                    {{ item.abc }}.{{ item.content }}
                    <div v-if="item.image" class="option-image">
                      <el-image :src="item.image" fit="contain" />
                    </div>
                  </el-checkbox>
                </el-checkbox-group>
              </div>

              <!-- 填空题 -->
              <div v-if="quData.quType === 4" class="blank-answer">
                <div class="question-text">
                  <span v-for="(part, index) in splitContent" :key="index">
                    {{ part }}
                    <template v-if="index < splitContent.length - 1">
                      <el-input
                        v-model="blankAnswers[index]"
                        class="blank-input"
                        placeholder="请输入答案"
                      />
                    </template>
                  </span>
                </div>
              </div>

              <!-- 简答题 -->
              <div v-if="quData.quType === 5" class="essay-answer">
                <el-input
                  v-model="shortAnswer"
                  :rows="6"
                  type="textarea"
                  placeholder="请输入答案"
                  maxlength="2000"
                  show-word-limit
                />
              </div>

              <!-- 天气预报表格题 -->
              <div v-if="quData.quType === 6" class="weather-table-answer">
                <weather-table-editor
                  :table-config="quData.weatherConfig || {}"
                  :initial-data="weatherTableData"
                  @cell-change="onWeatherCellChange"
                  @save="onWeatherTableSave"
                />
              </div>
            </div>

            <div class="question-footer">
              <el-button
                v-if="showPrevious"
                type="primary"
                plain
                icon="el-icon-arrow-left"
                @click="handPrevious"
              >
                上一题
              </el-button>

              <el-button
                v-if="showNext"
                type="primary"
                icon="el-icon-arrow-right"
                @click="handNext"
              >
                下一题
              </el-button>

              <el-button
                v-if="!showNext"
                type="primary"
                icon="el-icon-arrow-right"
                @click="handNext"
              >
                保存
              </el-button>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { paperDetail, quDetail, handExam, fillAnswer } from '@/api/paper/exam'
import { Loading } from 'element-ui'
import ExamTimer from '@/views/paper/exam/components/ExamTimer'
import WeatherTableEditor from '@/components/weather/WeatherTableEditor'

export default {
  name: 'ExamProcess',
  components: { ExamTimer, WeatherTableEditor },
  data() {
    return {
      questionTypes: {
        '单选题': 'radioList',
        '多选题': 'multiList',
        '判断题': 'judgeList',
        '填空题': 'blankList',
        '简答题': 'shortList',
        '天气预报表格题': 'weatherTableList'
      },
      // 鼠标监控相关
      mouseLeaveTime: null,
      mouseLeaveTimer: null,
      mouseLeaveSeconds: 60,
      mouseLeaveWarningVisible: false,
      // 全屏/不全屏
      isFullscreen: false,
      showPrevious: false,
      showNext: true,
      loading: false,
      handleText: '交卷',
      pageLoading: false,
      // 试卷ID
      paperId: '',
      // 当前答题卡
      cardItem: {},
      allItem: [],
      // 当前题目内容
      quData: {
        answerList: []
      },
      // 试卷信息
      paperData: {
        leftSeconds: 99999,
        radioList: [],
        multiList: [],
        judgeList: [],
        blankList: [],
        shortList: [],
        weatherTableList: []
      },
      // 单选选定值
      radioValue: '',
      // 多选选定值
      multiValue: [],
      // 已答ID
      answeredIds: [],
      blankAnswers: [], // 存储填空题答案
      splitContent: [], // 存储分割后的内容
      shortAnswer: '', // 简答题答案
      weatherTableData: {} // 天气预报表格数据
    }
  },
  created() {
    const id = this.$route.params.id
    if (typeof id !== 'undefined') {
      this.paperId = id
      this.fetchData(id)
    }
  },

  mounted() {
    // 移除这里的监听器设置，改为在fetchData中设置
  },

  beforeDestroy() {
    // 移除鼠标监听
    document.removeEventListener('mouseleave', this.handleMouseLeave)
    document.removeEventListener('mouseenter', this.handleMouseEnter)
    // 清除定时器
    if (this.mouseLeaveTimer) {
      clearInterval(this.mouseLeaveTimer)
    }
  },

  methods: {
    // 初始化鼠标监控
    initMouseMonitor() {
      if (this.paperData.monitorMouse) {
        // 添加鼠标监听
        document.addEventListener('mouseleave', this.handleMouseLeave)
        document.addEventListener('mouseenter', this.handleMouseEnter)

        // 显示监控开启提示
        this.$notify({
          title: '提示',
          message: '本场考试已开启鼠标监控，请勿离开考试页面超过60秒',
          type: 'warning',
          duration: 5000
        })
      }
    },

    // 处理鼠标离开页面
    handleMouseLeave() {
      if (!this.paperData.monitorMouse) return

      this.mouseLeaveTime = new Date()
      this.mouseLeaveWarningVisible = true

      // 显示初始警告
      const warningNotify = this.$notify({
        title: '警告',
        message: `请立即返回考试页面！${this.mouseLeaveSeconds}秒后将自动交卷`,
        type: 'error',
        duration: 0
      })

      this.mouseLeaveTimer = setInterval(() => {
        const now = new Date()
        const timeElapsed = Math.floor((now - this.mouseLeaveTime) / 1000)
        const timeLeft = this.mouseLeaveSeconds - timeElapsed

        // 更新警告消息
        if (timeLeft > 0) {
          warningNotify.message = `请立即返回考试页面！${timeLeft}秒后将自动交卷`
        }

        if (timeElapsed >= this.mouseLeaveSeconds) {
          clearInterval(this.mouseLeaveTimer)
          this.$notify.closeAll()
          this.$notify({
            title: '警告',
            message: '由于长时间离开考试页面，系统将自动交卷',
            type: 'error',
            duration: 2000
          })
          this.doHandler() // 自动交卷
        }
      }, 1000)
    },

    // 处理鼠标返回页面
    handleMouseEnter() {
      if (!this.paperData.monitorMouse) return

      if (this.mouseLeaveTimer) {
        clearInterval(this.mouseLeaveTimer)
        this.mouseLeaveTimer = null
      }
      if (this.mouseLeaveWarningVisible) {
        this.$notify.closeAll()
        this.mouseLeaveWarningVisible = false
      }
    },

    getQuestionType(type) {
      const types = {
        1: '【单选题】',
        2: '【多选题】',
        3: '【判断题】',
        4: '【填空题】',
        5: '【简答题】'
      }
      return types[type] || ''
    },

    // 答题卡样式
    cardItemClass(answered, quId) {
      if (quId === this.cardItem.quId) {
        return 'warning'
      }

      if (answered) {
        return 'success'
      }

      if (!answered) {
        return 'info'
      }
    },

    /**
     * 统计有多少题没答的
     * @returns {number}
     */
    countNotAnswered() {
      let notAnswered = 0

      // 添加空值检查
      if (!this.paperData) return notAnswered

      // 检查并处理单选题
      if (this.paperData.radioList && Array.isArray(this.paperData.radioList)) {
        this.paperData.radioList.forEach(function(item) {
          if (!item.answered) {
            notAnswered += 1
          }
        })
      }

      // 检查并处理多选题
      if (this.paperData.multiList && Array.isArray(this.paperData.multiList)) {
        this.paperData.multiList.forEach(function(item) {
          if (!item.answered) {
            notAnswered += 1
          }
        })
      }

      // 检查并处理判断题
      if (this.paperData.judgeList && Array.isArray(this.paperData.judgeList)) {
        this.paperData.judgeList.forEach(function(item) {
          if (!item.answered) {
            notAnswered += 1
          }
        })
      }

      // 检查并处理填空题
      if (this.paperData.blankList && Array.isArray(this.paperData.blankList)) {
        this.paperData.blankList.forEach(function(item) {
          if (!item.answered) {
            notAnswered += 1
          }
        })
      }

      // 检查并处理简答题
      if (this.paperData.shortList && Array.isArray(this.paperData.shortList)) {
        this.paperData.shortList.forEach(function(item) {
          if (!item.answered) {
            notAnswered += 1
          }
        })
      }

      // 检查并处理天气预报表格题
      if (this.paperData.weatherTableList && Array.isArray(this.paperData.weatherTableList)) {
        this.paperData.weatherTableList.forEach(function(item) {
          if (!item.answered) {
            notAnswered += 1
          }
        })
      }

      return notAnswered
    },

    /**
     * 下一题
     */
    handNext() {
      const index = this.cardItem.sort + 1
      this.handSave(this.allItem[index])
    },

    /**
     * 上一题
     */
    handPrevious() {
      const index = this.cardItem.sort - 1
      this.handSave(this.allItem[index])
    },

    doHandler() {
      this.handleText = '正在交卷，请等待...'
      this.loading = true

      const params = { id: this.paperId }
      handExam(params).then(() => {
        this.$message({
          message: '试卷提交成功！',
          type: 'success'
        })

        // 根据showResult属性决定跳转页面
        if (this.paperData.showResult) {
          // 显示试卷详情
          this.$router.push({ name: 'ShowExam', params: { id: this.paperId }})
        } else {
          // 返回考试列表
          this.$message({
            message: '根据考试设置，本次考试不显示详细结果',
            type: 'info',
            duration: 3000
          })
          this.$router.push({ name: 'ListMyExam' })
        }
      })
    },

    // 交卷操作
    handHandExam() {
      const that = this

      // 交卷保存答案
      this.handSave(this.cardItem, function() {
        const notAnswered = that.countNotAnswered()

        let msg = '确认要交卷吗？'

        if (notAnswered > 0) {
          msg = '您还有' + notAnswered + '题未作答，确认要交卷吗?'
        }

        that.$confirm(msg, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          that.doHandler()
        }).catch(() => {
          that.$message({
            type: 'info',
            message: '交卷已取消，您可以继续作答！'
          })
        })
      })
    },

    // 获取题目类型名称
    getQuestionType(quType) {
      const typeMap = {
        1: '单选题',
        2: '多选题',
        3: '判断题',
        4: '填空题',
        5: '简答题',
        6: '天气预报表格题'
      }
      return typeMap[quType] || '未知题型'
    },

    // 天气表格单元格变化处理
    onWeatherCellChange(changeData) {
      // 实时保存单元格变化
      console.log('Weather cell changed:', changeData)
    },

    // 天气表格保存处理
    onWeatherTableSave(data) {
      this.weatherTableData = data
      console.log('Weather table saved:', data)
    },

    // 保存答案
    handSave(item, callback) {
      if (item.id === this.allItem[0].id) {
        this.showPrevious = false
      } else {
        this.showPrevious = true
      }

      // 最后一个索引
      const last = this.allItem.length - 1

      if (item.id === this.allItem[last].id) {
        this.showNext = false
      } else {
        this.showNext = true
      }

      let answers = []
      let answer = ''

      // 处理填空题和简答题答案
      if (this.quData.quType === 4) {
        // 检查是否有填写答案
        const hasAnswer = this.blankAnswers.some(ans => ans && ans.trim() !== '')
        if (hasAnswer) {
          answer = this.blankAnswers.join('$') // 填空题答案
        }
      } else if (this.quData.quType === 5) {
        answer = this.shortAnswer // 简答题答案
      } else if (this.quData.quType === 6) {
        // 天气预报表格题答案
        if (this.weatherTableData && this.weatherTableData.cellData) {
          answer = JSON.stringify(this.weatherTableData)
        }
      } else {
        answers = [...this.multiValue]
        if (this.radioValue !== '') {
          answers.push(this.radioValue)
        }
      }

      const params = {
        paperId: this.paperId,
        quId: this.cardItem.quId,
        answers: answers,
        answer: answer,
        quType: this.quData.quType
      }

      fillAnswer(params).then(() => {
        // 必须选择一个值或填写答案
        if (answers.length > 0 || answer !== '') {
          // 加入已答列表
          this.cardItem.answered = true
        }

        // 最后一个动作，交卷
        if (callback) {
          callback()
        }

        // 查找详情
        this.fetchQuData(item)
      })
    },

    // 试卷详情
    fetchQuData(item) {
      // 打开
      const loading = Loading.service({
        text: '拼命加载中',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 获得详情
      this.cardItem = item

      // 查找下个详情
      const params = { paperId: this.paperId, quId: item.quId }
      quDetail(params).then(response => {
        console.log(response)
        this.quData = response.data
        this.radioValue = ''
        this.multiValue = []
        this.blankAnswers = [] // 重置填空答案
        this.shortAnswer = '' // 重置简答题答案
        this.weatherTableData = {} // 重置天气表格答案

        // 处理填空题内容
        this.handleBlankContent()

        // 填充该题目的答案
        this.quData.answerList.forEach((item) => {
          if ((this.quData.quType === 1 || this.quData.quType === 3) && item.checked) {
            this.radioValue = item.id
          }

          if (this.quData.quType === 2 && item.checked) {
            this.multiValue.push(item.id)
          }
        })

        // 填空题答案回显
        if (this.quData.quType === 4 && this.quData.answer) {
          const answers = this.quData.answer.split('$')
          answers.forEach((ans, index) => {
            if (index < this.blankAnswers.length) {
              this.blankAnswers[index] = ans
            }
          })
        }

        // 简答题答案回显
        if (this.quData.quType === 5 && this.quData.answer) {
          this.shortAnswer = this.quData.answer
        }

        // 天气预报表格题答案回显
        if (this.quData.quType === 6 && this.quData.answer) {
          try {
            this.weatherTableData = JSON.parse(this.quData.answer)
          } catch (e) {
            console.error('解析天气表格数据失败:', e)
            this.weatherTableData = {}
          }
        }

        // 关闭详情
        loading.close()
      })
    },

    // 处理填空题内容分割
    handleBlankContent() {
      if (this.quData.quType === 4 && this.quData.content) {
        this.splitContent = this.quData.content.split('____')
        // 初始化答案数组
        this.blankAnswers = new Array(this.splitContent.length - 1).fill('')
      } else {
        this.splitContent = []
        this.blankAnswers = []
      }
    },

    // 试卷详情
    fetchData(id) {
      const params = { id: id }
      paperDetail(params).then(response => {
        // 试卷内容
        this.paperData = response.data

        // 初始化鼠标监控
        this.initMouseMonitor()

        // 获得第一题内容
        if (this.paperData.radioList && this.paperData.radioList.length > 0) {
          this.cardItem = this.paperData.radioList[0]
        } else if (this.paperData.multiList && this.paperData.multiList.length > 0) {
          this.cardItem = this.paperData.multiList[0]
        } else if (this.paperData.judgeList && this.paperData.judgeList.length > 0) {
          this.cardItem = this.paperData.judgeList[0]
        } else if (this.paperData.blankList && this.paperData.blankList.length > 0) {
          this.cardItem = this.paperData.blankList[0]
        } else if (this.paperData.shortList && this.paperData.shortList.length > 0) {
          this.cardItem = this.paperData.shortList[0]
        } else if (this.paperData.weatherTableList && this.paperData.weatherTableList.length > 0) {
          this.cardItem = this.paperData.weatherTableList[0]
        }

        const that = this

        this.paperData.radioList.forEach(function(item) {
          that.allItem.push(item)
        })

        this.paperData.multiList.forEach(function(item) {
          that.allItem.push(item)
        })

        this.paperData.judgeList.forEach(function(item) {
          that.allItem.push(item)
        })
        this.paperData.blankList.forEach(function(item) {
          that.allItem.push(item)
        })
        this.paperData.shortList.forEach(function(item) {
          that.allItem.push(item)
        })
        this.paperData.weatherTableList.forEach(function(item) {
          that.allItem.push(item)
        })

        // 当前选定
        this.fetchQuData(this.cardItem)
      })
    }

  }
}
</script>

<style scoped lang="scss">
.exam-container {
  padding: 20px;
  min-height: 100vh;
  background-color: #f5f7fa;
}

.exam-header {
  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
  padding: 20px;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);

  .timer-box {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 16px;

    i {
      font-size: 20px;
    }
  }
}

.answer-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);

  .card-header {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #303133;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .card-legend {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
  }

  .question-section {
    margin-bottom: 20px;

    .section-title {
      font-size: 15px;
      color: #606266;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .number-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .number-tag {
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
      }
    }
  }

  .submit-area {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;
    text-align: center;

    .submit-btn {
      width: 100%;
      height: 40px;
      font-size: 16px;
      font-weight: bold;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
      }
    }

    .submit-tip {
      margin-top: 10px;
      color: #F56C6C;
      font-size: 14px;
    }
  }
}

.question-content {
  background: white;
  border-radius: 8px;
  padding: 30px;
  min-height: calc(100vh - 180px);
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);

  .question-box {
    max-width: 800px;
    margin: 0 20px;
  }

  .question-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 30px;

    .question-number {
      width: 40px;
      height: 40px;
      background: #409EFF;
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      font-weight: bold;
    }

    .question-type {
      font-size: 16px;
      font-weight: bold;
      color: #606266;
    }
  }

  .question-body {
    margin-bottom: 30px;

    .question-text {
      font-size: 16px;
      line-height: 1.8;
      color: #303133;
      margin-bottom: 20px;
    }

    .question-image {
      margin: 20px 0;
      text-align: center;

      img {
        max-width: 100%;
        border-radius: 4px;
      }
    }
  }

  .options-list {
    .option-item {
      display: block;
      margin-bottom: 15px;
      padding: 15px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      transition: all 0.3s;

      &:hover {
        border-color: #409EFF;
        background-color: #f5f7fa;
      }

      &.is-checked {
        border-color: #409EFF;
        background-color: #ecf5ff;
      }

      .option-image {
        margin-top: 10px;
        img {
          max-width: 100%;
          border-radius: 4px;
        }
      }
    }
  }

  .blank-answer {
    margin: 20px 0;

    .question-text {
      font-size: 16px;
      line-height: 2;
      color: #303133;
    }

    .blank-input {
      display: inline-block;
      width: 200px;
      height: 22px;
      line-height: 22px;
      margin: 0 4px;
      vertical-align: middle;

      ::v-deep .el-input__inner {
        border: none;
        border-bottom: 1px solid #666;
        border-radius: 0;
        text-align: center;
        background-color: transparent;
        height: 22px;
        line-height: 22px;
        padding: 0;
        color: #f39c12;
        font-weight: bold;
        font-size: 14px;

        &:focus {
          border-bottom: 2px solid #409EFF;
        }

        &::placeholder {
          color: #909399;
          font-size: 12px;
        }
      }
    }
  }

  .essay-answer {
    margin-top: 20px;
  }

  .question-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 40px;
  }
}

// 添加响应式
@media screen and (max-width: 768px) {
  .exam-container {
    padding: 10px;
  }

  .answer-card {
    margin-bottom: 20px;
  }

  .question-content {
    padding: 20px;
  }
}
</style>

