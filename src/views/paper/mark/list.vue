<template>

  <div>

    <!-- <div v-if="breakShow" style=" cursor: pointer; padding: 20px 20px 0px 20px" @click="toExam">
      <el-alert
        :closable="false"
        title="您有正在进行的考试，离线太久考试将被作废哦，点击此处可继续考试！"
        type="error"
      />
    </div> -->

    <data-table
      ref="pagingTable"
      :options="options"
      :list-query="listQuery"
    >
      <template #filter-content>
        <el-input
          v-model="listQuery.params.title"
          placeholder="搜索考试名称"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />

        <el-input
          v-model="listQuery.params.userName"
          placeholder="搜索考生姓名"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />

        <el-button
          v-waves
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >
          查询
        </el-button>

        <el-button
          class="filter-item"
          style="margin-left: 10px;"
          type="info"
          icon="el-icon-refresh"
          @click="resetFilter"
        >
          重置
        </el-button>
      </template>

      <template #data-columns>
        <el-table-column
          label="考试名称"
          prop="title"
          show-overflow-tooltip
        />

        <el-table-column
          label="考生姓名"
          prop="userName"
          align="center"
        />

        <el-table-column
          label="考试时间"
          width="180"
          align="center"
        >
          <template v-slot="scope">
            {{ scope.row.paperTime }}
          </template>
        </el-table-column>

        <el-table-column
          label="客观题得分"
          align="center"
          prop="objScore"
        />

        <el-table-column
          label="主观题得分"
          align="center"
          prop="subjScore"
        />

        <el-table-column
          label="总得分"
          align="center"
        >
          <template v-slot="scope">
            <span :style="{ color: (scope.row.objScore + scope.row.subjScore) >= scope.row.qualifyScore ? '#67C23A' : '#F56C6C' }">
              {{ (scope.row.objScore + scope.row.subjScore) + '/' + scope.row.totalScore }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          label="阅卷状态"
          align="center"
        >
          <template v-slot="scope">
            <el-tag :type="scope.row.paperState === 3 ? 'success' : 'warning'">
              {{ scope.row.paperState === 3 ? '已阅卷' : '待阅卷' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          align="center"
          width="120"
        >
          <template v-slot="scope">
            <el-button
              :type="scope.row.paperState === 3 ? 'warning' : 'primary'"
              type="primary"
              size="mini"
              @click="handleMark(scope.row.paperId)"
            >
              {{ scope.row.paperState === 3 ? '重新阅卷' : '阅卷' }}
            </el-button>
          </template>
        </el-table-column>

      </template>

    </data-table>

  </div>

</template>

<script>
import DataTable from '@/components/DataTable'
import { checkProcess } from '@/api/paper/exam'
import waves from '@/directive/waves' // waves directive

export default {
  name: 'ExamMarkList',
  components: { DataTable },
  directives: { waves },
  data() {
    return {
      breakShow: false,
      breakId: '',
      openTypes: [
        {
          value: 1,
          label: '完全开放'
        },
        {
          value: 2,
          label: '定向考试'
        }
      ],

      listQuery: {
        current: 1,
        size: 10,
        params: {
          title: '',
          userName: ''
        }
      },

      options: {
        // 可批量操作
        multi: false,
        // 列表请求URL
        listUrl: '/exam/api/exam/exam/mark-list'
      }
    }
  },

  created() {
    this.check()
  },
  methods: {

    // 开始考试
    handlePre(examId) {
      this.$router.push({ name: 'PreExam', params: { examId: examId }})
    },

    // 继续考试
    toExam() {
      this.$router.push({ name: 'StartExam', params: { id: this.breakId }})
    },

    // 检查进行中的考试
    check() {
      checkProcess().then(res => {
        if (res.data && res.data.id) {
          this.breakShow = true
          this.breakId = res.data.id
        }
      })
    },

    // 跳转到阅卷页面
    handleMark(paperId) {
      this.$router.push({
        path: `/exam/exam/paper/mark/${paperId}`
      })
    },

    // 处理查询
    handleFilter() {
      this.listQuery.current = 1
      this.$refs.pagingTable.getList()
    },

    // 重置查询条件
    resetFilter() {
      this.listQuery.current = 1
      this.listQuery.params.title = ''
      this.listQuery.params.userName = ''
      this.$refs.pagingTable.getList()
    }
  }
}
</script>

<style scoped>
.filter-item {
  margin-right: 10px;
}

/* 其他样式保持不变 */
</style>
