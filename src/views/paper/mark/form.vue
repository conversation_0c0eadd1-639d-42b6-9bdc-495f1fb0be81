<template>
  <el-container>
    <el-header height="120">
      <div class="exam-info-header">
        <div class="exam-title">
          <span class="title-text">{{ paperData.title }}</span>
        </div>
        <div class="exam-meta">
          <div class="meta-item">
            <i class="el-icon-user" />
            <span class="label">考生姓名：</span>
            <span class="value">{{ paperData.hideStudentName ? '***' : paperData.userId_dictText }}</span>
          </div>
          <div class="meta-item">
            <i class="el-icon-time" />
            <span class="label">考试时间：</span>
            <span class="value">{{ paperData.createTime }}</span>
          </div>
          <div>
            <i class="el-icon-time" />
            <span class="label">考试用时：</span>
            <span class="value">{{ paperData.userTime }}分钟</span>
          </div>
          <div class="meta-item">
            <i class="el-icon-s-data" />
            <span class="label">客观题得分：</span>
            <span class="value score">{{ paperData.objScore }}分</span>
          </div>
          <div class="meta-item">
            <i class="el-icon-trophy" />
            <span class="label">试卷总分：</span>
            <span class="value score">{{ paperData.totalScore }}分</span>
          </div>
        </div>
      </div>
    </el-header>

    <el-main>
      <el-card
        v-for="(item,index) in paperData.blankList.concat(paperData.shortList)"
        :key="item.id"
        style="min-height: 200px; margin-bottom: 18px"
      >
        <!--题目信息-->
        <div class="question-header">
          <div class="question-title">
            <i class="num">{{ index + 1 }}</i>
            <span class="qu-type">
              <span v-if="item.quType === 5">【简答题】</span>
              <span v-else-if="item.quType === 4">【填空题】</span>
            </span>
          </div>
          <div class="question-content">
            <span class="qu-content-text">
              <!-- 填空题显示 -->
              <template v-if="item.quType === 4">
                <span class="blank-line">
                  <template v-for="(part, pIndex) in item.content.split('____')">
                    {{ part }}
                    <el-input
                      v-if="pIndex < item.content.split('____').length - 1"
                      v-model="item.answer.split('$')[pIndex]"
                      class="blank-input"
                      disabled
                      placeholder="考生答案"
                    />
                  </template>
                </span>
              </template>
              <!-- 简答题显示 -->
              <template v-if="item.quType === 5">
                {{ item.content }}
                <div style="margin-top: 20px">
                  <div class="student-answer">
                    <div class="answer-header">考生答案：</div>
                    <div class="answer-body">
                      {{ item.answer || '该题考生未作答' }}
                    </div>
                  </div>
                </div>
              </template>
            </span>
          </div>
        </div>

        <!--评分区域-->
        <div class="answerResult">
          <!-- 填空题正确答案显示 -->
          <div v-if="item.quType === 4" class="true-answer">
            <div v-for="(ans, index) in item.trueAnswer.split('$')" :key="index" class="answer-item">
              第{{ index + 1 }}个填空参考答案：<span class="answer-text">{{ ans }}</span>
            </div>
          </div>

          <!-- 简答题正确答案显示 -->
          <div v-if="item.quType === 5" class="true-answer">
            <div class="answer-title">参考答案：</div>
            <div class="answer-content">{{ item.trueAnswer }}</div>
          </div>

          <div class="score-area">
            <el-switch
              v-model="item.isRight"
              style="margin: 20px 15px"
              active-text="正确"
              inactive-text="错误"
              active-color="#2ecc71"
              inactive-color="#e74c3c"
            />
            得分:
            <el-input-number
              v-model="item.actualScore"
              :min="0"
              :max="item.score"
              :step="1"
            />
          </div>
        </div>
      </el-card>

      <el-button
        style="margin: 20px; margin-left: 45%"
        type="primary"
        @click="submitMark"
      >提交阅卷</el-button>
    </el-main>
  </el-container>
</template>

<script>
import { getPaperData, submitMarkResult } from '@/api/paper/mark'

export default {
  name: 'MarkPaper',
  data() {
    return {
      paperId: null,
      paperData: {},
      paperData: {},
      questionList: []
    }
  },

  created() {
    this.paperId = this.$route.params.paperId
    this.getInitData()
  },

  methods: {
    async getInitData() {
      try {
        // 获取试卷数据
        const data = {
          id: this.paperId
        }
        const paperRes = await getPaperData(data)
        this.paperData = paperRes.data
      } catch (error) {
        console.error('获取数据失败:', error)
        this.$message.error('获取试卷数据失败')
      }
    },

    // 提交阅卷结果
    submitMark() {
      const markData = {
        paperId: this.paperId,
        paperList: this.paperData.blankList.concat(this.paperData.shortList)
      }

      submitMarkResult(markData).then(res => {
        this.$notify({
          title: '成功',
          message: '阅卷完成',
          type: 'success'
        })
        this.$router.push('/exam/exam/paper/list')
      }).catch(error => {
        console.error('提交失败:', error)
        this.$message.error('提交阅卷结果失败')
      })
    }
  }
}
</script>

  <style scoped lang="scss">
  * {
    font-weight: 800;
  }

  .el-container {
    width: 100%;
    height: 100%;
    background-color: #f5f7fa;
    min-height: 100vh;
    padding: 20px;
  }

  .el-container {
    animation: leftMoveIn 0.7s ease-in;
  }

  @keyframes leftMoveIn {
    0% {
      transform: translateX(-100%);
      opacity: 0;
    }
    100% {
      transform: translateX(0%);
      opacity: 1;
    }
  }

  .exam-info-header {
    background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
    border-radius: 8px;
    padding: 20px;
    color: white;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
    margin-bottom: 20px;

    .exam-title {
      margin-bottom: 15px;

      .title-text {
        font-size: 20px;
        font-weight: bold;
        position: relative;
        padding-left: 12px;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 20px;
          background-color: #fff;
          border-radius: 2px;
        }
      }
    }

    .exam-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;

      .meta-item {
        display: flex;
        align-items: center;

        i {
          margin-right: 6px;
          font-size: 16px;
        }

        .label {
          color: rgba(255, 255, 255, 0.9);
          margin-right: 4px;
        }

        .value {
          font-weight: 500;

          &.score {
            font-size: 16px;
            font-weight: bold;
            color: #ffd04b;
          }
        }
      }
    }
  }

  .el-main {
    padding: 0;
  }

  .el-card {
    margin-bottom: 18px;
  }

  .question-header {
    margin-bottom: 20px;
  }

  .question-title {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }

  .question-content {
    padding-left: 10px;
    line-height: 1.8;
  }

  .qu-type {
    font-weight: bold;
    flex-shrink: 0;
  }

  .qu-content-text {
    flex: 1;
    line-height: 1.5;
  }

  .blank-line {
    display: inline;
    line-height: 1.5;
  }

  .blank-input {
    display: inline-block;
    width: 200px;
    margin: 0 4px;
    vertical-align: middle;
  }

  ::v-deep .blank-input .el-input__inner {
    border: none;
    border-bottom: 1px solid #666;
    border-radius: 0;
    text-align: center;
    background-color: transparent;
    height: 22px;
    line-height: 22px;
    padding: 0;
    margin: 0 2px;
    font-size: 14px;
    color: #f39c12;
  }

  .student-answer {
    border: 1px solid #e4e7ed;
    border-radius: 4px;

    .answer-header {
      padding: 8px 15px;
      border-bottom: 1px solid #e4e7ed;
      background-color: #f5f7fa;
      color: #606266;
      font-size: 14px;
      font-weight: bold;
    }

    .answer-body {
      padding: 15px;
      min-height: 100px;
      line-height: 1.8;
      color: #f39c12;
      font-size: 14px;
      white-space: pre-wrap;
      word-break: break-all;

      &:empty::before {
        content: '该题考生未作答';
        color: #909399;
        font-style: italic;
      }
    }
  }

  .answerResult {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;

    .true-answer {
      margin-bottom: 15px;
      padding: 10px;
      background-color: #fff;
      border-radius: 4px;
      border-left: 4px solid #409EFF;

      .answer-title {
        font-weight: bold;
        color: #606266;
        margin-bottom: 8px;
      }

      .answer-content {
        color: #409EFF;
        font-weight: bold;
        line-height: 1.6;
        white-space: pre-wrap;
        word-break: break-all;
      }
    }

    .answer-item {
      line-height: 24px;
      margin: 5px 0;
      color: #606266;
      font-size: 14px;
    }

    .answer-text {
      color: #409EFF;
      font-weight: bold;
      margin-left: 5px;
    }

    .score-area {
      border-top: 1px dashed #dcdfe6;
      padding-top: 15px;
      margin-top: 10px;
    }
  }
  </style>
