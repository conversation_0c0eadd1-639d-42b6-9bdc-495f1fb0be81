<template>
  <div class="precipitation-test">
    <div class="test-header">
      <h2>降水分级落区预报绘制组件测试</h2>
      <p>这是一个测试页面，用于验证降水落区绘制组件的功能</p>
    </div>

    <div class="test-content">
      <precipitation-drawing
        ref="precipitationDrawing"
        :initial-data="testData"
        :region="selectedRegion"
        :readonly="false"
        @data-change="onDataChange"
      />
    </div>

    <div class="test-controls">
      <div class="control-group">
        <label>选择测试区域：</label>
        <el-select v-model="selectedRegion" @change="onRegionChange">
          <el-option
            v-for="region in regions"
            :key="region.value"
            :label="region.label"
            :value="region.value"
          />
        </el-select>
      </div>

      <div class="control-group">
        <el-button @click="clearData">清除数据</el-button>
        <el-button @click="loadTestData">加载测试数据</el-button>
        <el-button @click="exportData">导出数据</el-button>
      </div>
    </div>

    <div v-if="outputData" class="test-output">
      <h3>输出数据：</h3>
      <pre>{{ JSON.stringify(outputData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script>
import PrecipitationDrawing from '@/components/weather/PrecipitationDrawing'

export default {
  name: 'PrecipitationTest',
  components: {
    PrecipitationDrawing
  },
  data() {
    return {
      selectedRegion: 'region1',
      testData: {},
      outputData: null,
      regions: [
        { value: 'region1', label: '区域一(华北区域)' },
        { value: 'region2', label: '区域二(东北区域)' },
        { value: 'region3', label: '区域三(长江中下游区域)' },
        { value: 'region4', label: '区域四(华南区域)' },
        { value: 'region5', label: '区域五(西南地区东部)' },
        { value: 'region6', label: '区域六(青藏高原区域)' },
        { value: 'region7', label: '区域七(新疆区域)' },
        { value: 'region8', label: '区域八(西北地区东部区域)' },
        { value: 'region9', label: '区域九(内蒙古区域)' }
      ]
    }
  },
  methods: {
    onDataChange(data) {
      console.log('数据变更:', data)
      this.outputData = data
    },

    onRegionChange() {
      // 区域变更时清除数据
      this.testData = {}
      this.outputData = null
    },

    clearData() {
      this.testData = {}
      this.outputData = null
      if (this.$refs.precipitationDrawing) {
        this.$refs.precipitationDrawing.clearAllAreas()
      }
    },

    loadTestData() {
      // 加载一些测试数据
      this.testData = {
        light_rain: [
          {
            id: 'test_light_rain_1',
            geometry: {
              type: 'Polygon',
              coordinates: [[
                [116.0, 39.5],
                [116.5, 39.5],
                [116.5, 40.0],
                [116.0, 40.0],
                [116.0, 39.5]
              ]]
            },
            properties: {
              precipitationLevel: 'light_rain',
              createTime: new Date().toISOString()
            }
          }
        ],
        moderate_rain: [
          {
            id: 'test_moderate_rain_1',
            geometry: {
              type: 'Polygon',
              coordinates: [[
                [117.0, 39.0],
                [117.5, 39.0],
                [117.5, 39.5],
                [117.0, 39.5],
                [117.0, 39.0]
              ]]
            },
            properties: {
              precipitationLevel: 'moderate_rain',
              createTime: new Date().toISOString()
            }
          }
        ]
      }

      // 通知组件加载数据
      this.$nextTick(() => {
        if (this.$refs.precipitationDrawing) {
          this.$refs.precipitationDrawing.loadInitialData(this.testData)
        }
      })
    },

    exportData() {
      if (this.$refs.precipitationDrawing) {
        const data = this.$refs.precipitationDrawing.exportDrawingData()
        console.log('导出数据:', data)

        // 创建下载链接
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `precipitation_data_${new Date().getTime()}.json`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)

        this.$message.success('数据已导出')
      }
    }
  }
}
</script>

<style scoped>
.precipitation-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  margin-bottom: 30px;
  text-align: center;
}

.test-header h2 {
  color: #333;
  margin-bottom: 10px;
}

.test-header p {
  color: #666;
  font-size: 14px;
}

.test-content {
  margin-bottom: 30px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  background: #fff;
}

.test-controls {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.control-group label {
  font-weight: 500;
  color: #333;
}

.test-output {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.test-output h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.test-output pre {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
  max-height: 400px;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .test-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .control-group {
    justify-content: space-between;
  }
}
</style>
