<template>
  <div class="weather-exam-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-icon">
          <i class="el-icon-cloudy" />
        </div>
        <div class="header-text">
          <h1 class="page-title">历史个例考试</h1>
          <p class="page-subtitle">
            考察参赛选手对本区域天气气候特点掌握程度，考察灾害性天气的精细化预报及降水要素的分级落区预报能力。
          </p>
        </div>
      </div>
      <div class="header-decoration">
        <div class="decoration-circle circle-1" />
        <div class="decoration-circle circle-2" />
        <div class="decoration-circle circle-3" />
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-card">
        <div class="filter-header">
          <i class="el-icon-search" />
          <span>筛选考试</span>
        </div>
        <el-form :inline="true" class="filter-form">
          <el-form-item label="参与状态">
            <el-select
              v-model="filterStatus"
              placeholder="选择状态"
              clearable
              class="status-select"
            >
              <el-option label="所有考试" value="all">
                <i class="el-icon-document" />
                <span>所有考试</span>
              </el-option>
              <el-option label="未参加" value="not_participated">
                <i class="el-icon-circle-plus" />
                <span>未参加</span>
              </el-option>
              <el-option label="已参加" value="participated">
                <i class="el-icon-circle-check" />
                <span>已参加</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              :loading="loading"
              type="primary"
              class="search-btn"
              @click="getExamList"
            >
              <i class="el-icon-search" />
              <span>查询</span>
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 考试统计 -->
    <div v-if="examList.length > 0" class="stats-section">
      <div class="stats-card">
        <div class="stat-item">
          <div class="stat-number">{{ examList.length }}</div>
          <div class="stat-label">总考试数</div>
        </div>
        <div class="stat-divider" />
        <div class="stat-item">
          <div class="stat-number">{{ participatedCount }}</div>
          <div class="stat-label">已参加</div>
        </div>
        <div class="stat-divider" />
        <div class="stat-item">
          <div class="stat-number">{{ notParticipatedCount }}</div>
          <div class="stat-label">未参加</div>
        </div>
      </div>
    </div>

    <!-- 考试卡片列表 -->
    <div v-loading="loading" class="exam-grid">
      <div
        v-for="exam in examList"
        :key="exam.id"
        :class="['exam-card', getExamCardClass(exam)]"
        @click="handleCardClick(exam)"
      >
        <!-- 卡片状态指示器 -->
        <div :class="getStatusIndicatorClass(exam)" class="card-status-indicator" />

        <!-- 卡片头部 -->
        <div class="card-header">
          <div class="exam-title">{{ exam.title }}</div>
          <el-tag
            :type="getUserStatusType(exam)"
            size="small"
            class="status-tag"
            effect="dark"
          >
            {{ getUserStatusText(exam) }}
          </el-tag>
        </div>

        <!-- 考试信息 -->
        <div class="exam-details">
          <div class="detail-row">
            <div class="detail-item">
              <i class="el-icon-time detail-icon" />
              <span class="detail-text">{{ exam.totalTime }}分钟</span>
            </div>
            <div class="detail-item">
              <i class="el-icon-star-on detail-icon" />
              <span class="detail-text">{{ exam.totalScore }}分</span>
            </div>
          </div>
          <div class="detail-row">
            <div class="detail-item full-width">
              <i class="el-icon-date detail-icon" />
              <span class="detail-text">{{ formatDate(exam.startTime) }} - {{ formatDate(exam.endTime) }}</span>
            </div>
          </div>
        </div>

        <!-- 进度条（如果有进度信息） -->
        <div v-if="exam.isAnswering && exam.userProgress" class="progress-section">
          <div class="progress-label">答题进度</div>
          <el-progress
            :percentage="exam.userProgress"
            :stroke-width="6"
            :show-text="false"
            class="exam-progress"
          />
          <div class="progress-text">{{ exam.userProgress }}%</div>
        </div>

        <!-- 操作按钮 -->
        <div class="card-actions">
          <!-- 已提交的考试 -->
          <el-button
            v-if="exam.isSubmitted"
            type="success"
            class="action-btn success-btn"
            @click.stop="viewResult(exam)"
          >
            <div class="button-content-with-score">
              <div class="button-main-action">
                <i class="el-icon-view" />
                <span>查看结果</span>
              </div>
              <div v-if="exam.userScore !== null" class="score-display">
                <span class="score-label">得分:</span>
                <span class="score-value">{{ exam.userScore }}</span>
                <span class="score-unit">分</span>
              </div>
            </div>
          </el-button>

          <!-- 正在答题中的考试 -->
          <el-button
            v-else-if="exam.isAnswering"
            type="warning"
            class="action-btn warning-btn"
            @click.stop="continueExam(exam)"
          >
            <i class="el-icon-refresh" />
            <span>继续考试</span>
          </el-button>

          <!-- 未参加的考试 -->
          <el-button
            v-else-if="!exam.hasParticipated && !exam.isAnswering"
            type="primary"
            class="action-btn primary-btn"
            @click.stop="startExam(exam)"
          >
            <i class="el-icon-caret-right" />
            <span>开始考试</span>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && examList.length === 0" class="empty-state">
      <div class="empty-icon">
        <i class="el-icon-document-remove" />
      </div>
      <div class="empty-title">暂无考试</div>
      <div class="empty-description">当前没有可参加的历史个例考试</div>
    </div>
  </div>
</template>

<script>
import { post } from '@/utils/request'

export default {
  name: 'WeatherExamOnline',
  data() {
    return {
      examList: [],
      filterStatus: '',
      loading: false
    }
  },
  computed: {
    participatedCount() {
      return this.examList.filter(exam => exam.hasParticipated).length
    },
    notParticipatedCount() {
      return this.examList.filter(exam => !exam.hasParticipated).length
    }
  },
  created() {
    this.getExamList()
  },
  methods: {
    async getExamList() {
      this.loading = true
      try {
        const params = {
          current: 1,
          size: 100,
          params: {
            state: 1, // 只查询已发布的考试
            examType: 'weather'
          }
        }

        // 优化：online-paging接口现在一次性返回考试信息和用户参与状态
        // 避免了N+1查询问题，无需再循环调用check-status接口
        const response = await post('/exam/api/weather/exam/online-paging', params)
        if (response.code === 0) {
          let allExams = response.data.records || []

          // 根据用户参与状态进行过滤
          if (this.filterStatus) {
            if (this.filterStatus === 'not_participated') {
              // 只显示未参加的考试
              allExams = allExams.filter(exam => !exam.userAnswerId)
            } else if (this.filterStatus === 'participated') {
              // 只显示已参加的考试
              allExams = allExams.filter(exam => exam.userAnswerId)
            } else if (this.filterStatus === 'all') {
              // 显示所有考试，不进行过滤
              // allExams 保持不变
            }
          }

          this.examList = allExams
          // 直接处理接口返回的用户参与状态数据
          this.processExamStatus()

          console.log('考试列表加载完成，共', this.examList.length, '个考试')
        } else {
          this.$message.error('获取考试列表失败')
        }
      } catch (error) {
        console.error('获取考试列表错误:', error)
        this.$message.error('获取考试列表失败')
      } finally {
        this.loading = false
      }
    },

    async startExam(exam) {
      // 跳转到历史个例考试页面
      this.$router.push(`/weather/exam/start/${exam.id}`)
    },

    async continueExam(exam) {
      // 继续历史个例考试
      this.$router.push(`/weather/exam/start/${exam.id}`)
    },

    async viewResult(exam) {
      // 查看考试结果
      this.$router.push(`/weather/exam/result/${exam.id}`)
    },

    // 处理考试状态（使用online-paging接口返回的数据，无需额外请求）
    processExamStatus() {
      for (let i = 0; i < this.examList.length; i++) {
        const exam = this.examList[i]

        // 直接使用online-paging接口返回的用户参与状态数据
        if (exam.userAnswerId) {
          // 用户已参加过考试
          const hasSubmitted = exam.userAnswerState === 1

          // 设置参与状态
          this.$set(exam, 'hasParticipated', hasSubmitted)
          this.$set(exam, 'hasJoined', !hasSubmitted) // 如果未提交，表示正在答题中

          // 添加额外的状态信息
          this.$set(exam, 'userProgress', exam.userProgress || 0)
          this.$set(exam, 'userScore', exam.userTotalScore || null)
          this.$set(exam, 'isAnswering', exam.userAnswerState === 0)
          this.$set(exam, 'isSubmitted', hasSubmitted)

          console.log(`考试 ${exam.id} 状态:`, {
            hasParticipated: hasSubmitted,
            hasJoined: !hasSubmitted,
            userProgress: exam.userProgress,
            userScore: exam.userTotalScore,
            isAnswering: exam.userAnswerState === 0,
            isSubmitted: hasSubmitted
          })
        } else {
          // 用户未参加过考试
          this.$set(exam, 'hasParticipated', false)
          this.$set(exam, 'hasJoined', false)
          this.$set(exam, 'userProgress', 0)
          this.$set(exam, 'userScore', null)
          this.$set(exam, 'isAnswering', false)
          this.$set(exam, 'isSubmitted', false)
        }
      }
    },

    getStatusType(state) {
      const types = {
        0: 'info',
        1: 'success',
        2: 'warning'
      }
      return types[state] || 'info'
    },

    getStatusText(state) {
      const texts = {
        0: '未开始',
        1: '进行中',
        2: '已结束'
      }
      return texts[state] || '未知'
    },

    // 基于用户参与状态的标签类型
    getUserStatusType(exam) {
      if (exam.isSubmitted) return 'success' // 已提交 - 绿色
      if (exam.isAnswering) return 'warning' // 答题中 - 橙色
      return 'info' // 未参加 - 蓝色
    },

    // 基于用户参与状态的标签文本
    getUserStatusText(exam) {
      if (exam.isSubmitted) return '已参加'
      if (exam.isAnswering) return '答题中'
      return '未参加'
    },

    getExamCardClass(exam) {
      return {
        'exam-available': !exam.hasJoined && !exam.hasParticipated,
        'exam-in-progress': exam.hasJoined && !exam.hasParticipated,
        'exam-participated': exam.hasParticipated
      }
    },

    getStatusIndicatorClass(exam) {
      if (exam.isSubmitted) return 'status-completed'
      if (exam.isAnswering) return 'status-in-progress'
      return 'status-available'
    },

    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    handleCardClick(exam) {
      // 卡片点击事件，可以添加预览功能
      console.log('Card clicked:', exam.title)
    }
  }
}
</script>

<style scoped>
/* 全局容器 */
.weather-exam-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 0;
}

/* 页面头部 */
.page-header {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 60px 40px;
  overflow: hidden;
}

.header-content {
  display: flex;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.header-icon {
  font-size: 64px;
  margin-right: 30px;
  opacity: 0.9;
}

.header-icon i {
  display: block;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.page-title {
  font-size: 36px;
  font-weight: 700;
  margin: 0 0 10px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
  line-height: 1.6;
  max-width: 600px;
}

.header-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: pulse 4s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: -100px;
  right: -100px;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 50px;
  right: 200px;
  animation-delay: 1s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: -50px;
  right: 50px;
  animation-delay: 2s;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.1; }
  50% { transform: scale(1.1); opacity: 0.2; }
}

/* 筛选区域 */
.filter-section {
  padding: 30px 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.filter-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.filter-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.filter-header i {
  margin-right: 10px;
  color: #409eff;
  font-size: 20px;
}

.filter-form {
  margin: 0;
}

.status-select {
  width: 200px;
}

.status-select .el-select-dropdown__item {
  display: flex;
  align-items: center;
}

.status-select .el-select-dropdown__item i {
  margin-right: 8px;
  color: #909399;
}

.search-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  border-radius: 8px;
  padding: 10px 20px;
}

/* 统计区域 */
.stats-section {
  padding: 0 40px 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.stats-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

.stat-divider {
  width: 1px;
  height: 40px;
  background: #ebeef5;
}

/* 考试网格 */
.exam-grid {
  padding: 20px 40px 40px;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 24px;
}

/* 考试卡片 */
.exam-card {
  background: white;
  border-radius: 16px;
  padding: 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.exam-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.exam-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  transition: all 0.3s ease;
}

.exam-card.exam-participated::before {
  background: linear-gradient(90deg, #67c23a, #85ce61);
}

.exam-card.exam-in-progress::before {
  background: linear-gradient(90deg, #e6a23c, #f0a020);
}

/* 状态指示器 */
.card-status-indicator {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  z-index: 2;
}

.status-available {
  background: #409eff;
  box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.2);
}

.status-in-progress {
  background: #e6a23c;
  box-shadow: 0 0 0 3px rgba(230, 162, 60, 0.2);
  animation: blink 2s infinite;
}

.status-completed {
  background: #67c23a;
  box-shadow: 0 0 0 3px rgba(103, 194, 58, 0.2);
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 卡片头部 */
.card-header {
  padding: 24px 24px 16px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.exam-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
  flex: 1;
  margin-right: 16px;
}

.status-tag {
  border-radius: 12px;
  font-weight: 500;
}

/* 考试详情 */
.exam-details {
  padding: 0 24px 16px;
}

.detail-row {
  display: flex;
  margin-bottom: 12px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-item {
  display: flex;
  align-items: center;
  flex: 1;
}

.detail-item.full-width {
  flex: 1;
}

.detail-icon {
  font-size: 16px;
  color: #909399;
  margin-right: 8px;
  width: 16px;
}

.detail-text {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

/* 进度区域 */
.progress-section {
  padding: 16px 24px;
  background: #f8f9fa;
  margin: 0 24px 16px;
  border-radius: 8px;
}

.progress-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
  font-weight: 500;
}

.exam-progress {
  margin-bottom: 4px;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  text-align: right;
  font-weight: 600;
}

/* 操作按钮 */
.card-actions {
  padding: 16px 24px 24px;
}

.action-btn {
  width: 100%;
  min-height: 48px;
  height: auto;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  transition: all 0.3s ease;
  padding: 12px 16px;
}

.action-btn i {
  font-size: 16px;
}

.success-btn {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  border: none;
  color: white;
}

.success-btn:hover {
  background: linear-gradient(135deg, #85ce61, #67c23a);
  transform: translateY(-2px);
}

.warning-btn {
  background: linear-gradient(135deg, #e6a23c, #f0a020);
  border: none;
  color: white;
}

.warning-btn:hover {
  background: linear-gradient(135deg, #f0a020, #e6a23c);
  transform: translateY(-2px);
}

.primary-btn {
  background: linear-gradient(135deg, #409eff, #66b1ff);
  border: none;
  color: white;
}

.primary-btn:hover {
  background: linear-gradient(135deg, #66b1ff, #409eff);
  transform: translateY(-2px);
}

/* 带分数的按钮内容布局 */
.button-content-with-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  width: 100%;
}

.button-main-action {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 600;
}

.score-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 700;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.score-label {
  opacity: 0.9;
  font-size: 11px;
}

.score-value {
  font-size: 14px;
  font-weight: 800;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  margin: 0 1px;
}

.score-unit {
  opacity: 0.9;
  font-size: 11px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 40px;
  color: #909399;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #606266;
}

.empty-description {
  font-size: 14px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .exam-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
    padding: 20px 30px 40px;
  }

  .filter-section,
  .stats-section {
    padding-left: 30px;
    padding-right: 30px;
  }

  .page-header {
    padding: 40px 30px;
  }
}

@media (max-width: 768px) {
  .exam-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 20px 20px 40px;
  }

  .filter-section,
  .stats-section {
    padding-left: 20px;
    padding-right: 20px;
  }

  .page-header {
    padding: 30px 20px;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
  }

  .header-icon {
    margin-right: 0;
    margin-bottom: 20px;
  }

  .page-title {
    font-size: 28px;
  }

  .stats-card {
    flex-direction: column;
    gap: 20px;
  }

  .stat-divider {
    width: 100%;
    height: 1px;
  }

  .filter-form {
    flex-direction: column;
    gap: 16px;
  }

  .status-select {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 20px 15px;
  }

  .filter-section,
  .stats-section {
    padding-left: 15px;
    padding-right: 15px;
  }

  .exam-grid {
    padding: 15px 15px 30px;
  }

  .page-title {
    font-size: 24px;
  }

  .page-subtitle {
    font-size: 14px;
  }
}
</style>
