<template>

  <div>

    <data-table
      ref="pagingTable"
      :options="options"
      :list-query="listQuery"
      @multi-actions="handleMultiAction"
    >

      <template #filter-content>

        <el-input v-model="listQuery.params.userName" style="width: 200px" placeholder="搜索登录名" class="filter-item" />
        <el-input v-model="listQuery.params.realName" style="width: 200px" placeholder="搜索姓名" class="filter-item" />

        <depart-tree-select
          v-model="listQuery.params.departId"
          :options="treeData"
          :props="defaultProps"
          style="width: 200px"
          class="filter-item"
          placeholder="选择单位"
        />

        <el-select
          v-model="listQuery.params.state"
          placeholder="用户状态"
          clearable
          style="width: 120px"
          class="filter-item"
        >
          <el-option label="正常" value="0" />
          <el-option label="禁用" value="1" />
        </el-select>

        <el-select
          v-model="listQuery.params.groupName"
          placeholder="用户分组"
          clearable
          style="width: 120px"
          class="filter-item"
        >
          <el-option
            v-for="item in groupData"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          />
        </el-select>

        <!-- <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleSearch">
          查询
        </el-button> -->

        <el-button class="filter-item" type="primary" icon="el-icon-plus" @click="handleAdd">
          添加
        </el-button>

      </template>

      <template #data-columns>

        <el-table-column
          type="selection"
          width="55"
        />

        <el-table-column
          align="center"
          label="用户名"
        >
          <template v-slot="scope">
            <a @click="handleUpdate(scope.row)">{{ scope.row.userName }}</a>
          </template>

        </el-table-column>

        <el-table-column
          align="center"
          label="姓名"
          prop="realName"
        />

        <el-table-column
          align="center"
          label="所属单位"
          prop="deptName"
        />
        <el-table-column
          align="center"
          label="分组"
          prop="groupName"
        />

        <el-table-column
          align="center"
          label="创建时间"
          prop="createTime"
        />

        <el-table-column
          align="center"
          label="角色"
        >
          <template v-slot="scope">
            {{ scope.row.roles | roleFilter }}
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="状态"
          width="200"
        >
          <template v-slot="scope">
            <el-switch
              v-model="scope.row.state"
              :active-value="0"
              :inactive-value="1"
              active-color="#13ce66"
              inactive-color="#ff4949"
              active-text="正常"
              inactive-text="禁用"
              @change="handleStateChange(scope.row)"
            />
          </template>
        </el-table-column>
      </template>
    </data-table>

    <el-dialog :visible.sync="dialogVisible" title="添加用户" width="500px">

      <el-form :model="formData" label-position="left" label-width="100px">

        <el-form-item label="用户名">
          <el-input v-model="formData.userName" />
        </el-form-item>

        <el-form-item label="姓名">
          <el-input v-model="formData.realName" />
        </el-form-item>

        <el-form-item label="密码">
          <el-input v-model="formData.password" placeholder="不修改请留空" type="password" />
        </el-form-item>

        <el-form-item label="部门">
          <depart-tree-select v-model="formData.departId" :options="treeData" :props="defaultProps" />
        </el-form-item>

        <el-form-item label="角色">
          <meet-role v-model="formData.roles" />
        </el-form-item>
        <el-form-item label="分组">
          <el-radio-group v-model="formData.groupName" :options="groupData">
            <el-radio v-for="item in groupData" :key="item.id" :label="item.id" :value="item.id">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>

        <!--        <el-form-item label="头像" prop="avatar">-->

        <!--          <single-upload-->
        <!--            v-model="formData.avatar"-->
        <!--          />-->
        <!--        </el-form-item>-->

      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSave">确 定</el-button>
      </div>

    </el-dialog>

  </div>

</template>

<script>
import DataTable from '@/components/DataTable'
import MeetRole from '@/components/MeetRole'
import { saveData, updateUserState } from '@/api/sys/user/user'
import DepartTreeSelect from '@/components/DepartTreeSelect'
import { fetchTree } from '@/api/sys/depart/depart'

export default {
  name: 'SysUserList',
  components: { DepartTreeSelect, DataTable, MeetRole },
  filters: {
    // 订单状态
    userState(value) {
      const map = {
        '0': '正常',
        '1': '禁用'
      }
      return map[value]
    },
    // 角色显示转换
    roleFilter(roles) {
      if (!roles) return ''
      const roleMap = {
        'sa': '管理员',
        'student': '学员',
        'teacher': '教练'
      }
      if (typeof roles === 'string') {
        roles = roles.split(',')
      }
      return roles.map(role => roleMap[role] || role).join('、')
    }
  },
  data() {
    return {

      treeData: [],
      groupData: [
        { id: '预报', label: '预报' },
        { id: '县综', label: '县综' },
        { id: '人影', label: '人影' }
      ],
      defaultProps: {
        value: 'id',
        label: 'deptName',
        children: 'children'
      },
      dialogVisible: false,

      listQuery: {
        current: 1,
        size: 10,
        params: {
          userName: '',
          realName: '',
          departId: '',
          state: '',
          groupName: ''
        }
      },

      formData: {
        avatar: ''
      },

      options: {
        // 列表请求URL
        listUrl: '/exam/api/sys/user/paging',
        // 启用禁用
        stateUrl: '/sys/user/state',
        deleteUrl: '/exam/api/sys/user/delete',
        // 批量操作列表
        multiActions: [
          {
            value: 'enable',
            label: '启用'
          }, {
            value: 'disable',
            label: '禁用'
          },
          {
            value: 'delete',
            label: '删除'
          }
        ]
      }
    }
  },

  created() {
    fetchTree({}).then(response => {
      this.treeData = response.data
    })
  },

  methods: {
    handleStateChange(row) {
      const state = parseInt(row.state) // 确保是整数类型
      const userId = row.id
      // 调用状态更新接口
      updateUserState({
        id: userId,
        state: state
      }).then(response => {
        if (response.code === 0) {
          this.$message({
            type: 'success',
            message: '状态修改成功！'
          })
        } else {
          // 如果业务处理失败，回滚状态
          row.state = state === 0 ? 1 : 0
          this.$message({
            type: 'error',
            message: response.msg || '状态修改失败！'
          })
        }
      }).catch(error => {
        // 如果请求失败，回滚状态
        row.state = state === 0 ? 1 : 0
        this.$message({
          type: 'error',
          message: '状态修改失败！'
        })
      })
    },

    handleSearch() {
      this.listQuery.current = 1
      this.$refs.pagingTable.getList()
    },

    handleUploadSuccess(response) {
      // 上传图片赋值
      this.formData.avatar = response.data.url
    },

    handleAdd() {
      this.formData = {}
      this.dialogVisible = true
    },

    handleUpdate(row) {
      this.dialogVisible = true
      this.formData = { ...row } // 使用浅拷贝避免直接修改行数据
      this.formData.roles = Array.isArray(row.roles) ? row.roles : row.roles.split(',')
      this.formData.password = null
    },

    departSelected(data) {
      this.formData.departId = data.id
    },
    handleSave() {
      saveData(this.formData).then(response => {
        if (response.code === 0) {
          this.$message({
            type: 'success',
            message: '用户修改成功!'
          })
          this.dialogVisible = false
          this.$refs.pagingTable.getList()
        } else {
          this.$message({
            type: 'error',
            message: response.msg || '保存失败！'
          })
        }
      }).catch(error => {
        this.$message({
          type: 'error',
          message: '保存失败！'
        })
      })
    },

    // 批量操作监听
    handleMultiAction(obj) {
      if (obj.opt === 'cancel') {
        this.handleCancelOrder(obj.ids)
      }
    }
  }
}
</script>
