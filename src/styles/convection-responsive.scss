/**
 * 强对流模块响应式布局样式
 * 适配移动端、平板端和桌面端
 */

// 断点定义
$mobile: 768px;
$tablet: 1024px;
$desktop: 1440px;

// 通用响应式样式
.convection-responsive {
  // 基础响应式容器
  &-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
    
    @media (max-width: $mobile) {
      padding: 0 10px;
    }
  }
  
  // 响应式网格系统
  &-grid {
    display: grid;
    gap: 20px;
    
    // 桌面端：3列布局
    @media (min-width: $desktop) {
      grid-template-columns: repeat(3, 1fr);
    }
    
    // 平板端：2列布局
    @media (min-width: $tablet) and (max-width: #{$desktop - 1px}) {
      grid-template-columns: repeat(2, 1fr);
    }
    
    // 移动端：1列布局
    @media (max-width: #{$tablet - 1px}) {
      grid-template-columns: 1fr;
      gap: 15px;
    }
  }
  
  // 响应式卡片
  &-card {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    padding: 20px;
    
    @media (max-width: $mobile) {
      padding: 15px;
      border-radius: 6px;
    }
  }
}

// 强对流站点预报表格响应式适配
.convection-station-table {
  // 移动端适配
  @media (max-width: $mobile) {
    .table-header {
      flex-direction: column;
      gap: 15px;
      align-items: flex-start;
      
      .header-right {
        width: 100%;
        
        .progress-container {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;
          
          .progress-bar {
            width: 100%;
          }
        }
      }
    }
    
    .instruction-text {
      padding: 12px;
      font-size: 14px;
      
      ul {
        padding-left: 15px;
      }
      
      li {
        font-size: 13px;
        line-height: 1.5;
      }
    }
    
    // 移动端表格横向滚动
    .station-prediction-table {
      overflow-x: auto;
      display: block;
      white-space: nowrap;
      
      .el-table__body-wrapper {
        overflow-x: auto;
      }
    }
    
    // 站点表格单元格适配
    .station-cell {
      padding: 8px 3px;
      
      .weather-options {
        gap: 6px;
      }
      
      .weather-option {
        padding: 6px 8px;
        min-height: 40px;
        font-size: 12px;
        
        .option-content {
          .option-label {
            font-size: 12px;
          }
          
          .option-desc {
            font-size: 10px;
          }
        }
      }
    }
    
    // 预报依据区域
    .reasoning-section {
      padding: 15px;
      
      .reasoning-stats {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
        
        .reasoning-progress {
          width: 100%;
          justify-content: space-between;
        }
      }
    }
  }
  
  // 平板端适配
  @media (min-width: $mobile) and (max-width: #{$tablet - 1px}) {
    .station-cell {
      padding: 10px 5px;
      
      .weather-option {
        padding: 8px 10px;
        min-height: 42px;
        
        .option-content {
          .option-label {
            font-size: 13px;
          }
        }
      }
    }
  }
}

// 强对流落区绘制组件响应式适配
.convection-area-drawing {
  @media (max-width: $mobile) {
    .drawing-header {
      flex-direction: column;
      gap: 10px;
      
      .drawing-controls {
        flex-wrap: wrap;
        gap: 8px;
        
        .el-button {
          font-size: 12px;
          padding: 8px 12px;
        }
      }
    }
    
    // 工具栏适配
    .drawing-toolbar {
      flex-wrap: wrap;
      gap: 8px;
      padding: 10px;
      
      .toolbar-group {
        flex: 1;
        min-width: 120px;
        
        .el-button-group {
          .el-button {
            font-size: 12px;
            padding: 6px 10px;
          }
        }
      }
    }
    
    // 类型选择器
    .weather-type-selector {
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;
      
      .type-option {
        padding: 10px;
        font-size: 13px;
        
        .type-icon {
          font-size: 16px;
        }
        
        .type-info {
          .type-label {
            font-size: 13px;
          }
          
          .type-description {
            font-size: 11px;
          }
        }
      }
    }
    
    // 地图容器
    .map-container {
      height: 300px; // 移动端降低地图高度
      border-radius: 6px;
    }
    
    // 绘制统计
    .drawing-statistics {
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
      
      .stat-item {
        padding: 10px;
        font-size: 12px;
        
        .stat-value {
          font-size: 16px;
        }
      }
    }
  }
}

// 强对流考试答题页面响应式适配
.student-exam-detail {
  @media (max-width: $mobile) {
    .exam-header {
      padding: 15px;
      
      .exam-info {
        .exam-title {
          font-size: 18px;
          line-height: 1.4;
        }
        
        .exam-meta {
          flex-direction: column;
          gap: 5px;
          align-items: flex-start;
          
          .meta-item {
            font-size: 13px;
          }
        }
      }
      
      .exam-timer {
        .timer-display {
          font-size: 16px;
          padding: 8px 12px;
        }
        
        .timer-warning {
          font-size: 12px;
        }
      }
    }
    
    // 进度指示器
    .progress-indicators {
      flex-direction: column;
      gap: 10px;
      padding: 15px;
      
      .progress-item {
        .progress-label {
          font-size: 13px;
        }
        
        .progress-value {
          font-size: 12px;
        }
      }
    }
    
    // 答题区域
    .answer-content {
      gap: 20px;
      
      .answer-section {
        .section-title {
          font-size: 16px;
          padding: 12px 15px;
        }
      }
    }
    
    // 操作按钮区域
    .exam-actions {
      padding: 15px;
      flex-direction: column;
      gap: 10px;
      
      .action-group {
        flex-direction: column;
        gap: 8px;
        
        .el-button {
          width: 100%;
          padding: 12px 20px;
        }
      }
    }
  }
}

// 强对流批卷页面响应式适配
.convection-grading-detail {
  @media (max-width: $mobile) {
    .grading-layout {
      flex-direction: column;
      
      .grading-left {
        width: 100%;
        margin-bottom: 20px;
      }
      
      .grading-right {
        width: 100%;
      }
    }
    
    // 答案对比组件
    .answer-comparison {
      .comparison-header {
        .view-toggle {
          .el-button-group {
            .el-button {
              font-size: 12px;
              padding: 6px 10px;
            }
          }
        }
      }
      
      .comparison-content {
        &.side-by-side {
          flex-direction: column; // 移动端强制纵向布局
          
          .comparison-section {
            width: 100%;
            margin-bottom: 20px;
          }
        }
      }
      
      .station-comparison {
        overflow-x: auto;
        
        .comparison-table {
          min-width: 600px; // 确保表格最小宽度
        }
      }
    }
    
    // 评分面板
    .grading-panel {
      .score-inputs {
        .score-group {
          flex-direction: column;
          gap: 10px;
          
          .score-input {
            .input-group {
              flex-direction: column;
              gap: 5px;
              align-items: flex-start;
              
              .el-input-number {
                width: 100%;
              }
            }
          }
        }
      }
      
      .grading-comments {
        .el-textarea {
          .el-textarea__inner {
            min-height: 80px;
          }
        }
      }
    }
  }
}

// 强对流管理页面响应式适配
.convection-manage {
  @media (max-width: $mobile) {
    .page-header {
      flex-direction: column;
      gap: 15px;
      align-items: flex-start;
      padding: 15px;
      
      .header-right {
        width: 100%;
        
        .el-button {
          width: 100%;
          padding: 12px 20px;
        }
      }
    }
    
    // 筛选器
    .filter-container {
      padding: 15px;
      
      .el-form {
        .el-form-item {
          margin-bottom: 15px;
          
          .el-form-item__label {
            width: auto !important;
            margin-bottom: 5px;
          }
          
          .el-form-item__content {
            margin-left: 0 !important;
            
            .el-input,
            .el-date-picker {
              width: 100%;
            }
          }
        }
        
        .el-button {
          width: 100%;
          margin-bottom: 10px;
        }
      }
    }
    
    // 数据表格
    .el-table {
      font-size: 13px;
      
      .el-table__header {
        font-size: 12px;
      }
      
      // 隐藏部分列在移动端
      .mobile-hidden {
        display: none;
      }
      
      // 操作列按钮
      .el-button {
        font-size: 11px;
        padding: 4px 8px;
        margin: 2px;
      }
    }
    
    // 分页器
    .pagination-container {
      padding: 15px;
      text-align: center;
      
      .el-pagination {
        .el-pagination__total,
        .el-pagination__jump {
          display: none; // 移动端隐藏总数和跳转
        }
      }
    }
  }
}

// 通用移动端优化
@media (max-width: $mobile) {
  // 对话框优化
  .el-dialog {
    width: 95% !important;
    margin: 5% auto !important;
    
    .el-dialog__header {
      padding: 15px;
      
      .el-dialog__title {
        font-size: 16px;
      }
    }
    
    .el-dialog__body {
      padding: 15px;
      font-size: 14px;
    }
    
    .el-dialog__footer {
      padding: 15px;
      
      .el-button {
        width: 48%;
        margin: 0 1%;
      }
    }
  }
  
  // 消息提示优化
  .el-message {
    min-width: 300px;
    margin: 10px;
    
    .el-message__content {
      font-size: 14px;
      line-height: 1.4;
    }
  }
  
  // 确认框优化
  .el-message-box {
    width: 90% !important;
    
    .el-message-box__header {
      .el-message-box__title {
        font-size: 16px;
      }
    }
    
    .el-message-box__content {
      .el-message-box__message {
        font-size: 14px;
        line-height: 1.5;
      }
    }
    
    .el-message-box__btns {
      .el-button {
        width: 45%;
        margin: 0 2.5%;
      }
    }
  }
  
  // 加载提示优化
  .el-loading-mask {
    .el-loading-spinner {
      .el-loading-text {
        font-size: 14px;
        margin-top: 10px;
      }
    }
  }
}

// 平板端优化
@media (min-width: $mobile) and (max-width: #{$tablet - 1px}) {
  .convection-responsive-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  // 表格适配
  .el-table {
    .el-table__body-wrapper {
      overflow-x: auto;
    }
  }
  
  // 对话框适配
  .el-dialog {
    width: 80% !important;
    margin: 10% auto !important;
  }
}

// 高DPI屏幕优化
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .convection-station-table,
  .convection-area-drawing {
    // 高DPI屏幕下的图标和边框优化
    border-width: 0.5px;
    
    .weather-option {
      border-width: 0.5px;
    }
    
    .el-icon {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
  }
}

// 打印样式优化
@media print {
  .convection-station-table,
  .convection-area-drawing,
  .answer-comparison {
    // 打印时隐藏交互元素
    .el-button,
    .progress-container,
    .auto-save-indicator {
      display: none !important;
    }
    
    // 调整打印时的颜色和背景
    * {
      color: #000 !important;
      background: #fff !important;
      box-shadow: none !important;
    }
    
    // 优化表格打印
    .el-table {
      border: 1px solid #000 !important;
      
      th, td {
        border: 1px solid #000 !important;
        padding: 8px !important;
      }
    }
  }
} 