import Vue from 'vue'

import Cookies from 'js-cookie'

import 'normalize.css/normalize.css' // a modern alternative to CSS resets

import Element from 'element-ui'
import './styles/element-variables.scss'

import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'

import './icons' // icon
import './permission' // permission control
import './utils/error-log' // error log

import * as filters from './filters' // global filters

// 强对流模块错误处理器
import ConvectionErrorHandler from '@/utils/errorHandler'

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
if (process.env.NODE_ENV === 'production') {
  const { mockXHR } = require('../mock')
  mockXHR()
}

Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})

// 安装强对流错误处理器
Vue.use(ConvectionErrorHandler)

// 全局性能监控
if (process.env.NODE_ENV === 'development') {
  // 开发环境性能监控
  Vue.config.performance = true
}

// 全局错误处理
Vue.config.errorHandler = (err, vm, info) => {
  console.error('[Global Error]', err, info)
  ConvectionErrorHandler.ConvectionErrorHandler.handleApiError(err, '系统操作', {
    showMessage: true,
    logError: true
  })
}

// 添加全局前端性能监控
Vue.mixin({
  beforeCreate() {
    this.$performanceStart = performance.now()
  },
  mounted() {
    if (this.$performanceStart) {
      const renderTime = performance.now() - this.$performanceStart

      // 如果渲染时间超过100ms，记录性能日志
      if (renderTime > 100) {
        console.warn(`[Performance Warning] ${this.$options.name || 'Component'} 渲染时间: ${renderTime.toFixed(2)}ms`)
      }
    }
  }
})

// register global utility filters
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

Vue.config.productionTip = false

// 环境标识
Vue.prototype.$demo = process.env.NODE_ENV === 'demo'

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
