import { post, get } from '@/utils/request'

/**
 * 历史个例批卷相关API
 */

// ==================== 答案管理 ====================

/**
 * 获取未判卷的答案列表
 * @param {Object} params 查询参数
 */
export function getUngradedAnswers(params = {}) {
  const { examId, questionId, limit = 100 } = params
  return get('/exam/api/weather/grading/ungraded', {
    params: { examId, questionId, limit }
  })
}

/**
 * 获取判卷失败的答案列表
 * @param {Object} params 查询参数
 */
export function getFailedAnswers(params = {}) {
  const { examId, limit = 100 } = params
  return get('/exam/api/weather/grading/failed', {
    params: { examId, limit }
  })
}

/**
 * 获取所有答案列表（未判卷 + 判卷失败）
 * @param {Object} params 查询参数
 */
export function getAnswerList(params = {}) {
  // 先获取未判卷的答案，再获取判卷失败的答案
  return Promise.all([
    getUngradedAnswers(params),
    getFailedAnswers(params)
  ]).then(([ungradedRes, failedRes]) => {
    const ungradedList = ungradedRes.code === 0 ? ungradedRes.data : []
    const failedList = failedRes.code === 0 ? failedRes.data : []

    // 合并两个列表
    const allAnswers = [...ungradedList, ...failedList]

    return {
      code: 0,
      data: {
        records: allAnswers,
        total: allAnswers.length
      }
    }
  })
}

/**
 * 获取答案详情
 * @param {String} answerId 答案ID
 */
export function getAnswerDetail(answerId) {
  return get(`/exam/api/weather/grading/answer/${answerId}`)
}

/**
 * 删除答案
 * @param {String} answerId 答案ID
 */
export function deleteAnswer(answerId) {
  return post('/exam/api/weather/answer/delete', { id: answerId })
}

// ==================== 评分管理 ====================

/**
 * 单个答案评分
 * @param {String} answerId 答案ID
 * @param {String} configId 评分配置ID（可选，历史个例不需要）
 */
export function calculateScore(answerId, configId = null) {
  // 使用URLSearchParams来构建表单数据
  const params = new URLSearchParams()
  params.append('answerId', answerId)
  if (configId) {
    params.append('configId', configId)
  }
  params.append('forceRegrade', 'false')

  return post('/exam/api/weather/grading/single?' + params.toString())
}

/**
 * 批量评分
 * @param {Array} answerIds 答案ID列表
 * @param {String} configId 评分配置ID（可选，历史个例不需要）
 * @param {String} batchName 批次名称
 */
export function batchCalculateScore(answerIds, configId = null, batchName = null) {
  const answerIdsStr = answerIds.join(',')

  // 使用URLSearchParams来构建表单数据
  const params = new URLSearchParams()
  params.append('answerIds', answerIdsStr)
  if (configId) {
    params.append('configId', configId)
  }
  params.append('forceRegrade', 'false')

  return post('/exam/api/weather/grading/batch/answers?' + params.toString())
}

/**
 * 根据条件批量评分
 * @param {Object} params 条件参数
 */
export function batchCalculateByCondition(params) {
  return post('/exam/api/weather/scoring/batch/condition', params)
}

/**
 * 重新计算评分
 * @param {String} answerId 答案ID
 * @param {String} configId 新的配置ID（可选）
 */
export function recalculateScore(answerId, configId = null) {
  return post('/exam/api/weather/scoring/recalculate', {
    answerId,
    configId
  })
}

// ==================== 评分结果 ====================

/**
 * 获取评分结果详情
 * @param {String} answerId 答案ID
 */
export function getScoringResult(answerId) {
  return get(`/exam/api/weather/grading/result/${answerId}`)
}

/**
 * 分页查询评分结果
 * @param {Object} params 查询参数
 */
export function getScoringResultList(params) {
  return post('/exam/api/weather/scoring/result/paging', params)
}

/**
 * 导出评分结果
 * @param {String} answerId 答案ID
 */
export function exportScoringResult(answerId) {
  return post('/exam/api/weather/scoring/export', {
    answerId
  }, {
    responseType: 'blob'
  })
}

/**
 * 批量导出评分结果
 * @param {Array} answerIds 答案ID列表
 */
export function batchExportScoringResult(answerIds) {
  return post('/exam/api/weather/scoring/batch/export', {
    answerIds
  }, {
    responseType: 'blob'
  })
}

// ==================== 批量任务管理 ====================

/**
 * 获取批量任务状态
 * @param {String} taskId 任务ID
 */
export function getBatchTaskStatus(taskId) {
  return get(`/exam/api/weather/grading/task/status/${taskId}`)
}

/**
 * 获取批量任务详情
 * @param {String} taskId 任务ID
 */
export function getBatchTaskDetail(taskId) {
  return post('/exam/api/weather/scoring/batch/detail', {
    taskId
  })
}

/**
 * 分页查询批量任务
 * @param {Object} params 查询参数
 */
export function getBatchTaskList(params) {
  return post('/exam/api/weather/scoring/batch/paging', params)
}

/**
 * 取消批量任务
 * @param {String} taskId 任务ID
 */
export function cancelBatchTask(taskId) {
  return post('/exam/api/weather/scoring/batch/cancel', {
    taskId
  })
}

/**
 * 重试失败的批量任务
 * @param {String} taskId 任务ID
 */
export function retryBatchTask(taskId) {
  return post('/exam/api/weather/scoring/batch/retry', {
    taskId
  })
}

/**
 * 获取用户的任务历史
 * @param {String} userId 用户ID
 */
export function getUserTaskHistory(userId) {
  return post('/exam/api/weather/scoring/batch/user-history', {
    userId
  })
}

// ==================== 评分配置管理 ====================

/**
 * 获取评分配置列表
 */
export function getScoringConfigList() {
  return post('/exam/api/weather/scoring/config/list')
}

/**
 * 获取评分配置详情
 * @param {String} configId 配置ID
 */
export function getScoringConfigDetail(configId) {
  return post('/exam/api/weather/scoring/config/detail', {
    id: configId
  })
}

/**
 * 保存评分配置
 * @param {Object} config 配置数据
 */
export function saveScoringConfig(config) {
  return post('/exam/api/weather/scoring/config/save', config)
}

/**
 * 删除评分配置
 * @param {String} configId 配置ID
 */
export function deleteScoringConfig(configId) {
  return post('/exam/api/weather/scoring/config/delete', {
    id: configId
  })
}

/**
 * 激活评分配置
 * @param {String} configId 配置ID
 */
export function activateScoringConfig(configId) {
  return post('/exam/api/weather/scoring/config/activate', {
    id: configId
  })
}

// ==================== 统计分析 ====================

/**
 * 获取考试评分统计
 * @param {String} examId 考试ID
 */
export function getExamScoringStats(examId) {
  return post('/exam/api/weather/scoring/stats/exam', {
    examId
  })
}

/**
 * 获取站点评分统计
 * @param {String} stationCode 站点代码
 */
export function getStationScoringStats(stationCode) {
  return post('/exam/api/weather/scoring/stats/station', {
    stationCode
  })
}

/**
 * 获取系统整体评分统计
 */
export function getSystemScoringStats() {
  return post('/exam/api/weather/scoring/stats/system')
}

/**
 * 获取评分趋势统计
 * @param {Object} params 查询参数
 */
export function getScoringTrendStats(params) {
  return post('/exam/api/weather/scoring/stats/trend', params)
}

// ==================== 系统监控 ====================

/**
 * 获取评分系统健康状态
 */
export function getScoringSystemHealth() {
  return post('/exam/api/weather/scoring/health')
}

/**
 * 获取评分系统性能指标
 */
export function getScoringSystemMetrics() {
  return post('/exam/api/weather/scoring/metrics')
}

/**
 * 清理过期数据
 * @param {Number} retentionDays 保留天数
 */
export function cleanupExpiredData(retentionDays) {
  return post('/exam/api/weather/scoring/cleanup', {
    retentionDays
  })
}
