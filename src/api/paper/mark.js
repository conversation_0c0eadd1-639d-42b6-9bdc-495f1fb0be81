import { post, get } from '@/utils/request'

/**
 * 获取试卷数据
 * @param {Object} data 试卷ID
 * @returns {Promise}
 */
export function getPaperData(data) {
  return post('/exam/api/paper/paper/paper-detail2', data)
}

/**
 * 获取考试信息
 * @param {Object} data 考试ID
 * @returns {Promise}
 */
export function getExamInfo(data) {
  return get('/exam/api/exam/exam/info', data)
}

/**
 * 提交阅卷结果
 * @param {Object} data 阅卷数据
 * @returns {Promise}
 */
export function submitMarkResult(data) {
  return post('/exam/api/paper/paper/mark', data)
}
