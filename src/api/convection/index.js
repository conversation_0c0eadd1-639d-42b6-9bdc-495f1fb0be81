import ConvectionAnswerAPI from './answer'
import ConvectionGradingAPI from './grading'
import ConvectionCaseAPI from './case'
import ConvectionExamAPI from './exam'
import ConvectionQuestionAPI from './question'

// 统一导出所有强对流相关API
export {
  ConvectionAnswerAPI,
  ConvectionGradingAPI,
  ConvectionCaseAPI,
  ConvectionExamAPI,
  ConvectionQuestionAPI
}

// 默认导出一个包含所有API的对象
export default {
  answer: ConvectionAnswerAPI,
  grading: ConvectionGradingAPI,
  case: ConvectionCaseAPI,
  exam: ConvectionExamAPI,
  question: ConvectionQuestionAPI
}
