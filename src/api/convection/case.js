import request from '@/utils/request'

const ConvectionCaseAPI = {
  /**
   * 分页查询强对流试题
   * @param {Object} params 查询参数
   */
  paging(params) {
    return request({
      url: '/api/qu/paging',
      method: 'post',
      data: {
        ...params,
        params: {
          ...params.params,
          quType: 7 // 强对流试题类型
        }
      }
    })
  },

  /**
   * 保存强对流试题
   * @param {Object} data 试题数据
   */
  save(data) {
    return request({
      url: '/api/qu/save',
      method: 'post',
      data: {
        ...data,
        quType: 7, // 强制设置为强对流试题类型
        isConvectionType: true
      }
    })
  },

  /**
   * 获取强对流试题详情
   * @param {String} id 试题ID
   */
  getDetail(id) {
    return request({
      url: `/api/qu/detail/${id}`,
      method: 'get'
    })
  },

  /**
   * 删除强对流试题
   * @param {String} id 试题ID
   */
  delete(id) {
    return request({
      url: `/api/qu/delete/${id}`,
      method: 'delete'
    })
  },

  /**
   * 批量删除强对流试题
   * @param {Array} ids 试题ID数组
   */
  batchDelete(ids) {
    return request({
      url: '/api/qu/batch-delete',
      method: 'post',
      data: { ids }
    })
  },

  /**
   * 更新试题状态
   * @param {String} id 试题ID
   * @param {Number} state 状态
   */
  updateState(id, state) {
    return request({
      url: '/api/qu/update-state',
      method: 'post',
      data: { id, state }
    })
  },

  /**
   * 获取强对流试题统计信息
   */
  getStatistics() {
    return request({
      url: '/api/qu/convection-statistics',
      method: 'get'
    })
  },

  /**
   * 验证试题数据
   * @param {Object} data 试题数据
   */
  validate(data) {
    return request({
      url: '/api/qu/validate',
      method: 'post',
      data
    })
  },

  /**
   * 上传MICAPS文件
   * @param {FormData} formData 文件数据
   */
  uploadMicapsFile(formData) {
    return request({
      url: '/api/file/upload-micaps',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 上传强对流落区文件
   * @param {FormData} formData 文件数据
   */
  uploadAreaFile(formData) {
    return request({
      url: '/api/file/upload-convection-area',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 预览试题（学生视角）
   * @param {String} id 试题ID
   */
  preview(id) {
    return request({
      url: `/api/qu/preview/${id}`,
      method: 'get'
    })
  }
}

export default ConvectionCaseAPI
