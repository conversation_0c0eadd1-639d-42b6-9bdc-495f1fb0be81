import request from '@/utils/request'

// 强对流考试核心API
export const convectionApi = {
  // ========== 考试相关接口 ==========

  /**
   * 获取考试列表
   * @param {Object} params 查询参数
   */
  getExamList(params) {
    return request({
      url: '/convection/exam/list',
      method: 'get',
      params
    })
  },

  /**
   * 获取考试详情
   * @param {String} examId 考试ID
   */
  getExamDetail(examId) {
    return request({
      url: `/convection/exam/${examId}`,
      method: 'get'
    })
  },

  /**
   * 开始考试
   * @param {String} examId 考试ID
   */
  startExam(examId) {
    return request({
      url: `/convection/exam/${examId}/start`,
      method: 'post'
    })
  },

  /**
   * 提交考试答案
   * @param {Object} data 提交数据
   */
  submitExam(data) {
    return request({
      url: '/convection/exam/submit',
      method: 'post',
      data
    })
  },

  /**
   * 保存答案草稿
   * @param {Object} data 草稿数据
   */
  saveAnswerDraft(data) {
    return request({
      url: '/convection/answer/draft',
      method: 'post',
      data
    })
  },

  /**
   * 获取已有答案
   * @param {String} examId 考试ID
   * @param {String} questionId 题目ID
   */
  getExistingAnswer(examId, questionId) {
    return request({
      url: `/convection/answer/${examId}/${questionId}`,
      method: 'get'
    })
  },

  // ========== 试题相关接口 ==========

  /**
   * 获取试题列表
   * @param {Object} params 查询参数
   */
  getQuestionList(params) {
    return request({
      url: '/exam/api/convection/question/list',
      method: 'get',
      params
    })
  },

  /**
   * 获取题目详情
   * @param {String} questionId 题目ID
   */
  getQuestionDetail(questionId) {
    return request({
      url: `/exam/api/convection/question/${questionId}`,
      method: 'get'
    })
  },

  /**
   * 创建试题
   * @param {Object} data 试题数据
   */
  createQuestion(data) {
    return request({
      url: '/exam/api/convection/question',
      method: 'post',
      data
    })
  },

  /**
   * 更新试题
   * @param {String} questionId 题目ID
   * @param {Object} data 更新数据
   */
  updateQuestion(questionId, data) {
    return request({
      url: `/exam/api/convection/question/${questionId}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除试题
   * @param {String} questionId 题目ID
   */
  deleteQuestion(questionId) {
    return request({
      url: `/exam/api/convection/question/${questionId}`,
      method: 'delete'
    })
  },

  /**
   * 上传MICAPS文件
   * @param {FormData} formData 文件数据
   */
  uploadMicapsFile(formData) {
    return request({
      url: '/exam/api/convection/question/upload/micaps',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 上传强对流落区文件
   * @param {FormData} formData 文件数据
   */
  uploadConvectionAreaFile(formData) {
    return request({
      url: '/exam/api/convection/question/upload/area',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 获取标准答案
   * @param {String} questionId 题目ID
   */
  getStandardAnswer(questionId) {
    return request({
      url: `/exam/api/convection/question/${questionId}/standard-answer`,
      method: 'get'
    })
  },

  /**
   * 设置标准答案
   * @param {String} questionId 题目ID
   * @param {Object} data 标准答案数据
   */
  setStandardAnswer(questionId, data) {
    return request({
      url: `/exam/api/convection/question/${questionId}/standard-answer`,
      method: 'post',
      data
    })
  },

  // ========== 评分相关接口 ==========

  /**
   * 获取考试结果
   * @param {String} examId 考试ID
   */
  getExamResult(examId) {
    return request({
      url: `/convection/result/${examId}`,
      method: 'get'
    })
  },

  /**
   * 获取评分详情
   * @param {String} examId 考试ID
   * @param {String} userId 用户ID
   */
  getScoreDetail(examId, userId) {
    return request({
      url: `/convection/score/${examId}/${userId}`,
      method: 'get'
    })
  },

  /**
   * 重新评分
   * @param {String} examId 考试ID
   * @param {String} userId 用户ID
   */
  rescoreExam(examId, userId) {
    return request({
      url: `/convection/score/${examId}/${userId}/rescore`,
      method: 'post'
    })
  },

  // ========== 文件下载接口 ==========

  /**
   * 下载MICAPS文件
   * @param {String} fileId 文件ID
   */
  downloadMicapsFile(fileId) {
    return request({
      url: `/convection/file/micaps/${fileId}`,
      method: 'get',
      responseType: 'blob'
    })
  },

  /**
   * 获取文件下载URL
   * @param {String} fileId 文件ID
   * @param {String} fileType 文件类型
   */
  getFileDownloadUrl(fileId, fileType) {
    return request({
      url: `/convection/file/download-url/${fileId}`,
      method: 'get',
      params: { fileType }
    })
  },

  // ========== 统计相关接口 ==========

  /**
   * 获取考试统计数据
   * @param {String} examId 考试ID
   */
  getExamStatistics(examId) {
    return request({
      url: `/convection/statistics/exam/${examId}`,
      method: 'get'
    })
  },

  /**
   * 获取部门考试统计
   * @param {Object} params 查询参数
   */
  getDepartmentStatistics(params) {
    return request({
      url: '/convection/statistics/department',
      method: 'get',
      params
    })
  }
}

// 默认导出
export default convectionApi
