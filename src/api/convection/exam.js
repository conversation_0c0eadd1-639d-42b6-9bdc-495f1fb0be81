import request from '@/utils/request'

const ConvectionExamAPI = {
  /**
   * 分页查询强对流考试列表（管理端）
   * @param {Object} params 查询参数
   */
  paging(params) {
    return request({
      url: '/api/exam/paging',
      method: 'post',
      data: {
        ...params,
        params: {
          ...params.params,
          examType: 'convection'
        }
      }
    })
  },

  /**
   * 分页查询在线强对流考试列表（学生端）
   * @param {Object} params 查询参数
   */
  onlinePaging(params) {
    return request({
      url: '/api/exam/online-paging',
      method: 'post',
      data: {
        ...params,
        params: {
          ...params.params,
          examType: 'convection'
        }
      }
    })
  },

  /**
   * 保存强对流考试
   * @param {Object} data 考试数据
   */
  save(data) {
    return request({
      url: '/api/exam/save',
      method: 'post',
      data: {
        ...data,
        examType: 'convection'
      }
    })
  },

  /**
   * 获取强对流考试详情
   * @param {String} id 考试ID
   */
  getDetail(id) {
    return request({
      url: `/api/exam/detail/${id}`,
      method: 'get'
    })
  },

  /**
   * 删除强对流考试
   * @param {String} id 考试ID
   */
  delete(id) {
    return request({
      url: `/api/exam/delete/${id}`,
      method: 'delete'
    })
  },

  /**
   * 开始考试
   * @param {String} examId 考试ID
   */
  start(examId) {
    return request({
      url: '/api/exam/start',
      method: 'post',
      data: { examId }
    })
  },

  /**
   * 结束考试
   * @param {String} examId 考试ID
   */
  end(examId) {
    return request({
      url: '/api/exam/end',
      method: 'post',
      data: { examId }
    })
  },

  /**
   * 获取考试结果
   * @param {String} examId 考试ID
   * @param {String} userId 用户ID
   */
  getResult(examId, userId) {
    return request({
      url: `/api/exam/result/${examId}`,
      method: 'get',
      params: { userId }
    })
  },

  /**
   * 获取考试统计信息
   * @param {String} examId 考试ID
   */
  getStatistics(examId) {
    return request({
      url: `/api/exam/statistics/${examId}`,
      method: 'get'
    })
  },

  /**
   * 更新考试状态
   * @param {String} id 考试ID
   * @param {Number} state 状态
   */
  updateState(id, state) {
    return request({
      url: '/api/exam/update-state',
      method: 'post',
      data: { id, state }
    })
  },

  /**
   * 发布考试
   * @param {String} id 考试ID
   */
  publish(id) {
    return request({
      url: `/api/exam/publish/${id}`,
      method: 'post'
    })
  },

  /**
   * 取消发布考试
   * @param {String} id 考试ID
   */
  unpublish(id) {
    return request({
      url: `/api/exam/unpublish/${id}`,
      method: 'post'
    })
  },

  /**
   * 获取考试参与人员列表
   * @param {String} examId 考试ID
   * @param {Object} params 查询参数
   */
  getParticipants(examId, params) {
    return request({
      url: `/api/exam/participants/${examId}`,
      method: 'post',
      data: params
    })
  },

  /**
   * 导出考试结果
   * @param {String} examId 考试ID
   * @param {Object} params 导出参数
   */
  exportResults(examId, params) {
    return request({
      url: `/api/exam/export-results/${examId}`,
      method: 'post',
      data: params,
      responseType: 'blob'
    })
  },

  /**
   * 检查考试权限
   * @param {String} examId 考试ID
   */
  checkPermission(examId) {
    return request({
      url: `/api/exam/check-permission/${examId}`,
      method: 'get'
    })
  },

  /**
   * 获取我的强对流考试列表
   * @param {Object} params 查询参数
   */
  getMyExams(params) {
    return request({
      url: '/api/exam/my-convection-exams',
      method: 'post',
      data: params
    })
  }
}

export default ConvectionExamAPI
