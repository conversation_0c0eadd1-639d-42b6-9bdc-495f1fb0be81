import request from '@/utils/request'

const ConvectionAnswerAPI = {
  /**
   * 保存强对流考试答案
   * @param {Object} data 答案数据
   */
  save(data) {
    return request({
      url: '/api/convection/answer/save',
      method: 'post',
      data: {
        answerId: data.answerId,
        examId: data.examId,
        questionId: data.questionId,
        stationAnswer: data.stationAnswer,
        areaAnswer: data.areaAnswer,
        forecastReasoning: data.forecastReasoning
      }
    })
  },

  /**
   * 获取学生答案详情
   * @param {String} examId 考试ID
   * @param {String} userId 用户ID
   */
  getStudentAnswer(examId, userId) {
    return request({
      url: '/api/convection/answer/student-answer',
      method: 'get',
      params: { examId, userId }
    })
  },

  /**
   * 提交强对流考试
   * @param {Object} data 提交数据
   */
  submit(data) {
    return request({
      url: '/api/convection/answer/submit',
      method: 'post',
      data
    })
  },

  /**
   * 计算答题进度
   * @param {String} answerId 答案ID
   */
  calculateProgress(answerId) {
    return request({
      url: `/api/convection/answer/progress/${answerId}`,
      method: 'get'
    })
  },

  /**
   * 获取答案统计信息
   * @param {String} examId 考试ID
   */
  getStatistics(examId) {
    return request({
      url: `/api/convection/answer/statistics/${examId}`,
      method: 'get'
    })
  },

  /**
   * 获取答案详情（用于预览）
   * @param {String} answerId 答案ID
   */
  getDetail(answerId) {
    return request({
      url: `/api/convection/answer/detail/${answerId}`,
      method: 'get'
    })
  },

  /**
   * 获取待批卷答案列表
   * @param {Object} params 查询参数
   */
  getPendingGradingList(params) {
    return request({
      url: '/api/convection/answer/pending-grading',
      method: 'post',
      data: params
    })
  }
}

export default ConvectionAnswerAPI
