import request from '@/utils/request'

// 强对流人工批卷API
export const gradingApi = {
  // ========== 批卷任务管理接口 ==========

  /**
   * 获取批卷任务列表
   * @param {Object} params 查询参数
   */
  getGradingTaskList(params) {
    return request({
      url: '/convection/grading/task/list',
      method: 'get',
      params
    })
  },

  /**
   * 获取批卷任务详情
   * @param {String} taskId 任务ID
   */
  getGradingTask(taskId) {
    return request({
      url: `/convection/grading/task/${taskId}`,
      method: 'get'
    })
  },

  /**
   * 分配批卷任务
   * @param {Object} data 分配数据
   */
  assignGradingTask(data) {
    return request({
      url: '/convection/grading/task/assign',
      method: 'post',
      data
    })
  },

  /**
   * 批量分配批卷任务
   * @param {Object} data 批量分配数据
   */
  batchAssignGradingTask(data) {
    return request({
      url: '/convection/grading/task/batch-assign',
      method: 'post',
      data
    })
  },

  /**
   * 重新分配批卷任务
   * @param {String} taskId 任务ID
   * @param {Object} data 重新分配数据
   */
  reassignGradingTask(taskId, data) {
    return request({
      url: `/convection/grading/task/${taskId}/reassign`,
      method: 'put',
      data
    })
  },

  /**
   * 获取我的批卷任务
   * @param {Object} params 查询参数
   */
  getMyGradingTasks(params) {
    return request({
      url: '/convection/grading/task/my',
      method: 'get',
      params
    })
  },

  // ========== 批卷操作接口 ==========

  /**
   * 获取答案对比数据
   * @param {String} taskId 任务ID
   */
  getAnswerComparison(taskId) {
    return request({
      url: `/convection/grading/comparison/${taskId}`,
      method: 'get'
    })
  },

  /**
   * 开始批卷
   * @param {String} taskId 任务ID
   */
  startGrading(taskId) {
    return request({
      url: `/convection/grading/task/${taskId}/start`,
      method: 'post'
    })
  },

  /**
   * 保存批卷草稿
   * @param {Object} data 草稿数据
   */
  saveGradingDraft(data) {
    return request({
      url: '/convection/grading/draft',
      method: 'post',
      data
    })
  },

  /**
   * 提交批卷结果
   * @param {Object} data 批卷结果数据
   */
  submitGradingResult(data) {
    return request({
      url: '/convection/grading/submit',
      method: 'post',
      data
    })
  },

  /**
   * 更新评分
   * @param {String} taskId 任务ID
   * @param {Object} data 评分数据
   */
  updateGradingScore(taskId, data) {
    return request({
      url: `/convection/grading/task/${taskId}/score`,
      method: 'put',
      data
    })
  },

  /**
   * 获取批卷历史
   * @param {String} taskId 任务ID
   */
  getGradingHistory(taskId) {
    return request({
      url: `/convection/grading/task/${taskId}/history`,
      method: 'get'
    })
  },

  // ========== AI评分建议接口 ==========

  /**
   * 获取AI评分建议
   * @param {String} taskId 任务ID
   */
  getAISuggestions(taskId) {
    return request({
      url: `/convection/grading/ai-suggestions/${taskId}`,
      method: 'get'
    })
  },

  /**
   * 获取关键词匹配分析
   * @param {String} taskId 任务ID
   */
  getKeywordAnalysis(taskId) {
    return request({
      url: `/convection/grading/keyword-analysis/${taskId}`,
      method: 'get'
    })
  },

  /**
   * 获取文本相似度分析
   * @param {String} taskId 任务ID
   */
  getSimilarityAnalysis(taskId) {
    return request({
      url: `/convection/grading/similarity-analysis/${taskId}`,
      method: 'get'
    })
  },

  // ========== 批卷统计接口 ==========

  /**
   * 获取批卷统计数据
   * @param {Object} params 查询参数
   */
  getGradingStatistics(params) {
    return request({
      url: '/convection/grading/statistics',
      method: 'get',
      params
    })
  },

  /**
   * 获取批卷进度统计
   * @param {String} examId 考试ID
   */
  getGradingProgress(examId) {
    return request({
      url: `/convection/grading/progress/${examId}`,
      method: 'get'
    })
  },

  /**
   * 获取批卷质量统计
   * @param {Object} params 查询参数
   */
  getGradingQualityStats(params) {
    return request({
      url: '/convection/grading/quality-stats',
      method: 'get',
      params
    })
  },

  /**
   * 获取批卷教师工作负载
   * @param {Object} params 查询参数
   */
  getGraderWorkload(params) {
    return request({
      url: '/convection/grading/workload',
      method: 'get',
      params
    })
  },

  // ========== 批卷配置接口 ==========

  /**
   * 获取批卷配置
   */
  getGradingConfig() {
    return request({
      url: '/convection/grading/config',
      method: 'get'
    })
  },

  /**
   * 更新批卷配置
   * @param {Object} data 配置数据
   */
  updateGradingConfig(data) {
    return request({
      url: '/convection/grading/config',
      method: 'put',
      data
    })
  },

  /**
   * 获取评分标准
   */
  getGradingCriteria() {
    return request({
      url: '/convection/grading/criteria',
      method: 'get'
    })
  },

  /**
   * 更新评分标准
   * @param {Object} data 评分标准数据
   */
  updateGradingCriteria(data) {
    return request({
      url: '/convection/grading/criteria',
      method: 'put',
      data
    })
  },

  // ========== 批卷审核接口 ==========

  /**
   * 获取待审核的批卷结果
   * @param {Object} params 查询参数
   */
  getPendingReviews(params) {
    return request({
      url: '/convection/grading/review/pending',
      method: 'get',
      params
    })
  },

  /**
   * 审核批卷结果
   * @param {String} taskId 任务ID
   * @param {Object} data 审核数据
   */
  reviewGradingResult(taskId, data) {
    return request({
      url: `/convection/grading/review/${taskId}`,
      method: 'post',
      data
    })
  },

  /**
   * 批量审核批卷结果
   * @param {Object} data 批量审核数据
   */
  batchReviewGradingResults(data) {
    return request({
      url: '/convection/grading/review/batch',
      method: 'post',
      data
    })
  },

  // ========== 批卷导出接口 ==========

  /**
   * 导出批卷结果
   * @param {Object} params 导出参数
   */
  exportGradingResults(params) {
    return request({
      url: '/convection/grading/export',
      method: 'get',
      params,
      responseType: 'blob'
    })
  },

  /**
   * 导出批卷统计报告
   * @param {Object} params 导出参数
   */
  exportGradingReport(params) {
    return request({
      url: '/convection/grading/export/report',
      method: 'get',
      params,
      responseType: 'blob'
    })
  }
}

export default gradingApi
