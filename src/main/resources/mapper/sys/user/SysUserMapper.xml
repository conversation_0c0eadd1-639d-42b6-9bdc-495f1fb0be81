<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.exam.modules.sys.user.mapper.SysUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.exam.modules.sys.user.entity.SysUser">
        <id column="id" property="id" />
        <result column="user_name" property="userName" />
        <result column="real_name" property="realName" />
        <result column="password" property="password" />
        <result column="salt" property="salt" />
        <result column="role_ids" property="roleIds" />
        <result column="depart_id" property="departId" />
        <result column="group_name" property="groupName" />
        <result column="phone" property="phone" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="state" property="state" />
        <result column="score" property="score" />
    </resultMap>

    <!-- 过滤后的查询结果映射 -->
    <resultMap id="FilteredResultMap" type="com.yf.exam.modules.sys.user.dto.response.SysUserFilteredDTO">
        <id column="id" property="id" />
        <result column="user_name" property="userName" />
        <result column="real_name" property="realName" />
        <result column="depart_id" property="departId" />
        <result column="dept_name" property="deptName" />
        <result column="group_name" property="groupName" />
        <result column="phone" property="phone" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="state" property="state" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`,`user_name`,`real_name`,`password`,`salt`,`role_ids`,`depart_id`,`group_name`,`phone`,`create_time`,`update_time`,`state`,`score`
    </sql>

    <!-- 分页查询，包含单位信息 -->
    <select id="paging" resultMap="FilteredResultMap">
        SELECT 
            u.id,
            u.user_name,
            u.real_name,
            u.depart_id,
            d.dept_name,
            u.group_name,
            u.phone,
            u.create_time,
            u.update_time,
            u.state
        FROM sys_user u
        LEFT JOIN sys_depart d ON u.depart_id = d.id
        <where>
            <if test="params != null">
                <if test="params.userName != null and params.userName != ''">
                    AND u.user_name LIKE CONCAT('%', #{params.userName}, '%')
                </if>
                <if test="params.realName != null and params.realName != ''">
                    AND u.real_name LIKE CONCAT('%', #{params.realName}, '%')
                </if>
                <if test="params.departId != null and params.departId != ''">
                    AND u.depart_id = #{params.departId}
                </if>
                <if test="params.state != null">
                    AND u.state = #{params.state}
                </if>
                <if test="params.groupName != null and params.groupName != ''">
                    AND u.group_name = #{params.groupName}
                </if>
            </if>
        </where>
        ORDER BY u.create_time DESC
    </select>

    <select id="getRankingUser" resultType="com.yf.exam.modules.sys.user.entity.SysUser">
        SELECT * FROM sys_user WHERE group_name = (SELECT group_name FROM sys_user WHERE id = #{userId}) ORDER BY score DESC
    </select>

</mapper>
