<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.exam.modules.exam.mapper.ExamMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.exam.modules.exam.entity.Exam">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="content" property="content" />
        <result column="open_type" property="openType" />
        <result column="state" property="state" />
        <result column="time_limit" property="timeLimit" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="total_score" property="totalScore" />
        <result column="total_time" property="totalTime" />
        <result column="qualify_score" property="qualifyScore" />
        <result column="monitor_mouse" property="monitorMouse" />
        <result column="hide_student_name" property="hideStudentName" />
        <result column="show_result" property="showResult" />
        <result column="question_id" property="questionId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`,`title`,`content`,`open_type`,`join_type`,`level`,`state`,`time_limit`,`start_time`,`end_time`,`create_time`,`update_time`,`total_score`,`total_time`,`qualify_score`,`monitor_mouse`,`hide_student_name`,`show_result`,`question_id`
    </sql>


    <resultMap id="ReviewResultMap"
               type="com.yf.exam.modules.exam.dto.response.ExamReviewRespDTO"
               extends="BaseResultMap">
        <result column="examUser" property="examUser" />
        <result column="unreadPaper" property="unreadPaper" />
    </resultMap>

    <resultMap id="ExamMarkRespDTO"
               type="com.yf.exam.modules.exam.dto.response.ExamMarkRespDTO"
               extends="BaseResultMap">
        <result column="userName" property="userName" />
        <result column="objScore" property="objScore" />
        <result column="subjScore" property="subjScore" />
        <result column="paperState" property="paperState" />
        <result column="paperTime" property="paperTime" />
        <result column="paperId" property="paperId"></result>
    </resultMap>

    <resultMap id="ListResultMap"
               type="com.yf.exam.modules.exam.dto.ExamDTO"
    extends="BaseResultMap">
        <result column="questionTitle" property="questionTitle" />
    </resultMap>

    <select id="paging" resultMap="ListResultMap">
        SELECT ex.*, qu.title as questionTitle
        FROM el_exam ex
        LEFT JOIN el_qu qu ON ex.question_id = qu.id
        <where>
            <if test="query!=null">
                <if test="query.title!=null and query.title!=''">
                    AND title LIKE CONCAT('%',#{query.title},'%')
                </if>
                <if test="query.openType!=null">
                    AND open_type = #{query.openType}
                </if>
                <if test="query.startTime!=null">
                    AND start_time &gt;= #{query.startTime}
                </if>
                <if test="query.endTime!=null">
                    AND end_time &lt;= #{query.endTime}
                </if>

                <!-- 考试类型过滤：区分理论考试和历史个例考试 -->
                <if test="query.examType!=null and query.examType=='weather'">
                    AND ex.question_id IS NOT NULL
                </if>
                <if test="query.examType!=null and query.examType=='traditional'">
                    AND ex.question_id IS NULL
                </if>
            </if>

        AND ex.id IN (SELECT exam_id FROM el_exam_depart WHERE depart_id = (SELECT depart_id FROM sys_user WHERE id = #{userId}))
        </where>
    </select>

    <select id="reviewPaging" resultMap="ReviewResultMap">
        SELECT ex.*,
        (SELECT COUNT(DISTINCT user_id) FROM el_paper WHERE exam_id=ex.id) as examUser,
        (SELECT COUNT(0) FROM el_paper WHERE exam_id=ex.id AND state=1) as unreadPaper
        FROM el_exam ex
        WHERE ex.has_saq=1
    </select>

    <select id="markPaging" resultMap="ExamMarkRespDTO">
        SELECT 
            ex.*,
            u.real_name as userName,
            p.obj_score as objScore,
            p.subj_score as subjScore,
            p.state as paperState,
            p.id as paperId,
            p.update_time as paperTime
        FROM el_paper p
        INNER JOIN el_exam ex ON p.exam_id = ex.id
        INNER JOIN sys_user u ON p.user_id = u.id
        WHERE p.has_saq=1
        <if test="query != null and query.userName != null and query.userName != ''">
            AND u.real_name LIKE CONCAT('%',#{query.userName},'%')
        </if>
        <if test="query != null and query.title != null and query.title != ''">
            AND p.title LIKE CONCAT('%',#{query.title},'%')
        </if>
        AND ex.id IN (SELECT exam_id FROM el_exam_depart WHERE depart_id = (SELECT depart_id FROM sys_user WHERE id = #{userId}))
        ORDER BY ex.id, u.real_name
    </select>


    <resultMap id="OnlineResultMap"
               type="com.yf.exam.modules.exam.dto.response.ExamOnlineRespDTO"
               extends="BaseResultMap">
        <result column="calculated_state" property="calculatedState" jdbcType="INTEGER"/>
        <result column="user_answer_id" property="userAnswerId" jdbcType="VARCHAR"/>
        <result column="user_answer_state" property="userAnswerState" jdbcType="INTEGER"/>
        <result column="user_total_score" property="userTotalScore" jdbcType="DECIMAL"/>
        <result column="user_progress" property="userProgress" jdbcType="INTEGER"/>
        <result column="time_spent" property="timeSpent" jdbcType="INTEGER"/>
        <result column="answer_time" property="answerTime" jdbcType="TIMESTAMP"/>
        <result column="submit_time" property="submitTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="online" resultMap="OnlineResultMap">
        SELECT DISTINCT ex.id,
               ex.title,
               ex.content,
               ex.start_time,
               ex.end_time,
               ex.total_time,
               ex.total_score,
               ex.qualify_score,
               ex.question_id,
               ex.state,
               ex.time_limit,
               ex.open_type,
               ex.create_time,
               ex.update_time,
               ex.monitor_mouse,
               ex.hide_student_name,
               ex.show_result,
               -- 在SQL中计算考试状态，避免在Java代码中逐个计算
               CASE
                   WHEN ex.time_limit = 1 THEN
                       CASE
                           WHEN NOW() &lt; ex.start_time THEN 2
                           WHEN NOW() &gt; ex.end_time THEN 3
                           WHEN NOW() &gt;= ex.start_time AND NOW() &lt;= ex.end_time AND ex.state != 1 THEN 0
                           ELSE ex.state
                       END
                   ELSE ex.state
               END AS calculated_state,
               -- 查询用户是否已经参加过考试，避免前端再次调用check-status接口
               wea.id AS user_answer_id,
               wea.answer_status AS user_answer_state,
               wea.total_score AS user_total_score,
               wea.overall_progress AS user_progress,
               wea.time_spent AS time_spent,
               wea.answer_time AS answer_time,
               wea.submit_time AS submit_time
        FROM el_exam ex
        LEFT JOIN el_exam_depart dept ON ex.id COLLATE utf8mb4_general_ci = dept.exam_id COLLATE utf8mb4_general_ci AND ex.open_type=2
        LEFT JOIN sys_user uc ON uc.depart_id COLLATE utf8mb4_general_ci = dept.depart_id COLLATE utf8mb4_general_ci
        LEFT JOIN el_weather_history_exam_answer wea ON ex.id COLLATE utf8mb4_general_ci = wea.exam_id COLLATE utf8mb4_general_ci AND wea.user_id=#{userId}
        WHERE (ex.open_type=1 OR ex.open_type=3 OR uc.id COLLATE utf8mb4_general_ci = #{userId} COLLATE utf8mb4_general_ci)

        <if test="query!=null">
            <if test="query.title!=null and query.title!=''">
                AND ex.title LIKE CONCAT('%',#{query.title},'%')
            </if>

            <if test="query.openType!=null">
                AND ex.open_type=#{query.openType}
            </if>

            <if test="query.state!=null and query.state!=''">
                AND ex.state=#{query.state}
            </if>

            <!-- 过滤历史个例考试：有question_id的是历史个例考试 -->
            <if test="query.examType!=null and query.examType=='weather'">
                AND ex.question_id IS NOT NULL
            </if>

            <!-- 过滤传统考试：没有question_id的是传统考试 -->
            <if test="query.examType!=null and query.examType=='traditional'">
                AND ex.question_id IS NULL
            </if>
        </if>

    </select>

    <!-- 在线考试总数查询-考生视角 -->
    <select id="onlineCount" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT ex.id)
        FROM el_exam ex
        LEFT JOIN el_exam_depart dept ON ex.id=dept.exam_id AND ex.open_type=2
        LEFT JOIN sys_user uc ON uc.depart_id=dept.depart_id
        WHERE (ex.open_type=1 OR ex.open_type=3 OR uc.id=#{userId})

        <if test="query!=null">
            <if test="query.title!=null and query.title!=''">
                AND ex.title LIKE CONCAT('%',#{query.title},'%')
            </if>

            <if test="query.openType!=null">
                AND ex.open_type=#{query.openType}
            </if>

            <if test="query.state!=null and query.state!=''">
                AND ex.state=#{query.state}
            </if>

            <!-- 过滤历史个例考试：有question_id的是历史个例考试 -->
            <if test="query.examType!=null and query.examType=='weather'">
                AND ex.question_id IS NOT NULL
            </if>

            <!-- 过滤传统考试：没有question_id的是传统考试 -->
            <if test="query.examType!=null and query.examType=='traditional'">
                AND ex.question_id IS NULL
            </if>
        </if>
    </select>

</mapper>
