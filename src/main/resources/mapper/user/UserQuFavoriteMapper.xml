<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.exam.modules.user.mapper.UserQuFavoriteMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.exam.modules.user.entity.UserQuFavorite">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="qu_id" property="quId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 详情映射结果 -->
    <resultMap id="DetailResultMap" type="com.yf.exam.modules.user.dto.ext.UserQuFavoriteDetailDTO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="qu_id" property="quId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="content" property="content" />
        <result column="image" property="image" />
        <result column="qu_type" property="quType" />
        <result column="remark" property="remark" />
        <result column="analysis" property="analysis" />
        <result column="right_answer" property="rightAnswer" />
    </resultMap>

    <!-- 分页查询 -->
    <select id="paging" resultMap="DetailResultMap">
        SELECT f.*, q.content, q.image, q.qu_type, q.remark, q.analysis,
               (SELECT GROUP_CONCAT(a.content) 
                FROM el_qu_answer a 
                WHERE a.qu_id = f.qu_id AND a.is_right = 1) as right_answer
        FROM el_user_qu_favorite f
        LEFT JOIN el_qu q ON f.qu_id = q.id
        WHERE f.user_id = #{userId}
        ORDER BY f.create_time DESC
    </select>

    <!-- 查询详情 -->
    <select id="detail" resultMap="DetailResultMap">
        SELECT f.*, q.content, q.image, q.qu_type, q.remark, q.analysis,
               (SELECT GROUP_CONCAT(a.content ORDER BY a.is_right DESC) 
                FROM el_qu_answer a 
                WHERE a.qu_id = f.qu_id AND a.is_right = 1) as right_answer
        FROM el_user_qu_favorite f
        LEFT JOIN el_qu q ON f.qu_id = q.id
        WHERE f.user_id = #{userId} AND f.qu_id = #{quId}
        LIMIT 1
    </select>
</mapper> 