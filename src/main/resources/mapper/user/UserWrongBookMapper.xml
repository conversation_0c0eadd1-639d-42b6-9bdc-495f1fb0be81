<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.exam.modules.user.mapper.UserWrongBookMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.exam.modules.user.entity.UserWrongBook">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="qu_id" property="quId" />
        <result column="qu_type" property="quType" />
        <result column="answer" property="answer" />
        <result column="wrong_count" property="wrongCount" />
        <result column="is_mastered" property="isMastered" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 详情映射结果 -->
    <resultMap id="DetailResultMap" type="com.yf.exam.modules.user.dto.ext.UserWrongBookQuDetailDTO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="qu_id" property="quId" />
        <result column="qu_type" property="quType" />
        <result column="answer" property="answer" />
        <result column="wrong_count" property="wrongCount" />
        <result column="is_mastered" property="isMastered" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="content" property="content" />
        <result column="image" property="image" />
        <result column="remark" property="remark" />
        <result column="analysis" property="analysis" />
        <result column="level" property="level" />
        <result column="right_answer" property="rightAnswer" />
        <result column="wrong_answer" property="wrongAnswer" />
        <result column="wrong_answer_content" property="wrongAnswerContent" />
    </resultMap>

    <!-- 分页查询条件 -->
    <sql id="Paging_Where">
        <where>
            wb.user_id = #{userId}
            <if test="quType != null">
                AND wb.qu_type = #{quType}
            </if>
            <if test="isMastered != null">
                AND wb.is_mastered = #{isMastered}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="paging" resultMap="DetailResultMap">
        SELECT wb.*, q.content, q.image, q.remark, q.analysis, q.level,
               (SELECT GROUP_CONCAT(a.content ORDER BY a.is_right DESC) 
                FROM el_qu_answer a 
                WHERE a.qu_id = wb.qu_id AND a.is_right = 1) as right_answer,
               wb.answer as wrong_answer,
               (SELECT GROUP_CONCAT(a.content) 
                FROM el_qu_answer a 
                WHERE a.qu_id = wb.qu_id AND FIND_IN_SET(a.id, wb.answer)) as wrong_answer_content
        FROM el_user_wrong_book wb
        LEFT JOIN el_qu q ON wb.qu_id = q.id
        WHERE wb.user_id = #{userId}
        <if test="params.quType != null">
            AND wb.qu_type = #{params.quType}
        </if>
        <if test="params.isMastered != null">
            AND wb.is_mastered = #{params.isMastered}
        </if>
        ORDER BY wb.create_time DESC
    </select>

    <!-- 查询详情 -->
    <select id="selectDetailByUserAndQuId" resultMap="DetailResultMap">
        SELECT wb.*, q.content, q.image, q.remark, q.analysis, q.level,
               (SELECT GROUP_CONCAT(a.content ORDER BY a.is_right DESC) 
                FROM el_qu_answer a 
                WHERE a.qu_id = wb.qu_id AND a.is_right = 1) as right_answer,
               wb.answer as wrong_answer,
               (SELECT GROUP_CONCAT(a.content) 
                FROM el_qu_answer a 
                WHERE a.qu_id = wb.qu_id AND FIND_IN_SET(a.id, wb.answer)) as wrong_answer_content
        FROM el_user_wrong_book wb
        LEFT JOIN el_qu q ON wb.qu_id = q.id
        WHERE wb.user_id = #{userId} AND wb.id = #{wrongBookId}
        LIMIT 1
    </select>
</mapper> 