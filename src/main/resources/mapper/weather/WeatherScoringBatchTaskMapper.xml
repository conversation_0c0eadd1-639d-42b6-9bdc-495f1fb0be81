<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.exam.modules.weather.mapper.WeatherScoringBatchTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.exam.modules.weather.entity.WeatherScoringBatchTask">
        <id column="id" property="id" />
        <result column="task_name" property="taskName" />
        <result column="exam_id" property="examId" />
        <result column="question_id" property="questionId" />
        <result column="config_id" property="configId" />
        <result column="total_answers" property="totalAnswers" />
        <result column="processed_answers" property="processedAnswers" />
        <result column="success_count" property="successCount" />
        <result column="fail_count" property="failCount" />
        <result column="status" property="status" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="error_message" property="errorMessage" />
        <result column="result_summary" property="resultSummary" typeHandler="com.yf.exam.config.CustomJacksonTypeHandler" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_name, exam_id, question_id, config_id, 
        total_answers, processed_answers, success_count, fail_count,
        status, start_time, end_time, error_message, result_summary,
        create_time, update_time, create_by
    </sql>

    <!-- 根据多条件查询批量任务 -->
    <select id="selectTasksByConditions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM el_weather_scoring_batch_task
        <where>
            <if test="examId != null and examId != ''">
                AND exam_id = #{examId}
            </if>
            <if test="questionId != null and questionId != ''">
                AND question_id = #{questionId}
            </if>
            <if test="configId != null and configId != ''">
                AND config_id = #{configId}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="createBy != null and createBy != ''">
                AND create_by = #{createBy}
            </if>
            <if test="startDate != null">
                AND DATE(create_time) &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND DATE(create_time) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 批量更新任务状态 -->
    <update id="batchUpdateTaskStatus" parameterType="map">
        UPDATE el_weather_scoring_batch_task 
        SET 
            status = #{newStatus},
            error_message = #{errorMessage},
            update_time = NOW()
        WHERE id IN
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </update>

    <!-- 更新任务结果摘要 -->
    <update id="updateTaskResultSummary" parameterType="map">
        UPDATE el_weather_scoring_batch_task 
        SET 
            result_summary = #{resultSummary,typeHandler=com.yf.exam.config.CustomJacksonTypeHandler},
            update_time = NOW()
        WHERE id = #{taskId}
    </update>

    <!-- 查询任务执行详情（包含关联信息） -->
    <select id="selectTaskWithDetails" resultType="map">
        SELECT 
            t.*,
            c.config_name,
            c.config_version,
            e.exam_name,
            q.title as question_title,
            -- 计算任务耗时
            CASE 
                WHEN t.start_time IS NOT NULL AND t.end_time IS NOT NULL 
                THEN TIMESTAMPDIFF(SECOND, t.start_time, t.end_time)
                ELSE NULL 
            END as duration_seconds,
            -- 计算进度百分比
            CASE 
                WHEN t.total_answers > 0 
                THEN ROUND(t.processed_answers * 100.0 / t.total_answers, 2)
                ELSE 0 
            END as progress_percentage,
            -- 计算成功率
            CASE 
                WHEN t.processed_answers > 0 
                THEN ROUND(t.success_count * 100.0 / t.processed_answers, 2)
                ELSE 0 
            END as success_rate
        FROM el_weather_scoring_batch_task t
        LEFT JOIN el_weather_scoring_config c ON t.config_id = c.id
        LEFT JOIN el_exam e ON t.exam_id = e.id
        LEFT JOIN el_qu q ON t.question_id = q.id
        WHERE t.id = #{taskId}
    </select>

    <!-- 查询任务执行日志/进度历史 -->
    <select id="selectTaskProgressHistory" resultType="map">
        SELECT 
            DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as updateTime,
            processed_answers,
            success_count,
            fail_count,
            status,
            CASE 
                WHEN total_answers > 0 
                THEN ROUND(processed_answers * 100.0 / total_answers, 2)
                ELSE 0 
            END as progressPercentage
        FROM el_weather_scoring_batch_task_history
        WHERE task_id = #{taskId}
        ORDER BY update_time ASC
    </select>

    <!-- 统计任务执行情况（按时间段） -->
    <select id="selectTaskExecutionStats" resultType="map">
        SELECT 
            DATE(create_time) as executeDate,
            COUNT(1) as totalTasks,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completedTasks,
            SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failedTasks,
            SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END) as runningTasks,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pendingTasks,
            SUM(total_answers) as totalAnswersProcessed,
            SUM(success_count) as totalSuccessCount,
            AVG(CASE 
                WHEN start_time IS NOT NULL AND end_time IS NOT NULL 
                THEN TIMESTAMPDIFF(SECOND, start_time, end_time)
                ELSE NULL 
            END) as avgDurationSeconds
        FROM el_weather_scoring_batch_task
        WHERE create_time &gt;= #{startDate} AND create_time &lt;= #{endDate}
        GROUP BY DATE(create_time)
        ORDER BY executeDate DESC
    </select>

    <!-- 查询资源占用情况 -->
    <select id="selectResourceUsageStats" resultType="map">
        SELECT 
            status,
            COUNT(1) as taskCount,
            SUM(total_answers) as totalAnswers,
            SUM(processed_answers) as processedAnswers,
            AVG(CASE 
                WHEN total_answers > 0 
                THEN processed_answers * 100.0 / total_answers
                ELSE 0 
            END) as avgProgress,
            -- 预估剩余处理时间（基于当前处理速度）
            CASE 
                WHEN status = 'running' AND processed_answers > 0
                THEN AVG(TIMESTAMPDIFF(SECOND, start_time, NOW()) * (total_answers - processed_answers) / processed_answers)
                ELSE NULL
            END as estimatedRemainingSeconds
        FROM el_weather_scoring_batch_task
        WHERE status IN ('pending', 'running')
        GROUP BY status
    </select>

    <!-- 任务性能分析 -->
    <select id="selectTaskPerformanceAnalysis" resultType="map">
        SELECT 
            config_id,
            COUNT(1) as taskCount,
            AVG(total_answers) as avgAnswersPerTask,
            AVG(TIMESTAMPDIFF(SECOND, start_time, end_time)) as avgDurationSeconds,
            AVG(success_count * 1.0 / total_answers * 100) as avgSuccessRate,
            -- 处理速度：每秒处理答案数
            AVG(CASE 
                WHEN TIMESTAMPDIFF(SECOND, start_time, end_time) > 0
                THEN total_answers * 1.0 / TIMESTAMPDIFF(SECOND, start_time, end_time)
                ELSE NULL
            END) as avgAnswersPerSecond,
            MIN(TIMESTAMPDIFF(SECOND, start_time, end_time)) as minDurationSeconds,
            MAX(TIMESTAMPDIFF(SECOND, start_time, end_time)) as maxDurationSeconds
        FROM el_weather_scoring_batch_task
        WHERE status = 'completed' 
        AND start_time IS NOT NULL 
        AND end_time IS NOT NULL
        AND TIMESTAMPDIFF(SECOND, start_time, end_time) > 0
        <if test="configId != null and configId != ''">
            AND config_id = #{configId}
        </if>
        GROUP BY config_id
        ORDER BY avgAnswersPerSecond DESC
    </select>

    <!-- 清理完成的任务（保留指定天数） -->
    <delete id="cleanupCompletedTasks">
        DELETE FROM el_weather_scoring_batch_task 
        WHERE status IN ('completed', 'failed', 'cancelled')
        AND end_time &lt; DATE_SUB(NOW(), INTERVAL #{retentionDays} DAY)
        <if test="keepRecentCount != null and keepRecentCount > 0">
            AND id NOT IN (
                SELECT id FROM (
                    SELECT id 
                    FROM el_weather_scoring_batch_task 
                    WHERE status IN ('completed', 'failed', 'cancelled')
                    ORDER BY end_time DESC 
                    LIMIT #{keepRecentCount}
                ) recent_tasks
            )
        </if>
    </delete>

    <!-- 重置超时的运行任务 -->
    <update id="resetTimeoutRunningTasks">
        UPDATE el_weather_scoring_batch_task 
        SET 
            status = 'failed',
            error_message = CONCAT('任务执行超时，超时时间：', #{timeoutMinutes}, '分钟'),
            end_time = NOW(),
            update_time = NOW()
        WHERE status = 'running' 
        AND start_time &lt; DATE_SUB(NOW(), INTERVAL #{timeoutMinutes} MINUTE)
    </update>

</mapper> 