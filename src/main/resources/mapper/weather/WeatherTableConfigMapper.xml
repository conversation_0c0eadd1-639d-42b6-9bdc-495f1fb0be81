<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.exam.modules.weather.mapper.WeatherTableConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.exam.modules.weather.entity.WeatherTableConfig">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="table_schema" property="tableSchema" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
        <result column="validation_rules" property="validationRules" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
        <result column="scoring_config" property="scoringConfig" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
        <result column="template_type" property="templateType" />
        <result column="is_active" property="isActive" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 基本信息结果映射（不包含JSON字段） -->
    <resultMap id="BasicResultMap" type="com.yf.exam.modules.weather.entity.WeatherTableConfig">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="template_type" property="templateType" />
        <result column="is_active" property="isActive" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, description, table_schema, validation_rules, scoring_config, template_type, is_active, create_user, create_time, update_time
    </sql>

    <!-- 基本信息列（不包含JSON字段） -->
    <sql id="Basic_Column_List">
        id, name, description, template_type, is_active, create_user, create_time, update_time
    </sql>

    <!-- 根据模板类型查询启用的配置 -->
    <select id="selectActiveByTemplateType" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM el_weather_table_config
        WHERE template_type = #{templateType}
        AND is_active = 1
        ORDER BY create_time DESC
    </select>

    <!-- 查询所有启用的配置 -->
    <select id="selectAllActive" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM el_weather_table_config
        WHERE is_active = 1
        ORDER BY template_type, create_time DESC
    </select>

    <!-- 根据配置名称查询 -->
    <select id="selectByName" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM el_weather_table_config
        WHERE name = #{name}
        LIMIT 1
    </select>

    <!-- 批量更新配置状态 -->
    <update id="updateActiveStatus">
        UPDATE el_weather_table_config
        SET is_active = #{isActive}, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 统计指定模板类型的配置数量 -->
    <select id="countByTemplateType" resultType="int">
        SELECT COUNT(*)
        FROM el_weather_table_config
        WHERE template_type = #{templateType}
    </select>

    <!-- 查询配置的基本信息（不包含JSON字段） -->
    <select id="selectBasicInfo" resultMap="BasicResultMap">
        SELECT 
        <include refid="Basic_Column_List" />
        FROM el_weather_table_config
        ORDER BY is_active DESC, template_type, create_time DESC
    </select>

</mapper> 