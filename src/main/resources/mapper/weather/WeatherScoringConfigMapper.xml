<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.exam.modules.weather.mapper.WeatherScoringConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.exam.modules.weather.entity.WeatherScoringConfig">
        <id column="id" property="id" />
        <result column="config_name" property="configName" />
        <result column="config_version" property="configVersion" />
        <result column="total_score" property="totalScore" />
        <result column="station_weight" property="stationWeight" />
        <result column="element_weights" property="elementWeights" typeHandler="com.yf.exam.config.CustomJacksonTypeHandler" />
        <result column="tolerance_config" property="toleranceConfig" typeHandler="com.yf.exam.config.CustomJacksonTypeHandler" />
        <result column="is_active" property="isActive" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, config_name, config_version, total_score, station_weight, 
        element_weights, tolerance_config, is_active, 
        create_time, update_time, create_by, update_by
    </sql>

    <!-- 根据多条件查询配置列表 -->
    <select id="selectConfigsByConditions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM el_weather_scoring_config
        <where>
            <if test="configName != null and configName != ''">
                AND config_name LIKE CONCAT('%', #{configName}, '%')
            </if>
            <if test="configVersion != null and configVersion != ''">
                AND config_version = #{configVersion}
            </if>
            <if test="isActive != null">
                AND is_active = #{isActive}
            </if>
            <if test="createBy != null and createBy != ''">
                AND create_by = #{createBy}
            </if>
            <if test="startTime != null">
                AND create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 复制配置（创建新版本） -->
    <insert id="copyConfig" parameterType="map">
        INSERT INTO el_weather_scoring_config 
        (id, config_name, config_version, total_score, station_weight, 
         element_weights, tolerance_config, is_active, create_by)
        SELECT 
        #{newConfigId}, 
        #{newConfigName}, 
        #{newConfigVersion}, 
        total_score, 
        station_weight,
        element_weights, 
        tolerance_config, 
        #{newIsActive}, 
        #{createBy}
        FROM el_weather_scoring_config 
        WHERE id = #{sourceConfigId}
    </insert>

    <!-- 批量插入配置历史记录 -->
    <insert id="batchInsertConfigs" parameterType="list">
        INSERT INTO el_weather_scoring_config 
        (id, config_name, config_version, total_score, station_weight, 
         element_weights, tolerance_config, is_active, create_by)
        VALUES
        <foreach collection="list" item="config" separator=",">
        (#{config.id}, #{config.configName}, #{config.configVersion}, 
         #{config.totalScore}, #{config.stationWeight},
         #{config.elementWeights,typeHandler=com.yf.exam.config.CustomJacksonTypeHandler}, 
         #{config.toleranceConfig,typeHandler=com.yf.exam.config.CustomJacksonTypeHandler}, 
         #{config.isActive}, #{config.createBy})
        </foreach>
    </insert>

    <!-- 获取配置详情（包含使用统计） -->
    <select id="selectConfigWithUsageStats" resultType="map">
        SELECT 
            c.*,
            IFNULL(usage.usageCount, 0) as usageCount,
            IFNULL(usage.lastUsedTime, NULL) as lastUsedTime
        FROM el_weather_scoring_config c
        LEFT JOIN (
            SELECT 
                config_id,
                COUNT(1) as usageCount,
                MAX(scoring_time) as lastUsedTime
            FROM el_weather_scoring_result 
            WHERE config_id IS NOT NULL
            GROUP BY config_id
        ) usage ON c.id = usage.config_id
        WHERE c.id = #{configId}
    </select>

    <!-- 查询配置版本变更历史 -->
    <select id="selectVersionChangeHistory" resultType="map">
        SELECT 
            config_version,
            config_name,
            create_time,
            create_by,
            is_active,
            CASE 
                WHEN LAG(config_version) OVER (ORDER BY create_time) IS NULL THEN '初始版本'
                ELSE CONCAT('从 ', LAG(config_version) OVER (ORDER BY create_time), ' 升级')
            END as changeType
        FROM el_weather_scoring_config
        WHERE config_name = #{configName}
        ORDER BY create_time DESC
    </select>

    <!-- 验证配置JSON格式 -->
    <select id="validateConfigJson" resultType="boolean">
        SELECT 
            CASE 
                WHEN JSON_VALID(element_weights) = 1 
                AND JSON_VALID(IFNULL(tolerance_config, '{}')) = 1 
                THEN TRUE 
                ELSE FALSE 
            END as isValid
        FROM el_weather_scoring_config 
        WHERE id = #{configId}
    </select>

    <!-- 查询配置差异对比 -->
    <select id="compareConfigs" resultType="map">
        SELECT 
            'config1' as source,
            id, config_name, config_version, element_weights, tolerance_config
        FROM el_weather_scoring_config 
        WHERE id = #{configId1}
        
        UNION ALL
        
        SELECT 
            'config2' as source,
            id, config_name, config_version, element_weights, tolerance_config
        FROM el_weather_scoring_config 
        WHERE id = #{configId2}
    </select>

    <!-- 清理无效配置（无任何关联使用记录且非活跃） -->
    <delete id="cleanupUnusedConfigs">
        DELETE FROM el_weather_scoring_config 
        WHERE is_active = 0 
        AND id NOT IN (
            SELECT DISTINCT config_id 
            FROM el_weather_scoring_result 
            WHERE config_id IS NOT NULL
        )
        AND create_time &lt; DATE_SUB(NOW(), INTERVAL #{retentionDays} DAY)
    </delete>

</mapper> 