<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.exam.modules.weather.mapper.WeatherScoringResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.exam.modules.weather.entity.WeatherScoringResult">
        <id column="id" property="id" />
        <result column="answer_id" property="answerId" />
        <result column="exam_id" property="examId" />
        <result column="user_id" property="userId" />
        <result column="question_id" property="questionId" />
        <result column="detail_results" property="detailResults" typeHandler="com.yf.exam.config.CustomJacksonTypeHandler" />
        <result column="is_success" property="isSuccess" />
        <result column="final_score" property="finalScore" />
        <result column="total_score" property="totalScore" />
        <result column="max_score" property="maxScore" />
        <result column="score_percentage" property="scorePercentage" />
        <result column="station_count" property="stationCount" />
        <result column="station_scores" property="stationScores" typeHandler="com.yf.exam.config.CustomJacksonTypeHandler" />
        <result column="element_scores" property="elementScores" typeHandler="com.yf.exam.config.CustomJacksonTypeHandler" />
        <result column="error_analysis" property="errorAnalysis" typeHandler="com.yf.exam.config.CustomJacksonTypeHandler" />
        <result column="scoring_time" property="scoringTime" />
        <result column="config_version" property="configVersion" />
        <result column="config_id" property="configId" />
        <result column="scoring_duration" property="scoringDuration" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, answer_id, exam_id, user_id, question_id, detail_results, is_success, final_score,
        total_score, max_score, score_percentage, station_count,
        station_scores, element_scores, error_analysis,
        scoring_time, config_version, config_id, scoring_duration,
        create_time, update_time
    </sql>

    <!-- 根据多条件查询评分结果 -->
    <select id="selectResultsByConditions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM el_weather_scoring_result
        <where>
            <if test="examId != null and examId != ''">
                AND exam_id = #{examId}
            </if>
            <if test="userId != null and userId != ''">
                AND user_id = #{userId}
            </if>
            <if test="questionId != null and questionId != ''">
                AND question_id = #{questionId}
            </if>
            <if test="configId != null and configId != ''">
                AND config_id = #{configId}
            </if>
            <if test="minScore != null">
                AND total_score &gt;= #{minScore}
            </if>
            <if test="maxScore != null">
                AND total_score &lt;= #{maxScore}
            </if>
            <if test="minPercentage != null">
                AND score_percentage &gt;= #{minPercentage}
            </if>
            <if test="maxPercentage != null">
                AND score_percentage &lt;= #{maxPercentage}
            </if>
            <if test="startTime != null">
                AND scoring_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND scoring_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY scoring_time DESC
    </select>

    <!-- 批量插入评分结果 -->
    <insert id="batchInsertResults" parameterType="list">
        INSERT INTO el_weather_scoring_result
        (id, answer_id, exam_id, user_id, question_id, detail_results, is_success, final_score,
         total_score, max_score, score_percentage, station_count,
         station_scores, element_scores, error_analysis,
         config_version, config_id, scoring_duration)
        VALUES
        <foreach collection="list" item="result" separator=",">
        (#{result.id}, #{result.answerId}, #{result.examId}, #{result.userId}, #{result.questionId},
         #{result.detailResults,typeHandler=com.yf.exam.config.CustomJacksonTypeHandler},
         #{result.isSuccess}, #{result.finalScore},
         #{result.totalScore}, #{result.maxScore}, #{result.scorePercentage}, #{result.stationCount},
         #{result.stationScores,typeHandler=com.yf.exam.config.CustomJacksonTypeHandler},
         #{result.elementScores,typeHandler=com.yf.exam.config.CustomJacksonTypeHandler},
         #{result.errorAnalysis,typeHandler=com.yf.exam.config.CustomJacksonTypeHandler},
         #{result.configVersion}, #{result.configId}, #{result.scoringDuration})
        </foreach>
    </insert>

    <!-- 查询考试详细统计报告 -->
    <select id="selectExamDetailStatistics" resultType="map">
        SELECT 
            COUNT(1) as totalCount,
            AVG(total_score) as avgScore,
            STDDEV(total_score) as scoreStdDev,
            MAX(total_score) as maxScore,
            MIN(total_score) as minScore,
            AVG(score_percentage) as avgPercentage,
            
            -- 评分等级统计
            SUM(CASE WHEN score_percentage &gt;= 90 THEN 1 ELSE 0 END) as excellentCount,
            SUM(CASE WHEN score_percentage &gt;= 80 AND score_percentage &lt; 90 THEN 1 ELSE 0 END) as goodCount,
            SUM(CASE WHEN score_percentage &gt;= 60 AND score_percentage &lt; 80 THEN 1 ELSE 0 END) as passCount,
            SUM(CASE WHEN score_percentage &lt; 60 THEN 1 ELSE 0 END) as failCount,
            
            -- 分数区间统计
            SUM(CASE WHEN total_score &gt;= 9 THEN 1 ELSE 0 END) as score9to10,
            SUM(CASE WHEN total_score &gt;= 8 AND total_score &lt; 9 THEN 1 ELSE 0 END) as score8to9,
            SUM(CASE WHEN total_score &gt;= 7 AND total_score &lt; 8 THEN 1 ELSE 0 END) as score7to8,
            SUM(CASE WHEN total_score &gt;= 6 AND total_score &lt; 7 THEN 1 ELSE 0 END) as score6to7,
            SUM(CASE WHEN total_score &lt; 6 THEN 1 ELSE 0 END) as scoreBelow6,
            
            -- 评分效率统计
            AVG(scoring_duration) as avgScoringDuration,
            MAX(scoring_duration) as maxScoringDuration,
            MIN(scoring_duration) as minScoringDuration
            
        FROM el_weather_scoring_result 
        WHERE exam_id = #{examId}
    </select>

    <!-- 查询要素得分分布统计 -->
    <select id="selectElementScoreDistribution" resultType="map">
        SELECT 
            'windForce' as elementName,
            AVG(JSON_EXTRACT(element_scores, '$.windForce')) as avgScore,
            STDDEV(JSON_EXTRACT(element_scores, '$.windForce')) as scoreStdDev,
            MAX(JSON_EXTRACT(element_scores, '$.windForce')) as maxScore,
            MIN(JSON_EXTRACT(element_scores, '$.windForce')) as minScore
        FROM el_weather_scoring_result 
        WHERE exam_id = #{examId} AND element_scores IS NOT NULL
        
        UNION ALL
        
        SELECT 
            'windDirection' as elementName,
            AVG(JSON_EXTRACT(element_scores, '$.windDirection')) as avgScore,
            STDDEV(JSON_EXTRACT(element_scores, '$.windDirection')) as scoreStdDev,
            MAX(JSON_EXTRACT(element_scores, '$.windDirection')) as maxScore,
            MIN(JSON_EXTRACT(element_scores, '$.windDirection')) as minScore
        FROM el_weather_scoring_result 
        WHERE exam_id = #{examId} AND element_scores IS NOT NULL
        
        UNION ALL
        
        SELECT 
            'minTemperature' as elementName,
            AVG(JSON_EXTRACT(element_scores, '$.minTemperature')) as avgScore,
            STDDEV(JSON_EXTRACT(element_scores, '$.minTemperature')) as scoreStdDev,
            MAX(JSON_EXTRACT(element_scores, '$.minTemperature')) as maxScore,
            MIN(JSON_EXTRACT(element_scores, '$.minTemperature')) as minScore
        FROM el_weather_scoring_result 
        WHERE exam_id = #{examId} AND element_scores IS NOT NULL
        
        UNION ALL
        
        SELECT 
            'maxTemperature' as elementName,
            AVG(JSON_EXTRACT(element_scores, '$.maxTemperature')) as avgScore,
            STDDEV(JSON_EXTRACT(element_scores, '$.maxTemperature')) as scoreStdDev,
            MAX(JSON_EXTRACT(element_scores, '$.maxTemperature')) as maxScore,
            MIN(JSON_EXTRACT(element_scores, '$.maxTemperature')) as minScore
        FROM el_weather_scoring_result 
        WHERE exam_id = #{examId} AND element_scores IS NOT NULL
        
        UNION ALL
        
        SELECT 
            'precipitation' as elementName,
            AVG(JSON_EXTRACT(element_scores, '$.precipitation')) as avgScore,
            STDDEV(JSON_EXTRACT(element_scores, '$.precipitation')) as scoreStdDev,
            MAX(JSON_EXTRACT(element_scores, '$.precipitation')) as maxScore,
            MIN(JSON_EXTRACT(element_scores, '$.precipitation')) as minScore
        FROM el_weather_scoring_result 
        WHERE exam_id = #{examId} AND element_scores IS NOT NULL
        
        UNION ALL
        
        SELECT 
            'disasterWeather' as elementName,
            AVG(JSON_EXTRACT(element_scores, '$.disasterWeather')) as avgScore,
            STDDEV(JSON_EXTRACT(element_scores, '$.disasterWeather')) as scoreStdDev,
            MAX(JSON_EXTRACT(element_scores, '$.disasterWeather')) as maxScore,
            MIN(JSON_EXTRACT(element_scores, '$.disasterWeather')) as minScore
        FROM el_weather_scoring_result 
        WHERE exam_id = #{examId} AND element_scores IS NOT NULL
    </select>

    <!-- 查询用户评分趋势 -->
    <select id="selectUserScoreTrend" resultType="map">
        SELECT 
            DATE(scoring_time) as scoreDate,
            COUNT(1) as examCount,
            AVG(total_score) as avgScore,
            MAX(total_score) as maxScore,
            MIN(total_score) as minScore,
            AVG(score_percentage) as avgPercentage
        FROM el_weather_scoring_result 
        WHERE user_id = #{userId}
        <if test="startDate != null">
            AND DATE(scoring_time) &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND DATE(scoring_time) &lt;= #{endDate}
        </if>
        GROUP BY DATE(scoring_time)
        ORDER BY scoreDate DESC
        LIMIT #{limit}
    </select>

    <!-- 查询答题错误分析汇总 -->
    <select id="selectErrorAnalysisSummary" resultType="map">
        SELECT 
            errorType,
            COUNT(1) as errorCount,
            AVG(total_score) as avgScoreWithError
        FROM (
            SELECT 
                id,
                total_score,
                JSON_KEYS(error_analysis) as errorKeys
            FROM el_weather_scoring_result 
            WHERE exam_id = #{examId} 
            AND error_analysis IS NOT NULL
            AND JSON_LENGTH(error_analysis) > 0
        ) t
        CROSS JOIN JSON_TABLE(t.errorKeys, '$[*]' 
            COLUMNS (errorType VARCHAR(50) PATH '$')
        ) j
        GROUP BY errorType
        ORDER BY errorCount DESC
    </select>

    <!-- 更新评分结果（重新评分） -->
    <update id="updateScoringResult" parameterType="com.yf.exam.modules.weather.entity.WeatherScoringResult">
        UPDATE el_weather_scoring_result
        SET
            detail_results = #{detailResults,typeHandler=com.yf.exam.config.CustomJacksonTypeHandler},
            is_success = #{isSuccess},
            final_score = #{finalScore},
            total_score = #{totalScore},
            max_score = #{maxScore},
            score_percentage = #{scorePercentage},
            station_count = #{stationCount},
            station_scores = #{stationScores,typeHandler=com.yf.exam.config.CustomJacksonTypeHandler},
            element_scores = #{elementScores,typeHandler=com.yf.exam.config.CustomJacksonTypeHandler},
            error_analysis = #{errorAnalysis,typeHandler=com.yf.exam.config.CustomJacksonTypeHandler},
            config_version = #{configVersion},
            config_id = #{configId},
            scoring_duration = #{scoringDuration},
            scoring_time = NOW(),
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 删除过期的评分结果 -->
    <delete id="deleteExpiredResults">
        DELETE FROM el_weather_scoring_result 
        WHERE scoring_time &lt; DATE_SUB(NOW(), INTERVAL #{retentionDays} DAY)
        <if test="examId != null and examId != ''">
            AND exam_id = #{examId}
        </if>
    </delete>

</mapper> 