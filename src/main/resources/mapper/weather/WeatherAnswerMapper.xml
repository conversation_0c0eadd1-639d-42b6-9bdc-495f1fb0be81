<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.exam.modules.weather.mapper.WeatherAnswerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.exam.modules.weather.entity.WeatherAnswer">
        <id column="id" property="id" />
        <result column="paper_qu_id" property="paperQuId" />
        <result column="user_id" property="userId" />
        <result column="table_config_id" property="tableConfigId" />
        <result column="cell_data" property="cellData" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
        <result column="answer_status" property="answerStatus" />
        <result column="validation_result" property="validationResult" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
        <result column="score_details" property="scoreDetails" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
        <result column="total_score" property="totalScore" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, paper_qu_id, user_id, table_config_id, cell_data, answer_status, validation_result, score_details, total_score, create_time, update_time
    </sql>

    <!-- 根据试卷题目ID和用户ID查询答案 -->
    <select id="selectByPaperQuAndUser" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM el_weather_answer
        WHERE paper_qu_id = #{paperQuId}
        AND user_id = #{userId}
        LIMIT 1
    </select>

    <!-- 查询用户的所有天气预报答案 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM el_weather_answer
        WHERE user_id = #{userId}
        ORDER BY create_time DESC
    </select>

    <!-- 查询指定表格配置的所有答案 -->
    <select id="selectByTableConfigId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM el_weather_answer
        WHERE table_config_id = #{tableConfigId}
        ORDER BY create_time DESC
    </select>

    <!-- 批量更新答案状态 -->
    <update id="updateAnswerStatus">
        UPDATE el_weather_answer
        SET answer_status = #{answerStatus}, update_time = NOW()
        WHERE id IN
        <foreach collection="answerIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 更新答案分数 -->
    <update id="updateScore">
        UPDATE el_weather_answer
        SET total_score = #{totalScore},
            score_detail = #{scoreDetail, javaType=java.util.Map, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
            update_time = NOW()
        WHERE id = #{answerId}
    </update>

    <!-- 统计用户完成的答案数量 -->
    <select id="countCompletedByUserId" resultType="int">
        SELECT COUNT(*)
        FROM el_weather_answer
        WHERE user_id = #{userId}
        AND answer_status = 1
    </select>

    <!-- 查询指定分数范围的答案 -->
    <select id="selectByScoreRange" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM el_weather_answer
        WHERE total_score BETWEEN #{minScore} AND #{maxScore}
        ORDER BY total_score DESC, create_time DESC
    </select>

    <!-- 查询需要评分的答案（已完成但未评分） -->
    <select id="selectPendingScore" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM el_weather_answer
        WHERE answer_status = 1
        AND (total_score IS NULL OR total_score = 0)
        ORDER BY create_time ASC
    </select>

    <!-- 获取答案统计信息 -->
    <select id="getAnswerStatistics" resultType="java.util.Map">
        SELECT 
            COUNT(*) as totalAnswers,
            COUNT(CASE WHEN answer_status = 1 THEN 1 END) as completedAnswers,
            COUNT(CASE WHEN answer_status = 0 THEN 1 END) as pendingAnswers,
            COALESCE(AVG(CASE WHEN total_score &gt; 0 THEN total_score END), 0) as avgScore,
            COALESCE(MAX(total_score), 0) as maxScore,
            COALESCE(MIN(CASE WHEN total_score &gt; 0 THEN total_score END), 0) as minScore,
            COUNT(CASE WHEN total_score &gt;= 90 THEN 1 END) as excellentCount,
            COUNT(CASE WHEN total_score &gt;= 80 AND total_score &lt; 90 THEN 1 END) as goodCount,
            COUNT(CASE WHEN total_score &gt;= 70 AND total_score &lt; 80 THEN 1 END) as fairCount,
            COUNT(CASE WHEN total_score &gt;= 60 AND total_score &lt; 70 THEN 1 END) as poorCount,
            COUNT(CASE WHEN total_score &gt; 0 AND total_score &lt; 60 THEN 1 END) as failCount
        FROM el_weather_answer
        <where>
            <if test="tableConfigId != null and tableConfigId != ''">
                AND table_config_id = #{tableConfigId}
            </if>
        </where>
    </select>

</mapper> 