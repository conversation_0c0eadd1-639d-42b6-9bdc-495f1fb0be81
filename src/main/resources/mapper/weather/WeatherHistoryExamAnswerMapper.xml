<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.exam.modules.weather.mapper.WeatherHistoryExamAnswerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.exam.modules.weather.entity.WeatherHistoryExamAnswer">
        <id column="id" property="id" />
        <result column="exam_id" property="examId" />
        <result column="question_id" property="questionId" />
        <result column="user_id" property="userId" />
        <result column="precipitation_answer" property="precipitationAnswer" typeHandler="com.yf.exam.config.CustomJacksonTypeHandler" />
        <result column="weather_answer" property="weatherAnswer" typeHandler="com.yf.exam.config.CustomJacksonTypeHandler" />
        <result column="overall_progress" property="overallProgress" />
        <result column="answer_status" property="answerStatus" />
        <result column="answer_time" property="answerTime" />
        <result column="time_spent" property="timeSpent" />
        <result column="submit_time" property="submitTime" />
        <result column="total_score" property="totalScore" />
        <result column="score_details" property="scoreDetails" typeHandler="com.yf.exam.config.CustomJacksonTypeHandler" />
        <result column="grading_status" property="gradingStatus" />
        <result column="grading_time" property="gradingTime" />
        <result column="grader_id" property="graderId" />
        <result column="grading_remark" property="gradingRemark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, exam_id, question_id, user_id, precipitation_answer, weather_answer,
        overall_progress, answer_status, answer_time, time_spent, submit_time,
        total_score, score_details, grading_status, grading_time, grader_id,
        grading_remark, create_time, update_time
    </sql>

    <!-- 根据考试ID、题目ID和用户ID查询答案 -->
    <select id="findByExamAndQuestionAndUser" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM el_weather_history_exam_answer
        WHERE exam_id = #{examId}
          AND question_id = #{questionId}
          AND user_id = #{userId}
        LIMIT 1
    </select>

    <!-- 根据条件查询答案ID列表 -->
    <select id="queryAnswerIdsByCondition" resultType="java.lang.String">
        SELECT id
        FROM el_weather_history_exam_answer
        WHERE 1=1
        <if test="examId != null and examId != ''">
            AND exam_id = #{examId}
        </if>
        <if test="startDate != null">
            AND create_time >= #{startDate}
        </if>
        <if test="endDate != null">
            AND create_time &lt;= #{endDate}
        </if>
        ORDER BY create_time DESC
    </select>

</mapper>
