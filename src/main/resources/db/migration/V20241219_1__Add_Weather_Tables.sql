-- =====================================================
-- 天气预报历史个例考试功能 - 数据库表结构
-- 创建时间：2024-12-19
-- 版本：V20241219_1
-- 描述：新增天气预报表格配置、答案存储等相关表
-- =====================================================

-- 1. 创建天气预报表格配置表
CREATE TABLE `el_weather_table_config` (
  `id` varchar(64) NOT NULL COMMENT 'ID',
  `name` varchar(100) NOT NULL COMMENT '配置名称',
  `description` varchar(500) DEFAULT NULL COMMENT '配置描述',
  `table_schema` json NOT NULL COMMENT '表格结构JSON配置',
  `validation_rules` json DEFAULT NULL COMMENT '数据验证规则JSON',
  `scoring_config` json DEFAULT NULL COMMENT '评分配置JSON',
  `template_type` varchar(50) DEFAULT 'general' COMMENT '模板类型：general-通用，typhoon-台风，rainfall-降雨等',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
  `create_user` varchar(64) DEFAULT NULL COMMENT '创建用户ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_template_type` (`template_type`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='天气预报表格配置表';

-- 2. 扩展现有题目表，支持天气预报表格类型
ALTER TABLE `el_qu` 
ADD COLUMN `table_config_id` varchar(64) DEFAULT NULL COMMENT '关联的天气预报表格配置ID',
ADD COLUMN `weather_scenario` text DEFAULT NULL COMMENT '天气情景描述和背景材料',
ADD COLUMN `scenario_data` json DEFAULT NULL COMMENT '情景相关数据（观测数据、历史资料等）',
ADD INDEX `idx_table_config_id` (`table_config_id`);

-- 3. 创建天气预报答案数据表
CREATE TABLE `el_weather_answer` (
  `id` varchar(64) NOT NULL COMMENT 'ID',
  `paper_qu_id` varchar(64) NOT NULL COMMENT '试卷题目ID（关联el_paper_qu表）',
  `user_id` varchar(64) NOT NULL COMMENT '答题用户ID',
  `table_config_id` varchar(64) NOT NULL COMMENT '表格配置ID',
  `cell_data` json NOT NULL COMMENT '表格单元格数据JSON',
  `answer_status` tinyint(1) DEFAULT 0 COMMENT '答题状态：0-未完成，1-已完成',
  `validation_result` json DEFAULT NULL COMMENT '数据验证结果',
  `score_detail` json DEFAULT NULL COMMENT '详细评分信息',
  `total_score` decimal(5,2) DEFAULT 0.00 COMMENT '总得分',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_paper_qu_user` (`paper_qu_id`, `user_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_table_config_id` (`table_config_id`),
  KEY `idx_answer_status` (`answer_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='天气预报答案数据表';

-- 4. 创建天气预报评分记录表（用于存储评分历史和过程）
CREATE TABLE `el_weather_scoring_log` (
  `id` varchar(64) NOT NULL COMMENT 'ID',
  `weather_answer_id` varchar(64) NOT NULL COMMENT '天气答案ID',
  `scoring_type` varchar(20) NOT NULL COMMENT '评分类型：auto-自动评分，manual-人工评分',
  `score_components` json DEFAULT NULL COMMENT '分项得分详情',
  `total_score` decimal(5,2) NOT NULL COMMENT '总分',
  `scorer_id` varchar(64) DEFAULT NULL COMMENT '评分人ID（人工评分时使用）',
  `scoring_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '评分时间',
  `remark` text DEFAULT NULL COMMENT '评分备注',
  PRIMARY KEY (`id`),
  KEY `idx_weather_answer_id` (`weather_answer_id`),
  KEY `idx_scoring_type` (`scoring_type`),
  KEY `idx_scoring_time` (`scoring_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='天气预报评分记录表';

-- 5. 插入天气预报相关的基础配置数据

-- 插入标准的24小时天气预报表格配置
INSERT INTO `el_weather_table_config` 
(`id`, `name`, `description`, `table_schema`, `validation_rules`, `scoring_config`, `template_type`, `create_time`, `update_time`) 
VALUES 
('weather_24h_standard', '标准24小时天气预报表', '用于基础天气预报考试的标准表格模板', 
JSON_OBJECT(
  'columns', JSON_ARRAY(
    JSON_OBJECT('key', 'forecast_time', 'label', '预报起始日期及时间', 'type', 'datetime', 'width', 180, 'required', true),
    JSON_OBJECT('key', 'station_info', 'label', '站名站号', 'type', 'text', 'width', 120, 'required', true),
    JSON_OBJECT('key', 'max_wind_force', 'label', '最大风力(级)', 'type', 'select', 'width', 100, 'required', true, 'options', JSON_ARRAY('1-2', '3-4', '5-6', '7-8', '9-10', '11-12')),
    JSON_OBJECT('key', 'wind_direction', 'label', '最大风力时的风向', 'type', 'select', 'width', 120, 'required', true, 'options', JSON_ARRAY('北', '东北', '东', '东南', '南', '西南', '西', '西北')),
    JSON_OBJECT('key', 'min_temp', 'label', '最低气温℃', 'type', 'number', 'width', 100, 'required', true),
    JSON_OBJECT('key', 'max_temp', 'label', '最高气温℃', 'type', 'number', 'width', 100, 'required', true),
    JSON_OBJECT('key', 'precipitation', 'label', '降水(雨、雪)量级', 'type', 'select', 'width', 120, 'required', false, 'options', JSON_ARRAY('无', '小雨', '中雨', '大雨', '暴雨', '小雪', '中雪', '大雪')),
    JSON_OBJECT('key', 'disaster_weather', 'label', '灾害性天气类型', 'type', 'multi-select', 'width', 150, 'required', false, 'options', JSON_ARRAY('雷暴', '冰雹', '大风', '浓雾', '霜冻', '高温', '寒潮'))
  ),
  'rows', 24,
  'timeInterval', 1
),
JSON_OBJECT(
  'rules', JSON_ARRAY(
    JSON_OBJECT('field', 'min_temp', 'type', 'range', 'min', -50, 'max', 50, 'message', '最低气温应在-50到50度之间'),
    JSON_OBJECT('field', 'max_temp', 'type', 'range', 'min', -50, 'max', 50, 'message', '最高气温应在-50到50度之间'),
    JSON_OBJECT('field', 'temp_logic', 'type', 'logic', 'rule', 'max_temp >= min_temp', 'message', '最高气温不能低于最低气温')
  )
),
JSON_OBJECT(
  'weightConfig', JSON_OBJECT(
    'exactMatch', 0.4,
    'dataConsistency', 0.3,
    'trendAnalysis', 0.2,
    'professionalJudgment', 0.1
  ),
  'scoreRanges', JSON_OBJECT(
    'excellent', JSON_ARRAY(90, 100),
    'good', JSON_ARRAY(80, 89),
    'fair', JSON_ARRAY(70, 79),
    'poor', JSON_ARRAY(60, 69),
    'fail', JSON_ARRAY(0, 59)
  )
),
'general', NOW(), NOW());

-- 6. 添加外键约束（如果需要的话）
-- ALTER TABLE `el_qu` ADD CONSTRAINT `fk_qu_weather_config` FOREIGN KEY (`table_config_id`) REFERENCES `el_weather_table_config` (`id`);
-- ALTER TABLE `el_weather_answer` ADD CONSTRAINT `fk_weather_answer_config` FOREIGN KEY (`table_config_id`) REFERENCES `el_weather_table_config` (`id`);
-- ALTER TABLE `el_weather_scoring_log` ADD CONSTRAINT `fk_scoring_weather_answer` FOREIGN KEY (`weather_answer_id`) REFERENCES `el_weather_answer` (`id`);

-- 7. 创建用于查询性能优化的复合索引
CREATE INDEX `idx_qu_type_table_config` ON `el_qu` (`qu_type`, `table_config_id`);
CREATE INDEX `idx_weather_answer_user_status` ON `el_weather_answer` (`user_id`, `answer_status`, `create_time`);

-- =====================================================
-- 数据库迁移完成
-- ===================================================== 