-- 为el_qu表添加预报日期和时次字段，支持历史个例题目
ALTER TABLE `el_qu` 
ADD COLUMN `forecast_date` varchar(50) DEFAULT NULL COMMENT '预报起报日期（历史个例题目专用）' AFTER `title`,
ADD COLUMN `forecast_time` varchar(20) DEFAULT NULL COMMENT '预报起报时次（历史个例题目专用）' AFTER `forecast_date`;

-- 为现有的天气预报题目添加预报信息（从scenarioData中提取）
UPDATE `el_qu` 
SET `forecast_date` = '2035-09-09', 
    `forecast_time` = '08'
WHERE `id` = 'weather_qu_sample_001' AND `forecast_date` IS NULL;

-- 添加索引以提高查询性能
CREATE INDEX `idx_qu_forecast_date` ON `el_qu` (`forecast_date`);
CREATE INDEX `idx_qu_forecast_time` ON `el_qu` (`forecast_time`);
CREATE INDEX `idx_qu_forecast_date_time` ON `el_qu` (`forecast_date`, `forecast_time`);
