-- 创建天气预报判卷配置表
-- 用于管理自动判卷和批量处理的各种配置参数

CREATE TABLE `el_weather_grading_config` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置项键名',
  `config_name` varchar(200) NOT NULL COMMENT '配置项名称',
  `config_value` text COMMENT '配置值',
  `value_type` varchar(20) NOT NULL DEFAULT 'STRING' COMMENT '配置值类型：STRING, BOOLEAN, INTEGER, DOUBLE',
  `description` varchar(500) COMMENT '配置描述',
  `config_group` varchar(50) NOT NULL DEFAULT 'SYSTEM' COMMENT '配置分组：AUTO_GRADING, BATCH_PROCESSING, SYSTEM',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：0-禁用，1-启用',
  `is_readonly` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否只读：0-可修改，1-只读',
  `default_value` text COMMENT '默认值',
  `validation_rules` text COMMENT '配置值约束规则（JSON格式）',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `modifier_id` varchar(50) DEFAULT 'system' COMMENT '最后修改人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_config_group` (`config_group`),
  KEY `idx_enabled` (`is_enabled`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='天气预报判卷配置表';

-- 插入默认配置数据
INSERT INTO `el_weather_grading_config` (
  `id`, `config_key`, `config_name`, `config_value`, `value_type`, 
  `description`, `config_group`, `is_enabled`, `is_readonly`, 
  `default_value`, `validation_rules`, `sort_order`, `modifier_id`
) VALUES
-- 自动判卷配置组
('grading_config_001', 'auto_grading_enabled', '自动判卷启用', 'true', 'BOOLEAN', 
 '是否在考试提交后自动触发判卷', 'AUTO_GRADING', 1, 0, 
 'true', '{"valueType":"BOOLEAN"}', 1, 'system'),

('grading_config_002', 'default_scoring_config_id', '默认评分配置ID', '', 'STRING', 
 '系统默认使用的评分配置ID，为空时使用当前活跃配置', 'AUTO_GRADING', 1, 0, 
 '', '{"valueType":"STRING","maxLength":50}', 2, 'system'),

-- 批量处理配置组
('grading_config_003', 'max_batch_size', '最大批量处理数量', '100', 'INTEGER', 
 '单次批量判卷的最大处理数量', 'BATCH_PROCESSING', 1, 0, 
 '100', '{"valueType":"INTEGER","min":1,"max":1000}', 3, 'system'),

('grading_config_004', 'async_threshold', '异步处理阈值', '50', 'INTEGER', 
 '超过此数量的批量任务将使用异步处理', 'BATCH_PROCESSING', 1, 0, 
 '50', '{"valueType":"INTEGER","min":1,"max":1000}', 4, 'system'),

-- 系统性能配置组
('grading_config_005', 'grading_timeout_seconds', '判卷超时时间', '300', 'INTEGER', 
 '单个答案判卷的超时时间（秒）', 'SYSTEM', 1, 0, 
 '300', '{"valueType":"INTEGER","min":60,"max":3600}', 5, 'system'),

('grading_config_006', 'retry_attempts', '失败重试次数', '3', 'INTEGER', 
 '判卷失败时的重试次数', 'SYSTEM', 1, 0, 
 '3', '{"valueType":"INTEGER","min":0,"max":10}', 6, 'system'),

('grading_config_007', 'thread_pool_size', '并发线程池大小', '10', 'INTEGER', 
 '判卷任务的并发线程数', 'SYSTEM', 1, 0, 
 '10', '{"valueType":"INTEGER","min":1,"max":100}', 7, 'system');

-- 创建索引说明
-- uk_config_key: 唯一索引，确保配置键的唯一性
-- idx_config_group: 配置组索引，用于按组查询配置
-- idx_enabled: 启用状态索引，用于查询有效配置
-- idx_create_time: 创建时间索引，用于时间范围查询 