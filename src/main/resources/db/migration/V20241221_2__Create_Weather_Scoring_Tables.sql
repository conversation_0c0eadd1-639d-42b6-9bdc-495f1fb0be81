-- ========================================
-- 天气预报评分系统 - 数据库表结构创建脚本
-- 版本: V20241221_2
-- 创建时间: 2024-12-21
-- 描述: 创建评分配置表和评分结果表
-- ========================================

-- 评分配置表
CREATE TABLE `el_weather_scoring_config` (
  `id` varchar(64) NOT NULL COMMENT 'ID',
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `config_version` varchar(20) DEFAULT '1.0' COMMENT '配置版本',
  `total_score` int(11) NOT NULL DEFAULT 10 COMMENT '总分',
  `station_weight` int(11) NOT NULL DEFAULT 10 COMMENT '单站权重',
  `element_weights` json NOT NULL COMMENT '要素权重配置',
  `tolerance_config` json DEFAULT NULL COMMENT '容差配置',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_config_name` (`config_name`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_config_version` (`config_version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='天气预报评分配置表';

-- 评分结果表
CREATE TABLE `el_weather_scoring_result` (
  `id` varchar(64) NOT NULL COMMENT 'ID',
  `answer_id` varchar(64) NOT NULL COMMENT '答案ID',
  `exam_id` varchar(64) NOT NULL COMMENT '考试ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `question_id` varchar(64) NOT NULL COMMENT '题目ID',
  `total_score` decimal(5,2) NOT NULL COMMENT '总得分',
  `max_score` decimal(5,2) NOT NULL COMMENT '满分',
  `score_percentage` decimal(5,2) NOT NULL COMMENT '得分率',
  `station_count` int(11) DEFAULT 0 COMMENT '评分站点数量',
  `station_scores` json NOT NULL COMMENT '分站得分详情',
  `element_scores` json NOT NULL COMMENT '分要素得分详情',
  `error_analysis` json DEFAULT NULL COMMENT '错误分析',
  `scoring_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '评分时间',
  `config_version` varchar(20) DEFAULT NULL COMMENT '使用的配置版本',
  `config_id` varchar(64) DEFAULT NULL COMMENT '使用的配置ID',
  `scoring_duration` int(11) DEFAULT NULL COMMENT '评分耗时(毫秒)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_answer_id` (`answer_id`),
  KEY `idx_exam_user` (`exam_id`, `user_id`),
  KEY `idx_score_range` (`total_score`),
  KEY `idx_scoring_time` (`scoring_time`),
  KEY `idx_user_exam` (`user_id`, `exam_id`),
  KEY `idx_question_id` (`question_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='天气预报评分结果表';

-- 批量评分任务表
CREATE TABLE `el_weather_scoring_batch_task` (
  `id` varchar(64) NOT NULL COMMENT 'ID',
  `task_name` varchar(200) DEFAULT NULL COMMENT '任务名称',
  `exam_id` varchar(64) NOT NULL COMMENT '考试ID',
  `question_id` varchar(64) NOT NULL COMMENT '题目ID',
  `config_id` varchar(64) NOT NULL COMMENT '评分配置ID',
  `total_answers` int(11) NOT NULL DEFAULT 0 COMMENT '总答案数',
  `processed_answers` int(11) NOT NULL DEFAULT 0 COMMENT '已处理答案数',
  `success_count` int(11) NOT NULL DEFAULT 0 COMMENT '成功评分数',
  `fail_count` int(11) NOT NULL DEFAULT 0 COMMENT '失败评分数',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '任务状态:pending,running,completed,failed',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `result_summary` json DEFAULT NULL COMMENT '结果摘要',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`id`),
  KEY `idx_exam_id` (`exam_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='天气预报批量评分任务表';

-- 插入默认评分配置
INSERT INTO `el_weather_scoring_config` (
  `id`, 
  `config_name`, 
  `config_version`, 
  `total_score`, 
  `station_weight`, 
  `element_weights`,
  `tolerance_config`,
  `is_active`,
  `create_by`
) VALUES (
  'default_config_001', 
  '默认评分配置', 
  '1.0', 
  10, 
  10, 
  '{
    "windForce": {
      "maxScore": 1.0,
      "scoringType": "level_based"
    },
    "windDirection": {
      "maxScore": 1.0, 
      "scoringType": "angle_range_based"
    },
    "minTemperature": {
      "maxScore": 2.0,
      "scoringType": "tolerance_based",
      "toleranceRange": 2
    },
    "maxTemperature": {
      "maxScore": 2.0,
      "scoringType": "tolerance_based", 
      "toleranceRange": 2
    },
    "precipitation": {
      "maxScore": 2.0,
      "scoringType": "matrix_based"
    },
    "disasterWeather": {
      "maxScore": 2.0,
      "scoringType": "exact_match_with_proportion"
    }
  }',
  '{
    "temperature": {
      "maxDifference": 2,
      "unit": "℃"
    },
    "windDirection": {
      "angleRanges": {
        "北": ["337.5-22.5"],
        "东北": ["22.5-67.5"],
        "东": ["67.5-90.0", "90.0-112.5"],
        "东南": ["112.5-157.5"],
        "南": ["157.5-202.5"],
        "西南": ["202.5-247.5"],
        "西": ["247.5-292.5"],
        "西北": ["292.5-337.5"]
      }
    }
  }',
  1,
  'system'
);

-- 扩展现有答案表，添加评分状态字段
ALTER TABLE `el_weather_history_exam_answer` 
ADD COLUMN `scoring_status` varchar(20) DEFAULT 'unscored' COMMENT '评分状态:unscored,scoring,scored,failed' AFTER `update_time`,
ADD COLUMN `last_scoring_time` datetime DEFAULT NULL COMMENT '最后评分时间' AFTER `scoring_status`,
ADD INDEX `idx_scoring_status` (`scoring_status`); 