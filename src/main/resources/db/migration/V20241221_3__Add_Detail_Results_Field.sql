-- 为天气预报评分结果表添加缺失字段
-- 创建时间：2024-12-21
-- 版本：V20241221_3
-- 描述：添加detail_results、is_success、final_score字段

-- 添加详细结果字段
ALTER TABLE `el_weather_scoring_result`
ADD COLUMN `detail_results` json DEFAULT NULL COMMENT '详细评分结果JSON' AFTER `question_id`,
ADD COLUMN `is_success` tinyint(1) DEFAULT 1 COMMENT '是否评分成功' AFTER `detail_results`,
ADD COLUMN `final_score` decimal(5,2) DEFAULT NULL COMMENT '最终得分' AFTER `is_success`;

-- 添加索引以提高查询性能
CREATE INDEX `idx_is_success` ON `el_weather_scoring_result` (`is_success`);
CREATE INDEX `idx_final_score` ON `el_weather_scoring_result` (`final_score`);

-- 更新现有记录的字段（如果有需要的话）
-- UPDATE `el_weather_scoring_result` SET `detail_results` = '{}' WHERE `detail_results` IS NULL;
-- UPDATE `el_weather_scoring_result` SET `is_success` = 1 WHERE `is_success` IS NULL;
