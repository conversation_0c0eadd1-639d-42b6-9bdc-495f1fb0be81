(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[17],{

/***/ "./node_modules/cache-loader/dist/cjs.js?!./node_modules/babel-loader/lib/index.js!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/paper/exam/result.vue?vue&type=script&lang=js&":
/*!*********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/cache-loader/dist/cjs.js??ref--13-0!./node_modules/babel-loader/lib!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/views/paper/exam/result.vue?vue&type=script&lang=js& ***!
  \*********************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var core_js_modules_web_dom_iterable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/web.dom.iterable */ \"./node_modules/core-js/modules/web.dom.iterable.js\");\n/* harmony import */ var core_js_modules_web_dom_iterable__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_iterable__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _api_paper_exam__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/api/paper/exam */ \"./src/api/paper/exam.js\");\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  data: function data() {\n    return {\n      // 试卷ID\n      paperId: '',\n      paperData: {\n        quList: []\n      },\n      radioValues: {},\n      multiValues: {},\n      radioRights: {},\n      multiRights: {},\n      myRadio: {},\n      myMulti: {}\n    };\n  },\n  created: function created() {\n    var id = this.$route.params.id;\n\n    if (typeof id !== 'undefined') {\n      this.paperId = id;\n      this.fetchData(id);\n    }\n  },\n  methods: {\n    fetchData: function fetchData(id) {\n      var _this = this;\n\n      var params = {\n        id: id\n      };\n      Object(_api_paper_exam__WEBPACK_IMPORTED_MODULE_1__[\"paperResult\"])(params).then(function (response) {\n        // 试卷内容\n        _this.paperData = response.data; // 填充该题目的答案\n\n        _this.paperData.quList.forEach(function (item) {\n          var radioValue = '';\n          var radioRight = '';\n          var myRadio = '';\n          var multiValue = [];\n          var multiRight = [];\n          var myMulti = [];\n          item.answerList.forEach(function (an) {\n            // 用户选定的\n            if (an.checked) {\n              if (item.quType === 1 || item.quType === 3) {\n                radioValue = an.id;\n                myRadio = an.abc;\n              } else {\n                multiValue.push(an.id);\n                myMulti.push(an.abc);\n              }\n            } // 正确答案\n\n\n            if (an.isRight) {\n              if (item.quType === 1 || item.quType === 3) {\n                radioRight = an.abc;\n              } else {\n                multiRight.push(an.abc);\n              }\n            }\n          });\n          _this.multiValues[item.id] = multiValue;\n          _this.radioValues[item.id] = radioValue;\n          _this.radioRights[item.id] = radioRight;\n          _this.multiRights[item.id] = multiRight;\n          _this.myRadio[item.id] = myRadio;\n          _this.myMulti[item.id] = myMulti;\n        });\n\n        console.log(_this.multiValues);\n        console.log(_this.radioValues);\n      });\n    }\n  }\n});\n\n//# sourceURL=webpack:///./src/views/paper/exam/result.vue?./node_modules/cache-loader/dist/cjs.js??ref--13-0!./node_modules/babel-loader/lib!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options");

/***/ }),

/***/ "./node_modules/cache-loader/dist/cjs.js?{\"cacheDirectory\":\"node_modules/.cache/vue-loader\",\"cacheIdentifier\":\"9323b05c-vue-loader-template\"}!./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/paper/exam/result.vue?vue&type=template&id=14e8c060&scoped=true&":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/cache-loader/dist/cjs.js?{"cacheDirectory":"node_modules/.cache/vue-loader","cacheIdentifier":"9323b05c-vue-loader-template"}!./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/views/paper/exam/result.vue?vue&type=template&id=14e8c060&scoped=true& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"render\", function() { return render; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"staticRenderFns\", function() { return staticRenderFns; });\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"app-container\" },\n    [\n      _c(\"h2\", { staticClass: \"text-center\" }, [\n        _vm._v(_vm._s(_vm.paperData.title)),\n      ]),\n      _c(\"p\", { staticClass: \"text-center\", staticStyle: { color: \"#666\" } }, [\n        _vm._v(_vm._s(_vm.paperData.createTime)),\n      ]),\n      _c(\n        \"el-row\",\n        { staticStyle: { \"margin-top\": \"50px\" }, attrs: { gutter: 24 } },\n        [\n          _c(\"el-col\", { staticClass: \"text-center\", attrs: { span: 8 } }, [\n            _vm._v(\" 考生姓名：\" + _vm._s(_vm.paperData.userId_dictText) + \" \"),\n          ]),\n          _c(\"el-col\", { staticClass: \"text-center\", attrs: { span: 8 } }, [\n            _vm._v(\" 考试用时：\" + _vm._s(_vm.paperData.userTime) + \"分钟 \"),\n          ]),\n          _c(\"el-col\", { staticClass: \"text-center\", attrs: { span: 8 } }, [\n            _vm._v(\" 考试得分：\" + _vm._s(_vm.paperData.userScore) + \" \"),\n          ]),\n        ],\n        1\n      ),\n      _c(\n        \"el-card\",\n        { staticStyle: { \"margin-top\": \"20px\" } },\n        _vm._l(_vm.paperData.quList, function (item) {\n          return _c(\"div\", { key: item.id, staticClass: \"qu-content\" }, [\n            _c(\"p\", [\n              _vm._v(\n                _vm._s(item.sort + 1) +\n                  \".\" +\n                  _vm._s(item.content) +\n                  \"（得分：\" +\n                  _vm._s(item.actualScore) +\n                  \"）\"\n              ),\n            ]),\n            item.image != null && item.image != \"\"\n              ? _c(\n                  \"p\",\n                  [\n                    _c(\"el-image\", {\n                      staticStyle: { \"max-width\": \"100%\" },\n                      attrs: { src: item.image },\n                    }),\n                  ],\n                  1\n                )\n              : _vm._e(),\n            item.quType === 1 || item.quType === 3\n              ? _c(\n                  \"div\",\n                  [\n                    _c(\n                      \"el-radio-group\",\n                      {\n                        model: {\n                          value: _vm.radioValues[item.id],\n                          callback: function ($$v) {\n                            _vm.$set(_vm.radioValues, item.id, $$v)\n                          },\n                          expression: \"radioValues[item.id]\",\n                        },\n                      },\n                      _vm._l(item.answerList, function (an) {\n                        return _c(\"el-radio\", { attrs: { label: an.id } }, [\n                          _vm._v(\n                            \" \" +\n                              _vm._s(an.abc) +\n                              \".\" +\n                              _vm._s(an.content) +\n                              \" \"\n                          ),\n                          an.image != null && an.image != \"\"\n                            ? _c(\n                                \"div\",\n                                { staticStyle: { clear: \"both\" } },\n                                [\n                                  _c(\"el-image\", {\n                                    staticStyle: { \"max-width\": \"100%\" },\n                                    attrs: { src: an.image },\n                                  }),\n                                ],\n                                1\n                              )\n                            : _vm._e(),\n                        ])\n                      }),\n                      1\n                    ),\n                    _c(\n                      \"el-row\",\n                      { attrs: { gutter: 24 } },\n                      [\n                        _c(\n                          \"el-col\",\n                          {\n                            staticStyle: { color: \"#24da70\" },\n                            attrs: { span: 12 },\n                          },\n                          [\n                            _vm._v(\n                              \" 正确答案：\" +\n                                _vm._s(_vm.radioRights[item.id]) +\n                                \" \"\n                            ),\n                          ]\n                        ),\n                        !item.answered\n                          ? _c(\n                              \"el-col\",\n                              {\n                                staticStyle: {\n                                  \"text-align\": \"right\",\n                                  color: \"#ff0000\",\n                                },\n                                attrs: { span: 12 },\n                              },\n                              [_vm._v(\" 答题结果：未答 \")]\n                            )\n                          : _vm._e(),\n                        item.answered && !item.isRight\n                          ? _c(\n                              \"el-col\",\n                              {\n                                staticStyle: {\n                                  \"text-align\": \"right\",\n                                  color: \"#ff0000\",\n                                },\n                                attrs: { span: 12 },\n                              },\n                              [\n                                _vm._v(\n                                  \" 答题结果：\" +\n                                    _vm._s(_vm.myRadio[item.id]) +\n                                    \" \"\n                                ),\n                              ]\n                            )\n                          : _vm._e(),\n                        item.answered && item.isRight\n                          ? _c(\n                              \"el-col\",\n                              {\n                                staticStyle: {\n                                  \"text-align\": \"right\",\n                                  color: \"#24da70\",\n                                },\n                                attrs: { span: 12 },\n                              },\n                              [\n                                _vm._v(\n                                  \" 答题结果：\" +\n                                    _vm._s(_vm.myRadio[item.id]) +\n                                    \" \"\n                                ),\n                              ]\n                            )\n                          : _vm._e(),\n                      ],\n                      1\n                    ),\n                  ],\n                  1\n                )\n              : _vm._e(),\n            item.quType === 4\n              ? _c(\n                  \"div\",\n                  [\n                    _c(\n                      \"el-row\",\n                      { attrs: { gutter: 24 } },\n                      [\n                        _c(\"el-col\", { attrs: { span: 12 } }, [\n                          _vm._v(\" 我的回答：\" + _vm._s(item.answer) + \" \"),\n                        ]),\n                      ],\n                      1\n                    ),\n                  ],\n                  1\n                )\n              : _vm._e(),\n            item.quType === 2\n              ? _c(\n                  \"div\",\n                  [\n                    _c(\n                      \"el-checkbox-group\",\n                      {\n                        model: {\n                          value: _vm.multiValues[item.id],\n                          callback: function ($$v) {\n                            _vm.$set(_vm.multiValues, item.id, $$v)\n                          },\n                          expression: \"multiValues[item.id]\",\n                        },\n                      },\n                      _vm._l(item.answerList, function (an) {\n                        return _c(\n                          \"el-checkbox\",\n                          { key: an.id, attrs: { label: an.id } },\n                          [\n                            _vm._v(\n                              _vm._s(an.abc) + \".\" + _vm._s(an.content) + \" \"\n                            ),\n                            an.image != null && an.image != \"\"\n                              ? _c(\n                                  \"div\",\n                                  { staticStyle: { clear: \"both\" } },\n                                  [\n                                    _c(\"el-image\", {\n                                      staticStyle: { \"max-width\": \"100%\" },\n                                      attrs: { src: an.image },\n                                    }),\n                                  ],\n                                  1\n                                )\n                              : _vm._e(),\n                          ]\n                        )\n                      }),\n                      1\n                    ),\n                    _c(\n                      \"el-row\",\n                      { attrs: { gutter: 24 } },\n                      [\n                        _c(\n                          \"el-col\",\n                          {\n                            staticStyle: { color: \"#24da70\" },\n                            attrs: { span: 12 },\n                          },\n                          [\n                            _vm._v(\n                              \" 正确答案：\" +\n                                _vm._s(_vm.multiRights[item.id].join(\",\")) +\n                                \" \"\n                            ),\n                          ]\n                        ),\n                        !item.answered\n                          ? _c(\n                              \"el-col\",\n                              {\n                                staticStyle: {\n                                  \"text-align\": \"right\",\n                                  color: \"#ff0000\",\n                                },\n                                attrs: { span: 12 },\n                              },\n                              [_vm._v(\" 答题结果：未答 \")]\n                            )\n                          : _vm._e(),\n                        item.answered && !item.isRight\n                          ? _c(\n                              \"el-col\",\n                              {\n                                staticStyle: {\n                                  \"text-align\": \"right\",\n                                  color: \"#ff0000\",\n                                },\n                                attrs: { span: 12 },\n                              },\n                              [\n                                _vm._v(\n                                  \" 答题结果：\" +\n                                    _vm._s(_vm.myMulti[item.id].join(\",\")) +\n                                    \" \"\n                                ),\n                              ]\n                            )\n                          : _vm._e(),\n                        item.answered && item.isRight\n                          ? _c(\n                              \"el-col\",\n                              {\n                                staticStyle: {\n                                  \"text-align\": \"right\",\n                                  color: \"#24da70\",\n                                },\n                                attrs: { span: 12 },\n                              },\n                              [\n                                _vm._v(\n                                  \" 答题结果：\" +\n                                    _vm._s(_vm.myMulti[item.id].join(\",\")) +\n                                    \" \"\n                                ),\n                              ]\n                            )\n                          : _vm._e(),\n                      ],\n                      1\n                    ),\n                  ],\n                  1\n                )\n              : _vm._e(),\n          ])\n        }),\n        0\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\n\n\n//# sourceURL=webpack:///./src/views/paper/exam/result.vue?./node_modules/cache-loader/dist/cjs.js?%7B%22cacheDirectory%22:%22node_modules/.cache/vue-loader%22,%22cacheIdentifier%22:%229323b05c-vue-loader-template%22%7D!./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options");

/***/ }),

/***/ "./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/paper/exam/result.vue?vue&type=style&index=0&id=14e8c060&scoped=true&lang=css&":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--7-oneOf-1-2!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/views/paper/exam/result.vue?vue&type=style&index=0&id=14e8c060&scoped=true&lang=css& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../../../node_modules/css-loader/dist/runtime/api.js */ \"./node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.i, \"\\n.qu-content[data-v-14e8c060]{\\n\\n  border-bottom: #eee 1px solid;\\n  padding-bottom: 10px;\\n}\\n.qu-content div[data-v-14e8c060]{\\n  line-height: 30px;\\n}\\n.el-checkbox-group label[data-v-14e8c060],.el-radio-group label[data-v-14e8c060]{\\n  width: 100%;\\n}\\n.card-title[data-v-14e8c060]{\\n  background: #eee;\\n  line-height: 35px;\\n  text-align: center;\\n  font-size: 14px;\\n}\\n.card-line[data-v-14e8c060]{\\n  padding-left: 10px\\n}\\n.card-line span[data-v-14e8c060] {\\n  cursor: pointer;\\n  margin: 2px;\\n}\\n\\n\", \"\"]);\n// Exports\nmodule.exports = exports;\n\n\n//# sourceURL=webpack:///./src/views/paper/exam/result.vue?./node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--7-oneOf-1-2!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options");

/***/ }),

/***/ "./node_modules/vue-style-loader/index.js?!./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/paper/exam/result.vue?vue&type=style&index=0&id=14e8c060&scoped=true&lang=css&":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/vue-style-loader??ref--7-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--7-oneOf-1-2!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/views/paper/exam/result.vue?vue&type=style&index=0&id=14e8c060&scoped=true&lang=css& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = __webpack_require__(/*! !../../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src??ref--7-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib??vue-loader-options!./result.vue?vue&type=style&index=0&id=14e8c060&scoped=true&lang=css& */ \"./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/paper/exam/result.vue?vue&type=style&index=0&id=14e8c060&scoped=true&lang=css&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.i, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = __webpack_require__(/*! ../../../../node_modules/vue-style-loader/lib/addStylesClient.js */ \"./node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"0e154291\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(false) {}\n\n//# sourceURL=webpack:///./src/views/paper/exam/result.vue?./node_modules/vue-style-loader??ref--7-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--7-oneOf-1-2!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options");

/***/ }),

/***/ "./src/api/paper/exam.js":
/*!*******************************!*\
  !*** ./src/api/paper/exam.js ***!
  \*******************************/
/*! exports provided: createPaper, paperDetail, quDetail, fillAnswer, handExam, paperResult, training, checkProcess */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"createPaper\", function() { return createPaper; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"paperDetail\", function() { return paperDetail; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"quDetail\", function() { return quDetail; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"fillAnswer\", function() { return fillAnswer; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"handExam\", function() { return handExam; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"paperResult\", function() { return paperResult; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"training\", function() { return training; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"checkProcess\", function() { return checkProcess; });\n/* harmony import */ var _utils_request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/request */ \"./src/utils/request.js\");\n\n/**\n * 创建试卷\n * @param data\n */\n\nfunction createPaper(data) {\n  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__[\"post\"])('/exam/api/paper/paper/create-paper', data);\n}\n/**\n * 试卷详情\n * @param data\n */\n\nfunction paperDetail(data) {\n  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__[\"post\"])('/exam/api/paper/paper/paper-detail', data);\n}\n/**\n * 题目详情\n * @param data\n */\n\nfunction quDetail(data) {\n  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__[\"post\"])('/exam/api/paper/paper/qu-detail', data);\n}\n/**\n * 填充答案\n * @param data\n */\n\nfunction fillAnswer(data) {\n  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__[\"post\"])('/exam/api/paper/paper/fill-answer', data);\n}\n/**\n * 交卷\n * @param data\n */\n\nfunction handExam(data) {\n  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__[\"post\"])('/exam/api/paper/paper/hand-exam', data);\n}\n/**\n * 试卷详情\n * @param data\n */\n\nfunction paperResult(data) {\n  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__[\"post\"])('/exam/api/paper/paper/paper-result', data);\n}\n/**\n * 错题训练\n * @param data\n */\n\nfunction training(data) {\n  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__[\"post\"])('/exam/api/paper/paper/training', data);\n}\n/**\n * 检查是否有进行中的考试\n * @returns {*}\n */\n\nfunction checkProcess() {\n  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__[\"post\"])('/exam/api/paper/paper/check-process', {});\n}\n\n//# sourceURL=webpack:///./src/api/paper/exam.js?");

/***/ }),

/***/ "./src/views/paper/exam/result.vue":
/*!*****************************************!*\
  !*** ./src/views/paper/exam/result.vue ***!
  \*****************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _result_vue_vue_type_template_id_14e8c060_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./result.vue?vue&type=template&id=14e8c060&scoped=true& */ \"./src/views/paper/exam/result.vue?vue&type=template&id=14e8c060&scoped=true&\");\n/* harmony import */ var _result_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./result.vue?vue&type=script&lang=js& */ \"./src/views/paper/exam/result.vue?vue&type=script&lang=js&\");\n/* empty/unused harmony star reexport *//* harmony import */ var _result_vue_vue_type_style_index_0_id_14e8c060_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./result.vue?vue&type=style&index=0&id=14e8c060&scoped=true&lang=css& */ \"./src/views/paper/exam/result.vue?vue&type=style&index=0&id=14e8c060&scoped=true&lang=css&\");\n/* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ \"./node_modules/vue-loader/lib/runtime/componentNormalizer.js\");\n\n\n\n\n\n\n/* normalize component */\n\nvar component = Object(_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(\n  _result_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n  _result_vue_vue_type_template_id_14e8c060_scoped_true___WEBPACK_IMPORTED_MODULE_0__[\"render\"],\n  _result_vue_vue_type_template_id_14e8c060_scoped_true___WEBPACK_IMPORTED_MODULE_0__[\"staticRenderFns\"],\n  false,\n  null,\n  \"14e8c060\",\n  null\n  \n)\n\n/* hot reload */\nif (false) { var api; }\ncomponent.options.__file = \"src/views/paper/exam/result.vue\"\n/* harmony default export */ __webpack_exports__[\"default\"] = (component.exports);\n\n//# sourceURL=webpack:///./src/views/paper/exam/result.vue?");

/***/ }),

/***/ "./src/views/paper/exam/result.vue?vue&type=script&lang=js&":
/*!******************************************************************!*\
  !*** ./src/views/paper/exam/result.vue?vue&type=script&lang=js& ***!
  \******************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_cache_loader_dist_cjs_js_ref_13_0_node_modules_babel_loader_lib_index_js_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_result_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/babel-loader/lib!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib??vue-loader-options!./result.vue?vue&type=script&lang=js& */ \"./node_modules/cache-loader/dist/cjs.js?!./node_modules/babel-loader/lib/index.js!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/paper/exam/result.vue?vue&type=script&lang=js&\");\n/* empty/unused harmony star reexport */ /* harmony default export */ __webpack_exports__[\"default\"] = (_node_modules_cache_loader_dist_cjs_js_ref_13_0_node_modules_babel_loader_lib_index_js_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_result_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[\"default\"]); \n\n//# sourceURL=webpack:///./src/views/paper/exam/result.vue?");

/***/ }),

/***/ "./src/views/paper/exam/result.vue?vue&type=style&index=0&id=14e8c060&scoped=true&lang=css&":
/*!**************************************************************************************************!*\
  !*** ./src/views/paper/exam/result.vue?vue&type=style&index=0&id=14e8c060&scoped=true&lang=css& ***!
  \**************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_result_vue_vue_type_style_index_0_id_14e8c060_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/vue-style-loader??ref--7-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src??ref--7-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib??vue-loader-options!./result.vue?vue&type=style&index=0&id=14e8c060&scoped=true&lang=css& */ \"./node_modules/vue-style-loader/index.js?!./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/paper/exam/result.vue?vue&type=style&index=0&id=14e8c060&scoped=true&lang=css&\");\n/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_result_vue_vue_type_style_index_0_id_14e8c060_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_result_vue_vue_type_style_index_0_id_14e8c060_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_result_vue_vue_type_style_index_0_id_14e8c060_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__) if([\"default\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_result_vue_vue_type_style_index_0_id_14e8c060_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));\n\n\n//# sourceURL=webpack:///./src/views/paper/exam/result.vue?");

/***/ }),

/***/ "./src/views/paper/exam/result.vue?vue&type=template&id=14e8c060&scoped=true&":
/*!************************************************************************************!*\
  !*** ./src/views/paper/exam/result.vue?vue&type=template&id=14e8c060&scoped=true& ***!
  \************************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_cache_loader_dist_cjs_js_cacheDirectory_node_modules_cache_vue_loader_cacheIdentifier_9323b05c_vue_loader_template_node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_result_vue_vue_type_template_id_14e8c060_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/cache-loader/dist/cjs.js?{\"cacheDirectory\":\"node_modules/.cache/vue-loader\",\"cacheIdentifier\":\"9323b05c-vue-loader-template\"}!../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib??vue-loader-options!./result.vue?vue&type=template&id=14e8c060&scoped=true& */ \"./node_modules/cache-loader/dist/cjs.js?{\\\"cacheDirectory\\\":\\\"node_modules/.cache/vue-loader\\\",\\\"cacheIdentifier\\\":\\\"9323b05c-vue-loader-template\\\"}!./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/paper/exam/result.vue?vue&type=template&id=14e8c060&scoped=true&\");\n/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, \"render\", function() { return _node_modules_cache_loader_dist_cjs_js_cacheDirectory_node_modules_cache_vue_loader_cacheIdentifier_9323b05c_vue_loader_template_node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_result_vue_vue_type_template_id_14e8c060_scoped_true___WEBPACK_IMPORTED_MODULE_0__[\"render\"]; });\n\n/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, \"staticRenderFns\", function() { return _node_modules_cache_loader_dist_cjs_js_cacheDirectory_node_modules_cache_vue_loader_cacheIdentifier_9323b05c_vue_loader_template_node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_result_vue_vue_type_template_id_14e8c060_scoped_true___WEBPACK_IMPORTED_MODULE_0__[\"staticRenderFns\"]; });\n\n\n\n//# sourceURL=webpack:///./src/views/paper/exam/result.vue?");

/***/ })

}]);