spring:
  application:
    name: yf-exam-lite
  profiles:
      active: dev
  main:
    allow-bean-definition-overriding: true
server:
  port: 8101
  # 启用服务端压缩
  compression:
    enabled: true
    min-response-size: 10
    mime-types: application/json,application/xml,text/html,text/xml,text/plain,application/javascript,text/css

# 天气预报评分配置
weather:
  scoring:
    # 单站总分
    total-score: 10.0
    # 各要素评分配置
    elements:
      # 风力评分配置 (满分1.0分)
      wind-force:
        max-score: 1.0
        scoring-type: level_based
        levels:
          exact: 1.0      # 完全匹配
          adjacent: 0.8   # 相邻等级
          wrong: 0.0      # 其他情况
        # 特殊规则
        special-rules:
          calm-wind: # 静风特殊处理
            - student: 0  # 考生预报静风
              standard: [0, 1]  # 标准答案静风和1级均得满分
              score: 1.0
          level-1: # 1级特殊处理
            - student: 1  # 考生预报1级
              standard: [0, 1, 2]  # 标准答案0级、1级、2级均得满分
              score: 1.0
          level-12: # 12级特殊处理
            - student: 12  # 考生预报12级
              standard: [12, 13]  # 标准答案12级和大于12级均得满分(13表示大于12级)
              score: 1.0

      # 风向评分配置 (满分1.0分) - 根据风向评分表
      wind-direction:
        max-score: 1.0
        # 每个方位对应的角度范围和得分规则（根据评分表的列）
        directions:
          N:  # 北风 - N列
            perfect-ranges: ["0.0-22.5", "337.5-360.0"]     # 1.0分
            good-ranges: ["22.5-45.0", "315.0-337.5"]       # 0.6分
          NE: # 东北风 - NE列
            perfect-ranges: ["22.5-45.0", "45.0-67.5"]      # 1.0分
            good-ranges: ["0.0-22.5", "67.5-90.0"]          # 0.6分
          E:  # 东风 - E列
            perfect-ranges: ["67.5-90.0", "90.0-112.5"]     # 1.0分
            good-ranges: ["45.0-67.5", "112.5-135.0"]       # 0.6分
          SE: # 东南风 - SE列
            perfect-ranges: ["112.5-135.0", "135.0-157.5"]  # 1.0分
            good-ranges: ["90.0-112.5", "157.5-180.0"]      # 0.6分
          S:  # 南风 - S列
            perfect-ranges: ["157.5-180.0", "180.0-202.5"]  # 1.0分
            good-ranges: ["135.0-157.5", "202.5-225.0"]     # 0.6分
          SW: # 西南风 - SW列
            perfect-ranges: ["202.5-225.0", "225.0-247.5"]  # 1.0分
            good-ranges: ["180.0-202.5", "247.5-270.0"]     # 0.6分
          W:  # 西风 - W列
            perfect-ranges: ["247.5-270.0", "270.0-292.5"]  # 1.0分
            good-ranges: ["225.0-247.5", "292.5-315.0"]     # 0.6分
          NW: # 西北风 - NW列
            perfect-ranges: ["292.5-315.0", "315.0-337.5"]  # 1.0分
            good-ranges: ["270.0-292.5", "337.5-360.0"]     # 0.6分

      # 最低气温评分配置 (满分2.0分)
      min-temperature:
        max-score: 2.0
        scoring-type: tolerance_based
        tolerance-enabled: true
        tolerance-range: 2  # ±2℃容差
        unit: "℃"
        levels:
          within-tolerance: 2.0  # 容差范围内
          outside-tolerance: 0.0 # 超出容差范围

      # 最高气温评分配置 (满分2.0分)
      max-temperature:
        max-score: 2.0
        scoring-type: tolerance_based
        tolerance-enabled: true
        tolerance-range: 2  # ±2℃容差
        unit: "℃"
        levels:
          within-tolerance: 2.0  # 容差范围内
          outside-tolerance: 0.0 # 超出容差范围

      # 降水评分配置 (满分2.0分)
      precipitation:
        max-score: 2.0
        scoring-type: matrix_based
        levels:
          exact-match: 2.0      # 完全匹配
          adjacent-level: 1.2   # 相邻等级/特殊关系
          wrong: 0.0           # 其他情况
        # 降水评分矩阵在代码中初始化

      # 灾害性天气评分配置 (满分2.0分)
      disaster-weather:
        max-score: 2.0
        scoring-type: exact_match_with_proportion
        algorithm: proportion_based
        # 有效的灾害天气类型
        valid-types:
          - "无"
          - "暴雨"
          - "暴雪"
          - "高温"
          - "大风"
          - "寒潮"
          - "大雾"
          - "沙尘暴"
          - "雷暴"
          - "冰雹"
        # 评分规则
        rules:
          exact-match: 2.0      # 完全匹配得满分
          proportion-match: true # 多种灾害时按正确比例给分
          no-extra-penalty: true # 额外预报的灾害不扣分
          none-special-handling: true # "无"的特殊处理
        # 特殊规则
        special-rules:
          none-match: # "无"的匹配规则
            both-none: 2.0      # 都为"无"
            one-none: 0.0       # 一个为"无"，一个有灾害
          proportion-formula: "匹配数量 / 实况总数量 * 2.0" # 比例评分公式

