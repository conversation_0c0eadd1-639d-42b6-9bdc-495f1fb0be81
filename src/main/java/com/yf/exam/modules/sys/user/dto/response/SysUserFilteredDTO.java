package com.yf.exam.modules.sys.user.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
* <p>
* 过滤敏感信息的用户响应类
* </p>
*
* <AUTHOR>
* @since 2020-04-13 16:57
*/
@Data
@ApiModel(value="过滤后的用户信息", description="过滤后的用户信息")
public class SysUserFilteredDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID", required=true)
    private String id;

    @ApiModelProperty(value = "用户名", required=true)
    private String userName;

    @ApiModelProperty(value = "真实姓名", required=true)
    private String realName;

    @ApiModelProperty(value = "部门ID", required=true)
    private String departId;

    @ApiModelProperty(value = "部门名称", required=true)
    private String deptName;

    @ApiModelProperty(value = "单位名称", required=true)
    private String groupName;

    @ApiModelProperty(value = "手机号", required=true)
    private String phone;

    @ApiModelProperty(value = "创建时间", required=true)
    private Date createTime;

    @ApiModelProperty(value = "更新时间", required=true)
    private Date updateTime;

    @ApiModelProperty(value = "状态", required=true)
    private Integer state;

    @ApiModelProperty(value = "角色列表", required=true)
    private List<String> roles;
    @ApiModelProperty(value = "密码", required=true)
    private List<String> password;

} 