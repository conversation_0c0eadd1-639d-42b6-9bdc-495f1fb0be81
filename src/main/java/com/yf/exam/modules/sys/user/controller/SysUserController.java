package com.yf.exam.modules.sys.user.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.core.api.dto.BaseIdsReqDTO;
import com.yf.exam.core.api.dto.BaseStateReqDTO;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.modules.sys.user.dto.SysUserDTO;
import com.yf.exam.modules.sys.user.dto.request.SysUserLoginReqDTO;
import com.yf.exam.modules.sys.user.dto.request.SysUserSaveReqDTO;
import com.yf.exam.modules.sys.user.dto.request.UpdateUserStateReqDTO;
import com.yf.exam.modules.sys.user.dto.response.SysUserAppLoginDTO;
import com.yf.exam.modules.sys.user.dto.response.SysUserLoginDTO;
import com.yf.exam.modules.sys.user.dto.response.SysUserFilteredDTO;
import com.yf.exam.modules.sys.user.entity.SysUser;
import com.yf.exam.modules.sys.user.service.SysUserService;
import com.yf.exam.modules.user.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <p>
 * 管理用户控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-13 16:57
 */
@Api(tags = {"管理用户"})
@RestController
@RequestMapping("/exam/api/sys/user")
@Slf4j
public class SysUserController extends BaseController {

    @Autowired
    private SysUserService baseService;

    /**
     * 用户登录
     * @return
     */
    @CrossOrigin
    @ApiOperation(value = "用户登录")
    @RequestMapping(value = "/login", method = {RequestMethod.POST})
    public ApiRest<SysUserLoginDTO> login(@RequestBody SysUserLoginReqDTO reqDTO) {
        SysUserLoginDTO respDTO = baseService.login(reqDTO.getUsername(), reqDTO.getPassword());
        log.info("用户 {} 登录系统", respDTO.getRealName());
        return super.success(respDTO);
    }

    @CrossOrigin
    @ApiOperation(value = "用户登录")
    @RequestMapping(value = "/appLogin", method = {RequestMethod.POST})
    public ApiRest<SysUserAppLoginDTO> appLogin(@RequestBody SysUserLoginReqDTO reqDTO) {
        SysUserAppLoginDTO respDTO = baseService.login2(reqDTO.getUsername(), reqDTO.getPassword());
        log.info("用户 {} 通过APP登录系统", respDTO.getRealName());
        return super.success(respDTO);
    }

    @RequestMapping(value = "/getRankingUser", method = {RequestMethod.POST})
    public ApiRest<List<SysUserAppLoginDTO>> getRankingUser(){
        List<SysUserAppLoginDTO> rankingUser = baseService.getRankingUser();
        SysUserLoginDTO user = UserUtils.getUser();
        log.info("用户 {} 获取排行榜", user.getUserName());
        return super.success(rankingUser);
    }

    /**
     * 用户登录
     * @return
     */
    @CrossOrigin
    @ApiOperation(value = "用户登录")
    @RequestMapping(value = "/logout", method = {RequestMethod.POST})
    public ApiRest logout(HttpServletRequest request) {
        String token = request.getHeader("token");
        SysUserLoginDTO user = baseService.token(token);
        log.info("用户 {} 退出系统", user.getRealName());
        baseService.logout(token);
        return super.success();
    }

    /**
     * 获取会话
     * @return
     */
    @ApiOperation(value = "获取会话")
    @RequestMapping(value = "/info", method = {RequestMethod.POST})
    public ApiRest info(@RequestParam("token") String token) {
        SysUserLoginDTO respDTO = baseService.token(token);
        return success(respDTO);
    }

    /**
     * 修改用户资料
     * @return
     */
    @ApiOperation(value = "修改用户资料")
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ApiRest update(@RequestBody SysUserDTO reqDTO) {
        baseService.update(reqDTO);
        log.info("用户 {} 修改了个人资料", reqDTO.getRealName());
        return success("信息修改成功！",null);
    }


    /**
     * 保存或修改系统用户
     * @return
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "保存或修改")
    @RequestMapping(value = "/save", method = {RequestMethod.POST})
    public ApiRest save(@RequestBody SysUserSaveReqDTO reqDTO) {
        baseService.save(reqDTO);
        log.info("管理员创建/修改了用户：{}", reqDTO.getRealName());
        return super.success();
    }


    /**
     * 批量删除
     * @param reqDTO
     * @return
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "批量删除")
    @RequestMapping(value = "/delete", method = { RequestMethod.POST})
    public ApiRest edit(@RequestBody BaseIdsReqDTO reqDTO) {
        //根据ID删除
        baseService.removeByIds(reqDTO.getIds());
        log.info("管理员批量删除了用户，ID列表：{}", reqDTO.getIds());
        return super.success();
    }

    /**
     * 分页查找
     * @param reqDTO
     * @return
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "分页查找")
    @RequestMapping(value = "/paging", method = { RequestMethod.POST})
    public ApiRest<IPage<SysUserFilteredDTO>> paging(@RequestBody PagingReqDTO<SysUserDTO> reqDTO) {
        IPage<SysUserFilteredDTO> page = baseService.paging(reqDTO);
        return super.success(page);
    }

    /**
     * 修改用户状态
     * @param reqDTO
     * @return
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "修改用户状态")
    @RequestMapping(value = "/state", method = { RequestMethod.POST})
    public ApiRest<String> updateState(@RequestBody UpdateUserStateReqDTO reqDTO) {
        baseService.updateState(reqDTO);
        return super.success();
    }

    /**
     * 保存或修改系统用户
     * @return
     */
//    @ApiOperation(value = "学员注册")
//    @RequestMapping(value = "/reg", method = {RequestMethod.POST})
//    public ApiRest<SysUserLoginDTO> reg(@RequestBody SysUserDTO reqDTO) {
//        SysUserLoginDTO respDTO = baseService.reg(reqDTO);
//        return success(respDTO);
//    }

    /**
     * 快速注册，如果手机号存在则登录，不存在就注册
     * @return
     */
//    @ApiOperation(value = "快速注册")
//    @RequestMapping(value = "/quick-reg", method = {RequestMethod.POST})
//    public ApiRest<SysUserLoginDTO> quick(@RequestBody SysUserDTO reqDTO) {
//        SysUserLoginDTO respDTO = baseService.quickReg(reqDTO);
//        return success(respDTO);
//    }
}
