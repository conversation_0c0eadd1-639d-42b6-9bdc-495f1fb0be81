package com.yf.exam.modules.sys.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.modules.sys.user.dto.SysUserDTO;
import com.yf.exam.modules.sys.user.dto.response.SysUserFilteredDTO;
import com.yf.exam.modules.sys.user.entity.SysUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <p>
* 用户Mapper
* </p>
*
* <AUTHOR>
* @since 2020-04-13 16:57
*/
public interface SysUserMapper extends BaseMapper<SysUser> {
    
    /**
     * 分页查询用户信息
     * @param page 分页对象
     * @param query 查询参数
     * @return 分页结果
     */
    IPage<SysUserFilteredDTO> paging(IPage<SysUserFilteredDTO> page, @Param("params") SysUserDTO params);

    /**
     * 获取排名用户
     * @param userId
     * @return
     */
    List<SysUser> getRankingUser(@Param("userId") String userId);
}
