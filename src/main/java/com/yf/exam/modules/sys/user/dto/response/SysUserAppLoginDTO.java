package com.yf.exam.modules.sys.user.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
* <p>
* 管理用户请求类
* </p>
*
* <AUTHOR>
* @since 2020-04-13 16:57
*/
@Data
@ApiModel(value="管理用户登录响应类", description="管理用户登录响应类")
public class SysUserAppLoginDTO extends SysUserLoginDTO {


    private String phone;

    @ApiModelProperty(value = "状态", required=true)
    private Integer score;
    private Integer count;
    private Integer success;
    
}
