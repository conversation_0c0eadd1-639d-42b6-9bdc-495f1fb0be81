package com.yf.exam.modules.user.dto.request;

import com.yf.exam.core.api.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* <p>
* 错题集查询请求参数
* </p>
*
* <AUTHOR>
* @since 2020-05-25 17:31
*/
@Data
@ApiModel(value="错题集查询请求", description="错题集查询请求")
public class UserWrongBookQueryReqDTO extends BaseDTO {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "题目类型")
    private Integer quType;
    
    @ApiModelProperty(value = "是否已掌握")
    private Boolean isMastered;
} 