package com.yf.exam.modules.user.dto.ext;

import com.yf.exam.modules.user.entity.UserQuFavorite;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* <p>
* 用户试题收藏详情数据传输对象
* </p>
*
* <AUTHOR>
* @since 2020-05-27 17:56
*/
@Data
@ApiModel(value="用户试题收藏详情", description="用户试题收藏详情")
public class UserQuFavoriteDetailDTO extends UserQuFavorite {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "题目内容", required=true)
    private String content;

    @ApiModelProperty(value = "题目图片", required=true)
    private String image;

    @ApiModelProperty(value = "题目类型", required=true)
    private Integer quType;

    @ApiModelProperty(value = "题目备注", required=true)
    private String remark;

    @ApiModelProperty(value = "整题解析", required=true)
    private String analysis;

    @ApiModelProperty(value = "正确答案", required=true)
    private String rightAnswer;
} 