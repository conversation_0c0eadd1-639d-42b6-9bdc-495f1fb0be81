package com.yf.exam.modules.user.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.modules.paper.dto.request.APPFinishExamDTO;
import com.yf.exam.modules.user.dto.ext.UserWrongBookQuDetailDTO;
import com.yf.exam.modules.user.dto.request.UserWrongBookQueryReqDTO;
import com.yf.exam.modules.user.entity.UserWrongBook;

import java.util.List;

/**
* <p>
* 用户错题本业务类
* </p>
*
* <AUTHOR>
* @since 2020-05-25 16:33
*/
public interface UserWrongBookService extends IService<UserWrongBook> {

    /**
     * 保存用户错题记录
     * @param userId 用户ID
     * @param quId 题目ID
     * @param quType 题目类型
     * @param answer 用户答案
     * @return 是否成功
     */
    boolean saveUserWrong(String userId, String quId, Integer quType, String answer);

    /**
     * 保存用户错题（APP提交答案时使用）
     * @param questions 提交的题目列表
     */
    void saveUserWrongFromApp(List<APPFinishExamDTO> questions);

    /**
     * 分页查询错题列表
     * @param reqDTO 查询参数
     * @return 错题列表分页数据
     */
    IPage<UserWrongBookQuDetailDTO> paging(PagingReqDTO<UserWrongBookQueryReqDTO> reqDTO);

    /**
     * 查询错题详情
     * @param userId 用户ID
     * @param quId 题目ID
     * @return 错题详情
     */
    UserWrongBookQuDetailDTO detail(String userId, String quId);

    /**
     * 标记题目为已掌握
     * @param id 错题记录ID
     * @return 是否成功
     */
    boolean setMastered(String id);

    /**
     * 标记题目为未掌握
     * @param id 错题记录ID
     * @return 是否成功
     */
    boolean setUnMastered(String id);

} 