package com.yf.exam.modules.user.dto.ext;

import com.yf.exam.modules.user.dto.UserWrongBookDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
* <p>
* 错题集详情数据传输对象
* </p>
*
* <AUTHOR>
* @since 2020-05-25 17:31
*/
@Data
@ApiModel(value="错题集与题目详情", description="错题集与题目详情")
public class UserWrongBookQuDetailDTO extends UserWrongBookDTO {

    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "题目内容", required=true)
    private String content;
    
    @ApiModelProperty(value = "题目图片", required=true)
    private String image;
    
    @ApiModelProperty(value = "题目备注", required=true)
    private String remark;
    
    @ApiModelProperty(value = "整题解析", required=true)
    private String analysis;
    
    @ApiModelProperty(value = "难度", required=true)
    private Integer level;
    
    @ApiModelProperty(value = "选项列表", required=true)
    private List<String> options;
    
    @ApiModelProperty(value = "正确答案", required=true)
    private String rightAnswer;

    @ApiModelProperty(value = "用户的错误答案ID", required=true)
    private String wrongAnswer;

    @ApiModelProperty(value = "用户的错误答案内容", required=true)
    private String wrongAnswerContent;
} 