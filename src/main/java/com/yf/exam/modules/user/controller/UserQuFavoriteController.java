package com.yf.exam.modules.user.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.core.api.dto.BaseDTO;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.modules.sys.user.dto.response.SysUserLoginDTO;
import com.yf.exam.modules.user.UserUtils;
import com.yf.exam.modules.user.dto.ext.UserQuFavoriteDetailDTO;
import com.yf.exam.modules.user.service.UserQuFavoriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
* <p>
* 用户试题收藏控制器
* </p>
*
* <AUTHOR>
* @since 2020-05-27 17:56
*/
@Slf4j
@Api(tags={"用户试题收藏"})
@RestController
@RequestMapping("/exam/api/user/qu/favorite")
public class UserQuFavoriteController extends BaseController {

    @Autowired
    private UserQuFavoriteService userQuFavoriteService;

    @ApiOperation(value = "分页查询收藏列表")
    @RequestMapping(value = "/paging", method = RequestMethod.POST)
    public ApiRest<IPage<UserQuFavoriteDetailDTO>> paging(@RequestBody PagingReqDTO reqDTO) {
        // 设置当前用户ID
        reqDTO.setUserId(UserUtils.getUserId());
        // 查询分页数据
        IPage<UserQuFavoriteDetailDTO> page = userQuFavoriteService.paging(reqDTO);
        return super.success(page);
    }

    @ApiOperation(value = "查找详情")
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    public ApiRest<UserQuFavoriteDetailDTO> detail(@RequestBody QuIdReqDTO reqDTO) {
        UserQuFavoriteDetailDTO detail = userQuFavoriteService.detail(UserUtils.getUserId(), reqDTO.getQuId());
        return super.success(detail);
    }

    @ApiOperation(value = "收藏试题")
    @RequestMapping(value = "/favorite", method = RequestMethod.POST)
    public ApiRest<Boolean> favorite(@RequestBody QuIdReqDTO reqDTO) {
        SysUserLoginDTO user = UserUtils.getUser();
        boolean result = userQuFavoriteService.favorite(UserUtils.getUserId(), reqDTO.getQuId());
        log.info("用户 {} 收藏了试题，试题ID：{}", user.getUserName(), reqDTO.getQuId());
        return super.success(result);
    }

    @ApiOperation(value = "取消收藏")
    @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    public ApiRest<Boolean> cancelFavorite(@RequestBody QuIdReqDTO reqDTO) {
        SysUserLoginDTO user = UserUtils.getUser();
        boolean result = userQuFavoriteService.cancelFavorite(UserUtils.getUserId(), reqDTO.getQuId());
        log.info("用户 {} 取消收藏了试题，试题ID：{}", user.getUserName(), reqDTO.getQuId());
        return super.success(result);
    }

    @ApiOperation(value = "检查试题是否已被收藏")
    @RequestMapping(value = "/check", method = RequestMethod.POST)
    public ApiRest<Boolean> checkFavorite(@RequestBody QuIdReqDTO reqDTO) {
        boolean result = userQuFavoriteService.checkFavorite(UserUtils.getUserId(), reqDTO.getQuId());
        return super.success(result);
    }

    @Data
    public static class QuIdReqDTO extends BaseDTO {
        private String quId;
    }
} 