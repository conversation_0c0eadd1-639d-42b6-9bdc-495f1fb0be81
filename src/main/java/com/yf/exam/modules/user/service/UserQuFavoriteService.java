package com.yf.exam.modules.user.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.modules.user.dto.ext.UserQuFavoriteDetailDTO;
import com.yf.exam.modules.user.entity.UserQuFavorite;

/**
* <p>
* 用户试题收藏业务类
* </p>
*
* <AUTHOR>
* @since 2020-05-27 17:56
*/
public interface UserQuFavoriteService extends IService<UserQuFavorite> {

    /**
     * 分页查询收藏列表
     * @param reqDTO
     * @return
     */
    IPage<UserQuFavoriteDetailDTO> paging(PagingReqDTO reqDTO);

    /**
     * 查找详情
     * @param userId
     * @param quId
     * @return
     */
    UserQuFavoriteDetailDTO detail(String userId, String quId);

    /**
     * 收藏试题
     * @param userId
     * @param quId
     * @return
     */
    boolean favorite(String userId, String quId);

    /**
     * 取消收藏
     * @param userId
     * @param quId
     * @return
     */
    boolean cancelFavorite(String userId, String quId);

    /**
     * 检查试题是否已被收藏
     * @param userId
     * @param quId
     * @return
     */
    boolean checkFavorite(String userId, String quId);
} 