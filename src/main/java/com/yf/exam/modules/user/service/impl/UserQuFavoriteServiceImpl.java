package com.yf.exam.modules.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.core.exception.ServiceException;
import com.yf.exam.modules.user.dto.ext.UserQuFavoriteDetailDTO;
import com.yf.exam.modules.user.entity.UserQuFavorite;
import com.yf.exam.modules.user.mapper.UserQuFavoriteMapper;
import com.yf.exam.modules.user.service.UserQuFavoriteService;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
* <p>
* 用户试题收藏业务实现类
* </p>
*
* <AUTHOR>
* @since 2020-05-27 17:56
*/
@Service
public class UserQuFavoriteServiceImpl extends ServiceImpl<UserQuFavoriteMapper, UserQuFavorite> implements UserQuFavoriteService {

    // 最大收藏数量
    private static final int MAX_FAVORITE_COUNT = 25;

    @Override
    public IPage<UserQuFavoriteDetailDTO> paging(PagingReqDTO reqDTO) {
        
        // 创建分页对象
        Page page = new Page(reqDTO.getCurrent(), reqDTO.getSize());
        
        // 查询分页数据
        return baseMapper.paging(page, reqDTO.getUserId());
    }

    @Override
    public UserQuFavoriteDetailDTO detail(String userId, String quId) {
        return baseMapper.detail(userId, quId);
    }

    @Override
    public boolean favorite(String userId, String quId) {
        
        // 查找是否已经收藏
        QueryWrapper<UserQuFavorite> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(UserQuFavorite::getUserId, userId)
                .eq(UserQuFavorite::getQuId, quId);
        
        UserQuFavorite favorite = this.getOne(wrapper);
        
        // 如果已收藏，直接返回
        if(favorite != null) {
            return true;
        }

        // 检查收藏数量是否超过限制
        QueryWrapper<UserQuFavorite> countWrapper = new QueryWrapper<>();
        countWrapper.lambda().eq(UserQuFavorite::getUserId, userId);
        int count = this.count(countWrapper);
        
        if(count >= MAX_FAVORITE_COUNT) {
            throw new ServiceException(2,"收藏数量已达到上限（" + MAX_FAVORITE_COUNT + "道题），请取消部分收藏后再试！");
        }
        
        // 创建新记录
        favorite = new UserQuFavorite();
        favorite.setUserId(userId);
        favorite.setQuId(quId);
        favorite.setCreateTime(new Date());
        favorite.setUpdateTime(new Date());
        
        return this.save(favorite);
    }

    @Override
    public boolean cancelFavorite(String userId, String quId) {
        
        // 查找收藏记录
        QueryWrapper<UserQuFavorite> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(UserQuFavorite::getUserId, userId)
                .eq(UserQuFavorite::getQuId, quId);
        
        return this.remove(wrapper);
    }

    @Override
    public boolean checkFavorite(String userId, String quId) {
        // 查找是否已经收藏
        QueryWrapper<UserQuFavorite> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(UserQuFavorite::getUserId, userId)
                .eq(UserQuFavorite::getQuId, quId);
        
        return this.count(wrapper) > 0;
    }
} 