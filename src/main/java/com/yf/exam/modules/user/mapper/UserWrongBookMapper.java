package com.yf.exam.modules.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yf.exam.modules.user.dto.ext.UserWrongBookQuDetailDTO;
import com.yf.exam.modules.user.entity.UserWrongBook;
import com.yf.exam.modules.user.dto.request.UserWrongBookQueryReqDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <p>
* 用户错题本Mapper
* </p>
*
* <AUTHOR>
* @since 2020-05-25 16:33
*/
public interface UserWrongBookMapper extends BaseMapper<UserWrongBook> {

    /**
     * 查找错题详情列表
     * @param page 分页对象
     * @param userId 用户ID
     * @param params 查询参数
     * @return 错题详情列表
     */
    IPage<UserWrongBookQuDetailDTO> paging(Page page, 
                                         @Param("userId") String userId,
                                         @Param("params") UserWrongBookQueryReqDTO params);

    /**
     * 查找错题详情
     * @param userId 用户ID
     * @param wrongBookId 错题记录ID
     * @return 错题详情
     */
    UserWrongBookQuDetailDTO selectDetailByUserAndQuId(@Param("userId") String userId,
                                                     @Param("wrongBookId") String wrongBookId);
} 