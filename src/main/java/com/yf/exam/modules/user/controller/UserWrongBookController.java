package com.yf.exam.modules.user.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.core.api.dto.BaseIdReqDTO;
import com.yf.exam.core.api.dto.BaseIdsReqDTO;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.modules.user.UserUtils;
import com.yf.exam.modules.user.dto.ext.UserWrongBookQuDetailDTO;
import com.yf.exam.modules.user.dto.request.UserWrongBookQueryReqDTO;
import com.yf.exam.modules.user.service.UserWrongBookService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 用户错题集控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-25 17:31
 */
@Api(tags = {"错题集"})
@RestController
@RequestMapping("/exam/api/user/wrong-book-v2")
@Slf4j
public class UserWrongBookController extends BaseController {

    @Autowired
    private UserWrongBookService userWrongBookService;

    /**
     * 分页查询
     * @param reqDTO 请求参数
     * @return 错题集列表
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @RequestMapping(value = "/paging", method = { RequestMethod.POST })
    public ApiRest<IPage<UserWrongBookQuDetailDTO>> paging(@RequestBody PagingReqDTO<UserWrongBookQueryReqDTO> reqDTO) {
        IPage<UserWrongBookQuDetailDTO> page = userWrongBookService.paging(reqDTO);
        return super.success(page);
    }

    /**
     * 查询错题详情
     * @param reqDTO 请求参数，包含题目ID
     * @return 错题详情
     */
    @ApiOperation(value = "错题详情", notes = "错题详情")
    @RequestMapping(value = "/detail", method = { RequestMethod.POST })
    public ApiRest<UserWrongBookQuDetailDTO> detail(@RequestBody BaseIdReqDTO reqDTO) {
        UserWrongBookQuDetailDTO dto = userWrongBookService.detail(UserUtils.getUserId(), reqDTO.getId());
        return super.success(dto);
    }

    /**
     * 标记为已掌握
     * @param reqDTO 请求参数，包含错题ID
     * @return 操作结果
     */
    @ApiOperation(value = "标记为已掌握", notes = "标记为已掌握")
    @RequestMapping(value = "/set-mastered", method = { RequestMethod.POST })
    public ApiRest<Boolean> setMastered(@RequestBody BaseIdReqDTO reqDTO) {
        boolean result = userWrongBookService.setMastered(reqDTO.getId());
        return super.success(result);
    }

    /**
     * 标记为未掌握
     * @param reqDTO 请求参数，包含错题ID
     * @return 操作结果
     */
    @ApiOperation(value = "标记为未掌握", notes = "标记为未掌握")
    @RequestMapping(value = "/set-unmastered", method = { RequestMethod.POST })
    public ApiRest<Boolean> setUnMastered(@RequestBody BaseIdReqDTO reqDTO) {
        boolean result = userWrongBookService.setUnMastered(reqDTO.getId());
        return super.success(result);
    }
} 