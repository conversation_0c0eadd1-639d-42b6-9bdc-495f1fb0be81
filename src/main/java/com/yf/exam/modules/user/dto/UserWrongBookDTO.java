package com.yf.exam.modules.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* <p>
* 用户错题本请求类
* </p>
*
* <AUTHOR>
* @since 2020-05-25 17:31
*/
@Data
@ApiModel(value="用户错题本", description="用户错题本")
public class UserWrongBookDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID", required=true)
    private String id;

    @ApiModelProperty(value = "用户ID", required=true)
    private String userId;

    @ApiModelProperty(value = "题目ID", required=true)
    private String quId;

    @ApiModelProperty(value = "题目类型", required=true)
    private Integer quType;
    
    @ApiModelProperty(value = "用户的答案", required=true)
    private String answer;
    
    @ApiModelProperty(value = "错误次数", required=true)
    private Integer wrongCount;
    
    @ApiModelProperty(value = "是否已掌握", required=true)
    private Boolean isMastered;
    
    @ApiModelProperty(value = "创建时间", required=true)
    private Date createTime;
    
    @ApiModelProperty(value = "更新时间", required=true)
    private Date updateTime;
} 