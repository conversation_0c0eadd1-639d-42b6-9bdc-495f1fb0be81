package com.yf.exam.modules.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.core.utils.BeanMapper;
import com.yf.exam.modules.paper.dto.request.APPFinishExamDTO;
import com.yf.exam.modules.paper.dto.request.APPFinishExamAnswerDTO;
import com.yf.exam.modules.user.UserUtils;
import com.yf.exam.modules.user.dto.ext.UserWrongBookQuDetailDTO;
import com.yf.exam.modules.user.dto.request.UserWrongBookQueryReqDTO;
import com.yf.exam.modules.user.entity.UserWrongBook;
import com.yf.exam.modules.user.mapper.UserWrongBookMapper;
import com.yf.exam.modules.user.service.UserWrongBookService;
import com.yf.exam.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* <p>
* 用户错题本业务实现类
* </p>
*
* <AUTHOR>
* @since 2020-05-25 16:33
*/
@Slf4j
@Service
public class UserWrongBookServiceImpl extends ServiceImpl<UserWrongBookMapper, UserWrongBook> implements UserWrongBookService {

    @Override
    public boolean saveUserWrong(String userId, String quId, Integer quType, String answer) {
        
        // 查找是否已经存在该错题记录
        QueryWrapper<UserWrongBook> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(UserWrongBook::getUserId, userId)
                .eq(UserWrongBook::getQuId, quId);
        
        UserWrongBook wrongBook = this.getOne(wrapper);
        
        // 如果不存在，则创建新记录
        if(wrongBook == null) {
            wrongBook = new UserWrongBook();
            wrongBook.setUserId(userId);
            wrongBook.setQuId(quId);
            wrongBook.setQuType(quType);
            wrongBook.setAnswer(answer);
            wrongBook.setWrongCount(1);
            wrongBook.setIsMastered(false);
            wrongBook.setCreateTime(new Date());
            wrongBook.setUpdateTime(new Date());
            return this.save(wrongBook);
        }
        
        // 如果已存在，则更新错误次数和时间
        wrongBook.setWrongCount(wrongBook.getWrongCount() + 1);
        String oldAnswer = wrongBook.getAnswer();
        wrongBook.setAnswer(answer);
        wrongBook.setUpdateTime(new Date());
        return this.updateById(wrongBook);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveUserWrongFromApp(List<APPFinishExamDTO> questions) {
        
        if(questions == null || questions.isEmpty()) {
            return;
        }
        
        String userId = UserUtils.getUserId();
        
        // 遍历提交的题目，找出错误的保存
        for(APPFinishExamDTO question : questions) {
            // 只有答错的题目才保存到错题集
            if(!question.getResult()) {
                try {
                    // 获取用户选择的答案ID列表
                    List<String> userAnswerIds = StringUtils.isBlank(question.getUserAnswer()) ? 
                            new ArrayList<>() : 
                            Arrays.asList(question.getUserAnswer().split(","));
                    
                    // 从答案列表中找出用户选择的答案内容
                    List<String> wrongAnswerContents = question.getAnswers().stream()
                            .filter(answer -> userAnswerIds.contains(answer.getId()))
                            .map(answer -> answer.getContent())
                            .collect(Collectors.toList());
                    
                    String wrongAnswerContent = String.join(",", wrongAnswerContents);
                    // log.info("保存用户错题，用户ID：{}，题目ID：{}，错误答案内容：{}", userId, question.getId(), wrongAnswerContent);
                    this.saveUserWrong(userId, question.getId(), question.getQuType(), wrongAnswerContent);
                } catch (Exception e) {
                    // log.error("保存用户错题失败，用户ID：{}，题目ID：{}", userId, question.getId(), e);
                }
            }
        }
    }

    @Override
    public IPage<UserWrongBookQuDetailDTO> paging(PagingReqDTO<UserWrongBookQueryReqDTO> reqDTO) {
        
        // 创建分页对象
        Page page = new Page(reqDTO.getCurrent(), reqDTO.getSize());
        
        // 获取查询参数
        UserWrongBookQueryReqDTO params = reqDTO.getParams();
        if(params == null) {
            params = new UserWrongBookQueryReqDTO();
        }
        
        // 获取当前用户ID
        String userId = UserUtils.getUserId();
        
        // 查询分页数据
        return baseMapper.paging(page, userId, params);
    }

    @Override
    public UserWrongBookQuDetailDTO detail(String userId, String quId) {
        if(StringUtils.isBlank(userId)) {
            userId = UserUtils.getUserId();
        }
        return baseMapper.selectDetailByUserAndQuId(userId, quId);
    }

    @Override
    public boolean setMastered(String id) {
        UserWrongBook wrongBook = this.getById(id);
        if(wrongBook != null) {
            wrongBook.setIsMastered(true);
            wrongBook.setUpdateTime(new Date());
            return this.updateById(wrongBook);
        }
        return false;
    }

    @Override
    public boolean setUnMastered(String id) {
        UserWrongBook wrongBook = this.getById(id);
        if(wrongBook != null) {
            wrongBook.setIsMastered(false);
            wrongBook.setUpdateTime(new Date());
            return this.updateById(wrongBook);
        }
        return false;
    }
} 