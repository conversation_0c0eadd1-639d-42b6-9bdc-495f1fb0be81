package com.yf.exam.modules.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yf.exam.modules.user.dto.ext.UserQuFavoriteDetailDTO;
import com.yf.exam.modules.user.entity.UserQuFavorite;
import org.apache.ibatis.annotations.Param;

/**
* <p>
* 用户试题收藏Mapper
* </p>
*
* <AUTHOR>
* @since 2020-05-27 17:56
*/
public interface UserQuFavoriteMapper extends BaseMapper<UserQuFavorite> {

    /**
     * 分页查询收藏列表
     * @param page
     * @param userId
     * @return
     */
    IPage<UserQuFavoriteDetailDTO> paging(Page page, @Param("userId") String userId);

    /**
     * 查找详情
     * @param userId
     * @param quId
     * @return
     */
    UserQuFavoriteDetailDTO detail(@Param("userId") String userId, @Param("quId") String quId);
} 