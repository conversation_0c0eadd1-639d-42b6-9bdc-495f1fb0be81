package com.yf.exam.modules.user.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
* <p>
* 用户错题本实体类
* </p>
*
* <AUTHOR>
* @since 2020-05-25 17:31
*/
@Data
@TableName("el_user_wrong_book")
public class UserWrongBook extends Model<UserWrongBook> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 题目ID
     */
    @TableField("qu_id")
    private String quId;

    /**
     * 题目类型
     */
    @TableField("qu_type")
    private Integer quType;
    
    /**
     * 用户的答案
     */
    private String answer;
    
    /**
     * 错误次数
     */
    @TableField("wrong_count")
    private Integer wrongCount;
    
    /**
     * 是否已掌握
     */
    @TableField("is_mastered")
    private Boolean isMastered;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;
} 