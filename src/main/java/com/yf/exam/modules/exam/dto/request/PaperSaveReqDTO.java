package com.yf.exam.modules.exam.dto.request;

import com.yf.exam.modules.exam.dto.ExamDTO;
import com.yf.exam.modules.exam.dto.ext.ExamRepoExtDTO;
import com.yf.exam.modules.paper.dto.PaperQuDTO;
import com.yf.exam.modules.paper.dto.PaperQuDTO2;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
* <p>
* 考试保存请求类
* </p>
*
* <AUTHOR>
* @since 2020-07-25 16:18
*/
@Data
@ApiModel(value="批卷保存请求类", description="批卷保存请求类")
public class PaperSaveReqDTO{

    private static final long serialVersionUID = 1L;


    private String paperId;
    private List<PaperQuDTO> paperList;

}
