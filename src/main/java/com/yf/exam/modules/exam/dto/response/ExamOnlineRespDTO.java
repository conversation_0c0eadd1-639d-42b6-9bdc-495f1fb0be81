package com.yf.exam.modules.exam.dto.response;

import com.yf.exam.modules.exam.dto.ExamDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* <p>
* 考试分页响应类
* </p>
*
* <AUTHOR>
* @since 2020-07-25 16:18
*/
@Data
@ApiModel(value="在线考试分页响应类", description="在线考试分页响应类")
public class ExamOnlineRespDTO extends ExamDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "计算后的考试状态（从数据库查询时计算）", required=false)
    private Integer calculatedState;

    @ApiModelProperty(value = "用户答题记录ID（如果用户已参加考试）", required=false)
    private String userAnswerId;

    @ApiModelProperty(value = "用户答题状态（0=答题中，1=已提交）", required=false)
    private Integer userAnswerState;

    @ApiModelProperty(value = "用户总得分", required=false)
    private java.math.BigDecimal userTotalScore;

    @ApiModelProperty(value = "用户答题进度百分比", required=false)
    private Integer userProgress;

    @ApiModelProperty(value = "已用时间（秒）", required=false)
    private Integer timeSpent;

    @ApiModelProperty(value = "答题时间（最后保存时间）", required=false)
    private java.util.Date answerTime;

    @ApiModelProperty(value = "提交时间", required=false)
    private java.util.Date submitTime;

    /**
     * 重写getState方法，优先使用数据库计算的状态
     * 如果calculatedState不为空，直接返回，避免重复计算
     */
    @Override
    public Integer getState() {
        if (calculatedState != null) {
            return calculatedState;
        }
        // 如果没有计算状态，回退到父类的动态计算逻辑
        return super.getState();
    }

    /**
     * 判断用户是否已经参加过这个考试
     */
    public boolean hasUserParticipated() {
        return userAnswerId != null;
    }

    /**
     * 判断用户是否已经提交了考试
     */
    public boolean hasUserSubmitted() {
        return userAnswerId != null && userAnswerState != null && userAnswerState == 1;
    }

    /**
     * 获取用户得分（如果已提交）
     */
    public java.math.BigDecimal getUserScore() {
        return hasUserSubmitted() ? userTotalScore : null;
    }

    /**
     * 获取完整的考试状态信息，替代check-status接口
     * 返回与WeatherHistoryExamAnswerDTO兼容的数据结构
     */
    public java.util.Map<String, Object> getExamStatusInfo() {
        java.util.Map<String, Object> statusInfo = new java.util.HashMap<>();

        // 基本信息
        statusInfo.put("id", userAnswerId);
        statusInfo.put("examId", getId());
        statusInfo.put("userId", null); // 当前用户ID，可以从UserUtils获取

        // 答题状态信息
        statusInfo.put("answerStatus", userAnswerState);
        statusInfo.put("overallProgress", userProgress);
        statusInfo.put("totalScore", userTotalScore);
        statusInfo.put("timeSpent", timeSpent);
        statusInfo.put("answerTime", answerTime);
        statusInfo.put("submitTime", submitTime);

        // 如果用户没有参加过考试，返回null（与check-status接口行为一致）
        if (userAnswerId == null) {
            return null;
        }

        return statusInfo;
    }

    /**
     * 判断用户是否正在答题中
     */
    public boolean isAnswering() {
        return userAnswerId != null && userAnswerState != null && userAnswerState == 0;
    }
}
