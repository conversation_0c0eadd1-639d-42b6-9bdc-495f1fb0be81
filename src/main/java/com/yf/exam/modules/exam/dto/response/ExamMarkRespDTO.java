package com.yf.exam.modules.exam.dto.response;

import com.yf.exam.modules.exam.dto.ExamDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value="阅卷分页响应类", description="阅卷分页响应类")
public class ExamMarkRespDTO extends ExamDTO {

    private static final long serialVersionUID = 1L;



    @ApiModelProperty(value = "考生姓名", required=true)
    private String userName;

    @ApiModelProperty(value = "主观题得分", required=true)
    private Integer objScore;

    @ApiModelProperty(value = "客观题得分", required=true)
    private Integer subjScore;
    private Integer paperState;
    private String paperId;
    private Date paperTime;





}
