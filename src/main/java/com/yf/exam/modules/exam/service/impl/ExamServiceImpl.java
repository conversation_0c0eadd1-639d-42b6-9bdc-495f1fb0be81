package com.yf.exam.modules.exam.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.core.enums.OpenType;
import com.yf.exam.core.exception.ServiceException;
import com.yf.exam.core.utils.BeanMapper;
import com.yf.exam.core.utils.NumberUtils;
import com.yf.exam.modules.exam.dto.ExamDTO;
import com.yf.exam.modules.exam.dto.ExamRepoDTO;
import com.yf.exam.modules.exam.dto.ext.ExamRepoExtDTO;
import com.yf.exam.modules.exam.dto.request.ExamSaveReqDTO;
import com.yf.exam.modules.exam.dto.response.ExamMarkRespDTO;
import com.yf.exam.modules.exam.dto.response.ExamOnlineRespDTO;
import com.yf.exam.modules.exam.dto.response.ExamReviewRespDTO;
import com.yf.exam.modules.exam.entity.Exam;
import com.yf.exam.modules.exam.entity.ExamRepo;
import com.yf.exam.modules.qu.service.QuService;

import com.yf.exam.modules.qu.dto.QuDTO;
import com.yf.exam.modules.qu.entity.Qu;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import java.util.Map;
import java.util.List;
import com.yf.exam.modules.exam.mapper.ExamMapper;
import com.yf.exam.modules.exam.service.ExamDepartService;
import com.yf.exam.modules.exam.service.ExamRepoService;
import com.yf.exam.modules.exam.service.ExamService;
import com.yf.exam.modules.user.UserUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <p>
* 考试业务实现类
* </p>
*
* <AUTHOR>
* @since 2020-07-25 16:18
*/
@Service
public class ExamServiceImpl extends ServiceImpl<ExamMapper, Exam> implements ExamService {


    @Autowired
    private ExamRepoService examRepoService;

    @Autowired
    private ExamDepartService examDepartService;

    @Autowired
    private QuService quService;



    @Override
    public void save(ExamSaveReqDTO reqDTO) {
        System.out.println("=== ExamService.save 开始 ===");
        System.out.println("前端传入数据 - totalTime: " + reqDTO.getTotalTime() + ", totalScore: " + reqDTO.getTotalScore());
        System.out.println("前端传入数据 - questionId: " + reqDTO.getQuestionId());

        // ID
        String id = reqDTO.getId();

        if(StringUtils.isBlank(id)){
            id = IdWorker.getIdStr();
        }

        //复制参数
        Exam entity = new Exam();

        // 计算分值
        this.calcScore(reqDTO);

        System.out.println("计算分值后 - totalTime: " + reqDTO.getTotalTime() + ", totalScore: " + reqDTO.getTotalScore());

        // 复制基本数据
        BeanMapper.copy(reqDTO, entity);
        entity.setId(id);

        System.out.println("复制到实体后 - totalTime: " + entity.getTotalTime() + ", totalScore: " + entity.getTotalScore());

        // 修复状态
        if (reqDTO.getTimeLimit()!=null
                && !reqDTO.getTimeLimit()
                && reqDTO.getState()!=null
                && reqDTO.getState() == 2) {
            entity.setState(0);
        } else {
            entity.setState(reqDTO.getState());
        }

        // 题库组卷（历史个例考试不需要题库，跳过）
        if (!isWeatherExam(reqDTO)) {
            try {
                examRepoService.saveAll(id, reqDTO.getRepoList());
            }catch (DuplicateKeyException e){
                throw new ServiceException(1, "不能选择重复的题库！");
            }
        }


        // 开放的部门
        if(OpenType.DEPT_OPEN.equals(reqDTO.getOpenType())){
            examDepartService.saveAll(id, reqDTO.getDepartIds());
        }

        this.saveOrUpdate(entity);

        System.out.println("=== ExamService.save 完成 ===");
        System.out.println("最终保存的数据 - ID: " + entity.getId() + ", totalTime: " + entity.getTotalTime() + ", totalScore: " + entity.getTotalScore());

    }

    @Override
    public ExamSaveReqDTO findDetail(String id) {
        ExamSaveReqDTO respDTO = new ExamSaveReqDTO();
        Exam exam = this.getById(id);
        BeanMapper.copy(exam, respDTO);

        // 考试部门
        List<String> departIds = examDepartService.listByExam(id);
        respDTO.setDepartIds(departIds);

        // 题库
        List<ExamRepoExtDTO> repos = examRepoService.listByExam(id);
        respDTO.setRepoList(repos);

        return respDTO;
    }

    @Override
    public ExamDTO findById(String id) {
        ExamDTO respDTO = new ExamDTO();
        Exam exam = this.getById(id);
        BeanMapper.copy(exam, respDTO);
        return respDTO;
    }

    @Override
    public IPage<ExamDTO> paging(PagingReqDTO<ExamDTO> reqDTO) {

        //创建分页对象
        Page page = new Page(reqDTO.getCurrent(), reqDTO.getSize());
        String userId = UserUtils.getUserId();

        //转换结果
        IPage<ExamDTO> pageData = baseMapper.paging(page, reqDTO.getParams(),userId);
        return pageData;
     }

    @Override
    public IPage<ExamOnlineRespDTO> onlinePaging(PagingReqDTO<ExamDTO> reqDTO) {

        // 获取当前用户ID
        String userId = UserUtils.getUserId();

        // 先查询总数
        Long total = baseMapper.onlineCount(reqDTO.getParams(), userId);

        // 创建分页对象，禁用自动COUNT查询
        Page page = new Page(reqDTO.getCurrent(), reqDTO.getSize(), false);
        page.setTotal(total);

        // 查找分页数据
        IPage<ExamOnlineRespDTO> pageData = baseMapper.online(page, reqDTO.getParams(), userId);

        return pageData;
    }

    @Override
    public IPage<ExamReviewRespDTO> reviewPaging(PagingReqDTO<ExamDTO> reqDTO) {
        // 创建分页对象
        Page page = new Page(reqDTO.getCurrent(), reqDTO.getSize());

        // 查找分页
        IPage<ExamReviewRespDTO> pageData = baseMapper.reviewPaging(page, reqDTO.getParams());

        return pageData;
    }

    @Override
    public IPage<ExamMarkRespDTO> markPaging(PagingReqDTO<ExamDTO> reqDTO) {
        // 创建分页对象
        Page page = new Page(reqDTO.getCurrent(), reqDTO.getSize());
        String userId = UserUtils.getUserId();
        // 查找分页
        IPage<ExamMarkRespDTO> pageData = baseMapper.markPaging(page, reqDTO.getParams(),userId);

        return pageData;
    }


    /**
     * 计算分值
     * @param reqDTO
     */
    private void calcScore(ExamSaveReqDTO reqDTO){

        // 检查是否为历史个例考试（天气预报类型）
        if (isWeatherExam(reqDTO)) {
            // 历史个例考试：如果前端没有传入totalScore，则自动计算
            if (reqDTO.getTotalScore() == null || reqDTO.getTotalScore() <= 0) {
                // 每个站点10分（风力1分+风向1分+最低温2分+最高温2分+降水2分+灾害天气2分）
                int stationCount = getWeatherExamStationCount(reqDTO);
                int weatherScore = stationCount * 10;
                reqDTO.setTotalScore(weatherScore);
                System.out.println("历史个例考试自动计算总分: " + weatherScore + " (站点数: " + stationCount + ")");
            } else {
                System.out.println("历史个例考试使用前端传入的总分: " + reqDTO.getTotalScore());
            }
            return;
        }

        // 传统题型分数计算
        int objScore = 0;

        // 题库组卷
        List<ExamRepoExtDTO> repoList = reqDTO.getRepoList();

        if (repoList != null) {
            for(ExamRepoDTO item: repoList){
                if(item.getRadioCount()!=null
                        && item.getRadioCount()>0
                        && item.getRadioScore()!=null
                        && item.getRadioScore()>0){
                    objScore+=item.getRadioCount()*item.getRadioScore();
                }
                if(item.getMultiCount()!=null
                        && item.getMultiCount()>0
                        && item.getMultiScore()!=null
                        && item.getMultiScore()>0){
                    objScore+=item.getMultiCount()*item.getMultiScore();
                }
                if(item.getJudgeCount()!=null
                        && item.getJudgeCount()>0
                        && item.getJudgeScore()!=null
                        && item.getJudgeScore()>0){
                    objScore+=item.getJudgeCount()*item.getJudgeScore();
                }
                if(item.getBlankCount()!=null
                        && item.getBlankCount()>0
                        && item.getBlankScore()!=null
                        && item.getBlankScore()>0){
                    objScore+=item.getBlankCount()*item.getBlankScore();
                }
                if(item.getShortCount()!=null
                        && item.getShortCount()>0
                        && item.getShortScore()!=null
                        && item.getShortScore()>0){
                    objScore+=item.getShortCount()*item.getShortScore();
                }
            }
        }

        reqDTO.setTotalScore(objScore);
    }

    /**
     * 判断是否为天气预报历史个例考试
     * @param reqDTO
     * @return
     */
    private boolean isWeatherExam(ExamSaveReqDTO reqDTO) {
        // 历史个例考试通过questionId字段来判断，而不是通过题库
        if (reqDTO.getQuestionId() != null && !reqDTO.getQuestionId().trim().isEmpty()) {
            try {
                // 查询题目详情，检查是否为天气预报表格题（quType = 6）
                Qu question = quService.getById(reqDTO.getQuestionId());
                return question != null && question.getQuType() != null && question.getQuType().equals(6);
            } catch (Exception e) {
                System.err.println("查询题目类型失败: " + e.getMessage());
                return false;
            }
        }
        return false;
    }

    /**
     * 获取天气考试的站点数量
     * @param reqDTO
     * @return
     */
    private int getWeatherExamStationCount(ExamSaveReqDTO reqDTO) {
        // 历史个例考试直接从选择的题目中获取站点数量
        if (reqDTO.getQuestionId() != null && !reqDTO.getQuestionId().trim().isEmpty()) {
            try {
                Qu question = quService.getById(reqDTO.getQuestionId());
                if (question != null && question.getScenarioData() != null) {
                    String scenarioData = question.getScenarioData();
                    if (!scenarioData.trim().isEmpty()) {
                        ObjectMapper mapper = new ObjectMapper();
                        Map<String, Object> data = mapper.readValue(scenarioData, new TypeReference<Map<String, Object>>() {});

                        @SuppressWarnings("unchecked")
                        List<String> stations = (List<String>) data.get("stations");

                        if (stations != null) {
                            return stations.size();
                        }
                    }
                }
            } catch (Exception e) {
                System.err.println("解析天气题目站点数量失败: " + e.getMessage());
            }
        }

        // 默认返回6个站点
        return 6;
    }



    private void processRepoData(ExamDTO reqDTO, ExamRepo examRepo) {
        // ... 现有代码 ...


        // ... 现有代码 ...
    }

}
