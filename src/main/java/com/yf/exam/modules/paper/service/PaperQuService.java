package com.yf.exam.modules.paper.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.modules.paper.dto.PaperQuDTO;
import com.yf.exam.modules.paper.dto.PaperQuDTO2;
import com.yf.exam.modules.paper.dto.ext.PaperQuDetailDTO;
import com.yf.exam.modules.paper.entity.PaperQu;

import java.util.List;

/**
* <p>
* 试卷考题业务类
* </p>
*
* <AUTHOR>
* @since 2020-05-25 16:33
*/
public interface PaperQuService extends IService<PaperQu> {

    /**
    * 分页查询数据
    * @param reqDTO
    * @return
    */
    IPage<PaperQuDTO> paging(PagingReqDTO<PaperQuDTO> reqDTO);

    /**
     * 查找试卷的题目列表
     * @param paperId 试卷ID
     * @return
     */
    List<PaperQuDTO> listByPaper(String paperId);

    /**
     * 查找详情
     * @param paperId
     * @param quId
     * @return
     */
    PaperQu findByKey(String paperId, String quId);

    /**
     * 根据组合索引更新
     * @param qu
     */
    void updateByKey(PaperQu qu);

    /**
     * 统计客观分
     * @param paperId
     * @return
     */
    int sumObjective(String paperId);

    /**
     * 统计主观分
     * @param paperId
     * @return
     */
    int sumSubjective(String paperId);

    /**
     * 找出全部试题列表
     * @param paperId
     * @return
     */
    List<PaperQuDetailDTO> listForPaperResult(String paperId);

    /**
     * 获取试卷的主观题列表
     * @param paperId 试卷ID
     * @return 主观题列表
     */
    List<PaperQuDTO2> listSubjectiveQuestions(String paperId, Integer quType);

    /**
     * 保存试题
     * @param qu
     * @param paperId
     */
    void save(PaperQuDTO qu, String paperId);
}
