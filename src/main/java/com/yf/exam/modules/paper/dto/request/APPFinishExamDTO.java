package com.yf.exam.modules.paper.dto.request;

import com.yf.exam.core.api.dto.BaseDTO;
import lombok.Data;

import java.util.List;

@Data
public class APPFinishExamDTO extends BaseDTO {
        private String id;
        private String image;
        private Integer level;
        private Integer quType;
        private String remark;
        private String updateTime;
        private Boolean result;
        private String userAnswer;  // 用户选择的答案ID，多个用逗号分隔
        private List<APPFinishExamAnswerDTO> answers;  // 答案选项列表
}
