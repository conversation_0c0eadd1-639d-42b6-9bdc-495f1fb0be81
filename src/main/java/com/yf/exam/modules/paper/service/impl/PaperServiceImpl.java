package com.yf.exam.modules.paper.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.exam.ability.job.enums.JobGroup;
import com.yf.exam.ability.job.enums.JobPrefix;
import com.yf.exam.ability.job.service.JobService;
import com.yf.exam.core.api.ApiError;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.core.exception.ServiceException;
import com.yf.exam.core.utils.BeanMapper;
import com.yf.exam.core.utils.CronUtils;
import com.yf.exam.modules.exam.dto.ExamDTO;
import com.yf.exam.modules.exam.dto.ExamRepoDTO;
import com.yf.exam.modules.exam.dto.ext.ExamRepoExtDTO;
import com.yf.exam.modules.exam.service.ExamRepoService;
import com.yf.exam.modules.exam.service.ExamService;
import com.yf.exam.modules.paper.dto.PaperDTO;
import com.yf.exam.modules.paper.dto.PaperQuDTO;
import com.yf.exam.modules.paper.dto.PaperQuDTO2;
import com.yf.exam.modules.paper.dto.ext.PaperQuAnswerExtDTO;
import com.yf.exam.modules.paper.dto.ext.PaperQuDetailDTO;
import com.yf.exam.modules.paper.dto.request.APPFinishExamDTO;
import com.yf.exam.modules.paper.dto.request.PaperAnswerDTO;
import com.yf.exam.modules.paper.dto.request.PaperListReqDTO;
import com.yf.exam.modules.paper.dto.response.ExamDetailRespDTO;
import com.yf.exam.modules.paper.dto.response.ExamResultRespDTO;
import com.yf.exam.modules.paper.dto.response.PaperDetailRespDTO;
import com.yf.exam.modules.paper.dto.response.PaperListRespDTO;
import com.yf.exam.modules.paper.entity.Paper;
import com.yf.exam.modules.paper.entity.PaperQu;
import com.yf.exam.modules.paper.entity.PaperQuAnswer;
import com.yf.exam.modules.paper.enums.ExamState;
import com.yf.exam.modules.paper.enums.PaperState;
import com.yf.exam.modules.paper.job.BreakExamJob;
import com.yf.exam.modules.paper.mapper.PaperMapper;
import com.yf.exam.modules.paper.mapper.PaperQuMapper;
import com.yf.exam.modules.paper.service.PaperQuAnswerService;
import com.yf.exam.modules.paper.service.PaperQuService;
import com.yf.exam.modules.paper.service.PaperService;
import com.yf.exam.modules.qu.dto.QuDTO;
import com.yf.exam.modules.qu.dto.QuDTO2;
import com.yf.exam.modules.qu.entity.Qu;
import com.yf.exam.modules.qu.entity.Qu2;
import com.yf.exam.modules.qu.entity.QuAnswer;
import com.yf.exam.modules.qu.enums.QuType;
import com.yf.exam.modules.qu.mapper.QuMapper;
import com.yf.exam.modules.qu.service.QuAnswerService;
import com.yf.exam.modules.qu.service.QuService;
import com.yf.exam.modules.sys.user.entity.SysUser;
import com.yf.exam.modules.sys.user.service.SysUserService;
import com.yf.exam.modules.user.UserUtils;
import com.yf.exam.modules.user.book.service.UserBookService;
import com.yf.exam.modules.user.exam.service.UserExamService;
import com.yf.exam.modules.user.service.UserWrongBookService;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.weaver.patterns.TypePatternQuestions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;

/**
* <p>
* 语言设置 服务实现类
* </p>
*
* <AUTHOR>
* @since 2020-05-25 16:33
*/
@Service
public class PaperServiceImpl extends ServiceImpl<PaperMapper, Paper> implements PaperService {


    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private ExamService examService;

    @Autowired
    private QuService quService;

    @Autowired
    private QuAnswerService quAnswerService;

    @Autowired
    private PaperService paperService;

    @Autowired
    private PaperQuService paperQuService;

    @Autowired
    private PaperQuAnswerService paperQuAnswerService;

    @Autowired
    private UserBookService userBookService;

    @Autowired
    private ExamRepoService examRepoService;

    @Autowired
    private UserExamService userExamService;

    @Autowired
    private JobService jobService;


    @Autowired
    private QuMapper quMapper;

    @Autowired
    private UserWrongBookService userWrongBookService;

    /**
     * 展示的选项，ABC这样
     */
    private static List<String> ABC = Arrays.asList(new String[]{
            "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K","L","M","N","O","P","Q","R","S","T","U","V","W","X"
            ,"Y","Z"
    });





    @Transactional(rollbackFor = Exception.class)
    @Override
    public String createPaper(String userId, String examId) {

        // 先检查用户是否参加过该考试
        QueryWrapper<Paper> examWrapper = new QueryWrapper<>();
        examWrapper.lambda()
                .eq(Paper::getUserId, userId)
                .eq(Paper::getExamId, examId)
                .ne(Paper::getState, PaperState.ING);  // 排除正在考试的记录
        
        int count = this.count(examWrapper);
       if (count > 0) {
           throw new ServiceException(1, "已参加过该考试！");
       }

        // 校验是否有正在考试的试卷
        QueryWrapper<Paper> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(Paper::getUserId, userId)
                .eq(Paper::getState, PaperState.ING);

        int exists = this.count(wrapper);


        if (exists > 0) {
            throw new ServiceException(ApiError.ERROR_20010002);
        }

        // 查找考试
        ExamDTO exam = examService.findById(examId);

        if(exam == null){
            throw new ServiceException(1, "考试不存在！");
        }

        // 检查考试状态
        if (isWeatherExam(exam)) {
            // 历史个例考试：状态1表示已发布，可以参加考试
            if (!Integer.valueOf(1).equals(exam.getState())) {
                throw new ServiceException(1, "考试状态不正确！历史个例考试需要先发布才能参加。");
            }
        } else {
            // 传统考试：状态0表示启用，可以参加考试
            if (!ExamState.ENABLE.equals(exam.getState())) {
                throw new ServiceException(1, "考试状态不正确！");
            }
        }

        // 考试题目列表
        List<PaperQu> quList;
        if (isWeatherExam(exam)) {
            // 历史个例考试：直接使用关联的题目
            quList = this.generateWeatherExamQuestions(exam);
        } else {
            // 传统考试：从题库中随机抽取题目
            quList = this.generateByRepo(examId);
        }

        if(CollectionUtils.isEmpty(quList)){
            throw new ServiceException(1, "规则不正确，无对应的考题！");
        }

        //保存试卷内容
        Paper paper = this.savePaper(userId, exam, quList);

        // 强制交卷任务
        String jobName = JobPrefix.BREAK_EXAM + paper.getId();
        jobService.addCronJob(BreakExamJob.class, jobName, CronUtils.dateToCron(paper.getLimitTime()), paper.getId());

        return paper.getId();
    }

    /**
     * 判断是否为历史个例考试
     * @param exam
     * @return
     */
    private boolean isWeatherExam(ExamDTO exam) {
        // 历史个例考试通过questionId字段来判断
        return exam.getQuestionId() != null && !exam.getQuestionId().trim().isEmpty();
    }

    /**
     * 为历史个例考试生成题目列表
     * @param exam
     * @return
     */
    private List<PaperQu> generateWeatherExamQuestions(ExamDTO exam) {
        List<PaperQu> quList = new ArrayList<>();

        // 获取关联的题目
        Qu question = quService.getById(exam.getQuestionId());
        if (question == null) {
            throw new ServiceException(1, "关联的历史个例题目不存在！");
        }

        // 创建试卷题目
        PaperQu paperQu = new PaperQu();
        paperQu.setId(IdWorker.getIdStr());
        paperQu.setQuId(question.getId());
        paperQu.setQuType(question.getQuType());
        paperQu.setSort(1); // 历史个例考试通常只有一个题目
        paperQu.setScore(exam.getTotalScore()); // 使用考试的总分作为题目分数

        quList.add(paperQu);
        return quList;
    }

    @Override
    public ExamDetailRespDTO paperDetail(String paperId) {


        ExamDetailRespDTO respDTO = new ExamDetailRespDTO();

        // 试题基本信息
        Paper paper = paperService.getById(paperId);
        BeanMapper.copy(paper, respDTO);

        // 查找题目列表
        List<PaperQuDTO> list = paperQuService.listByPaper(paperId);

        List<PaperQuDTO> radioList = new ArrayList<>();
        List<PaperQuDTO> multiList = new ArrayList<>();
        List<PaperQuDTO> judgeList = new ArrayList<>();
        List<PaperQuDTO> blankList = new ArrayList<>();
        List<PaperQuDTO> shortList = new ArrayList<>();
        for(PaperQuDTO item: list){
            if(QuType.RADIO.equals(item.getQuType())){
                radioList.add(item);
            }
            if(QuType.MULTI.equals(item.getQuType())){
                multiList.add(item);
            }
            if(QuType.JUDGE.equals(item.getQuType())){
                judgeList.add(item);
            }
            if(QuType.BLANK.equals(item.getQuType())){
                blankList.add(item);
            }
            if(QuType.SHORT.equals(item.getQuType())){
                shortList.add(item);
            }
        }

        respDTO.setRadioList(radioList);
        respDTO.setMultiList(multiList);
        respDTO.setJudgeList(judgeList);
        respDTO.setBlankList(blankList);
        respDTO.setShortList(shortList);
        return respDTO;
    }

    @Override
    public ExamResultRespDTO paperResult(String paperId) {

        ExamResultRespDTO respDTO = new ExamResultRespDTO();

        // 试题基本信息
        Paper paper = paperService.getById(paperId);
        BeanMapper.copy(paper, respDTO);

        List<PaperQuDetailDTO> quList = paperQuService.listForPaperResult(paperId);
        respDTO.setQuList(quList);

        return respDTO;
    }

    @Override
    public PaperQuDetailDTO findQuDetail(String paperId, String quId) {

        PaperQuDetailDTO respDTO = new PaperQuDetailDTO();
        // 问题
        Qu qu = quService.getById(quId);

        // 基本信息
        PaperQu paperQu = paperQuService.findByKey(paperId, quId);
        BeanMapper.copy(paperQu, respDTO);
        respDTO.setContent(qu.getContent());
        respDTO.setImage(qu.getImage());

        // 答案列表 - 只有选择题和判断题需要答案选项
        if (respDTO.getQuType() != 4 && respDTO.getQuType() != 5 && respDTO.getQuType() != 6) {
            List<PaperQuAnswerExtDTO> list = paperQuAnswerService.listForExam(paperId, quId);
            respDTO.setAnswerList(list);
        }

        // 天气预报表格题需要返回表格配置信息
        if (respDTO.getQuType() == 6) {
            // 暂时设置一个标识，前端可以根据这个标识加载配置
            respDTO.setWeatherConfigId("1000000000000000001");
        }

        return respDTO;
    }


    /**
     * 题库组题方式产生题目列表
     * @param examId
     * @return
     */
    private List<PaperQu> generateByRepo(String examId) {
        // 先查找该考试是否已经生成过试题
        QueryWrapper<Paper> paperWrapper = new QueryWrapper<>();
        paperWrapper.lambda().eq(Paper::getExamId, examId)
                .orderByDesc(Paper::getCreateTime)
                .last("LIMIT 1");
        Paper existPaper = this.getOne(paperWrapper);
        
        // 如果存在已生成的试卷，直接返回其试题列表
        if (existPaper != null) {
            List<PaperQu> existQuList = paperQuService.list(new QueryWrapper<PaperQu>()
                    .lambda()
                    .eq(PaperQu::getPaperId, existPaper.getId())
                    .orderByAsc(PaperQu::getSort));
            
            // 深拷贝试题列表，移除原试卷ID
            List<PaperQu> newQuList = new ArrayList<>();
            for (PaperQu qu : existQuList) {
                PaperQu newQu = new PaperQu();
                BeanMapper.copy(qu, newQu);
                newQu.setId(null);  // 清空ID，后续会重新生成
                newQu.setPaperId(null);  // 清空试卷ID，后续会重新设置
                newQu.setAnswered(false);  // 重置答题状态
                newQu.setIsRight(false);   // 重置正确状态
                newQu.setActualScore(0);
                newQu.setAnswer("");
                newQuList.add(newQu);
            }
            return newQuList;
        }

        // 如果不存在，则按原逻辑生成新的试题列表
        // 查找规则指定的题库
        List<ExamRepoExtDTO> list = examRepoService.listByExam(examId);

        //最终的题目列表
        List<PaperQu> quList = new ArrayList<>();

        //排除ID，避免题目重复
        List<String> excludes = new ArrayList<>();
        excludes.add("none");

        if (!CollectionUtils.isEmpty(list)) {
            for (ExamRepoExtDTO item : list) {

                // 单选题
                if(item.getRadioCount() > 0){
                    List<Qu> radioList = quService.listByRandom(item.getRepoId(), QuType.RADIO, excludes, item.getRadioCount());
                    for (Qu qu : radioList) {
                        PaperQu paperQu = this.processPaperQu(item, qu);
                        quList.add(paperQu);
                        excludes.add(qu.getId());
                    }
                }

                //多选题
                if(item.getMultiCount() > 0) {
                    List<Qu> multiList = quService.listByRandom(item.getRepoId(), QuType.MULTI, excludes,
                            item.getMultiCount());
                    for (Qu qu : multiList) {
                        PaperQu paperQu = this.processPaperQu(item, qu);
                        quList.add(paperQu);
                        excludes.add(qu.getId());
                    }
                }

                // 判断题
                if(item.getJudgeCount() > 0) {
                    List<Qu> judgeList = quService.listByRandom(item.getRepoId(), QuType.JUDGE, excludes,
                            item.getJudgeCount());
                    for (Qu qu : judgeList) {
                        PaperQu paperQu = this.processPaperQu(item, qu);
                        quList.add(paperQu);
                        excludes.add(qu.getId());
                    }
                }

                // 填空题
                if(item.getBlankCount() > 0) {
                    List<Qu> blankList = quService.listByRandom(item.getRepoId(), QuType.BLANK, excludes, item.getBlankCount());
                    for (Qu qu : blankList) {
                        PaperQu paperQu = this.processPaperQu(item, qu);
                        quList.add(paperQu);
                        excludes.add(qu.getId());
                    }
                }

                // 简答题
                if(item.getShortCount() > 0) {
                    List<Qu> shortList = quService.listByRandom(item.getRepoId(), QuType.SHORT, excludes, item.getShortCount());
                    for (Qu qu : shortList) {
                        PaperQu paperQu = this.processPaperQu(item, qu);
                        quList.add(paperQu);
                        excludes.add(qu.getId());
                    }
                }
            }
        }
        return quList;
    }



    /**
     * 填充试题题目信息
     * @param repo
     * @param qu
     * @return
     */
    private PaperQu processPaperQu(ExamRepoDTO repo, Qu qu) {

        //保存试题信息
        PaperQu paperQu = new PaperQu();
        paperQu.setQuId(qu.getId());
        paperQu.setAnswered(false);
        paperQu.setIsRight(false);
        paperQu.setQuType(qu.getQuType());

        if (QuType.RADIO.equals(qu.getQuType())) {
            paperQu.setScore(repo.getRadioScore());
            paperQu.setActualScore(repo.getRadioScore());
        }

        if (QuType.MULTI.equals(qu.getQuType())) {
            paperQu.setScore(repo.getMultiScore());
            paperQu.setActualScore(repo.getMultiScore());
        }

        if (QuType.JUDGE.equals(qu.getQuType())) {
            paperQu.setScore(repo.getJudgeScore());
            paperQu.setActualScore(repo.getJudgeScore());
        }

        // 填空题和简答题
        if(qu.getQuType() == 5){
            paperQu.setScore(repo.getShortScore());
            paperQu.setActualScore(0);
        }

        if(qu.getQuType() == 4){
            paperQu.setScore(repo.getBlankScore());
            paperQu.setActualScore(0);
        }

        // 天气预报表格题
        if(qu.getQuType() == 6){
            paperQu.setScore(repo.getShortScore()); // 暂时使用简答题分数，后续可以单独配置
            paperQu.setActualScore(0);
        }

        return paperQu;
    }


    /**
     * 保存试卷
     * @param userId
     * @param exam
     * @param quList
     * @return
     */
    private Paper savePaper(String userId, ExamDTO exam, List<PaperQu> quList) {

        // 获取用户信息
        SysUser user = sysUserService.getById(userId);

        // 计算考试时长
        Calendar cl = Calendar.getInstance();
        
        // 创建试卷
        Paper paper = new Paper();
        boolean hasShortAnswer = quList.stream().anyMatch(qu -> qu.getQuType() == 5);
        boolean hasBlank = quList.stream().anyMatch(qu -> qu.getQuType() == 4);
        paper.setHasSaq(hasShortAnswer || hasBlank);
        paper.setDepartId(user.getDepartId());
        paper.setExamId(exam.getId());
        paper.setTitle(exam.getTitle());
        paper.setTotalScore(exam.getTotalScore());
        paper.setTotalTime(exam.getTotalTime());
        paper.setQualifyScore(exam.getQualifyScore());
        paper.setMonitorMouse(exam.getMonitorMouse());
        paper.setHideStudentName(exam.getHideStudentName());
        paper.setShowResult(exam.getShowResult());
        paper.setUserId(userId);
        paper.setState(PaperState.ING);
        paper.setCreateTime(new Date());
        paper.setUpdateTime(new Date());
        paper.setHasMark(false);

        // 截止时间
        cl.add(Calendar.MINUTE, exam.getTotalTime());
        paper.setLimitTime(cl.getTime());

        this.save(paper);

        // 保存试题
        this.savePaperQu(paper.getId(), quList);

        return paper;
    }



    /**
     * 保存试卷试题列表
     * @param paperId
     * @param quList
     */
    private void savePaperQu(String paperId, List<PaperQu> quList){

        List<PaperQu> batchQuList = new ArrayList<>();
        List<PaperQuAnswer> batchAnswerList = new ArrayList<>();

        int sort = 0;
        quList.sort(Comparator.comparingInt(PaperQu::getQuType));
        for (PaperQu item : quList) {

            item.setPaperId(paperId);
            item.setSort(sort);
            item.setId(IdWorker.getIdStr());

            //回答列表
            List<QuAnswer> answerList = quAnswerService.listAnswerByRandom(item.getQuId());

            if (!CollectionUtils.isEmpty(answerList)) {

                int ii = 0;
                for (QuAnswer answer : answerList) {
                    PaperQuAnswer paperQuAnswer = new PaperQuAnswer();
                    paperQuAnswer.setId(UUID.randomUUID().toString());
                    paperQuAnswer.setPaperId(paperId);
                    paperQuAnswer.setQuId(answer.getQuId());
                    paperQuAnswer.setAnswerId(answer.getId());
                    paperQuAnswer.setChecked(false);
                    paperQuAnswer.setSort(ii);
                    paperQuAnswer.setAbc(ABC.get(ii));
                    paperQuAnswer.setIsRight(answer.getIsRight());
                    ii++;
                    batchAnswerList.add(paperQuAnswer);
                }
            }

            batchQuList.add(item);
            sort++;
        }

        //添加问题
        paperQuService.saveBatch(batchQuList);

        //批量添加问题答案
        paperQuAnswerService.saveBatch(batchAnswerList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void fillAnswer(PaperAnswerDTO reqDTO) {

        // 查找试题信息
        PaperQu paperQu = paperQuService.findByKey(reqDTO.getPaperId(), reqDTO.getQuId());
        if(paperQu == null){
            throw new ServiceException(1, "试题不存在！");
        }

        // 如果不是单选、多选、判断题，直接按原有逻辑处理
        if(paperQu.getQuType() > 3){
            // 未作答
            if(StringUtils.isBlank(reqDTO.getAnswer())){
                return;
            }

            //修改为已回答
            PaperQu qu = new PaperQu();
            qu.setQuId(reqDTO.getQuId());
            qu.setPaperId(reqDTO.getPaperId());
            qu.setAnswer(reqDTO.getAnswer());
            qu.setAnswered(true);

            // 对于天气预报表格题，需要特殊处理评分和答案保存
            if(paperQu.getQuType() == 6) {
                // 天气预报表格题暂时给满分，后续可以实现专门的评分算法
                qu.setActualScore(paperQu.getScore());
                qu.setIsRight(true);

                // TODO: 这里可以调用WeatherAnswerService保存详细的天气预报答案
                // 目前先保存在PaperQu的answer字段中
            }

            paperQuService.updateByKey(qu);
            return;
        }

        // 未作答
        if(CollectionUtils.isEmpty(reqDTO.getAnswers())){
            return;
        }

        //查找答案列表
        List<PaperQuAnswer> list = paperQuAnswerService.listForFill(reqDTO.getPaperId(), reqDTO.getQuId());

        //是否正确
        boolean right = true;

        //更新正确答案
        for (PaperQuAnswer item : list) {

            if (reqDTO.getAnswers().contains(item.getId())) {
                item.setChecked(true);
            } else {
                item.setChecked(false);
            }

            //有一个对不上就是错的
            if (item.getIsRight()!=null && !item.getIsRight().equals(item.getChecked())) {
                right = false;
            }
            paperQuAnswerService.updateById(item);
        }

        //修改为已回答
        PaperQu qu = new PaperQu();
        qu.setQuId(reqDTO.getQuId());
        qu.setPaperId(reqDTO.getPaperId());
        qu.setIsRight(right);
        qu.setActualScore(right?paperQu.getScore():0);
        qu.setAnswer(reqDTO.getAnswer());
        qu.setAnswered(true);

        paperQuService.updateByKey(qu);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void handExam(String paperId) {

        //获取试卷信息
        Paper paper = paperService.getById(paperId);

        //如果不是正常的，抛出异常
        if(!PaperState.ING.equals(paper.getState())){
            throw new ServiceException(1, "试卷状态不正确！");
        }

        // 客观分
        int objScore = paperQuService.sumObjective(paperId);
        paper.setObjScore(objScore);
        paper.setUserScore(objScore);

        // 主观分，因为要阅卷，所以给0
        paper.setSubjScore(0);

        // 待阅卷
        if(paper.getHasSaq()) {
            paper.setState(PaperState.WAIT_OPT);
        }else {

            // 同步保存考试成绩
            userExamService.joinResult(paper.getUserId(), paper.getExamId(), objScore, objScore>=paper.getQualifyScore());

            paper.setState(PaperState.FINISHED);
        }
        paper.setUpdateTime(new Date());

        //计算考试时长
        Calendar cl = Calendar.getInstance();
        cl.setTimeInMillis(System.currentTimeMillis());
        int userTime = (int)((System.currentTimeMillis() - paper.getCreateTime().getTime()) / 1000 / 60);
        if(userTime == 0){
            userTime = 1;
        }
        paper.setUserTime(userTime);

        //更新试卷
        paperService.updateById(paper);


        // 终止定时任务
        String name = JobPrefix.BREAK_EXAM + paperId;
        jobService.deleteJob(name, JobGroup.SYSTEM);

        //把打错的问题加入错题本
        List<PaperQuDTO> list = paperQuService.listByPaper(paperId);
        for(PaperQuDTO qu: list){
            // 主观题和对的都不加入错题库
            if(qu.getIsRight()){
                continue;
            }
            //加入错题本
            new Thread(() -> userBookService.addBook(paper.getExamId(), qu.getQuId())).run();
        }
    }

    @Override
    public IPage<PaperListRespDTO> paging(PagingReqDTO<PaperListReqDTO> reqDTO) {
        return baseMapper.paging(reqDTO.toPage(), reqDTO.getParams());
    }


    @Override
    public PaperDTO checkProcess(String userId) {

        QueryWrapper<Paper> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(Paper::getUserId, userId)
                .eq(Paper::getState, PaperState.ING);

        Paper paper = this.getOne(wrapper, false);

        if (paper != null) {
            return BeanMapper.map(paper, PaperDTO.class);
        }

        return null;
    }

    @Override
    public PaperDetailRespDTO paperDetail2(String paperId) {
        
        // 1. 试卷基础信息
        Paper paper = this.getById(paperId);
        if(paper == null){
            throw new ServiceException(1, "试卷不存在！");
        }

        // 2. 查找试卷的主观题列表（填空题和简答题）
        List<PaperQuDTO2> blankList = paperQuService.listSubjectiveQuestions(paperId,4);
        List<PaperQuDTO2> shortList = paperQuService.listSubjectiveQuestions(paperId,5);

        // 3. 构造返回对象
        PaperDetailRespDTO respDTO = new PaperDetailRespDTO();
        BeanMapper.copy(paper, respDTO);
        respDTO.setBlankList(blankList);
        respDTO.setShortList(shortList);
        
        return respDTO;
    }

    @Override
    public void savePaperQuList(List<PaperQuDTO> quList) {
        if(CollectionUtils.isEmpty(quList)){
            return;
        }
        List<PaperQu> list = new ArrayList<>();
        // 批量更新试题得分
        quList.forEach(qu -> {
            PaperQu paperQu = new PaperQu();
            BeanUtils.copyProperties(qu, paperQu);
            list.add(paperQu);
        });
        paperQuService.saveOrUpdateBatch(list);
    }

    @Override
    public List<QuDTO2> randomQuestions(String userId, Integer count, List<Integer> types, List<String> repoIds) {

        List<Qu2> questions = quMapper.listByRandom2(userId, types, count, repoIds);
        List<QuDTO2> quList = new ArrayList<>();
        for (Qu2 question : questions) {
            QuDTO2 quDTO2 = new QuDTO2();
            BeanMapper.copy(question,quDTO2);
            quList.add(quDTO2);
        }
        return quList;
    }

    @Override
    public SysUser AppFinishExam(List<APPFinishExamDTO> questions) {
        Integer totalScore = 0;
        int success = 0;
        for (APPFinishExamDTO question : questions) {
            if (question.getResult()) {
                success++;
                switch(question.getQuType()){
                    case 1:
                        totalScore += 1;
                        break;
                    case 2:
                        totalScore += 2;
                        break;
                    case 3:
                        totalScore += 1;
                        break;
                }
            }
        }
        String userId = UserUtils.getUserId();
        SysUser user = sysUserService.getById(userId);
        user.setScore(user.getScore() + totalScore);
        user.setSuccess(user.getSuccess() + success);
        user.setCount(user.getCount() + questions.size());
        sysUserService.updateById(user);
        
        // 保存错题到错题集
        userWrongBookService.saveUserWrongFromApp(questions);
        
        return user;
    }
}

