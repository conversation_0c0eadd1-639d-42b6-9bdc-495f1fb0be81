package com.yf.exam.modules.paper.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.modules.paper.dto.PaperDTO;
import com.yf.exam.modules.paper.dto.PaperQuDTO;
import com.yf.exam.modules.paper.dto.ext.PaperQuDetailDTO;
import com.yf.exam.modules.paper.dto.request.APPFinishExamDTO;
import com.yf.exam.modules.paper.dto.request.PaperAnswerDTO;
import com.yf.exam.modules.paper.dto.request.PaperListReqDTO;
import com.yf.exam.modules.paper.dto.response.ExamDetailRespDTO;
import com.yf.exam.modules.paper.dto.response.ExamResultRespDTO;
import com.yf.exam.modules.paper.dto.response.PaperDetailRespDTO;
import com.yf.exam.modules.paper.dto.response.PaperListRespDTO;
import com.yf.exam.modules.paper.entity.Paper;
import com.yf.exam.modules.paper.entity.PaperQu;
import com.yf.exam.modules.qu.dto.QuDTO2;
import com.yf.exam.modules.qu.entity.Qu2;
import com.yf.exam.modules.sys.user.entity.SysUser;
import io.swagger.models.auth.In;

import java.util.List;

/**
* <p>
* 试卷业务类
* </p>
*
* <AUTHOR>
* @since 2020-05-25 16:33
*/
public interface PaperService extends IService<Paper> {

    /**
     * 创建试卷
     * @param userId
     * @param examId
     * @return
     */
    String createPaper(String userId, String examId);


    /**
     * 查找详情
     * @param paperId
     * @return
     */
    ExamDetailRespDTO paperDetail(String paperId);

    /**
     * 考试结果
     * @param paperId
     * @return
     */
    ExamResultRespDTO paperResult(String paperId);

    /**
     * 查找题目详情
     * @param paperId
     * @param quId
     * @return
     */
    PaperQuDetailDTO findQuDetail(String paperId, String quId);

    /**
     * 填充答案
     * @param reqDTO
     */
    void fillAnswer(PaperAnswerDTO reqDTO);

    /**
     * 交卷操作
     * @param paperId
     * @return
     */
    void handExam(String paperId);

    /**
     * 试卷列表响应类
     * @param reqDTO
     * @return
     */
    IPage<PaperListRespDTO> paging(PagingReqDTO<PaperListReqDTO> reqDTO);

    /**
     * 检测是否有进行中的考试
     * @param userId
     * @return
     */
    PaperDTO checkProcess(String userId);

    /**
     * 试卷详情，用于教师批阅主观题
     * @param paperId 试卷ID
     * @return 试卷详情
     */
    PaperDetailRespDTO paperDetail2(String paperId);

    /**
     * 批量保存试题得分
     * @param quList 试题列表
     */
    void savePaperQuList(List<PaperQuDTO> quList);

    /**
     * 随机抽取试题生成试卷
     * @param userId 用户ID
     * @param count 试题数量
     * @param types 题型列表
     * @param repoIds 试题库ID列表，为null时查询所有题库
     * @return 试卷ID
     */
    List<QuDTO2> randomQuestions(String userId, Integer count, List<Integer> types, List<String> repoIds);

    SysUser AppFinishExam(List<APPFinishExamDTO> questions);

}
