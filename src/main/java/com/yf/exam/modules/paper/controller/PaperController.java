package com.yf.exam.modules.paper.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.core.api.dto.BaseIdReqDTO;
import com.yf.exam.core.api.dto.BaseIdRespDTO;
import com.yf.exam.core.api.dto.BaseIdsReqDTO;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.core.utils.BeanMapper;
import com.yf.exam.modules.exam.dto.request.PaperSaveReqDTO;
import com.yf.exam.modules.paper.dto.PaperDTO;
import com.yf.exam.modules.paper.dto.ext.PaperQuDetailDTO;
import com.yf.exam.modules.paper.dto.request.*;
import com.yf.exam.modules.paper.dto.response.ExamDetailRespDTO;
import com.yf.exam.modules.paper.dto.response.ExamResultRespDTO;
import com.yf.exam.modules.paper.dto.response.PaperDetailRespDTO;
import com.yf.exam.modules.paper.dto.response.PaperListRespDTO;
import com.yf.exam.modules.paper.entity.Paper;
import com.yf.exam.modules.paper.entity.PaperQu;
import com.yf.exam.modules.paper.service.PaperService;
import com.yf.exam.modules.qu.dto.QuDTO2;
import com.yf.exam.modules.qu.entity.Qu2;
import com.yf.exam.modules.sys.user.dto.response.SysUserAppLoginDTO;
import com.yf.exam.modules.sys.user.dto.response.SysUserLoginDTO;
import com.yf.exam.modules.sys.user.entity.SysUser;
import com.yf.exam.modules.user.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 试卷控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-25 16:33
 */
@Api(tags = { "试卷" })
@RestController
@RequestMapping("/exam/api/paper/paper")
@Slf4j
public class PaperController extends BaseController {

    @Autowired
    private PaperService baseService;

    /**
     * 分页查找
     * 
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "分页查找")
    @RequestMapping(value = "/paging", method = { RequestMethod.POST })
    public ApiRest<IPage<PaperListRespDTO>> paging(@RequestBody PagingReqDTO<PaperListReqDTO> reqDTO) {
        // 分页查询并转换
        IPage<PaperListRespDTO> page = baseService.paging(reqDTO);
        return super.success(page);
    }

    /**
     * 创建试卷
     * 
     * @param reqDTO`
     * @return
     */
    @ApiOperation(value = "创建试卷")
    @RequestMapping(value = "/create-paper", method = { RequestMethod.POST })
    public ApiRest<BaseIdRespDTO> save(@RequestBody PaperCreateReqDTO reqDTO) {
        // 获取当前用户信息
        SysUserLoginDTO user = UserUtils.getUser();
        // 复制参数
        String paperId = baseService.createPaper(UserUtils.getUserId(), reqDTO.getExamId());
        log.info("用户 {} 创建了试卷，试卷ID为：{}", user.getRealName(), paperId);
        return super.success(new BaseIdRespDTO(paperId));
    }

    @ApiOperation(value = "随机试题")
    @RequestMapping(value = "/random-question", method = { RequestMethod.POST })
    public ApiRest<List<QuDTO2>> randomQuestion(@RequestBody Map<String, Object> params) {
        // 获取当前用户信息
        SysUserLoginDTO user = UserUtils.getUser();
        Integer count = Integer.parseInt(params.get("count").toString());

        if (count>20) {
            log.warn("用户 {} 请求试题数量过多，有盗题嫌疑，数量为：{}", user.getUserName(), count);
            user.setState(1);
            return null;
        }
        String userId = UserUtils.getUserId();

        List<Integer> types = new ArrayList<>();
        Object typeParam = params.get("type");

        if (typeParam instanceof Integer && ((Integer) typeParam).equals(1)) {
            types = Arrays.asList(1, 2, 3);
        } else if (typeParam instanceof Integer) {
            types = Arrays.asList((Integer) typeParam);
        }

        // 获取试题库ID列表参数
        List<String> repoIds = null;
        if (params.containsKey("repoIds") && params.get("repoIds") != null) {
            if (params.get("repoIds") instanceof List) {
                repoIds = (List<String>) params.get("repoIds");
            }
        }

        List<QuDTO2> questions = baseService.randomQuestions(userId, count, types, repoIds);
        log.info("用户 {} 随机创建了试题，试题数量为：{}，题型为：{}，题库ID：{}", 
                user.getUserName(), questions.size(), types.toArray(), repoIds != null ? repoIds.toArray() : "全部");
        // 返回试卷ID
        return super.success(questions);
    }

    @ApiOperation(value = "APP提交答案")
    @RequestMapping(value = "/finish-exam", method = { RequestMethod.POST })
    public ApiRest<SysUserAppLoginDTO> finishExam(@RequestBody List<APPFinishExamDTO> questions) {
        // 获取当前用户信息
        SysUserLoginDTO user = UserUtils.getUser();
        SysUser sysUser = baseService.AppFinishExam(questions);
        SysUserAppLoginDTO userApp = new SysUserAppLoginDTO();
        BeanMapper.copy(sysUser, userApp);
        log.info("用户 {} 通过APP提交了答案，提交题目数量：{}", user.getUserName(), questions.size());
        return super.success("测试结束，当前积分为：" + userApp.getScore() + ",继续加油！", userApp);
    }

    /**
     * 批量删除
     * 
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "试卷详情")
    @RequestMapping(value = "/paper-detail", method = { RequestMethod.POST })
    public ApiRest<ExamDetailRespDTO> paperDetail(@RequestBody BaseIdReqDTO reqDTO) {
        // 根据ID删除
        ExamDetailRespDTO respDTO = baseService.paperDetail(reqDTO.getId());
        return super.success(respDTO);
    }

    @ApiOperation(value = "试卷详情(教师批阅)", notes = "试卷详情(教师批阅)")
    @RequestMapping(value = "/paper-detail2", method = RequestMethod.POST)
    public ApiRest<PaperDetailRespDTO> paperDetail2(@RequestBody BaseIdReqDTO reqDTO) {
        PaperDetailRespDTO respDTO = baseService.paperDetail2(reqDTO.getId());

        // 检查 hideStudentName 属性
        if (respDTO.getHideStudentName() != null && respDTO.getHideStudentName()) {
            respDTO.setUserName("***"); // 隐藏考生姓名
            respDTO.setUserId(null); // 隐藏考生ID
        }

        return super.success(respDTO);
    }

    @RequiresRoles(value = { "sa", "teacher" }, logical = Logical.OR)
    @ApiOperation(value = "试卷详情(教师批阅)", notes = "试卷详情(教师批阅)")
    @RequestMapping(value = "/mark", method = RequestMethod.POST)
    public ApiRest<PaperDetailRespDTO> mark(@RequestBody PaperSaveReqDTO reqDTO) {
        // 获取当前用户信息
        SysUserLoginDTO user = UserUtils.getUser();
        // 获取试卷信息
        Paper paper = baseService.getById(reqDTO.getPaperId());

        // 计算主观题总分
        Integer subjScore = reqDTO.getPaperList().stream()
                .mapToInt(qu -> qu.getActualScore())
                .sum();

        // 更新试卷主观题得分
        paper.setSubjScore(subjScore);
        // 更新总分 = 客观题得分 + 主观题得分
        paper.setUserScore(paper.getObjScore() + subjScore);
        // 更新状态为已批改
        paper.setState(3);
        baseService.updateById(paper);

        // 批量保存试题得分
        baseService.savePaperQuList(reqDTO.getPaperList());

        log.info("用户 {} 批阅了试卷，试卷ID：{}，主观题得分：{}，总分：{}",
                user.getRealName(), reqDTO.getPaperId(), subjScore, paper.getUserScore());

        return super.success(baseService.paperDetail2(reqDTO.getPaperId()));
    }

    /**
     * 批量删除
     * 
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "试题详情")
    @RequestMapping(value = "/qu-detail", method = { RequestMethod.POST })
    public ApiRest<PaperQuDetailDTO> quDetail(@RequestBody PaperQuQueryDTO reqDTO) {
        // 根据ID删除
        PaperQuDetailDTO respDTO = baseService.findQuDetail(reqDTO.getPaperId(), reqDTO.getQuId());
        return super.success(respDTO);
    }

    /**
     * 填充答案
     * 
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "填充答案")
    @RequestMapping(value = "/fill-answer", method = { RequestMethod.POST })
    public ApiRest<PaperQuDetailDTO> fillAnswer(@RequestBody PaperAnswerDTO reqDTO) {
        // 获取当前用户信息
        SysUserLoginDTO user = UserUtils.getUser();
        // 根据ID删除
        baseService.fillAnswer(reqDTO);
        log.info("用户 {} 填充了答案，试卷ID：{}，题目ID：{}",
                user.getRealName(), reqDTO.getPaperId(), reqDTO.getQuId());
        return super.success();
    }

    /**
     * 交卷操作
     * 
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "交卷操作")
    @RequestMapping(value = "/hand-exam", method = { RequestMethod.POST })
    public ApiRest<PaperQuDetailDTO> handleExam(@RequestBody BaseIdReqDTO reqDTO) {
        // 获取当前用户信息
        SysUserLoginDTO user = UserUtils.getUser();
        // 根据ID删除
        baseService.handExam(reqDTO.getId());
        log.info("用户 {} 交卷了，试卷ID：{}", user.getRealName(), reqDTO.getId());
        return super.success();
    }

    /**
     * 批量删除
     * 
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "试卷详情")
    @RequestMapping(value = "/paper-result", method = { RequestMethod.POST })
    public ApiRest<ExamResultRespDTO> paperResult(@RequestBody BaseIdReqDTO reqDTO) {
        // 根据ID删除
        ExamResultRespDTO respDTO = baseService.paperResult(reqDTO.getId());
        return super.success(respDTO);
    }

    /**
     * 检测用户有没有中断的考试
     * 
     * @return
     */
    @ApiOperation(value = "检测进行中的考试")
    @RequestMapping(value = "/check-process", method = { RequestMethod.POST })
    public ApiRest<PaperDTO> checkProcess() {
        // 复制参数
        PaperDTO dto = baseService.checkProcess(UserUtils.getUserId());
        return super.success(dto);
    }
}
