package com.yf.exam.modules.paper.dto.response;

import com.yf.exam.modules.paper.dto.PaperDTO;
import com.yf.exam.modules.paper.dto.PaperQuDTO;
import com.yf.exam.modules.paper.dto.PaperQuDTO2;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PaperDetailRespDTO extends PaperDTO {

     @ApiModelProperty(value = "单选题列表", required=true)
     private List<PaperQuDTO2> radioList;
     private String userName;

     @ApiModelProperty(value = "多选题列表", required=true)
     private List<PaperQuDTO2> multiList;

     @ApiModelProperty(value = "判断题", required=true)
     private List<PaperQuDTO2> judgeList;
     private List<PaperQuDTO2> blankList;
     private List<PaperQuDTO2> shortList;
}
