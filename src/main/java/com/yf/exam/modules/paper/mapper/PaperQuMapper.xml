<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.exam.modules.paper.mapper.PaperQuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.exam.modules.paper.entity.PaperQu">
        <result column="id" property="id" />
        <result column="paper_id" property="paperId" />
        <result column="qu_id" property="quId" />
        <result column="qu_type" property="quType" />
        <result column="answered" property="answered" />
        <result column="answer" property="answer" />
        <result column="sort" property="sort" />
        <result column="score" property="score" />
        <result column="actual_score" property="actualScore" />
        <result column="is_right" property="isRight" />
    </resultMap>

    <!-- 查询试卷的题目列表 -->
    <select id="listByPaper" resultType="com.yf.exam.modules.paper.dto.PaperQuDTO">
        SELECT * FROM el_paper_qu
        WHERE paper_id = #{paperId}
        ORDER BY sort ASC
    </select>

</mapper> 