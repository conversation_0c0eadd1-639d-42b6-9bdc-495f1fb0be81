package com.yf.exam.modules.convection.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.core.api.dto.BaseIdReqDTO;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.modules.convection.dto.ConvectionGradingDTO;
import com.yf.exam.modules.convection.dto.request.ConvectionGradingSubmitDTO;
import com.yf.exam.modules.convection.service.ConvectionGradingService;
import com.yf.exam.modules.user.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 强对流人工批卷控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
@Api(tags = {"强对流人工批卷"})
@RestController
@RequestMapping("/exam/api/convection/grading")
@Slf4j
public class ConvectionGradingController extends BaseController {

    @Autowired
    private ConvectionGradingService convectionGradingService;

    /**
     * 分页查询批卷任务列表
     */
    @RequiresRoles(value = {"sa", "teacher"}, logical = Logical.OR)
    @ApiOperation(value = "分页查询批卷任务列表")
    @PostMapping("/task/paging")
    public ApiRest<IPage<ConvectionGradingDTO>> gradingTaskPaging(@RequestBody PagingReqDTO<ConvectionGradingDTO> reqDTO) {
        IPage<ConvectionGradingDTO> page = convectionGradingService.gradingTaskPaging(reqDTO);
        return super.success(page);
    }

    /**
     * 获取批卷详情
     */
    @RequiresRoles(value = {"sa", "teacher"}, logical = Logical.OR)
    @ApiOperation(value = "获取批卷详情")
    @PostMapping("/detail")
    public ApiRest<ConvectionGradingDTO> getGradingDetail(@RequestBody BaseIdReqDTO reqDTO) {
        ConvectionGradingDTO detail = convectionGradingService.getGradingDetail(reqDTO.getId());
        return super.success(detail);
    }

    /**
     * 开始批卷（分配批卷任务）
     */
    @RequiresRoles(value = {"sa", "teacher"}, logical = Logical.OR)
    @ApiOperation(value = "开始批卷")
    @PostMapping("/start")
    public ApiRest<String> startGrading(@RequestBody GradingStartRequest request) {
        String recordId = convectionGradingService.startGrading(
            request.getAnswerId(), 
            UserUtils.getUserId()
        );
        log.info("用户 {} 开始批卷，答案ID：{}，批卷记录ID：{}", 
            UserUtils.getUser().getUserName(), request.getAnswerId(), recordId);
        return super.success(recordId);
    }

    /**
     * 提交批卷结果
     */
    @RequiresRoles(value = {"sa", "teacher"}, logical = Logical.OR)
    @ApiOperation(value = "提交批卷结果")
    @PostMapping("/submit")
    public ApiRest<Map<String, Object>> submitGrading(@Valid @RequestBody ConvectionGradingSubmitDTO submitDTO) {
        Map<String, Object> result = convectionGradingService.submitGrading(submitDTO);
        log.info("用户 {} 提交了批卷结果，答案ID：{}", 
            UserUtils.getUser().getUserName(), submitDTO.getAnswerId());
        return super.success(result);
    }

    /**
     * 批量分配批卷任务
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "批量分配批卷任务")
    @PostMapping("/batch-assign")
    public ApiRest<Map<String, Object>> batchAssignGrading(@RequestBody BatchAssignRequest request) {
        Map<String, Object> result = convectionGradingService.batchAssignGrading(
            request.getAnswerIds(), 
            request.getGraderUserIds()
        );
        log.info("管理员 {} 批量分配了批卷任务，答案数量：{}，教师数量：{}", 
            UserUtils.getUser().getUserName(), 
            request.getAnswerIds().size(), 
            request.getGraderUserIds().size());
        return super.success(result);
    }

    /**
     * 查询批卷统计信息
     */
    @RequiresRoles(value = {"sa", "teacher"}, logical = Logical.OR)
    @ApiOperation(value = "查询批卷统计信息")
    @PostMapping("/statistics")
    public ApiRest<Map<String, Object>> getGradingStatistics(@RequestBody StatisticsRequest request) {
        Date startTime = null;
        Date endTime = null;
        
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            if (request.getStartTime() != null) {
                startTime = sdf.parse(request.getStartTime());
            }
            if (request.getEndTime() != null) {
                endTime = sdf.parse(request.getEndTime());
            }
        } catch (Exception e) {
            return super.failure("时间格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式");
        }
        
        Map<String, Object> statistics = convectionGradingService.getGradingStatistics(
            request.getExamId(), startTime, endTime
        );
        return super.success(statistics);
    }

    /**
     * 查询教师批卷工作量统计
     */
    @RequiresRoles(value = {"sa", "teacher"}, logical = Logical.OR)
    @ApiOperation(value = "查询教师批卷工作量统计")
    @PostMapping("/workload-statistics")
    public ApiRest<Map<String, Object>> getGraderWorkloadStatistics(@RequestBody WorkloadStatisticsRequest request) {
        Date startTime = null;
        Date endTime = null;
        
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            if (request.getStartTime() != null) {
                startTime = sdf.parse(request.getStartTime());
            }
            if (request.getEndTime() != null) {
                endTime = sdf.parse(request.getEndTime());
            }
        } catch (Exception e) {
            return super.failure("时间格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式");
        }
        
        String graderUserId = request.getGraderUserId();
        if (graderUserId == null) {
            graderUserId = UserUtils.getUserId();
        }
        
        Map<String, Object> statistics = convectionGradingService.getGraderWorkloadStatistics(
            graderUserId, startTime, endTime
        );
        return super.success(statistics);
    }

    /**
     * 分页查询需要复核的批卷记录
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "分页查询需要复核的批卷记录")
    @PostMapping("/pending-review/paging")
    public ApiRest<IPage<ConvectionGradingDTO>> pendingReviewPaging(@RequestBody PagingReqDTO<ConvectionGradingDTO> reqDTO) {
        IPage<ConvectionGradingDTO> page = convectionGradingService.pendingReviewPaging(reqDTO);
        return super.success(page);
    }

    /**
     * 提交复核意见
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "提交复核意见")
    @PostMapping("/review/submit")
    public ApiRest<Map<String, Object>> submitReview(@RequestBody ReviewSubmitRequest request) {
        Map<String, Object> result = convectionGradingService.submitReview(
            request.getRecordId(), 
            UserUtils.getUserId(), 
            request.getReviewComments()
        );
        log.info("管理员 {} 提交了复核意见，批卷记录ID：{}", 
            UserUtils.getUser().getUserName(), request.getRecordId());
        return super.success(result);
    }

    /**
     * 查询考试的批卷进度
     */
    @RequiresRoles(value = {"sa", "teacher"}, logical = Logical.OR)
    @ApiOperation(value = "查询考试的批卷进度")
    @PostMapping("/progress")
    public ApiRest<Map<String, Object>> getGradingProgress(@RequestBody ExamProgressRequest request) {
        Map<String, Object> progress = convectionGradingService.getGradingProgress(request.getExamId());
        return super.success(progress);
    }

    /**
     * 重新分配批卷任务
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "重新分配批卷任务")
    @PostMapping("/reassign")
    public ApiRest<Map<String, Object>> reassignGrading(@RequestBody ReassignRequest request) {
        Map<String, Object> result = convectionGradingService.reassignGrading(
            request.getRecordId(), 
            request.getNewGraderUserId()
        );
        log.info("管理员 {} 重新分配了批卷任务，批卷记录ID：{}，新教师ID：{}", 
            UserUtils.getUser().getUserName(), request.getRecordId(), request.getNewGraderUserId());
        return super.success(result);
    }

    /**
     * 取消批卷任务
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "取消批卷任务")
    @PostMapping("/cancel")
    public ApiRest<Map<String, Object>> cancelGrading(@RequestBody CancelGradingRequest request) {
        Map<String, Object> result = convectionGradingService.cancelGrading(
            request.getRecordId(), 
            request.getReason()
        );
        log.info("管理员 {} 取消了批卷任务，批卷记录ID：{}，原因：{}", 
            UserUtils.getUser().getUserName(), request.getRecordId(), request.getReason());
        return super.success(result);
    }

    /**
     * 导出批卷结果
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "导出批卷结果")
    @PostMapping("/export")
    public ApiRest<List<Map<String, Object>>> exportGradingResults(@RequestBody ExamProgressRequest request) {
        List<Map<String, Object>> data = convectionGradingService.exportGradingResults(request.getExamId());
        return super.success(data);
    }

    /**
     * 自动分配批卷任务
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "自动分配批卷任务")
    @PostMapping("/auto-assign")
    public ApiRest<Map<String, Object>> autoAssignGrading(@RequestBody AutoAssignRequest request) {
        Map<String, Object> result = convectionGradingService.autoAssignGrading(
            request.getExamId(), 
            request.getGraderUserIds()
        );
        log.info("管理员 {} 自动分配了批卷任务，考试ID：{}，教师数量：{}", 
            UserUtils.getUser().getUserName(), request.getExamId(), request.getGraderUserIds().size());
        return super.success(result);
    }

    // ========== 请求对象定义 ==========

    public static class GradingStartRequest {
        private String answerId;

        public String getAnswerId() {
            return answerId;
        }

        public void setAnswerId(String answerId) {
            this.answerId = answerId;
        }
    }

    public static class BatchAssignRequest {
        private List<String> answerIds;
        private List<String> graderUserIds;

        public List<String> getAnswerIds() {
            return answerIds;
        }

        public void setAnswerIds(List<String> answerIds) {
            this.answerIds = answerIds;
        }

        public List<String> getGraderUserIds() {
            return graderUserIds;
        }

        public void setGraderUserIds(List<String> graderUserIds) {
            this.graderUserIds = graderUserIds;
        }
    }

    public static class StatisticsRequest {
        private String examId;
        private String startTime;
        private String endTime;

        public String getExamId() {
            return examId;
        }

        public void setExamId(String examId) {
            this.examId = examId;
        }

        public String getStartTime() {
            return startTime;
        }

        public void setStartTime(String startTime) {
            this.startTime = startTime;
        }

        public String getEndTime() {
            return endTime;
        }

        public void setEndTime(String endTime) {
            this.endTime = endTime;
        }
    }

    public static class WorkloadStatisticsRequest {
        private String graderUserId;
        private String startTime;
        private String endTime;

        public String getGraderUserId() {
            return graderUserId;
        }

        public void setGraderUserId(String graderUserId) {
            this.graderUserId = graderUserId;
        }

        public String getStartTime() {
            return startTime;
        }

        public void setStartTime(String startTime) {
            this.startTime = startTime;
        }

        public String getEndTime() {
            return endTime;
        }

        public void setEndTime(String endTime) {
            this.endTime = endTime;
        }
    }

    public static class ReviewSubmitRequest {
        private String recordId;
        private String reviewComments;

        public String getRecordId() {
            return recordId;
        }

        public void setRecordId(String recordId) {
            this.recordId = recordId;
        }

        public String getReviewComments() {
            return reviewComments;
        }

        public void setReviewComments(String reviewComments) {
            this.reviewComments = reviewComments;
        }
    }

    public static class ExamProgressRequest {
        private String examId;

        public String getExamId() {
            return examId;
        }

        public void setExamId(String examId) {
            this.examId = examId;
        }
    }

    public static class ReassignRequest {
        private String recordId;
        private String newGraderUserId;

        public String getRecordId() {
            return recordId;
        }

        public void setRecordId(String recordId) {
            this.recordId = recordId;
        }

        public String getNewGraderUserId() {
            return newGraderUserId;
        }

        public void setNewGraderUserId(String newGraderUserId) {
            this.newGraderUserId = newGraderUserId;
        }
    }

    public static class CancelGradingRequest {
        private String recordId;
        private String reason;

        public String getRecordId() {
            return recordId;
        }

        public void setRecordId(String recordId) {
            this.recordId = recordId;
        }

        public String getReason() {
            return reason;
        }

        public void setReason(String reason) {
            this.reason = reason;
        }
    }

    public static class AutoAssignRequest {
        private String examId;
        private List<String> graderUserIds;

        public String getExamId() {
            return examId;
        }

        public void setExamId(String examId) {
            this.examId = examId;
        }

        public List<String> getGraderUserIds() {
            return graderUserIds;
        }

        public void setGraderUserIds(List<String> graderUserIds) {
            this.graderUserIds = graderUserIds;
        }
    }
} 