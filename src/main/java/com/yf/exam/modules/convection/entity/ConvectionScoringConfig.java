package com.yf.exam.modules.convection.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.yf.exam.config.CustomJacksonTypeHandler;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 强对流评分配置实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@TableName("el_convection_scoring_config")
public class ConvectionScoringConfig extends Model<ConvectionScoringConfig> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 配置名称
     */
    @TableField("config_name")
    private String configName;

    /**
     * 配置版本
     */
    @TableField("config_version")
    private String configVersion;

    /**
     * 总分
     */
    @TableField("total_score")
    private Integer totalScore;

    /**
     * 站点预报分数
     */
    @TableField("station_forecast_score")
    private Integer stationForecastScore;

    /**
     * 落区绘制分数
     */
    @TableField("area_forecast_score")
    private Integer areaForecastScore;

    /**
     * 评分规则JSON配置
     * 包含详细的评分算法和权重配置
     */
    @TableField(value = "scoring_rules", typeHandler = CustomJacksonTypeHandler.class)
    private Map<String, Object> scoringRules;

    /**
     * 容差配置JSON
     * 用于设置评分的容错范围
     */
    @TableField(value = "tolerance_config", typeHandler = CustomJacksonTypeHandler.class)
    private Map<String, Object> toleranceConfig;

    /**
     * 是否启用
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    // ========== 业务方法 ==========

    /**
     * 判断配置是否有效
     */
    public boolean isValid() {
        return this.isActive != null && this.isActive && 
               this.totalScore != null && this.totalScore > 0 &&
               this.stationForecastScore != null && this.stationForecastScore > 0 &&
               this.areaForecastScore != null && this.areaForecastScore > 0 &&
               this.scoringRules != null && !this.scoringRules.isEmpty();
    }

    /**
     * 验证分数配置是否合理
     */
    public boolean isScoreConfigValid() {
        if (this.totalScore == null || this.stationForecastScore == null || this.areaForecastScore == null) {
            return false;
        }
        
        // 验证站点预报分数 + 落区绘制分数 = 总分
        return (this.stationForecastScore + this.areaForecastScore) == this.totalScore;
    }

    /**
     * 获取站点预报评分规则
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getStationForecastRules() {
        if (this.scoringRules != null && this.scoringRules.containsKey("station_forecast")) {
            return (Map<String, Object>) this.scoringRules.get("station_forecast");
        }
        return null;
    }

    /**
     * 获取落区绘制评分规则
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getAreaForecastRules() {
        if (this.scoringRules != null && this.scoringRules.containsKey("area_forecast")) {
            return (Map<String, Object>) this.scoringRules.get("area_forecast");
        }
        return null;
    }

    /**
     * 获取容差配置
     */
    public Double getTolerance(String key) {
        if (this.toleranceConfig != null && this.toleranceConfig.containsKey(key)) {
            Object value = this.toleranceConfig.get(key);
            if (value instanceof Number) {
                return ((Number) value).doubleValue();
            }
        }
        return 0.0;
    }

    /**
     * 获取配置描述
     */
    public String getConfigDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append(this.configName);
        if (this.configVersion != null) {
            sb.append(" v").append(this.configVersion);
        }
        sb.append(" (总分:").append(this.totalScore);
        sb.append(", 站点:").append(this.stationForecastScore);
        sb.append(", 落区:").append(this.areaForecastScore).append(")");
        return sb.toString();
    }

    /**
     * 检查是否为默认配置
     */
    public boolean isDefaultConfig() {
        return "default_convection_scoring".equals(this.id) || 
               (this.configName != null && this.configName.contains("默认"));
    }
} 