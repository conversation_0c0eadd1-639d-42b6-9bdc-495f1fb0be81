package com.yf.exam.modules.convection.scoring;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 强对流评分结果类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
public class ConvectionScoringResult {

    /**
     * 答案ID
     */
    private String answerId;

    /**
     * 站点预报得分 (68分)
     */
    private BigDecimal stationScore;

    /**
     * 落区绘制得分 (32分)
     */
    private BigDecimal areaScore;

    /**
     * 总分 (100分)
     */
    private BigDecimal totalScore;

    /**
     * 预报依据人工评分 (20分)
     * 注：需要人工批卷时才有此分数
     */
    private BigDecimal reasoningScore;

    /**
     * 评分是否成功
     */
    private boolean success;

    /**
     * 评分消息
     */
    private String message;

    /**
     * 详细得分说明
     */
    private Map<String, Object> scoreDetails;

    /**
     * 评分时间
     */
    private Date scoringTime;

    /**
     * 算法版本
     */
    private String algorithmVersion;

    /**
     * 评分类型：auto-自动评分，manual-人工评分
     */
    private String scoringType;

    /**
     * 错误信息（评分失败时）
     */
    private String errorMessage;

    /**
     * 构造函数
     */
    public ConvectionScoringResult() {
        this.stationScore = BigDecimal.ZERO;
        this.areaScore = BigDecimal.ZERO;
        this.totalScore = BigDecimal.ZERO;
        this.reasoningScore = BigDecimal.ZERO;
        this.success = false;
        this.scoringTime = new Date();
        this.algorithmVersion = "1.0.0";
        this.scoringType = "auto";
    }

    /**
     * 是否需要人工批卷
     */
    public boolean needsManualGrading() {
        // 如果评分不成功，或者预报依据得分为0，需要人工批卷
        return !success || reasoningScore.compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * 获取最终得分（包含人工评分）
     */
    public BigDecimal getFinalScore() {
        return totalScore.add(reasoningScore);
    }

    /**
     * 获取得分率
     */
    public BigDecimal getScoreRate() {
        BigDecimal maxScore = new BigDecimal("100");
        if (reasoningScore.compareTo(BigDecimal.ZERO) > 0) {
            maxScore = new BigDecimal("120"); // 包含预报依据分数
        }
        
        if (maxScore.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        
        return getFinalScore().divide(maxScore, 4, java.math.RoundingMode.HALF_UP)
                             .multiply(new BigDecimal("100"))
                             .setScale(1, java.math.RoundingMode.HALF_UP);
    }

    /**
     * 评分是否通过（>=60分）
     */
    public boolean isPassed() {
        return getFinalScore().compareTo(new BigDecimal("60")) >= 0;
    }

    /**
     * 获取评分等级
     */
    public String getGradeLevel() {
        BigDecimal finalScore = getFinalScore();
        
        if (finalScore.compareTo(new BigDecimal("90")) >= 0) {
            return "优秀";
        } else if (finalScore.compareTo(new BigDecimal("80")) >= 0) {
            return "良好";
        } else if (finalScore.compareTo(new BigDecimal("70")) >= 0) {
            return "中等";
        } else if (finalScore.compareTo(new BigDecimal("60")) >= 0) {
            return "及格";
        } else {
            return "不及格";
        }
    }
} 