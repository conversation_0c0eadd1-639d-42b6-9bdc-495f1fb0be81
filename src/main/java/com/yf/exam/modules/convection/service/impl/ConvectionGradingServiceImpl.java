package com.yf.exam.modules.convection.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.core.utils.BeanMapper;
import com.yf.exam.modules.convection.dto.ConvectionGradingDTO;
import com.yf.exam.modules.convection.dto.request.ConvectionGradingSubmitDTO;
import com.yf.exam.modules.convection.entity.ConvectionGradingRecord;
import com.yf.exam.modules.convection.mapper.ConvectionGradingRecordMapper;
import com.yf.exam.modules.convection.service.ConvectionGradingService;
import com.yf.exam.modules.user.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * <p>
 * 强对流人工批卷业务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class ConvectionGradingServiceImpl extends ServiceImpl<ConvectionGradingRecordMapper, ConvectionGradingRecord> 
        implements ConvectionGradingService {

    @Autowired
    private ConvectionGradingRecordMapper convectionGradingRecordMapper;

    @Override
    public IPage<ConvectionGradingDTO> gradingTaskPaging(PagingReqDTO<ConvectionGradingDTO> reqDTO) {
        Page<ConvectionGradingDTO> page = new Page<>(reqDTO.getCurrent(), reqDTO.getSize());
        
        ConvectionGradingDTO params = reqDTO.getParams();
        String examId = params != null ? params.getExamId() : null;
        Integer gradingStatus = params != null ? params.getGradingStatus() : null;
        String graderUserId = params != null ? params.getGraderUserId() : null;
        
        return convectionGradingRecordMapper.selectGradingTaskList(page, examId, gradingStatus, graderUserId);
    }

    @Override
    public ConvectionGradingDTO getGradingDetail(String recordId) {
        if (!StringUtils.hasText(recordId)) {
            log.warn("批卷记录ID不能为空");
            return null;
        }
        
        return convectionGradingRecordMapper.selectGradingDetail(recordId);
    }

    @Override
    public String startGrading(String answerId, String graderUserId) {
        if (!StringUtils.hasText(answerId)) {
            throw new IllegalArgumentException("答案ID不能为空");
        }
        if (!StringUtils.hasText(graderUserId)) {
            throw new IllegalArgumentException("批卷教师用户ID不能为空");
        }

        // 检查是否已存在批卷记录
        ConvectionGradingRecord existingRecord = convectionGradingRecordMapper.selectByAnswerAndGrader(answerId, graderUserId);
        if (existingRecord != null) {
            log.info("批卷记录已存在，记录ID：{}", existingRecord.getId());
            return existingRecord.getId();
        }

        // 创建新的批卷记录
        ConvectionGradingRecord record = new ConvectionGradingRecord();
        record.setAnswerId(answerId);
        record.setGraderUserId(graderUserId);
        record.setGradingStatus(0); // 未批卷
        record.setReviewStatus(0); // 未复核
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());

        // TODO: 根据answerId查询examId和studentUserId
        // record.setExamId(examId);
        // record.setStudentUserId(studentUserId);

        convectionGradingRecordMapper.insert(record);
        
        log.info("创建批卷记录成功，记录ID：{}，答案ID：{}，批卷教师：{}", 
                record.getId(), answerId, graderUserId);
        
        return record.getId();
    }

    @Override
    public Map<String, Object> submitGrading(ConvectionGradingSubmitDTO submitDTO) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证数据
            Map<String, Object> validationResult = validateGradingData(submitDTO);
            if (!(Boolean) validationResult.get("success")) {
                return validationResult;
            }

            // 查询批卷记录
            ConvectionGradingRecord record = convectionGradingRecordMapper.selectByAnswerId(submitDTO.getAnswerId());
            if (record == null) {
                result.put("success", false);
                result.put("message", "未找到对应的批卷记录");
                return result;
            }

            // 更新批卷记录
            record.setReasoningGradingBasisScore(submitDTO.getReasoningGradingBasisScore());
            record.setReasoningExtremeScore(submitDTO.getReasoningExtremeScore());
            record.setReasoningTotalScore(submitDTO.getReasoningGradingBasisScore().add(submitDTO.getReasoningExtremeScore()));
            record.setGradingComments(submitDTO.getGradingComments());
            record.setImprovementSuggestions(submitDTO.getImprovementSuggestions());
            record.setGradingStatus(1); // 已批卷
            record.setGradingTime(new Date());
            record.setUpdateTime(new Date());

            // 计算批卷耗时
            if (submitDTO.getGradingStartTime() != null) {
                long duration = (System.currentTimeMillis() - submitDTO.getGradingStartTime()) / 1000;
                record.setGradingDuration((int) duration);
            }

            convectionGradingRecordMapper.updateById(record);

            result.put("success", true);
            result.put("message", "批卷提交成功");
            result.put("recordId", record.getId());
            result.put("totalScore", record.getReasoningTotalScore());
            
            log.info("批卷提交成功，记录ID：{}，总分：{}", record.getId(), record.getReasoningTotalScore());
            
        } catch (Exception e) {
            log.error("批卷提交失败", e);
            result.put("success", false);
            result.put("message", "批卷提交失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> batchAssignGrading(List<String> answerIds, List<String> graderUserIds) {
        Map<String, Object> result = new HashMap<>();
        
        if (answerIds == null || answerIds.isEmpty()) {
            result.put("success", false);
            result.put("message", "答案ID列表不能为空");
            return result;
        }
        
        if (graderUserIds == null || graderUserIds.isEmpty()) {
            result.put("success", false);
            result.put("message", "批卷教师ID列表不能为空");
            return result;
        }

        try {
            int successCount = 0;
            int failCount = 0;
            List<String> failedAnswers = new ArrayList<>();

            // 轮询分配
            for (int i = 0; i < answerIds.size(); i++) {
                String answerId = answerIds.get(i);
                String graderUserId = graderUserIds.get(i % graderUserIds.size());
                
                try {
                    startGrading(answerId, graderUserId);
                    successCount++;
                } catch (Exception e) {
                    log.warn("分配批卷任务失败，答案ID：{}，批卷教师：{}", answerId, graderUserId, e);
                    failCount++;
                    failedAnswers.add(answerId);
                }
            }

            result.put("success", true);
            result.put("message", String.format("批量分配完成，成功：%d，失败：%d", successCount, failCount));
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("failedAnswers", failedAnswers);
            
            log.info("批量分配批卷任务完成，总数：{}，成功：{}，失败：{}", 
                    answerIds.size(), successCount, failCount);
            
        } catch (Exception e) {
            log.error("批量分配批卷任务失败", e);
            result.put("success", false);
            result.put("message", "批量分配失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getGradingStatistics(String examId, Date startTime, Date endTime) {
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            statistics = convectionGradingRecordMapper.selectGradingStatistics(examId, startTime, endTime);
            statistics.put("success", true);
            
        } catch (Exception e) {
            log.error("查询批卷统计信息失败", e);
            statistics.put("success", false);
            statistics.put("message", "查询统计信息失败：" + e.getMessage());
        }
        
        return statistics;
    }

    @Override
    public Map<String, Object> getGraderWorkloadStatistics(String graderUserId, Date startTime, Date endTime) {
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            statistics = convectionGradingRecordMapper.selectGraderWorkloadStatistics(graderUserId, startTime, endTime);
            statistics.put("success", true);
            
        } catch (Exception e) {
            log.error("查询教师批卷工作量统计失败", e);
            statistics.put("success", false);
            statistics.put("message", "查询工作量统计失败：" + e.getMessage());
        }
        
        return statistics;
    }

    @Override
    public IPage<ConvectionGradingDTO> pendingReviewPaging(PagingReqDTO<ConvectionGradingDTO> reqDTO) {
        Page<ConvectionGradingDTO> page = new Page<>(reqDTO.getCurrent(), reqDTO.getSize());
        
        ConvectionGradingDTO params = reqDTO.getParams();
        String examId = params != null ? params.getExamId() : null;
        
        return convectionGradingRecordMapper.selectPendingReviewList(page, examId);
    }

    @Override
    public Map<String, Object> validateGradingData(ConvectionGradingSubmitDTO submitDTO) {
        Map<String, Object> result = new HashMap<>();
        
        if (submitDTO == null) {
            result.put("success", false);
            result.put("message", "提交数据不能为空");
            return result;
        }
        
        if (!StringUtils.hasText(submitDTO.getAnswerId())) {
            result.put("success", false);
            result.put("message", "答案ID不能为空");
            return result;
        }
        
        if (submitDTO.getReasoningGradingBasisScore() == null) {
            result.put("success", false);
            result.put("message", "分级依据阐述得分不能为空");
            return result;
        }
        
        if (submitDTO.getReasoningExtremeScore() == null) {
            result.put("success", false);
            result.put("message", "极端天气预报理由得分不能为空");
            return result;
        }
        
        // 验证分数范围
        if (submitDTO.getReasoningGradingBasisScore().compareTo(BigDecimal.ZERO) < 0 || 
            submitDTO.getReasoningGradingBasisScore().compareTo(new BigDecimal("10")) > 0) {
            result.put("success", false);
            result.put("message", "分级依据阐述得分必须在0-10分之间");
            return result;
        }
        
        if (submitDTO.getReasoningExtremeScore().compareTo(BigDecimal.ZERO) < 0 || 
            submitDTO.getReasoningExtremeScore().compareTo(new BigDecimal("10")) > 0) {
            result.put("success", false);
            result.put("message", "极端天气预报理由得分必须在0-10分之间");
            return result;
        }
        
        result.put("success", true);
        result.put("message", "数据验证通过");
        return result;
    }

    @Override
    public Map<String, Object> getGradingQualityAnalysis(String graderUserId, String examId) {
        Map<String, Object> analysis = new HashMap<>();

        try {
            analysis = convectionGradingRecordMapper.selectGradingQualityAnalysis(graderUserId, examId);
            analysis.put("success", true);

        } catch (Exception e) {
            log.error("获取批卷质量分析失败", e);
            analysis.put("success", false);
            analysis.put("message", "获取质量分析失败：" + e.getMessage());
        }

        return analysis;
    }

    @Override
    public Map<String, Object> autoAssignGrading(String examId, List<String> graderUserIds) {
        Map<String, Object> result = new HashMap<>();

        if (!StringUtils.hasText(examId)) {
            result.put("success", false);
            result.put("message", "考试ID不能为空");
            return result;
        }

        if (graderUserIds == null || graderUserIds.isEmpty()) {
            result.put("success", false);
            result.put("message", "批卷教师ID列表不能为空");
            return result;
        }

        try {
            // TODO: 查询考试下所有需要批卷的答案ID
            List<String> answerIds = new ArrayList<>(); // 这里需要实际查询

            if (answerIds.isEmpty()) {
                result.put("success", false);
                result.put("message", "未找到需要批卷的答案");
                return result;
            }

            // 调用批量分配方法
            return batchAssignGrading(answerIds, graderUserIds);

        } catch (Exception e) {
            log.error("自动分配批卷任务失败", e);
            result.put("success", false);
            result.put("message", "自动分配失败：" + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> reassignGrading(String recordId, String newGraderUserId) {
        Map<String, Object> result = new HashMap<>();

        if (!StringUtils.hasText(recordId)) {
            result.put("success", false);
            result.put("message", "批卷记录ID不能为空");
            return result;
        }

        if (!StringUtils.hasText(newGraderUserId)) {
            result.put("success", false);
            result.put("message", "新批卷教师ID不能为空");
            return result;
        }

        try {
            ConvectionGradingRecord record = convectionGradingRecordMapper.selectById(recordId);
            if (record == null) {
                result.put("success", false);
                result.put("message", "未找到批卷记录");
                return result;
            }

            // 更新批卷教师
            record.setGraderUserId(newGraderUserId);
            record.setGradingStatus(0); // 重置为未批卷状态
            record.setGradingTime(null);
            record.setGradingDuration(null);
            record.setReasoningGradingBasisScore(null);
            record.setReasoningExtremeScore(null);
            record.setReasoningTotalScore(null);
            record.setGradingComments(null);
            record.setImprovementSuggestions(null);
            record.setUpdateTime(new Date());

            convectionGradingRecordMapper.updateById(record);

            result.put("success", true);
            result.put("message", "重新分配成功");
            result.put("recordId", recordId);

            log.info("重新分配批卷任务成功，记录ID：{}，新批卷教师：{}", recordId, newGraderUserId);

        } catch (Exception e) {
            log.error("重新分配批卷任务失败", e);
            result.put("success", false);
            result.put("message", "重新分配失败：" + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> cancelGrading(String recordId, String reason) {
        Map<String, Object> result = new HashMap<>();

        if (!StringUtils.hasText(recordId)) {
            result.put("success", false);
            result.put("message", "批卷记录ID不能为空");
            return result;
        }

        try {
            ConvectionGradingRecord record = convectionGradingRecordMapper.selectById(recordId);
            if (record == null) {
                result.put("success", false);
                result.put("message", "未找到批卷记录");
                return result;
            }

            // 删除批卷记录
            convectionGradingRecordMapper.deleteById(recordId);

            result.put("success", true);
            result.put("message", "取消批卷任务成功");
            result.put("recordId", recordId);
            result.put("reason", reason);

            log.info("取消批卷任务成功，记录ID：{}，原因：{}", recordId, reason);

        } catch (Exception e) {
            log.error("取消批卷任务失败", e);
            result.put("success", false);
            result.put("message", "取消批卷任务失败：" + e.getMessage());
        }

        return result;
    }

    @Override
    public List<Map<String, Object>> exportGradingResults(String examId) {
        List<Map<String, Object>> results = new ArrayList<>();

        try {
            if (!StringUtils.hasText(examId)) {
                log.warn("考试ID不能为空");
                return results;
            }

            results = convectionGradingRecordMapper.selectGradingResultsForExport(examId);

            log.info("导出批卷结果成功，考试ID：{}，记录数：{}", examId, results.size());

        } catch (Exception e) {
            log.error("导出批卷结果失败", e);
        }

        return results;
    }

    @Override
    public Map<String, Object> submitReview(String recordId, String reviewerUserId, String reviewComments) {
        Map<String, Object> result = new HashMap<>();

        if (!StringUtils.hasText(recordId)) {
            result.put("success", false);
            result.put("message", "批卷记录ID不能为空");
            return result;
        }

        if (!StringUtils.hasText(reviewerUserId)) {
            result.put("success", false);
            result.put("message", "复核教师ID不能为空");
            return result;
        }

        try {
            ConvectionGradingRecord record = convectionGradingRecordMapper.selectById(recordId);
            if (record == null) {
                result.put("success", false);
                result.put("message", "未找到批卷记录");
                return result;
            }

            // 更新复核信息
            int updateCount = convectionGradingRecordMapper.updateReviewInfo(recordId, reviewerUserId, reviewComments);

            if (updateCount > 0) {
                result.put("success", true);
                result.put("message", "复核提交成功");
                result.put("recordId", recordId);

                log.info("复核提交成功，记录ID：{}，复核教师：{}", recordId, reviewerUserId);
            } else {
                result.put("success", false);
                result.put("message", "复核提交失败");
            }

        } catch (Exception e) {
            log.error("复核提交失败", e);
            result.put("success", false);
            result.put("message", "复核提交失败：" + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getGradingProgress(String examId) {
        Map<String, Object> progress = new HashMap<>();

        try {
            if (!StringUtils.hasText(examId)) {
                progress.put("success", false);
                progress.put("message", "考试ID不能为空");
                return progress;
            }

            progress = convectionGradingRecordMapper.selectGradingProgress(examId);
            progress.put("success", true);

        } catch (Exception e) {
            log.error("查询批卷进度失败", e);
            progress.put("success", false);
            progress.put("message", "查询批卷进度失败：" + e.getMessage());
        }

        return progress;
    }

    @Override
    public List<ConvectionGradingRecord> listByStudent(String studentUserId) {
        if (!StringUtils.hasText(studentUserId)) {
            log.warn("学生用户ID不能为空");
            return new ArrayList<>();
        }

        try {
            return convectionGradingRecordMapper.selectByStudent(studentUserId);
        } catch (Exception e) {
            log.error("查询学生批卷记录失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ConvectionGradingRecord> listByGrader(String graderUserId) {
        if (!StringUtils.hasText(graderUserId)) {
            log.warn("批卷教师用户ID不能为空");
            return new ArrayList<>();
        }

        try {
            return convectionGradingRecordMapper.selectByGrader(graderUserId);
        } catch (Exception e) {
            log.error("查询教师批卷记录失败", e);
            return new ArrayList<>();
        }
    }
}
