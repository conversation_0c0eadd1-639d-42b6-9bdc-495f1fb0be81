package com.yf.exam.modules.convection.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 保存强对流考试答案请求DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@ApiModel(value = "保存强对流考试答案请求", description = "保存强对流考试答案请求")
public class ConvectionAnswerSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "答案ID（更新时传入）")
    private String answerId;

    @ApiModelProperty(value = "考试ID", required = true)
    @NotBlank(message = "考试ID不能为空")
    private String examId;

    @ApiModelProperty(value = "题目ID", required = true)
    @NotBlank(message = "题目ID不能为空")
    private String questionId;

    @ApiModelProperty(value = "站点预报答案JSON")
    private Map<String, Object> stationAnswer;

    @ApiModelProperty(value = "落区绘制答案JSON")
    private Map<String, Object> areaAnswer;

    @ApiModelProperty(value = "预报依据阐述")
    private String forecastReasoning;

    @ApiModelProperty(value = "站点预报进度百分比")
    private Integer stationProgress;

    @ApiModelProperty(value = "落区绘制进度百分比")
    private Integer areaProgress;

    @ApiModelProperty(value = "整体进度百分比")
    private Integer overallProgress;

    @ApiModelProperty(value = "是否自动计算进度", notes = "true-自动计算进度，false-使用传入的进度值")
    private Boolean autoCalculateProgress = true;

    @ApiModelProperty(value = "保存类型", notes = "draft-草稿保存，auto-自动保存")
    private String saveType = "auto";
} 