package com.yf.exam.modules.convection.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.modules.convection.dto.ConvectionGradingDTO;
import com.yf.exam.modules.convection.dto.request.ConvectionGradingSubmitDTO;
import com.yf.exam.modules.convection.entity.ConvectionGradingRecord;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 强对流人工批卷业务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
public interface ConvectionGradingService extends IService<ConvectionGradingRecord> {

    /**
     * 分页查询批卷任务列表
     * 
     * @param reqDTO 分页请求参数
     * @return 批卷任务分页列表
     */
    IPage<ConvectionGradingDTO> gradingTaskPaging(PagingReqDTO<ConvectionGradingDTO> reqDTO);

    /**
     * 获取批卷详情
     * 
     * @param recordId 批卷记录ID
     * @return 批卷详情
     */
    ConvectionGradingDTO getGradingDetail(String recordId);

    /**
     * 开始批卷（分配批卷任务）
     * 
     * @param answerId 答案ID
     * @param graderUserId 批卷教师用户ID
     * @return 批卷记录ID
     */
    String startGrading(String answerId, String graderUserId);

    /**
     * 提交批卷结果
     * 
     * @param submitDTO 批卷提交数据
     * @return 提交结果
     */
    Map<String, Object> submitGrading(ConvectionGradingSubmitDTO submitDTO);

    /**
     * 批量分配批卷任务
     * 
     * @param answerIds 答案ID列表
     * @param graderUserIds 批卷教师用户ID列表
     * @return 分配结果
     */
    Map<String, Object> batchAssignGrading(List<String> answerIds, List<String> graderUserIds);

    /**
     * 查询批卷统计信息
     * 
     * @param examId 考试ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    Map<String, Object> getGradingStatistics(String examId, Date startTime, Date endTime);

    /**
     * 查询教师批卷工作量统计
     * 
     * @param graderUserId 批卷教师用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 工作量统计
     */
    Map<String, Object> getGraderWorkloadStatistics(String graderUserId, Date startTime, Date endTime);

    /**
     * 分页查询需要复核的批卷记录
     * 
     * @param reqDTO 分页请求参数
     * @return 需要复核的记录分页列表
     */
    IPage<ConvectionGradingDTO> pendingReviewPaging(PagingReqDTO<ConvectionGradingDTO> reqDTO);

    /**
     * 提交复核意见
     * 
     * @param recordId 批卷记录ID
     * @param reviewerUserId 复核教师用户ID
     * @param reviewComments 复核意见
     * @return 复核结果
     */
    Map<String, Object> submitReview(String recordId, String reviewerUserId, String reviewComments);

    /**
     * 查询考试的批卷进度
     * 
     * @param examId 考试ID
     * @return 批卷进度信息
     */
    Map<String, Object> getGradingProgress(String examId);

    /**
     * 根据学生用户ID查询批卷记录列表
     * 
     * @param studentUserId 学生用户ID
     * @return 批卷记录列表
     */
    List<ConvectionGradingRecord> listByStudent(String studentUserId);

    /**
     * 根据批卷教师用户ID查询批卷记录列表
     * 
     * @param graderUserId 批卷教师用户ID
     * @return 批卷记录列表
     */
    List<ConvectionGradingRecord> listByGrader(String graderUserId);

    /**
     * 重新分配批卷任务
     * 
     * @param recordId 批卷记录ID
     * @param newGraderUserId 新的批卷教师用户ID
     * @return 重新分配结果
     */
    Map<String, Object> reassignGrading(String recordId, String newGraderUserId);

    /**
     * 取消批卷任务
     * 
     * @param recordId 批卷记录ID
     * @param reason 取消原因
     * @return 取消结果
     */
    Map<String, Object> cancelGrading(String recordId, String reason);

    /**
     * 导出批卷结果
     * 
     * @param examId 考试ID
     * @return 导出数据
     */
    List<Map<String, Object>> exportGradingResults(String examId);

    /**
     * 验证批卷数据
     * 
     * @param submitDTO 批卷提交数据
     * @return 验证结果
     */
    Map<String, Object> validateGradingData(ConvectionGradingSubmitDTO submitDTO);

    /**
     * 获取批卷质量分析
     * 
     * @param graderUserId 批卷教师用户ID
     * @param examId 考试ID
     * @return 质量分析结果
     */
    Map<String, Object> getGradingQualityAnalysis(String graderUserId, String examId);

    /**
     * 自动分配批卷任务（基于工作量均衡）
     * 
     * @param examId 考试ID
     * @param graderUserIds 可用的批卷教师ID列表
     * @return 自动分配结果
     */
    Map<String, Object> autoAssignGrading(String examId, List<String> graderUserIds);
} 