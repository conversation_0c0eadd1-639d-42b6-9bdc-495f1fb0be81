package com.yf.exam.modules.convection.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.core.exception.ServiceException;
import com.yf.exam.core.utils.BeanMapper;
import com.yf.exam.modules.convection.dto.ConvectionAnswerDTO;
import com.yf.exam.modules.convection.dto.request.ConvectionAnswerSaveDTO;
import com.yf.exam.modules.convection.entity.ConvectionExamAnswer;
import com.yf.exam.modules.convection.mapper.ConvectionExamAnswerMapper;
import com.yf.exam.modules.convection.service.ConvectionAnswerService;
import com.yf.exam.modules.user.UserUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <p>
 * 强对流考试答案业务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
@Service
public class ConvectionAnswerServiceImpl extends ServiceImpl<ConvectionExamAnswerMapper, ConvectionExamAnswer> 
        implements ConvectionAnswerService {

    @Autowired
    private ConvectionExamAnswerMapper convectionAnswerMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveAnswer(ConvectionAnswerSaveDTO saveDTO) {
        String userId = UserUtils.getUserId();
        if (StringUtils.isBlank(userId)) {
            throw new ServiceException("用户未登录");
        }

        // 验证数据
        Map<String, Object> validationResult = validateAnswerData(saveDTO);
        if (!(Boolean) validationResult.get("valid")) {
            throw new ServiceException(validationResult.get("message").toString());
        }

        ConvectionExamAnswer answer;
        
        // 检查是否已有答案记录
        if (StringUtils.isNotBlank(saveDTO.getAnswerId())) {
            answer = this.getById(saveDTO.getAnswerId());
            if (answer == null) {
                throw new ServiceException("答案记录不存在");
            }
            // 检查权限
            if (!userId.equals(answer.getUserId())) {
                throw new ServiceException("无权限修改此答案");
            }
            // 检查是否已提交
            if (answer.isSubmitted()) {
                throw new ServiceException("考试已提交，无法修改答案");
            }
        } else {
            // 检查是否已有答案记录
            answer = convectionAnswerMapper.selectByExamAndUser(saveDTO.getExamId(), userId);
            if (answer != null && answer.isSubmitted()) {
                throw new ServiceException("考试已提交，无法修改答案");
            }
            if (answer == null) {
                answer = new ConvectionExamAnswer();
                answer.setExamId(saveDTO.getExamId());
                answer.setQuestionId(saveDTO.getQuestionId());
                answer.setUserId(userId);
                answer.setAnswerStatus(0); // 答题中
                answer.setScoringStatus(0); // 未评分
            }
        }

        // 更新答案数据
        answer.setStationAnswer(saveDTO.getStationAnswer());
        answer.setAreaAnswer(saveDTO.getAreaAnswer());
        answer.setForecastReasoning(saveDTO.getForecastReasoning());
        
        // 更新字数统计
        answer.updateReasoningWordCount();

        // 计算进度
        if (saveDTO.getAutoCalculateProgress()) {
            Integer stationProgress = calculateStationProgress(saveDTO.getStationAnswer(), saveDTO.getForecastReasoning());
            Integer areaProgress = calculateAreaProgress(saveDTO.getAreaAnswer());
            Integer overallProgress = calculateOverallProgress(stationProgress, areaProgress);
            
            answer.setStationProgress(stationProgress);
            answer.setAreaProgress(areaProgress);
            answer.setOverallProgress(overallProgress);
        } else {
            answer.setStationProgress(saveDTO.getStationProgress());
            answer.setAreaProgress(saveDTO.getAreaProgress());
            answer.setOverallProgress(saveDTO.getOverallProgress());
        }

        // 保存答案
        this.saveOrUpdate(answer);
        
        return answer.getId();
    }

    @Override
    public ConvectionAnswerDTO getStudentAnswer(String examId, String userId) {
        ConvectionExamAnswer answer = convectionAnswerMapper.selectByExamAndUser(examId, userId);
        if (answer == null) {
            return null;
        }
        
        ConvectionAnswerDTO dto = new ConvectionAnswerDTO();
        BeanMapper.copy(answer, dto);
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> submitExam(String examId, String userId) {
        ConvectionExamAnswer answer = convectionAnswerMapper.selectByExamAndUser(examId, userId);
        if (answer == null) {
            throw new ServiceException("未找到答案记录，请先答题");
        }
        
        if (answer.isSubmitted()) {
            throw new ServiceException("考试已提交，请勿重复提交");
        }

        // 检查是否可以提交
        if (!canSubmit(answer)) {
            throw new ServiceException("答题内容未完成，无法提交考试");
        }

        // 更新提交状态
        answer.setAnswerStatus(1); // 已提交
        answer.setSubmitTime(new Date());
        
        // 如果有预报依据，设置为需要人工批卷
        if (answer.hasForecastReasoning()) {
            answer.setScoringStatus(1); // 自动评分完成，等待人工批卷
        } else {
            answer.setScoringStatus(0); // 仅自动评分
        }
        
        this.updateById(answer);

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("answerId", answer.getId());
        result.put("submitTime", answer.getSubmitTime());
        result.put("needsManualGrading", answer.needsManualGrading());
        
        return result;
    }

    @Override
    public Map<String, Object> calculateProgress(ConvectionExamAnswer answer) {
        Map<String, Object> result = new HashMap<>();
        
        Integer stationProgress = calculateStationProgress(answer.getStationAnswer(), answer.getForecastReasoning());
        Integer areaProgress = calculateAreaProgress(answer.getAreaAnswer());
        Integer overallProgress = calculateOverallProgress(stationProgress, areaProgress);
        
        result.put("stationProgress", stationProgress);
        result.put("areaProgress", areaProgress);
        result.put("overallProgress", overallProgress);
        result.put("description", getProgressDescription(answer));
        
        return result;
    }

    @Override
    public Map<String, Object> validateAnswerData(ConvectionAnswerSaveDTO saveDTO) {
        Map<String, Object> result = new HashMap<>();
        
        if (StringUtils.isBlank(saveDTO.getExamId())) {
            result.put("valid", false);
            result.put("message", "考试ID不能为空");
            return result;
        }
        
        if (StringUtils.isBlank(saveDTO.getQuestionId())) {
            result.put("valid", false);
            result.put("message", "题目ID不能为空");
            return result;
        }
        
        result.put("valid", true);
        result.put("message", "数据验证通过");
        return result;
    }

    @Override
    public Map<String, Object> getAnswerStatistics(String examId) {
        List<Map<String, Object>> statistics = convectionAnswerMapper.selectAnswerStatistics(
            Collections.singletonList(examId)
        );
        
        Map<String, Object> result = new HashMap<>();
        if (!CollectionUtils.isEmpty(statistics)) {
            result.putAll(statistics.get(0));
        } else {
            result.put("totalAnswers", 0);
            result.put("submittedAnswers", 0);
            result.put("avgProgress", 0);
            result.put("pendingGrading", 0);
        }
        
        return result;
    }

    @Override
    public IPage<ConvectionAnswerDTO> pendingGradingPaging(PagingReqDTO<ConvectionAnswerDTO> reqDTO) {
        Page<ConvectionAnswerDTO> page = new Page<>(reqDTO.getCurrent(), reqDTO.getSize());
        
        ConvectionAnswerDTO params = reqDTO.getParams();
        String examId = params != null ? params.getExamId() : null;
        
        return convectionAnswerMapper.selectPendingGradingAnswers(page, examId, 1, 1);
    }

    @Override
    public ConvectionAnswerDTO getAnswerDetail(String answerId) {
        return convectionAnswerMapper.selectAnswerDetail(answerId);
    }

    @Override
    public void updateProgress(String answerId, Integer stationProgress, Integer areaProgress, Integer overallProgress) {
        convectionAnswerMapper.updateProgress(answerId, stationProgress, areaProgress, overallProgress);
    }

    @Override
    public void updateScoringStatus(String answerId, Integer scoringStatus) {
        convectionAnswerMapper.updateScoringStatus(answerId, scoringStatus);
    }

    @Override
    public Map<String, Object> getProgressStatistics(String examId) {
        return convectionAnswerMapper.selectProgressStatistics(examId);
    }

    @Override
    public long countPendingManualGrading(String examId) {
        return convectionAnswerMapper.countPendingManualGrading(examId);
    }

    @Override
    public List<ConvectionExamAnswer> listByUser(String userId) {
        return convectionAnswerMapper.selectByUser(userId);
    }

    @Override
    public Integer calculateStationProgress(Map<String, Object> stationAnswerData, String forecastReasoning) {
        if (CollectionUtils.isEmpty(stationAnswerData)) {
            return 0;
        }

        int totalElements = 0;
        int completedElements = 0;

        // 遍历站点答案
        for (String stationKey : stationAnswerData.keySet()) {
            Object stationDataObj = stationAnswerData.get(stationKey);
            if (stationDataObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> stationData = (Map<String, Object>) stationDataObj;
                
                totalElements += 3; // 每站点3类天气现象
                
                if (stationData.get("shortTimeRainfall") != null) completedElements++;
                if (stationData.get("thunderstormWind") != null) completedElements++;
                if (stationData.get("hail") != null) completedElements++;
            }
        }

        // 站点选择进度占80%
        int selectionProgress = totalElements > 0 ? (completedElements * 80 / totalElements) : 0;
        
        // 预报依据进度占20%
        int reasoningProgress = (StringUtils.isNotBlank(forecastReasoning) && forecastReasoning.length() > 100) ? 20 : 0;

        return Math.min(100, selectionProgress + reasoningProgress);
    }

    @Override
    public Integer calculateAreaProgress(Map<String, Object> areaAnswerData) {
        if (CollectionUtils.isEmpty(areaAnswerData)) {
            return 0;
        }

        int totalTypes = 4; // 4类强对流天气
        int completedTypes = 0;

        String[] weatherTypes = {"heavy_rainfall", "thunderstorm_wind", "hail", "tornado"};
        
        for (String type : weatherTypes) {
            Object typeData = areaAnswerData.get(type);
            if (typeData != null && typeData instanceof List) {
                @SuppressWarnings("unchecked")
                List<Object> areas = (List<Object>) typeData;
                if (!areas.isEmpty()) {
                    completedTypes++;
                }
            }
        }

        return totalTypes > 0 ? (completedTypes * 100 / totalTypes) : 0;
    }

    @Override
    public Integer calculateOverallProgress(Integer stationProgress, Integer areaProgress) {
        if (stationProgress == null) stationProgress = 0;
        if (areaProgress == null) areaProgress = 0;
        
        // 站点预报权重68%，落区绘制权重32%
        return (int) Math.round(stationProgress * 0.68 + areaProgress * 0.32);
    }

    @Override
    public boolean canSubmit(ConvectionExamAnswer answer) {
        if (answer == null) {
            return false;
        }
        
        // 至少完成一部分内容
        boolean hasStationAnswer = answer.hasStationAnswer();
        boolean hasAreaAnswer = answer.hasAreaAnswer();
        
        return hasStationAnswer || hasAreaAnswer;
    }

    @Override
    public String getProgressDescription(ConvectionExamAnswer answer) {
        if (answer == null) {
            return "未开始";
        }
        
        return answer.getProgressDescription();
    }
} 