package com.yf.exam.modules.convection.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.yf.exam.config.CustomJacksonTypeHandler;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 强对流考试答案实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@TableName("el_convection_exam_answer")
public class ConvectionExamAnswer extends Model<ConvectionExamAnswer> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 考试ID
     */
    @TableField("exam_id")
    private String examId;

    /**
     * 题目ID
     */
    @TableField("question_id")
    private String questionId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 第一部分：站点预报答案JSON
     * 格式：{"station_001": {"rainfall": "level2", "wind": "severe", "hail": null}, ...}
     */
    @TableField(value = "station_answer", typeHandler = CustomJacksonTypeHandler.class)
    private Map<String, Object> stationAnswer;

    /**
     * 站点预报进度百分比
     */
    @TableField("station_progress")
    private Integer stationProgress;

    /**
     * 第二部分：落区绘制答案JSON
     * 格式：{"heavy_rainfall": [GeoJSON], "thunderstorm_wind": [GeoJSON], ...}
     */
    @TableField(value = "area_answer", typeHandler = CustomJacksonTypeHandler.class)
    private Map<String, Object> areaAnswer;

    /**
     * 落区绘制进度百分比
     */
    @TableField("area_progress")
    private Integer areaProgress;

    /**
     * 预报依据阐述（考生输入）
     */
    @TableField("forecast_reasoning")
    private String forecastReasoning;

    /**
     * 预报依据字数
     */
    @TableField("reasoning_word_count")
    private Integer reasoningWordCount;

    /**
     * 整体进度百分比
     */
    @TableField("overall_progress")
    private Integer overallProgress;

    /**
     * 答题状态：0-答题中，1-已提交
     */
    @TableField("answer_status")
    private Integer answerStatus;

    /**
     * 考试提交时间
     */
    @TableField("submit_time")
    private Date submitTime;

    /**
     * 站点预报得分
     */
    @TableField("station_score")
    private BigDecimal stationScore;

    /**
     * 落区绘制得分
     */
    @TableField("area_score")
    private BigDecimal areaScore;

    /**
     * 总得分
     */
    @TableField("total_score")
    private BigDecimal totalScore;

    /**
     * 评分状态：0-未评分，1-自动评分完成，2-人工批卷完成
     */
    @TableField("scoring_status")
    private Integer scoringStatus;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    // ========== 业务方法 ==========

    /**
     * 判断是否已提交
     */
    public boolean isSubmitted() {
        return this.answerStatus != null && this.answerStatus == 1;
    }

    /**
     * 判断是否有站点预报答案
     */
    public boolean hasStationAnswer() {
        return this.stationAnswer != null && !this.stationAnswer.isEmpty();
    }

    /**
     * 判断是否有落区绘制答案
     */
    public boolean hasAreaAnswer() {
        return this.areaAnswer != null && !this.areaAnswer.isEmpty();
    }

    /**
     * 判断是否有预报依据阐述
     */
    public boolean hasForecastReasoning() {
        return this.forecastReasoning != null && this.forecastReasoning.trim().length() > 0;
    }

    /**
     * 计算预报依据字数并更新
     */
    public void updateReasoningWordCount() {
        if (this.forecastReasoning != null) {
            this.reasoningWordCount = this.forecastReasoning.length();
        } else {
            this.reasoningWordCount = 0;
        }
    }

    /**
     * 检查是否需要人工批卷
     */
    public boolean needsManualGrading() {
        return this.hasForecastReasoning() && this.scoringStatus != null && this.scoringStatus < 2;
    }

    /**
     * 获取完成度描述
     */
    public String getProgressDescription() {
        if (this.overallProgress == null) {
            return "未开始";
        }
        if (this.overallProgress == 0) {
            return "未开始";
        } else if (this.overallProgress < 30) {
            return "刚开始";
        } else if (this.overallProgress < 60) {
            return "进行中";
        } else if (this.overallProgress < 100) {
            return "接近完成";
        } else {
            return this.isSubmitted() ? "已提交" : "已完成";
        }
    }
} 