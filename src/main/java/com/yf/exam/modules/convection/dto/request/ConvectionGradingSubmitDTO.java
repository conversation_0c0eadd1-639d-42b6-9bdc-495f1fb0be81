package com.yf.exam.modules.convection.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 提交强对流人工批卷请求DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@ApiModel(value = "提交强对流人工批卷请求", description = "提交强对流人工批卷请求")
public class ConvectionGradingSubmitDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "答案ID", required = true)
    @NotBlank(message = "答案ID不能为空")
    private String answerId;

    @ApiModelProperty(value = "分级依据阐述得分(0-10分)", required = true)
    @NotNull(message = "分级依据阐述得分不能为空")
    @DecimalMin(value = "0.0", message = "分级依据阐述得分不能小于0分")
    @DecimalMax(value = "10.0", message = "分级依据阐述得分不能大于10分")
    private BigDecimal reasoningGradingBasisScore;

    @ApiModelProperty(value = "极端天气预报理由得分(0-10分)", required = true)
    @NotNull(message = "极端天气预报理由得分不能为空")
    @DecimalMin(value = "0.0", message = "极端天气预报理由得分不能小于0分")
    @DecimalMax(value = "10.0", message = "极端天气预报理由得分不能大于10分")
    private BigDecimal reasoningExtremeScore;

    @ApiModelProperty(value = "批卷评语")
    private String gradingComments;

    @ApiModelProperty(value = "改进建议")
    private String improvementSuggestions;

    @ApiModelProperty(value = "批卷开始时间戳（用于计算批卷耗时）")
    private Long gradingStartTime;

    @ApiModelProperty(value = "是否需要复核", notes = "true-需要复核，false-不需要复核")
    private Boolean needsReview = false;
} 