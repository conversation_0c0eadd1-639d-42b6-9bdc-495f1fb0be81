package com.yf.exam.modules.convection.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yf.exam.modules.convection.dto.ConvectionAnswerDTO;
import com.yf.exam.modules.convection.entity.ConvectionExamAnswer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 强对流考试答案Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
@Mapper
public interface ConvectionExamAnswerMapper extends BaseMapper<ConvectionExamAnswer> {

    /**
     * 根据考试ID和用户ID查询答案
     * 
     * @param examId 考试ID
     * @param userId 用户ID
     * @return 强对流考试答案
     */
    @Select("SELECT * FROM el_convection_exam_answer WHERE exam_id = #{examId} AND user_id = #{userId}")
    ConvectionExamAnswer selectByExamAndUser(@Param("examId") String examId, @Param("userId") String userId);

    /**
     * 分页查询待批卷答案列表
     * 
     * @param page 分页对象
     * @param examId 考试ID
     * @param answerStatus 答题状态
     * @param scoringStatus 评分状态
     * @return 待批卷答案分页列表
     */
    IPage<ConvectionAnswerDTO> selectPendingGradingAnswers(Page<ConvectionAnswerDTO> page,
                                                         @Param("examId") String examId,
                                                         @Param("answerStatus") Integer answerStatus,
                                                         @Param("scoringStatus") Integer scoringStatus);

    /**
     * 根据考试ID查询已提交答案列表
     * 
     * @param examId 考试ID
     * @return 已提交答案列表
     */
    List<ConvectionExamAnswer> selectSubmittedByExam(@Param("examId") String examId);

    /**
     * 更新答案进度
     * 
     * @param id 答案ID
     * @param stationProgress 站点预报进度
     * @param areaProgress 落区绘制进度
     * @param overallProgress 整体进度
     * @return 更新行数
     */
    @Update("UPDATE el_convection_exam_answer SET station_progress = #{stationProgress}, " +
            "area_progress = #{areaProgress}, overall_progress = #{overallProgress}, " +
            "update_time = NOW() WHERE id = #{id}")
    int updateProgress(@Param("id") String id,
                      @Param("stationProgress") Integer stationProgress,
                      @Param("areaProgress") Integer areaProgress,
                      @Param("overallProgress") Integer overallProgress);

    /**
     * 更新评分状态
     * 
     * @param id 答案ID
     * @param scoringStatus 评分状态
     * @return 更新行数
     */
    @Update("UPDATE el_convection_exam_answer SET scoring_status = #{scoringStatus}, " +
            "update_time = NOW() WHERE id = #{id}")
    int updateScoringStatus(@Param("id") String id, @Param("scoringStatus") Integer scoringStatus);

    /**
     * 批量查询考试答案统计信息
     * 
     * @param examIds 考试ID列表
     * @return 统计信息列表
     */
    List<Map<String, Object>> selectAnswerStatistics(@Param("examIds") List<String> examIds);

    /**
     * 查询考试答案进度统计
     * 
     * @param examId 考试ID
     * @return 进度统计信息
     */
    Map<String, Object> selectProgressStatistics(@Param("examId") String examId);

    /**
     * 根据用户ID查询答案列表
     * 
     * @param userId 用户ID
     * @return 答案列表
     */
    List<ConvectionExamAnswer> selectByUser(@Param("userId") String userId);

    /**
     * 查询需要人工批卷的答案数量
     * 
     * @param examId 考试ID
     * @return 需要人工批卷的答案数量
     */
    @Select("SELECT COUNT(*) FROM el_convection_exam_answer " +
            "WHERE exam_id = #{examId} AND answer_status = 1 " +
            "AND forecast_reasoning IS NOT NULL AND TRIM(forecast_reasoning) != '' " +
            "AND scoring_status < 2")
    long countPendingManualGrading(@Param("examId") String examId);

    /**
     * 根据评分状态分页查询答案
     * 
     * @param page 分页对象
     * @param scoringStatus 评分状态
     * @return 答案分页列表
     */
    IPage<ConvectionExamAnswer> selectByScoringStatus(Page<ConvectionExamAnswer> page,
                                                    @Param("scoringStatus") Integer scoringStatus);

    /**
     * 查询答案详情（包含关联信息）
     * 
     * @param answerId 答案ID
     * @return 答案详情
     */
    ConvectionAnswerDTO selectAnswerDetail(@Param("answerId") String answerId);

    /**
     * 根据题目ID查询答案列表
     * 
     * @param questionId 题目ID
     * @return 答案列表
     */
    List<ConvectionExamAnswer> selectByQuestion(@Param("questionId") String questionId);
} 