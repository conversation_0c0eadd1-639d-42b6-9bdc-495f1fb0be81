package com.yf.exam.modules.convection.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 强对流人工批卷数据传输类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@ApiModel(value = "强对流人工批卷", description = "强对流人工批卷")
public class ConvectionGradingDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "批卷记录ID")
    private String id;

    @ApiModelProperty(value = "关联的答案ID", required = true)
    private String answerId;

    @ApiModelProperty(value = "考试ID", required = true)
    private String examId;

    @ApiModelProperty(value = "学生用户ID", required = true)
    private String studentUserId;

    @ApiModelProperty(value = "批卷教师用户ID", required = true)
    private String graderUserId;

    @ApiModelProperty(value = "分级依据阐述得分(10分)")
    private BigDecimal reasoningGradingBasisScore;

    @ApiModelProperty(value = "极端天气预报理由得分(10分)")
    private BigDecimal reasoningExtremeScore;

    @ApiModelProperty(value = "预报依据总得分(20分)")
    private BigDecimal reasoningTotalScore;

    @ApiModelProperty(value = "批卷评语")
    private String gradingComments;

    @ApiModelProperty(value = "改进建议")
    private String improvementSuggestions;

    @ApiModelProperty(value = "批卷状态：0-未批卷，1-已批卷")
    private Integer gradingStatus;

    @ApiModelProperty(value = "批卷完成时间")
    private Date gradingTime;

    @ApiModelProperty(value = "批卷耗时(秒)")
    private Integer gradingDuration;

    @ApiModelProperty(value = "复核教师用户ID")
    private String reviewerUserId;

    @ApiModelProperty(value = "复核状态：0-未复核，1-已复核")
    private Integer reviewStatus;

    @ApiModelProperty(value = "复核时间")
    private Date reviewTime;

    @ApiModelProperty(value = "复核意见")
    private String reviewComments;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    // ========== 扩展字段 ==========

    @ApiModelProperty(value = "学生姓名")
    private String studentName;

    @ApiModelProperty(value = "批卷教师姓名")
    private String graderName;

    @ApiModelProperty(value = "复核教师姓名")
    private String reviewerName;

    @ApiModelProperty(value = "考试标题")
    private String examTitle;

    @ApiModelProperty(value = "学生预报依据")
    private String studentForecastReasoning;

    @ApiModelProperty(value = "标准预报依据")
    private String standardForecastReasoning;

    @ApiModelProperty(value = "答案提交时间")
    private Date answerSubmitTime;

    @ApiModelProperty(value = "总体进度")
    private Integer overallProgress;
} 