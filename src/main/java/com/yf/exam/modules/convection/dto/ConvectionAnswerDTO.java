package com.yf.exam.modules.convection.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 强对流考试答案数据传输类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@ApiModel(value = "强对流考试答案", description = "强对流考试答案")
public class ConvectionAnswerDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "答案ID")
    private String id;

    @ApiModelProperty(value = "考试ID", required = true)
    private String examId;

    @ApiModelProperty(value = "题目ID", required = true)
    private String questionId;

    @ApiModelProperty(value = "用户ID", required = true)
    private String userId;

    @ApiModelProperty(value = "站点预报答案JSON")
    private Map<String, Object> stationAnswer;

    @ApiModelProperty(value = "站点预报进度百分比")
    private Integer stationProgress;

    @ApiModelProperty(value = "落区绘制答案JSON")
    private Map<String, Object> areaAnswer;

    @ApiModelProperty(value = "落区绘制进度百分比")
    private Integer areaProgress;

    @ApiModelProperty(value = "预报依据阐述")
    private String forecastReasoning;

    @ApiModelProperty(value = "预报依据字数")
    private Integer reasoningWordCount;

    @ApiModelProperty(value = "整体进度百分比")
    private Integer overallProgress;

    @ApiModelProperty(value = "答题状态：0-答题中，1-已提交")
    private Integer answerStatus;

    @ApiModelProperty(value = "考试提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "站点预报得分")
    private BigDecimal stationScore;

    @ApiModelProperty(value = "落区绘制得分")
    private BigDecimal areaScore;

    @ApiModelProperty(value = "总得分")
    private BigDecimal totalScore;

    @ApiModelProperty(value = "评分状态：0-未评分，1-自动评分完成，2-人工批卷完成")
    private Integer scoringStatus;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
} 