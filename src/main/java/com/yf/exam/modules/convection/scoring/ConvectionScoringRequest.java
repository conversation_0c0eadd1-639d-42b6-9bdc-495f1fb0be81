package com.yf.exam.modules.convection.scoring;

import com.yf.exam.modules.convection.entity.ConvectionScoringConfig;
import lombok.Data;

import java.util.Map;

/**
 * <p>
 * 强对流评分请求类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
public class ConvectionScoringRequest {

    /**
     * 答案ID
     */
    private String answerId;

    /**
     * 学生站点预报答案
     */
    private Map<String, Object> studentStationAnswer;

    /**
     * 标准站点预报答案
     */
    private Map<String, Object> standardStationAnswer;

    /**
     * 学生落区绘制答案
     */
    private Map<String, Object> studentAreaAnswer;

    /**
     * 标准落区绘制答案
     */
    private Map<String, Object> standardAreaAnswer;

    /**
     * 评分配置
     */
    private ConvectionScoringConfig config;

    /**
     * 题目ID
     */
    private String questionId;

    /**
     * 考试ID
     */
    private String examId;

    /**
     * 用户ID
     */
    private String userId;
} 