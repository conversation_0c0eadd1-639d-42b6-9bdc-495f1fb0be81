package com.yf.exam.modules.convection.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yf.exam.modules.convection.entity.ConvectionScoringConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>
 * 强对流评分配置Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
@Mapper
public interface ConvectionScoringConfigMapper extends BaseMapper<ConvectionScoringConfig> {

    /**
     * 根据配置名称查询配置
     * 
     * @param configName 配置名称
     * @return 评分配置
     */
    @Select("SELECT * FROM el_convection_scoring_config WHERE config_name = #{configName}")
    ConvectionScoringConfig selectByConfigName(@Param("configName") String configName);

    /**
     * 查询所有启用的配置
     * 
     * @return 启用的配置列表
     */
    @Select("SELECT * FROM el_convection_scoring_config WHERE is_active = 1 ORDER BY create_time DESC")
    List<ConvectionScoringConfig> selectActiveConfigs();

    /**
     * 查询默认配置
     * 
     * @return 默认配置
     */
    @Select("SELECT * FROM el_convection_scoring_config WHERE id = 'default_convection_scoring' " +
            "OR config_name LIKE '%默认%' LIMIT 1")
    ConvectionScoringConfig selectDefaultConfig();

    /**
     * 根据版本查询配置
     * 
     * @param configVersion 配置版本
     * @return 配置列表
     */
    @Select("SELECT * FROM el_convection_scoring_config WHERE config_version = #{configVersion} " +
            "ORDER BY create_time DESC")
    List<ConvectionScoringConfig> selectByVersion(@Param("configVersion") String configVersion);

    /**
     * 查询最新版本的配置
     * 
     * @return 最新版本配置
     */
    @Select("SELECT * FROM el_convection_scoring_config WHERE is_active = 1 " +
            "ORDER BY config_version DESC, create_time DESC LIMIT 1")
    ConvectionScoringConfig selectLatestConfig();

    /**
     * 批量更新配置状态
     * 
     * @param ids 配置ID列表
     * @param isActive 是否启用
     * @param updateBy 更新人
     * @return 更新数量
     */
    @Update("UPDATE el_convection_scoring_config SET is_active = #{isActive}, " +
            "update_by = #{updateBy}, update_time = NOW() " +
            "WHERE id IN (${ids})")
    int updateStatusBatch(@Param("ids") String ids, 
                         @Param("isActive") Boolean isActive, 
                         @Param("updateBy") String updateBy);

    /**
     * 查询有效的配置数量
     * 
     * @return 有效配置数量
     */
    @Select("SELECT COUNT(*) FROM el_convection_scoring_config WHERE is_active = 1")
    long countActiveConfigs();

    /**
     * 根据创建人查询配置列表
     * 
     * @param createBy 创建人
     * @return 配置列表
     */
    @Select("SELECT * FROM el_convection_scoring_config WHERE create_by = #{createBy} " +
            "ORDER BY create_time DESC")
    List<ConvectionScoringConfig> selectByCreator(@Param("createBy") String createBy);
} 