package com.yf.exam.modules.convection.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 强对流人工批卷记录实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@TableName("el_convection_grading_record")
public class ConvectionGradingRecord extends Model<ConvectionGradingRecord> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 关联的答案ID
     */
    @TableField("answer_id")
    private String answerId;

    /**
     * 考试ID
     */
    @TableField("exam_id")
    private String examId;

    /**
     * 学生用户ID
     */
    @TableField("student_user_id")
    private String studentUserId;

    /**
     * 批卷教师用户ID
     */
    @TableField("grader_user_id")
    private String graderUserId;

    /**
     * 分级依据阐述得分(10分)
     */
    @TableField("reasoning_grading_basis_score")
    private BigDecimal reasoningGradingBasisScore;

    /**
     * 极端天气预报理由得分(10分)
     */
    @TableField("reasoning_extreme_score")
    private BigDecimal reasoningExtremeScore;

    /**
     * 预报依据总得分(20分)
     */
    @TableField("reasoning_total_score")
    private BigDecimal reasoningTotalScore;

    /**
     * 批卷评语
     */
    @TableField("grading_comments")
    private String gradingComments;

    /**
     * 改进建议
     */
    @TableField("improvement_suggestions")
    private String improvementSuggestions;

    /**
     * 批卷状态：0-未批卷，1-已批卷
     */
    @TableField("grading_status")
    private Integer gradingStatus;

    /**
     * 批卷完成时间
     */
    @TableField("grading_time")
    private Date gradingTime;

    /**
     * 批卷耗时(秒)
     */
    @TableField("grading_duration")
    private Integer gradingDuration;

    /**
     * 复核教师用户ID
     */
    @TableField("reviewer_user_id")
    private String reviewerUserId;

    /**
     * 复核状态：0-未复核，1-已复核
     */
    @TableField("review_status")
    private Integer reviewStatus;

    /**
     * 复核时间
     */
    @TableField("review_time")
    private Date reviewTime;

    /**
     * 复核意见
     */
    @TableField("review_comments")
    private String reviewComments;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    // ========== 业务方法 ==========

    /**
     * 判断是否已完成批卷
     */
    public boolean isGradingCompleted() {
        return this.gradingStatus != null && this.gradingStatus == 1;
    }

    /**
     * 判断是否已复核
     */
    public boolean isReviewed() {
        return this.reviewStatus != null && this.reviewStatus == 1;
    }

    /**
     * 计算预报依据总分
     */
    public void calculateReasoningTotalScore() {
        BigDecimal basisScore = this.reasoningGradingBasisScore != null ? 
            this.reasoningGradingBasisScore : BigDecimal.ZERO;
        BigDecimal extremeScore = this.reasoningExtremeScore != null ? 
            this.reasoningExtremeScore : BigDecimal.ZERO;
        
        this.reasoningTotalScore = basisScore.add(extremeScore);
    }

    /**
     * 验证评分是否合理
     */
    public boolean isValidScore() {
        if (this.reasoningGradingBasisScore != null) {
            if (this.reasoningGradingBasisScore.compareTo(BigDecimal.ZERO) < 0 ||
                this.reasoningGradingBasisScore.compareTo(new BigDecimal("10")) > 0) {
                return false;
            }
        }
        
        if (this.reasoningExtremeScore != null) {
            if (this.reasoningExtremeScore.compareTo(BigDecimal.ZERO) < 0 ||
                this.reasoningExtremeScore.compareTo(new BigDecimal("10")) > 0) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 获取批卷状态描述
     */
    public String getGradingStatusDescription() {
        if (this.gradingStatus == null || this.gradingStatus == 0) {
            return "待批卷";
        } else if (this.gradingStatus == 1) {
            if (this.reviewStatus != null && this.reviewStatus == 1) {
                return "已复核";
            } else {
                return "已批卷";
            }
        }
        return "未知状态";
    }

    /**
     * 判断评分是否完整
     */
    public boolean isScoreComplete() {
        return this.reasoningGradingBasisScore != null && this.reasoningExtremeScore != null;
    }

    /**
     * 获取批卷效率描述
     */
    public String getGradingEfficiencyDescription() {
        if (this.gradingDuration == null || this.gradingDuration <= 0) {
            return "未记录";
        }
        
        int minutes = this.gradingDuration / 60;
        if (minutes < 5) {
            return "很快 (" + minutes + "分钟)";
        } else if (minutes < 15) {
            return "正常 (" + minutes + "分钟)";
        } else if (minutes < 30) {
            return "较慢 (" + minutes + "分钟)";
        } else {
            return "很慢 (" + minutes + "分钟)";
        }
    }
} 