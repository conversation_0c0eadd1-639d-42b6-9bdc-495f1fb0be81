package com.yf.exam.modules.convection.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.modules.convection.dto.ConvectionAnswerDTO;
import com.yf.exam.modules.convection.dto.ConvectionStationAnswerDTO;
import com.yf.exam.modules.convection.dto.request.ConvectionAnswerSaveDTO;
import com.yf.exam.modules.convection.entity.ConvectionExamAnswer;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 强对流考试答案业务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
public interface ConvectionAnswerService extends IService<ConvectionExamAnswer> {

    /**
     * 保存强对流考试答案
     * 
     * @param saveDTO 保存请求数据
     * @return 答案ID
     */
    String saveAnswer(ConvectionAnswerSaveDTO saveDTO);

    /**
     * 获取考生答案详情
     * 
     * @param examId 考试ID
     * @param userId 用户ID
     * @return 答案详情
     */
    ConvectionAnswerDTO getStudentAnswer(String examId, String userId);

    /**
     * 提交强对流考试
     * 
     * @param examId 考试ID
     * @param userId 用户ID
     * @return 提交结果
     */
    Map<String, Object> submitExam(String examId, String userId);

    /**
     * 计算答题进度
     * 
     * @param answer 答案实体
     * @return 进度详情
     */
    Map<String, Object> calculateProgress(ConvectionExamAnswer answer);

    /**
     * 验证答案数据完整性
     * 
     * @param saveDTO 答案数据
     * @return 验证结果
     */
    Map<String, Object> validateAnswerData(ConvectionAnswerSaveDTO saveDTO);

    /**
     * 获取考试答案统计
     * 
     * @param examId 考试ID
     * @return 统计信息
     */
    Map<String, Object> getAnswerStatistics(String examId);

    /**
     * 分页查询待批卷答案列表
     * 
     * @param reqDTO 分页请求参数
     * @return 待批卷答案分页列表
     */
    IPage<ConvectionAnswerDTO> pendingGradingPaging(PagingReqDTO<ConvectionAnswerDTO> reqDTO);

    /**
     * 根据答案ID获取详细信息（含学生信息、考试信息）
     * 
     * @param answerId 答案ID
     * @return 答案详细信息
     */
    ConvectionAnswerDTO getAnswerDetail(String answerId);

    /**
     * 更新答案进度
     * 
     * @param answerId 答案ID
     * @param stationProgress 站点预报进度
     * @param areaProgress 落区绘制进度
     * @param overallProgress 整体进度
     */
    void updateProgress(String answerId, Integer stationProgress, Integer areaProgress, Integer overallProgress);

    /**
     * 更新评分状态
     * 
     * @param answerId 答案ID
     * @param scoringStatus 评分状态
     */
    void updateScoringStatus(String answerId, Integer scoringStatus);

    /**
     * 获取考试进度统计
     * 
     * @param examId 考试ID
     * @return 进度统计信息
     */
    Map<String, Object> getProgressStatistics(String examId);

    /**
     * 查询需要人工批卷的答案数量
     * 
     * @param examId 考试ID
     * @return 需要人工批卷的答案数量
     */
    long countPendingManualGrading(String examId);

    /**
     * 根据用户ID查询答案列表
     * 
     * @param userId 用户ID
     * @return 答案列表
     */
    List<ConvectionExamAnswer> listByUser(String userId);

    /**
     * 计算站点预报进度
     * 
     * @param stationAnswerData 站点预报答案数据
     * @param forecastReasoning 预报依据
     * @return 进度百分比
     */
    Integer calculateStationProgress(Map<String, Object> stationAnswerData, String forecastReasoning);

    /**
     * 计算落区绘制进度
     * 
     * @param areaAnswerData 落区绘制答案数据
     * @return 进度百分比
     */
    Integer calculateAreaProgress(Map<String, Object> areaAnswerData);

    /**
     * 计算整体进度
     * 
     * @param stationProgress 站点预报进度
     * @param areaProgress 落区绘制进度
     * @return 整体进度百分比
     */
    Integer calculateOverallProgress(Integer stationProgress, Integer areaProgress);

    /**
     * 检查答案是否可以提交
     * 
     * @param answer 答案实体
     * @return 是否可以提交
     */
    boolean canSubmit(ConvectionExamAnswer answer);

    /**
     * 获取答案完成度描述
     * 
     * @param answer 答案实体
     * @return 完成度描述
     */
    String getProgressDescription(ConvectionExamAnswer answer);
} 