package com.yf.exam.modules.convection.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.core.api.dto.BaseIdReqDTO;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.modules.qu.dto.QuDTO;
import com.yf.exam.modules.qu.dto.QuDetailDTO;
import com.yf.exam.modules.qu.dto.QuQueryReqDTO;
import com.yf.exam.modules.qu.service.QuService;
import com.yf.exam.modules.user.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 强对流试题管理控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
@Api(tags = {"强对流试题管理"})
@RestController
@RequestMapping("/exam/api/convection/question")
@Slf4j
public class ConvectionQuestionController extends BaseController {

    @Autowired
    private QuService quService;

    /**
     * 获取强对流试题列表
     */
    @ApiOperation(value = "获取强对流试题列表")
    @GetMapping("/list")
    public ApiRest<Map<String, Object>> getQuestionList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String difficulty) {
        
        try {
            // 构建分页请求
            PagingReqDTO<QuQueryReqDTO> reqDTO = new PagingReqDTO<>();
            reqDTO.setCurrent(page);
            reqDTO.setSize(size);
            
            QuQueryReqDTO params = new QuQueryReqDTO();
            params.setQuType(7); // 强对流试题类型
            params.setContent(title); // 标题搜索
            reqDTO.setParams(params);
            
            IPage<QuDTO> pageResult = quService.paging(reqDTO);
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("records", pageResult.getRecords());
            result.put("list", pageResult.getRecords()); // 兼容前端
            result.put("total", pageResult.getTotal());
            result.put("current", pageResult.getCurrent());
            result.put("size", pageResult.getSize());
            result.put("pages", pageResult.getPages());
            
            return super.success(result);
            
        } catch (Exception e) {
            log.error("获取强对流试题列表失败", e);
            return super.fail("获取试题列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取强对流试题详情
     */
    @ApiOperation(value = "获取强对流试题详情")
    @GetMapping("/{questionId}")
    public ApiRest<QuDetailDTO> getQuestionDetail(@PathVariable String questionId) {
        try {
            QuDetailDTO detail = quService.detail(questionId);
            if (detail == null) {
                return super.fail("试题不存在");
            }
            
            // 验证是否为强对流试题
            if (detail.getQuType() == null || !detail.getQuType().equals(7)) {
                return super.fail("该试题不是强对流试题");
            }
            
            return super.success(detail);
            
        } catch (Exception e) {
            log.error("获取强对流试题详情失败，试题ID：{}", questionId, e);
            return super.fail("获取试题详情失败：" + e.getMessage());
        }
    }

    /**
     * 创建强对流试题
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "创建强对流试题")
    @PostMapping
    public ApiRest<String> createQuestion(@Valid @RequestBody QuDetailDTO reqDTO) {
        try {
            // 强制设置为强对流试题类型
            reqDTO.setQuType(7);
            
            quService.save(reqDTO);
            
            log.info("用户 {} 创建了强对流试题，试题ID：{}", 
                UserUtils.getUser().getUserName(), reqDTO.getId());
            
            return super.success(reqDTO.getId());
            
        } catch (Exception e) {
            log.error("创建强对流试题失败", e);
            return super.fail("创建试题失败：" + e.getMessage());
        }
    }

    /**
     * 更新强对流试题
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "更新强对流试题")
    @PutMapping("/{questionId}")
    public ApiRest<String> updateQuestion(
            @PathVariable String questionId,
            @Valid @RequestBody QuDetailDTO reqDTO) {
        
        try {
            // 验证试题是否存在
            QuDetailDTO existing = quService.detail(questionId);
            if (existing == null) {
                return super.fail("试题不存在");
            }
            
            // 验证是否为强对流试题
            if (existing.getQuType() == null || !existing.getQuType().equals(7)) {
                return super.fail("该试题不是强对流试题");
            }
            
            // 设置ID和类型
            reqDTO.setId(questionId);
            reqDTO.setQuType(7);
            
            quService.save(reqDTO);
            
            log.info("用户 {} 更新了强对流试题，试题ID：{}", 
                UserUtils.getUser().getUserName(), questionId);
            
            return super.success(questionId);
            
        } catch (Exception e) {
            log.error("更新强对流试题失败，试题ID：{}", questionId, e);
            return super.fail("更新试题失败：" + e.getMessage());
        }
    }

    /**
     * 删除强对流试题
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "删除强对流试题")
    @DeleteMapping("/{questionId}")
    public ApiRest<String> deleteQuestion(@PathVariable String questionId) {
        try {
            // 验证试题是否存在
            QuDetailDTO existing = quService.detail(questionId);
            if (existing == null) {
                return super.fail("试题不存在");
            }
            
            // 验证是否为强对流试题
            if (existing.getQuType() == null || !existing.getQuType().equals(7)) {
                return super.fail("该试题不是强对流试题");
            }
            
            quService.delete(List.of(questionId));
            
            log.info("用户 {} 删除了强对流试题，试题ID：{}", 
                UserUtils.getUser().getUserName(), questionId);
            
            return super.success("删除成功");
            
        } catch (Exception e) {
            log.error("删除强对流试题失败，试题ID：{}", questionId, e);
            return super.fail("删除试题失败：" + e.getMessage());
        }
    }

    /**
     * 上传MICAPS文件
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "上传MICAPS文件")
    @PostMapping("/upload/micaps")
    public ApiRest<Map<String, Object>> uploadMicapsFile(@RequestParam("file") MultipartFile file) {
        try {
            // TODO: 实现MICAPS文件上传逻辑
            // 这里需要根据实际需求实现文件上传和处理逻辑
            
            Map<String, Object> result = new HashMap<>();
            result.put("id", System.currentTimeMillis());
            result.put("name", file.getOriginalFilename());
            result.put("size", file.getSize());
            result.put("url", "/uploads/micaps/" + file.getOriginalFilename());
            
            log.info("用户 {} 上传了MICAPS文件：{}", 
                UserUtils.getUser().getUserName(), file.getOriginalFilename());
            
            return super.success(result);
            
        } catch (Exception e) {
            log.error("上传MICAPS文件失败", e);
            return super.fail("文件上传失败：" + e.getMessage());
        }
    }

    /**
     * 上传强对流落区文件
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "上传强对流落区文件")
    @PostMapping("/upload/area")
    public ApiRest<Map<String, Object>> uploadConvectionAreaFile(@RequestParam("file") MultipartFile file) {
        try {
            // TODO: 实现强对流落区文件上传逻辑
            // 这里需要根据实际需求实现文件上传和处理逻辑
            
            Map<String, Object> result = new HashMap<>();
            result.put("id", System.currentTimeMillis());
            result.put("name", file.getOriginalFilename());
            result.put("size", file.getSize());
            result.put("url", "/uploads/area/" + file.getOriginalFilename());
            
            log.info("用户 {} 上传了强对流落区文件：{}", 
                UserUtils.getUser().getUserName(), file.getOriginalFilename());
            
            return super.success(result);
            
        } catch (Exception e) {
            log.error("上传强对流落区文件失败", e);
            return super.fail("文件上传失败：" + e.getMessage());
        }
    }

    /**
     * 获取标准答案
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "获取标准答案")
    @GetMapping("/{questionId}/standard-answer")
    public ApiRest<Map<String, Object>> getStandardAnswer(@PathVariable String questionId) {
        try {
            QuDetailDTO detail = quService.detail(questionId);
            if (detail == null) {
                return super.fail("试题不存在");
            }
            
            // 验证是否为强对流试题
            if (detail.getQuType() == null || !detail.getQuType().equals(7)) {
                return super.fail("该试题不是强对流试题");
            }
            
            // TODO: 实现获取标准答案的逻辑
            Map<String, Object> standardAnswer = new HashMap<>();
            // 这里需要根据实际需求返回标准答案数据
            
            return super.success(standardAnswer);
            
        } catch (Exception e) {
            log.error("获取标准答案失败，试题ID：{}", questionId, e);
            return super.fail("获取标准答案失败：" + e.getMessage());
        }
    }

    /**
     * 设置标准答案
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "设置标准答案")
    @PostMapping("/{questionId}/standard-answer")
    public ApiRest<String> setStandardAnswer(
            @PathVariable String questionId,
            @RequestBody Map<String, Object> standardAnswer) {
        
        try {
            QuDetailDTO detail = quService.detail(questionId);
            if (detail == null) {
                return super.fail("试题不存在");
            }
            
            // 验证是否为强对流试题
            if (detail.getQuType() == null || !detail.getQuType().equals(7)) {
                return super.fail("该试题不是强对流试题");
            }
            
            // TODO: 实现设置标准答案的逻辑
            
            log.info("用户 {} 设置了强对流试题标准答案，试题ID：{}", 
                UserUtils.getUser().getUserName(), questionId);
            
            return super.success("设置成功");
            
        } catch (Exception e) {
            log.error("设置标准答案失败，试题ID：{}", questionId, e);
            return super.fail("设置标准答案失败：" + e.getMessage());
        }
    }
}
