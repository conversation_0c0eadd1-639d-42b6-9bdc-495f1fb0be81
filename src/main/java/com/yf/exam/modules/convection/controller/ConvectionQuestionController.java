package com.yf.exam.modules.convection.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.core.api.dto.BaseIdReqDTO;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.core.utils.BeanMapper;
import com.yf.exam.modules.qu.dto.QuAnswerDTO;
import com.yf.exam.modules.qu.dto.QuDTO;
import com.yf.exam.modules.qu.dto.ext.QuDetailDTO;
import com.yf.exam.modules.qu.dto.request.QuQueryReqDTO;
import com.yf.exam.modules.qu.service.QuService;
import com.yf.exam.modules.user.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.File;
import java.util.*;
import java.util.Date;

/**
 * <p>
 * 强对流试题管理控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
@Api(tags = {"强对流试题管理"})
@RestController
@RequestMapping("/exam/api/convection/question")
@Slf4j
public class ConvectionQuestionController extends BaseController {

    @Autowired
    private QuService quService;

    /**
     * 强对流试题数据传输对象
     */
    @Data
    public static class ConvectionQuestionDTO {
        private String id;
        private String title;
        private String difficulty;
        private List<Map<String, Object>> micapsFiles;
        private List<Map<String, Object>> areaFiles;
        private List<StationDTO> stations;
        private ForecastRegionDTO forecastRegion;
        private String standardReasoning;
    }

    @Data
    public static class StationDTO {
        private String id;
        private String name;
        private String rainfallLevel;
        private String windLevel;
        private String hailLevel;
    }

    @Data
    public static class ForecastRegionDTO {
        private Double centerLon;
        private Double centerLat;
        private Integer zoom;
    }

    /**
     * 获取强对流试题列表
     */
    @ApiOperation(value = "获取强对流试题列表")
    @GetMapping("/list")
    public ApiRest<Map<String, Object>> getQuestionList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String difficulty) {
        
        try {
            // 构建分页请求
            PagingReqDTO<QuQueryReqDTO> reqDTO = new PagingReqDTO<>();
            reqDTO.setCurrent(page);
            reqDTO.setSize(size);
            
            QuQueryReqDTO params = new QuQueryReqDTO();
            params.setQuType(7); // 强对流试题类型
            params.setContent(title); // 标题搜索
            reqDTO.setParams(params);
            
            IPage<QuDTO> pageResult = quService.paging(reqDTO);
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("records", pageResult.getRecords());
            result.put("list", pageResult.getRecords()); // 兼容前端
            result.put("total", pageResult.getTotal());
            result.put("current", pageResult.getCurrent());
            result.put("size", pageResult.getSize());
            result.put("pages", pageResult.getPages());
            
            return super.success(result);
            
        } catch (Exception e) {
            log.error("获取强对流试题列表失败", e);
            return super.failure("获取试题列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取强对流试题详情
     */
    @ApiOperation(value = "获取强对流试题详情")
    @GetMapping("/{questionId}")
    public ApiRest<ConvectionQuestionDTO> getQuestionDetail(@PathVariable String questionId) {
        try {
            QuDetailDTO detail = quService.detail(questionId);
            if (detail == null) {
                return super.failure("试题不存在");
            }

            // 验证是否为强对流试题
            if (detail.getQuType() == null || !detail.getQuType().equals(7)) {
                return super.failure("该试题不是强对流试题");
            }

            // 转换为强对流试题格式
            ConvectionQuestionDTO convectionDTO = convertToConvectionDTO(detail);

            return super.success(convectionDTO);

        } catch (Exception e) {
            log.error("获取强对流试题详情失败，试题ID：{}", questionId, e);
            return super.failure("获取试题详情失败：" + e.getMessage());
        }
    }

    /**
     * 将标准的QuDetailDTO转换为强对流试题DTO
     */
    private ConvectionQuestionDTO convertToConvectionDTO(QuDetailDTO quDetailDTO) {
        ConvectionQuestionDTO convectionDTO = new ConvectionQuestionDTO();

        // 基本信息
        convectionDTO.setId(quDetailDTO.getId());
        convectionDTO.setTitle(quDetailDTO.getTitle());

        // 设置难度
        Integer level = quDetailDTO.getLevel();
        if (level != null) {
            switch (level) {
                case 1:
                    convectionDTO.setDifficulty("easy");
                    break;
                case 2:
                    convectionDTO.setDifficulty("medium");
                    break;
                case 3:
                    convectionDTO.setDifficulty("hard");
                    break;
                default:
                    convectionDTO.setDifficulty("medium");
            }
        } else {
            convectionDTO.setDifficulty("medium");
        }

        // 从scenarioData字段中解析强对流特有数据（参考历史个例的存储方式）
        String scenarioData = quDetailDTO.getScenarioData();
        if (scenarioData != null && !scenarioData.isEmpty()) {
            try {
                Map<String, Object> convectionData = JSON.parseObject(scenarioData, Map.class);

                // 从answers中重建stations列表
                Object answersObj = convectionData.get("answers");
                if (answersObj != null) {
                    Map<String, Object> answersMap = (Map<String, Object>) answersObj;
                    List<StationDTO> stations = new ArrayList<>();

                    for (Map.Entry<String, Object> entry : answersMap.entrySet()) {
                        String stationName = entry.getKey();
                        Map<String, Object> stationAnswer = (Map<String, Object>) entry.getValue();

                        StationDTO station = new StationDTO();
                        station.setName(stationName);
                        station.setRainfallLevel((String) stationAnswer.get("rainfallLevel"));
                        station.setWindLevel((String) stationAnswer.get("windLevel"));
                        station.setHailLevel((String) stationAnswer.get("hailLevel"));

                        stations.add(station);
                    }

                    convectionDTO.setStations(stations);
                }

                // 解析forecastRegion
                Object forecastRegionObj = convectionData.get("forecastRegion");
                if (forecastRegionObj != null) {
                    ForecastRegionDTO forecastRegion = JSON.parseObject(JSON.toJSONString(forecastRegionObj), ForecastRegionDTO.class);
                    convectionDTO.setForecastRegion(forecastRegion);
                }

                // 解析standardReasoning
                Object standardReasoningObj = convectionData.get("standardReasoning");
                if (standardReasoningObj != null) {
                    convectionDTO.setStandardReasoning(standardReasoningObj.toString());
                }

                // 解析文件列表
                Object micapsFilesObj = convectionData.get("micapsFiles");
                if (micapsFilesObj != null) {
                    List<Map<String, Object>> micapsFiles = (List<Map<String, Object>>) micapsFilesObj;
                    convectionDTO.setMicapsFiles(micapsFiles);
                } else {
                    convectionDTO.setMicapsFiles(new ArrayList<>());
                }

                Object areaFilesObj = convectionData.get("areaFiles");
                if (areaFilesObj != null) {
                    List<Map<String, Object>> areaFiles = (List<Map<String, Object>>) areaFilesObj;
                    convectionDTO.setAreaFiles(areaFiles);
                } else {
                    convectionDTO.setAreaFiles(new ArrayList<>());
                }

            } catch (Exception e) {
                log.warn("解析强对流试题数据失败，试题ID：{}", quDetailDTO.getId(), e);
                // 设置默认值
                convectionDTO.setStations(new ArrayList<>());
                convectionDTO.setMicapsFiles(new ArrayList<>());
                convectionDTO.setAreaFiles(new ArrayList<>());
            }
        } else {
            // 设置默认值
            convectionDTO.setStations(new ArrayList<>());
            convectionDTO.setMicapsFiles(new ArrayList<>());
            convectionDTO.setAreaFiles(new ArrayList<>());
        }

        return convectionDTO;
    }

    /**
     * 创建强对流试题
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "创建强对流试题")
    @PostMapping
    public ApiRest<String> createQuestion(@Valid @RequestBody ConvectionQuestionDTO convectionDTO) {
        try {
            // 转换为标准的QuDetailDTO
            QuDetailDTO reqDTO = convertToQuDetailDTO(convectionDTO);

            // 强制设置为强对流试题类型
            reqDTO.setQuType(7);

            quService.save(reqDTO);

            log.info("用户 {} 创建了强对流试题，试题ID：{}",
                UserUtils.getUser().getUserName(), reqDTO.getId());

            return super.success(reqDTO.getId());

        } catch (Exception e) {
            log.error("创建强对流试题失败", e);
            return super.failure("创建试题失败：" + e.getMessage());
        }
    }

    /**
     * 将强对流试题DTO转换为标准的QuDetailDTO
     */
    private QuDetailDTO convertToQuDetailDTO(ConvectionQuestionDTO convectionDTO) {
        QuDetailDTO quDetailDTO = new QuDetailDTO();

        // 基本信息
        quDetailDTO.setId(convectionDTO.getId());
        quDetailDTO.setTitle(convectionDTO.getTitle());
        quDetailDTO.setContent(""); // 强对流试题不使用content字段
        quDetailDTO.setQuType(7); // 强对流试题类型

        // 设置难度等级
        if ("easy".equals(convectionDTO.getDifficulty())) {
            quDetailDTO.setLevel(1);
        } else if ("medium".equals(convectionDTO.getDifficulty())) {
            quDetailDTO.setLevel(2);
        } else if ("hard".equals(convectionDTO.getDifficulty())) {
            quDetailDTO.setLevel(3);
        } else {
            quDetailDTO.setLevel(2); // 默认中等难度
        }

        // 将强对流特有数据存储到scenarioData字段中（参考历史个例的存储方式）
        Map<String, Object> scenarioData = new HashMap<>();

        // 预报区域信息
        scenarioData.put("forecastRegion", convectionDTO.getForecastRegion());

        // 标准答案推理过程
        scenarioData.put("standardReasoning", convectionDTO.getStandardReasoning());

        // 文件信息
        scenarioData.put("micapsFiles", convectionDTO.getMicapsFiles());
        scenarioData.put("areaFiles", convectionDTO.getAreaFiles());

        // 强对流试题的答案数据（站点预报答案）
        Map<String, Object> answers = new HashMap<>();
        if (convectionDTO.getStations() != null) {
            for (StationDTO station : convectionDTO.getStations()) {
                Map<String, Object> stationAnswer = new HashMap<>();
                stationAnswer.put("name", station.getName()); // 保存站点名称
                stationAnswer.put("rainfallLevel", station.getRainfallLevel());
                stationAnswer.put("windLevel", station.getWindLevel());
                stationAnswer.put("hailLevel", station.getHailLevel());
                answers.put(station.getName(), stationAnswer);
            }
        }
        scenarioData.put("answers", answers);

        // 将scenarioData序列化为JSON字符串存储
        quDetailDTO.setScenarioData(JSON.toJSONString(scenarioData));

        // 创建默认的答案选项（强对流试题不需要传统的选择题答案）
        List<QuAnswerDTO> answerList = new ArrayList<>();
        quDetailDTO.setAnswerList(answerList);

        // 设置默认题库（可以根据需要调整）
        List<String> repoIds = new ArrayList<>();
        // 可以添加强对流试题的默认题库ID，例如强对流专用题库
        quDetailDTO.setRepoIds(repoIds);

        return quDetailDTO;
    }

    /**
     * 更新强对流试题
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "更新强对流试题")
    @PutMapping("/{questionId}")
    public ApiRest<String> updateQuestion(
            @PathVariable String questionId,
            @Valid @RequestBody ConvectionQuestionDTO convectionDTO) {

        try {
            // 验证试题是否存在
            QuDetailDTO existing = quService.detail(questionId);
            if (existing == null) {
                return super.failure("试题不存在");
            }

            // 验证是否为强对流试题
            if (existing.getQuType() == null || !existing.getQuType().equals(7)) {
                return super.failure("该试题不是强对流试题");
            }

            // 转换为标准的QuDetailDTO
            QuDetailDTO reqDTO = convertToQuDetailDTO(convectionDTO);

            // 设置ID和类型
            reqDTO.setId(questionId);
            reqDTO.setQuType(7);

            quService.save(reqDTO);

            log.info("用户 {} 更新了强对流试题，试题ID：{}",
                UserUtils.getUser().getUserName(), questionId);

            return super.success(questionId);

        } catch (Exception e) {
            log.error("更新强对流试题失败，试题ID：{}", questionId, e);
            return super.failure("更新试题失败：" + e.getMessage());
        }
    }

    /**
     * 删除强对流试题
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "删除强对流试题")
    @DeleteMapping("/{questionId}")
    public ApiRest<String> deleteQuestion(@PathVariable String questionId) {
        try {
            // 验证试题是否存在
            QuDetailDTO existing = quService.detail(questionId);
            if (existing == null) {
                return super.failure("试题不存在");
            }

            // 验证是否为强对流试题
            if (existing.getQuType() == null || !existing.getQuType().equals(7)) {
                return super.failure("该试题不是强对流试题");
            }

            quService.delete(Arrays.asList(questionId));
            
            log.info("用户 {} 删除了强对流试题，试题ID：{}", 
                UserUtils.getUser().getUserName(), questionId);
            
            return super.success("删除成功");
            
        } catch (Exception e) {
            log.error("删除强对流试题失败，试题ID：{}", questionId, e);
            return super.failure("删除试题失败：" + e.getMessage());
        }
    }

    /**
     * 上传MICAPS文件
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "上传MICAPS文件")
    @PostMapping("/upload/micaps")
    public ApiRest<Map<String, Object>> uploadMicapsFile(@RequestParam("file") MultipartFile file) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                return super.failure("上传文件不能为空");
            }

            // 验证文件类型（MICAPS文件通常没有特定扩展名，这里允许所有类型）
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                return super.failure("文件名不能为空");
            }

            // 验证文件大小（限制为50MB）
            long maxSize = 50 * 1024 * 1024; // 50MB
            if (file.getSize() > maxSize) {
                return super.failure("文件大小不能超过50MB");
            }

            // 创建上传目录
            String uploadDir = System.getProperty("user.dir") + "/uploads/micaps/";
            File dir = new File(uploadDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 生成唯一文件名（保留原始扩展名）
            String fileExtension = "";
            int lastDotIndex = originalFilename.lastIndexOf(".");
            if (lastDotIndex > 0) {
                fileExtension = originalFilename.substring(lastDotIndex);
            }

            String uniqueFileName = System.currentTimeMillis() + "_" +
                originalFilename.replaceAll("[^a-zA-Z0-9._-]", "_");

            // 保存文件
            File targetFile = new File(uploadDir + uniqueFileName);
            file.transferTo(targetFile);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("id", System.currentTimeMillis());
            result.put("name", originalFilename);
            result.put("fileName", uniqueFileName);
            result.put("size", file.getSize());
            result.put("url", "/uploads/micaps/" + uniqueFileName);
            result.put("path", targetFile.getAbsolutePath());
            result.put("uploadTime", new Date());

            log.info("用户 {} 成功上传MICAPS文件：{} -> {}",
                UserUtils.getUser().getUserName(), originalFilename, uniqueFileName);

            return super.success(result);

        } catch (Exception e) {
            log.error("上传MICAPS文件失败", e);
            return super.failure("文件上传失败：" + e.getMessage());
        }
    }

    /**
     * 上传强对流落区文件
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "上传强对流落区文件")
    @PostMapping("/upload/area")
    public ApiRest<Map<String, Object>> uploadConvectionAreaFile(@RequestParam("file") MultipartFile file) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                return super.failure("上传文件不能为空");
            }

            // 验证文件类型（落区文件通常是图片或数据文件）
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                return super.failure("文件名不能为空");
            }

            // 验证文件大小（限制为100MB）
            long maxSize = 100 * 1024 * 1024; // 100MB
            if (file.getSize() > maxSize) {
                return super.failure("文件大小不能超过100MB");
            }

            // 创建上传目录
            String uploadDir = System.getProperty("user.dir") + "/uploads/area/";
            File dir = new File(uploadDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 生成唯一文件名（保留原始扩展名）
            String fileExtension = "";
            int lastDotIndex = originalFilename.lastIndexOf(".");
            if (lastDotIndex > 0) {
                fileExtension = originalFilename.substring(lastDotIndex);
            }

            String uniqueFileName = System.currentTimeMillis() + "_" +
                originalFilename.replaceAll("[^a-zA-Z0-9._-]", "_");

            // 保存文件
            File targetFile = new File(uploadDir + uniqueFileName);
            file.transferTo(targetFile);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("id", System.currentTimeMillis());
            result.put("name", originalFilename);
            result.put("fileName", uniqueFileName);
            result.put("size", file.getSize());
            result.put("url", "/uploads/area/" + uniqueFileName);
            result.put("path", targetFile.getAbsolutePath());
            result.put("uploadTime", new Date());

            log.info("用户 {} 成功上传强对流落区文件：{} -> {}",
                UserUtils.getUser().getUserName(), originalFilename, uniqueFileName);

            return super.success(result);

        } catch (Exception e) {
            log.error("上传强对流落区文件失败", e);
            return super.failure("文件上传失败：" + e.getMessage());
        }
    }

    /**
     * 获取标准答案
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "获取标准答案")
    @GetMapping("/{questionId}/standard-answer")
    public ApiRest<Map<String, Object>> getStandardAnswer(@PathVariable String questionId) {
        try {
            QuDetailDTO detail = quService.detail(questionId);
            if (detail == null) {
                return super.failure("试题不存在");
            }

            // 验证是否为强对流试题
            if (detail.getQuType() == null || !detail.getQuType().equals(7)) {
                return super.failure("该试题不是强对流试题");
            }
            
            // TODO: 实现获取标准答案的逻辑
            Map<String, Object> standardAnswer = new HashMap<>();
            // 这里需要根据实际需求返回标准答案数据
            
            return super.success(standardAnswer);
            
        } catch (Exception e) {
            log.error("获取标准答案失败，试题ID：{}", questionId, e);
            return super.failure("获取标准答案失败：" + e.getMessage());
        }
    }

    /**
     * 设置标准答案
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "设置标准答案")
    @PostMapping("/{questionId}/standard-answer")
    public ApiRest<String> setStandardAnswer(
            @PathVariable String questionId,
            @RequestBody Map<String, Object> standardAnswer) {
        
        try {
            QuDetailDTO detail = quService.detail(questionId);
            if (detail == null) {
                return super.failure("试题不存在");
            }

            // 验证是否为强对流试题
            if (detail.getQuType() == null || !detail.getQuType().equals(7)) {
                return super.failure("该试题不是强对流试题");
            }
            
            // TODO: 实现设置标准答案的逻辑
            
            log.info("用户 {} 设置了强对流试题标准答案，试题ID：{}", 
                UserUtils.getUser().getUserName(), questionId);
            
            return super.success("设置成功");
            
        } catch (Exception e) {
            log.error("设置标准答案失败，试题ID：{}", questionId, e);
            return super.failure("设置标准答案失败：" + e.getMessage());
        }
    }
}
