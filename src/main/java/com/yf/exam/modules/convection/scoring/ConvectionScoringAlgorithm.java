package com.yf.exam.modules.convection.scoring;

import com.yf.exam.modules.convection.dto.ConvectionStationAnswerDTO;
import com.yf.exam.modules.convection.entity.ConvectionScoringConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 强对流考试评分算法
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
@Slf4j
@Component
public class ConvectionScoringAlgorithm {

    /**
     * 计算强对流考试总分
     */
    public ConvectionScoringResult calculateTotalScore(ConvectionScoringRequest request) {
        ConvectionScoringResult result = new ConvectionScoringResult();
        result.setAnswerId(request.getAnswerId());
        
        try {
            // 1. 计算站点预报得分 (68分)
            BigDecimal stationScore = calculateStationScore(
                request.getStudentStationAnswer(), 
                request.getStandardStationAnswer(),
                request.getConfig()
            );
            result.setStationScore(stationScore);
            
            // 2. 计算落区绘制得分 (32分)  
            BigDecimal areaScore = calculateAreaScore(
                request.getStudentAreaAnswer(),
                request.getStandardAreaAnswer(),
                request.getConfig()
            );
            result.setAreaScore(areaScore);
            
            // 3. 计算总分
            BigDecimal totalScore = stationScore.add(areaScore);
            result.setTotalScore(totalScore);
            
            // 4. 生成详细得分说明
            result.setScoreDetails(generateScoreDetails(stationScore, areaScore, request));
            
            result.setSuccess(true);
            result.setMessage("评分完成");
            
            log.info("强对流考试评分完成，答案ID：{}，站点得分：{}，落区得分：{}，总分：{}", 
                request.getAnswerId(), stationScore, areaScore, totalScore);
                
        } catch (Exception e) {
            log.error("强对流考试评分异常，答案ID：{}", request.getAnswerId(), e);
            result.setSuccess(false);
            result.setMessage("评分异常：" + e.getMessage());
            result.setTotalScore(BigDecimal.ZERO);
        }
        
        return result;
    }

    /**
     * 计算站点预报得分 (68分)
     */
    private BigDecimal calculateStationScore(Map<String, Object> studentAnswer, 
                                           Map<String, Object> standardAnswer,
                                           ConvectionScoringConfig config) {
        if (CollectionUtils.isEmpty(studentAnswer) || CollectionUtils.isEmpty(standardAnswer)) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal totalScore = BigDecimal.ZERO;
        int totalStations = standardAnswer.size();
        
        if (totalStations == 0) {
            return BigDecimal.ZERO;
        }
        
        // 每个站点的基础分值 = 68 / 站点数量
        BigDecimal baseScorePerStation = new BigDecimal("68")
            .divide(new BigDecimal(totalStations), 2, RoundingMode.HALF_UP);
        
        for (String stationKey : standardAnswer.keySet()) {
            Object standardStationObj = standardAnswer.get(stationKey);
            Object studentStationObj = studentAnswer.get(stationKey);
            
            if (standardStationObj instanceof Map && studentStationObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> standardStation = (Map<String, Object>) standardStationObj;
                @SuppressWarnings("unchecked")
                Map<String, Object> studentStation = (Map<String, Object>) studentStationObj;
                
                BigDecimal stationScore = calculateSingleStationScore(
                    studentStation, standardStation, baseScorePerStation
                );
                totalScore = totalScore.add(stationScore);
            }
        }
        
        return totalScore.setScale(1, RoundingMode.HALF_UP);
    }

    /**
     * 计算单个站点得分
     */
    private BigDecimal calculateSingleStationScore(Map<String, Object> studentStation,
                                                 Map<String, Object> standardStation,
                                                 BigDecimal baseScore) {
        BigDecimal stationScore = BigDecimal.ZERO;
        
        // 短时强降水评分 (占站点总分的40%)
        BigDecimal rainfallScore = calculateWeatherPhenomenonScore(
            (String) studentStation.get("shortTimeRainfall"),
            (String) standardStation.get("shortTimeRainfall"),
            baseScore.multiply(new BigDecimal("0.4"))
        );
        
        // 雷暴大风评分 (占站点总分的35%) 
        BigDecimal windScore = calculateWeatherPhenomenonScore(
            (String) studentStation.get("thunderstormWind"),
            (String) standardStation.get("thunderstormWind"),
            baseScore.multiply(new BigDecimal("0.35"))
        );
        
        // 冰雹评分 (占站点总分的25%)
        BigDecimal hailScore = calculateWeatherPhenomenonScore(
            (String) studentStation.get("hail"),
            (String) standardStation.get("hail"),
            baseScore.multiply(new BigDecimal("0.25"))
        );
        
        stationScore = rainfallScore.add(windScore).add(hailScore);
        
        return stationScore.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 计算单个天气现象得分
     */
    private BigDecimal calculateWeatherPhenomenonScore(String studentAnswer, 
                                                     String standardAnswer, 
                                                     BigDecimal maxScore) {
        // 如果标准答案为空，学生答案也为空，给满分
        if ((standardAnswer == null || standardAnswer.trim().isEmpty()) && 
            (studentAnswer == null || studentAnswer.trim().isEmpty())) {
            return maxScore;
        }
        
        // 如果标准答案不为空，学生答案为空，得0分
        if (standardAnswer != null && !standardAnswer.trim().isEmpty() && 
            (studentAnswer == null || studentAnswer.trim().isEmpty())) {
            return BigDecimal.ZERO;
        }
        
        // 如果标准答案为空，学生答案不为空，得0分 (误报)
        if ((standardAnswer == null || standardAnswer.trim().isEmpty()) && 
            studentAnswer != null && !studentAnswer.trim().isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        // 完全匹配给满分
        if (standardAnswer != null && standardAnswer.equals(studentAnswer)) {
            return maxScore;
        }
        
        // 等级匹配评分
        return calculateLevelMatchScore(studentAnswer, standardAnswer, maxScore);
    }

    /**
     * 计算等级匹配得分
     */
    private BigDecimal calculateLevelMatchScore(String studentAnswer, 
                                              String standardAnswer, 
                                              BigDecimal maxScore) {
        if (studentAnswer == null || standardAnswer == null) {
            return BigDecimal.ZERO;
        }
        
        // 根据天气现象类型和等级差异给分
        if (isRainfallLevel(studentAnswer) && isRainfallLevel(standardAnswer)) {
            return calculateRainfallLevelScore(studentAnswer, standardAnswer, maxScore);
        }
        
        if (isWindLevel(studentAnswer) && isWindLevel(standardAnswer)) {
            return calculateWindLevelScore(studentAnswer, standardAnswer, maxScore);
        }
        
        return BigDecimal.ZERO;
    }

    /**
     * 判断是否为降水等级
     */
    private boolean isRainfallLevel(String level) {
        return level != null && (level.equals("level1") || level.equals("level2") || level.equals("level3"));
    }

    /**
     * 判断是否为大风等级
     */
    private boolean isWindLevel(String level) {
        return level != null && (level.equals("moderate") || level.equals("severe") || level.equals("extreme"));
    }

    /**
     * 计算降水等级得分
     */
    private BigDecimal calculateRainfallLevelScore(String studentLevel, String standardLevel, BigDecimal maxScore) {
        Map<String, Integer> levelValues = new HashMap<>();
        levelValues.put("level1", 1);
        levelValues.put("level2", 2);
        levelValues.put("level3", 3);
        
        int studentValue = levelValues.getOrDefault(studentLevel, 0);
        int standardValue = levelValues.getOrDefault(standardLevel, 0);
        
        int diff = Math.abs(studentValue - standardValue);
        
        if (diff == 0) return maxScore; // 完全正确
        if (diff == 1) return maxScore.multiply(new BigDecimal("0.7")); // 相差1级，70%分数
        if (diff == 2) return maxScore.multiply(new BigDecimal("0.3")); // 相差2级，30%分数
        
        return BigDecimal.ZERO;
    }

    /**
     * 计算大风等级得分
     */
    private BigDecimal calculateWindLevelScore(String studentLevel, String standardLevel, BigDecimal maxScore) {
        Map<String, Integer> levelValues = new HashMap<>();
        levelValues.put("moderate", 1);
        levelValues.put("severe", 2);
        levelValues.put("extreme", 3);
        
        int studentValue = levelValues.getOrDefault(studentLevel, 0);
        int standardValue = levelValues.getOrDefault(standardLevel, 0);
        
        int diff = Math.abs(studentValue - standardValue);
        
        if (diff == 0) return maxScore; // 完全正确
        if (diff == 1) return maxScore.multiply(new BigDecimal("0.7")); // 相差1级，70%分数
        if (diff == 2) return maxScore.multiply(new BigDecimal("0.3")); // 相差2级，30%分数
        
        return BigDecimal.ZERO;
    }

    /**
     * 计算落区绘制得分 (32分)
     */
    private BigDecimal calculateAreaScore(Map<String, Object> studentAnswer,
                                        Map<String, Object> standardAnswer,
                                        ConvectionScoringConfig config) {
        if (CollectionUtils.isEmpty(studentAnswer) || CollectionUtils.isEmpty(standardAnswer)) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal totalScore = BigDecimal.ZERO;
        String[] weatherTypes = {"heavy_rainfall", "thunderstorm_wind", "hail", "tornado"};
        
        // 每种天气现象8分
        BigDecimal scorePerType = new BigDecimal("8");
        
        for (String weatherType : weatherTypes) {
            Object studentAreas = studentAnswer.get(weatherType);
            Object standardAreas = standardAnswer.get(weatherType);
            
            BigDecimal typeScore = calculateAreaTypeScore(studentAreas, standardAreas, scorePerType);
            totalScore = totalScore.add(typeScore);
        }
        
        return totalScore.setScale(1, RoundingMode.HALF_UP);
    }

    /**
     * 计算单个天气类型的落区得分
     */
    private BigDecimal calculateAreaTypeScore(Object studentAreas, Object standardAreas, BigDecimal maxScore) {
        // 简化的落区评分算法
        // 实际项目中需要根据地理坐标计算重叠面积
        
        boolean hasStudentAreas = studentAreas != null && 
            studentAreas instanceof java.util.List && 
            !((java.util.List<?>) studentAreas).isEmpty();
            
        boolean hasStandardAreas = standardAreas != null && 
            standardAreas instanceof java.util.List && 
            !((java.util.List<?>) standardAreas).isEmpty();
        
        // 如果标准答案和学生答案都没有落区，给满分
        if (!hasStandardAreas && !hasStudentAreas) {
            return maxScore;
        }
        
        // 如果标准答案有落区，学生答案没有，得0分
        if (hasStandardAreas && !hasStudentAreas) {
            return BigDecimal.ZERO;
        }
        
        // 如果标准答案没有落区，学生答案有，得0分 (误报)
        if (!hasStandardAreas && hasStudentAreas) {
            return BigDecimal.ZERO;
        }
        
        // 如果都有落区，给部分分数 (实际需要计算重叠度)
        if (hasStandardAreas && hasStudentAreas) {
            return maxScore.multiply(new BigDecimal("0.8")); // 简化给80%分数
        }
        
        return BigDecimal.ZERO;
    }

    /**
     * 生成详细得分说明
     */
    private Map<String, Object> generateScoreDetails(BigDecimal stationScore, 
                                                   BigDecimal areaScore,
                                                   ConvectionScoringRequest request) {
        Map<String, Object> details = new HashMap<>();
        
        details.put("stationScore", stationScore);
        details.put("stationMaxScore", 68);
        details.put("stationPercentage", calculatePercentage(stationScore, new BigDecimal("68")));
        
        details.put("areaScore", areaScore);
        details.put("areaMaxScore", 32);
        details.put("areaPercentage", calculatePercentage(areaScore, new BigDecimal("32")));
        
        details.put("totalScore", stationScore.add(areaScore));
        details.put("totalMaxScore", 100);
        details.put("totalPercentage", calculatePercentage(stationScore.add(areaScore), new BigDecimal("100")));
        
        details.put("scoringTime", new java.util.Date());
        details.put("algorithmVersion", "1.0.0");
        
        return details;
    }

    /**
     * 计算百分比
     */
    private BigDecimal calculatePercentage(BigDecimal score, BigDecimal maxScore) {
        if (maxScore.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return score.divide(maxScore, 4, RoundingMode.HALF_UP)
                   .multiply(new BigDecimal("100"))
                   .setScale(1, RoundingMode.HALF_UP);
    }
} 