package com.yf.exam.modules.convection.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.core.api.dto.BaseIdReqDTO;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.modules.convection.dto.ConvectionAnswerDTO;
import com.yf.exam.modules.convection.dto.request.ConvectionAnswerSaveDTO;
import com.yf.exam.modules.convection.service.ConvectionAnswerService;
import com.yf.exam.modules.user.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
 * <p>
 * 强对流考试答案控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
@Api(tags = {"强对流考试答案"})
@RestController
@RequestMapping("/exam/api/convection/answer")
@Slf4j
public class ConvectionAnswerController extends BaseController {

    @Autowired
    private ConvectionAnswerService convectionAnswerService;

    /**
     * 保存强对流考试答案
     */
    @ApiOperation(value = "保存强对流考试答案")
    @PostMapping("/save")
    public ApiRest<String> saveAnswer(@Valid @RequestBody ConvectionAnswerSaveDTO saveDTO) {
        String answerId = convectionAnswerService.saveAnswer(saveDTO);
        log.info("用户 {} 保存了强对流考试答案，答案ID：{}", UserUtils.getUser().getUserName(), answerId);
        return super.success(answerId);
    }

    /**
     * 获取考生答案详情
     */
    @ApiOperation(value = "获取考生答案详情")
    @PostMapping("/detail")
    public ApiRest<ConvectionAnswerDTO> getStudentAnswer(@RequestBody ConvectionAnswerRequest request) {
        ConvectionAnswerDTO answer = convectionAnswerService.getStudentAnswer(
            request.getExamId(), 
            UserUtils.getUserId()
        );
        return super.success(answer);
    }

    /**
     * 提交强对流考试
     */
    @ApiOperation(value = "提交强对流考试")
    @PostMapping("/submit")
    public ApiRest<Map<String, Object>> submitExam(@RequestBody ConvectionAnswerRequest request) {
        Map<String, Object> result = convectionAnswerService.submitExam(
            request.getExamId(), 
            UserUtils.getUserId()
        );
        log.info("用户 {} 提交了强对流考试，考试ID：{}", UserUtils.getUser().getUserName(), request.getExamId());
        return super.success(result);
    }

    /**
     * 计算答题进度
     */
    @ApiOperation(value = "计算答题进度")
    @PostMapping("/progress")
    public ApiRest<Map<String, Object>> calculateProgress(@RequestBody BaseIdReqDTO reqDTO) {
        com.yf.exam.modules.convection.entity.ConvectionExamAnswer answer = 
            convectionAnswerService.getById(reqDTO.getId());
        if (answer == null) {
            return super.failure("答案记录不存在");
        }
        
        Map<String, Object> progress = convectionAnswerService.calculateProgress(answer);
        return super.success(progress);
    }

    /**
     * 获取考试答案统计
     */
    @ApiOperation(value = "获取考试答案统计")
    @PostMapping("/statistics")
    public ApiRest<Map<String, Object>> getAnswerStatistics(@RequestBody ConvectionAnswerRequest request) {
        Map<String, Object> statistics = convectionAnswerService.getAnswerStatistics(request.getExamId());
        return super.success(statistics);
    }

    /**
     * 分页查询待批卷答案列表
     */
    @ApiOperation(value = "分页查询待批卷答案列表")
    @PostMapping("/pending-grading/paging")
    public ApiRest<IPage<ConvectionAnswerDTO>> pendingGradingPaging(@RequestBody PagingReqDTO<ConvectionAnswerDTO> reqDTO) {
        IPage<ConvectionAnswerDTO> page = convectionAnswerService.pendingGradingPaging(reqDTO);
        return super.success(page);
    }

    /**
     * 根据答案ID获取详细信息
     */
    @ApiOperation(value = "根据答案ID获取详细信息")
    @PostMapping("/detail/{answerId}")
    public ApiRest<ConvectionAnswerDTO> getAnswerDetail(@PathVariable String answerId) {
        ConvectionAnswerDTO detail = convectionAnswerService.getAnswerDetail(answerId);
        return super.success(detail);
    }

    /**
     * 获取考试进度统计
     */
    @ApiOperation(value = "获取考试进度统计")
    @PostMapping("/progress-statistics")
    public ApiRest<Map<String, Object>> getProgressStatistics(@RequestBody ConvectionAnswerRequest request) {
        Map<String, Object> statistics = convectionAnswerService.getProgressStatistics(request.getExamId());
        return super.success(statistics);
    }

    /**
     * 查询需要人工批卷的答案数量
     */
    @ApiOperation(value = "查询需要人工批卷的答案数量")
    @PostMapping("/pending-manual-grading/count")
    public ApiRest<Long> countPendingManualGrading(@RequestBody ConvectionAnswerRequest request) {
        long count = convectionAnswerService.countPendingManualGrading(request.getExamId());
        return super.success(count);
    }

    /**
     * 验证答案数据完整性
     */
    @ApiOperation(value = "验证答案数据完整性")
    @PostMapping("/validate")
    public ApiRest<Map<String, Object>> validateAnswerData(@RequestBody ConvectionAnswerSaveDTO saveDTO) {
        Map<String, Object> result = convectionAnswerService.validateAnswerData(saveDTO);
        return super.success(result);
    }

    /**
     * 获取我的答案列表
     */
    @ApiOperation(value = "获取我的答案列表")
    @PostMapping("/my-answers")
    public ApiRest<java.util.List<com.yf.exam.modules.convection.entity.ConvectionExamAnswer>> getMyAnswers() {
        java.util.List<com.yf.exam.modules.convection.entity.ConvectionExamAnswer> answers = 
            convectionAnswerService.listByUser(UserUtils.getUserId());
        return super.success(answers);
    }

    /**
     * 强对流答案请求类
     */
    public static class ConvectionAnswerRequest {
        private String examId;
        private String questionId;

        // getters and setters
        public String getExamId() {
            return examId;
        }

        public void setExamId(String examId) {
            this.examId = examId;
        }

        public String getQuestionId() {
            return questionId;
        }

        public void setQuestionId(String questionId) {
            this.questionId = questionId;
        }
    }
} 