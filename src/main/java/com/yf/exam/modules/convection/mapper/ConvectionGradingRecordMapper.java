package com.yf.exam.modules.convection.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yf.exam.modules.convection.dto.ConvectionGradingDTO;
import com.yf.exam.modules.convection.entity.ConvectionGradingRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 强对流人工批卷记录Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
@Mapper
public interface ConvectionGradingRecordMapper extends BaseMapper<ConvectionGradingRecord> {

    /**
     * 根据答案ID和批卷教师ID查询批卷记录
     * 
     * @param answerId 答案ID
     * @param graderUserId 批卷教师用户ID
     * @return 批卷记录
     */
    @Select("SELECT * FROM el_convection_grading_record WHERE answer_id = #{answerId} AND grader_user_id = #{graderUserId}")
    ConvectionGradingRecord selectByAnswerAndGrader(@Param("answerId") String answerId, 
                                                   @Param("graderUserId") String graderUserId);

    /**
     * 根据答案ID查询批卷记录
     * 
     * @param answerId 答案ID
     * @return 批卷记录
     */
    @Select("SELECT * FROM el_convection_grading_record WHERE answer_id = #{answerId}")
    ConvectionGradingRecord selectByAnswerId(@Param("answerId") String answerId);

    /**
     * 分页查询批卷任务列表（包含学生和考试信息）
     * 
     * @param page 分页对象
     * @param examId 考试ID
     * @param gradingStatus 批卷状态
     * @param graderUserId 批卷教师用户ID
     * @return 批卷任务分页列表
     */
    IPage<ConvectionGradingDTO> selectGradingTaskList(Page<ConvectionGradingDTO> page,
                                                    @Param("examId") String examId,
                                                    @Param("gradingStatus") Integer gradingStatus,
                                                    @Param("graderUserId") String graderUserId);

    /**
     * 查询批卷详情（包含学生答案和标准答案）
     * 
     * @param recordId 批卷记录ID
     * @return 批卷详情
     */
    ConvectionGradingDTO selectGradingDetail(@Param("recordId") String recordId);

    /**
     * 根据学生用户ID查询批卷记录列表
     * 
     * @param studentUserId 学生用户ID
     * @return 批卷记录列表
     */
    List<ConvectionGradingRecord> selectByStudent(@Param("studentUserId") String studentUserId);

    /**
     * 根据批卷教师用户ID查询批卷记录列表
     * 
     * @param graderUserId 批卷教师用户ID
     * @return 批卷记录列表
     */
    List<ConvectionGradingRecord> selectByGrader(@Param("graderUserId") String graderUserId);

    /**
     * 更新批卷评分
     * 
     * @param id 记录ID
     * @param reasoningGradingBasisScore 分级依据阐述得分
     * @param reasoningExtremeScore 极端天气预报理由得分
     * @param reasoningTotalScore 预报依据总得分
     * @param gradingComments 批卷评语
     * @param improvementSuggestions 改进建议
     * @param gradingDuration 批卷耗时
     * @return 更新行数
     */
    @Update("UPDATE el_convection_grading_record SET " +
            "reasoning_grading_basis_score = #{reasoningGradingBasisScore}, " +
            "reasoning_extreme_score = #{reasoningExtremeScore}, " +
            "reasoning_total_score = #{reasoningTotalScore}, " +
            "grading_comments = #{gradingComments}, " +
            "improvement_suggestions = #{improvementSuggestions}, " +
            "grading_duration = #{gradingDuration}, " +
            "grading_status = 1, grading_time = NOW(), update_time = NOW() " +
            "WHERE id = #{id}")
    int updateGradingScore(@Param("id") String id,
                          @Param("reasoningGradingBasisScore") BigDecimal reasoningGradingBasisScore,
                          @Param("reasoningExtremeScore") BigDecimal reasoningExtremeScore,
                          @Param("reasoningTotalScore") BigDecimal reasoningTotalScore,
                          @Param("gradingComments") String gradingComments,
                          @Param("improvementSuggestions") String improvementSuggestions,
                          @Param("gradingDuration") Integer gradingDuration);

    /**
     * 更新复核信息
     * 
     * @param id 记录ID
     * @param reviewerUserId 复核教师用户ID
     * @param reviewComments 复核意见
     * @return 更新行数
     */
    @Update("UPDATE el_convection_grading_record SET " +
            "reviewer_user_id = #{reviewerUserId}, " +
            "review_comments = #{reviewComments}, " +
            "review_status = 1, review_time = NOW(), update_time = NOW() " +
            "WHERE id = #{id}")
    int updateReviewInfo(@Param("id") String id,
                        @Param("reviewerUserId") String reviewerUserId,
                        @Param("reviewComments") String reviewComments);

    /**
     * 查询批卷统计信息
     * 
     * @param examId 考试ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    Map<String, Object> selectGradingStatistics(@Param("examId") String examId,
                                               @Param("startTime") Date startTime,
                                               @Param("endTime") Date endTime);

    /**
     * 查询教师批卷工作量统计
     * 
     * @param graderUserId 批卷教师用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 工作量统计
     */
    Map<String, Object> selectGraderWorkloadStatistics(@Param("graderUserId") String graderUserId,
                                                       @Param("startTime") Date startTime,
                                                       @Param("endTime") Date endTime);

    /**
     * 查询需要复核的批卷记录
     * 
     * @param page 分页对象
     * @param reviewStatus 复核状态
     * @return 需要复核的记录分页列表
     */
    IPage<ConvectionGradingDTO> selectPendingReview(Page<ConvectionGradingDTO> page,
                                                   @Param("reviewStatus") Integer reviewStatus);

    /**
     * 查询考试的批卷进度
     * 
     * @param examId 考试ID
     * @return 批卷进度信息
     */
    @Select("SELECT " +
            "COUNT(*) as total_count, " +
            "SUM(CASE WHEN grading_status = 1 THEN 1 ELSE 0 END) as completed_count, " +
            "AVG(CASE WHEN grading_status = 1 THEN reasoning_total_score END) as avg_score, " +
            "AVG(CASE WHEN grading_status = 1 THEN grading_duration END) as avg_duration " +
            "FROM el_convection_grading_record WHERE exam_id = #{examId}")
    Map<String, Object> selectGradingProgress(@Param("examId") String examId);

    /**
     * 根据考试ID查询所有批卷记录
     *
     * @param examId 考试ID
     * @return 批卷记录列表
     */
    List<ConvectionGradingRecord> selectByExam(@Param("examId") String examId);

    /**
     * 分页查询需要复核的批卷记录列表
     *
     * @param page 分页对象
     * @param examId 考试ID
     * @return 需要复核的记录分页列表
     */
    IPage<ConvectionGradingDTO> selectPendingReviewList(Page<ConvectionGradingDTO> page,
                                                       @Param("examId") String examId);

    /**
     * 获取批卷质量分析
     *
     * @param graderUserId 批卷教师用户ID
     * @param examId 考试ID
     * @return 质量分析结果
     */
    Map<String, Object> selectGradingQualityAnalysis(@Param("graderUserId") String graderUserId,
                                                    @Param("examId") String examId);

    /**
     * 导出批卷结果
     *
     * @param examId 考试ID
     * @return 导出数据
     */
    List<Map<String, Object>> selectGradingResultsForExport(@Param("examId") String examId);
}