package com.yf.exam.modules.convection.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 强对流站点预报答案数据传输类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
@Data
@ApiModel(value = "强对流站点预报答案", description = "强对流站点预报答案")
public class ConvectionStationAnswerDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "站点预报答案数据")
    private Map<String, ConvectionStationData> stationAnswers;

    @ApiModelProperty(value = "预报依据阐述")
    private String forecastReasoning;

    @ApiModelProperty(value = "预报依据字数")
    private Integer reasoningWordCount;

    @ApiModelProperty(value = "站点预报进度百分比")
    private Integer progress;

    @ApiModelProperty(value = "站点列表")
    private List<String> stations;

    /**
     * 单个站点强对流数据
     */
    @Data
    @ApiModel(value = "单个站点强对流数据", description = "单个站点强对流数据")
    public static class ConvectionStationData implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "短时强降水预报：level1/level2/level3")
        private String shortTimeRainfall;

        @ApiModelProperty(value = "雷暴大风预报：moderate/severe/extreme")
        private String thunderstormWind;

        @ApiModelProperty(value = "冰雹预报：large（2cm以上大冰雹）")
        private String hail;

        @ApiModelProperty(value = "预报时间")
        private String forecastTime;

        @ApiModelProperty(value = "预报信心度：1-5")
        private Integer confidence;

        @ApiModelProperty(value = "站点名称")
        private String stationName;

        @ApiModelProperty(value = "站点编码")
        private String stationCode;

        /**
         * 检查是否有任何天气现象被选择
         */
        public boolean hasAnyWeatherSelected() {
            return (shortTimeRainfall != null && !shortTimeRainfall.isEmpty()) ||
                   (thunderstormWind != null && !thunderstormWind.isEmpty()) ||
                   (hail != null && !hail.isEmpty());
        }

        /**
         * 获取已选择的天气现象数量
         */
        public int getSelectedWeatherCount() {
            int count = 0;
            if (shortTimeRainfall != null && !shortTimeRainfall.isEmpty()) count++;
            if (thunderstormWind != null && !thunderstormWind.isEmpty()) count++;
            if (hail != null && !hail.isEmpty()) count++;
            return count;
        }

        /**
         * 获取选择的天气现象描述
         */
        public String getWeatherDescription() {
            StringBuilder sb = new StringBuilder();
            if (shortTimeRainfall != null && !shortTimeRainfall.isEmpty()) {
                sb.append("短时强降水(").append(getRainfallDescription(shortTimeRainfall)).append(")");
            }
            if (thunderstormWind != null && !thunderstormWind.isEmpty()) {
                if (sb.length() > 0) sb.append("、");
                sb.append("雷暴大风(").append(getWindDescription(thunderstormWind)).append(")");
            }
            if (hail != null && !hail.isEmpty()) {
                if (sb.length() > 0) sb.append("、");
                sb.append("冰雹(2cm以上)");
            }
            return sb.toString();
        }

        private String getRainfallDescription(String level) {
            switch (level) {
                case "level1": return "20≤R1<40mm/h";
                case "level2": return "40≤R1<80mm/h";
                case "level3": return "80≤R1mm/h以上";
                default: return level;
            }
        }

        private String getWindDescription(String level) {
            switch (level) {
                case "moderate": return "8级≤Wg<10级或6级≤W2<8级";
                case "severe": return "10级≤Wg<12级或8级≤W2<10级";
                case "extreme": return "12级≤Wg或龙卷或10级≤W2";
                default: return level;
            }
        }
    }

    /**
     * 计算进度百分比
     */
    public Integer calculateProgress() {
        if (stationAnswers == null || stationAnswers.isEmpty()) {
            return 0;
        }

        int totalStations = stationAnswers.size();
        int completedStations = 0;

        for (ConvectionStationData data : stationAnswers.values()) {
            if (data.hasAnyWeatherSelected()) {
                completedStations++;
            }
        }

        // 站点选择进度占80%，预报依据进度占20%
        int selectionProgress = totalStations > 0 ? (completedStations * 80 / totalStations) : 0;
        int reasoningProgress = (reasoningWordCount != null && reasoningWordCount > 100) ? 20 : 0;

        return Math.min(100, selectionProgress + reasoningProgress);
    }

    /**
     * 验证数据完整性
     */
    public boolean isValid() {
        if (stationAnswers == null || stationAnswers.isEmpty()) {
            return false;
        }

        // 检查至少有一个站点有选择
        for (ConvectionStationData data : stationAnswers.values()) {
            if (data.hasAnyWeatherSelected()) {
                return true;
            }
        }

        return false;
    }
} 