package com.yf.exam.modules.station.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
* <p>
* 气象站点DTO类
* </p>
*
* <AUTHOR>
* @since 2025-01-23
*/
@Data
@ApiModel(value="气象站点", description="气象站点")
public class ElStationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "站点ID")
    private String id;

    @ApiModelProperty(value = "行政区划代码中文")
    private String adminCodeChn;

    @ApiModelProperty(value = "海拔高度")
    private Float alti;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "县")
    private String cnty;

    @ApiModelProperty(value = "纬度")
    private Float lat;

    @ApiModelProperty(value = "经度")
    private Double lon;

    @ApiModelProperty(value = "站点识别码")
    private String stationIdC;

    @ApiModelProperty(value = "站点等级")
    private String stationLevl;

    @ApiModelProperty(value = "站点名称")
    private String stationName;

    @ApiModelProperty(value = "别名")
    private String alias;

    @ApiModelProperty(value = "是否在线")
    private Integer online;

    @ApiModelProperty(value = "是否绘制到地图")
    private Integer drawTown;

    @ApiModelProperty(value = "排序")
    private Integer sort;
}
