package com.yf.exam.modules.station.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.core.api.dto.BaseIdReqDTO;
import com.yf.exam.core.api.dto.BaseIdsReqDTO;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.modules.station.dto.ElStationDTO;
import com.yf.exam.modules.station.dto.request.ElStationReqDTO;
import com.yf.exam.modules.station.dto.request.ElStationRegionReqDTO;
import com.yf.exam.modules.station.dto.response.ElStationRespDTO;
import com.yf.exam.modules.station.entity.ElStation;
import com.yf.exam.modules.station.service.ElStationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* <p>
* 气象站点控制器
* </p>
*
* <AUTHOR>
* @since 2025-01-23
*/
@Api(tags={"气象站点"})
@RestController
@RequestMapping("/exam/api/station")
public class ElStationController extends BaseController {

    @Autowired
    private ElStationService baseService;

    /**
    * 添加或修改
    * @param reqDTO 站点信息
    * @return 操作结果
    */
    @ApiOperation(value = "添加或修改")
    @RequestMapping(value = "/save", method = { RequestMethod.POST})
    public ApiRest save(@RequestBody ElStationDTO reqDTO) {
        baseService.save(reqDTO);
        return super.success();
    }

    /**
    * 批量删除
    * @param reqDTO ID列表
    * @return 操作结果
    */
    @ApiOperation(value = "批量删除")
    @RequestMapping(value = "/delete", method = { RequestMethod.POST})
    public ApiRest delete(@RequestBody BaseIdsReqDTO reqDTO) {
        // 根据ID删除
        baseService.removeByIds(reqDTO.getIds());
        return super.success();
    }

    /**
    * 查找详情
    * @param reqDTO ID请求
    * @return 站点详情
    */
    @ApiOperation(value = "查找详情")
    @RequestMapping(value = "/detail", method = { RequestMethod.POST})
    public ApiRest<ElStationDTO> detail(@RequestBody BaseIdReqDTO reqDTO) {
        ElStation entity = baseService.getById(reqDTO.getId());
        ElStationDTO dto = new ElStationDTO();
        BeanUtils.copyProperties(entity, dto);
        return super.success(dto);
    }

    /**
    * 分页查找
    * @param reqDTO 分页查询请求
    * @return 分页结果
    */
    @ApiOperation(value = "分页查找")
    @RequestMapping(value = "/paging", method = { RequestMethod.POST})
    public ApiRest<IPage<ElStationRespDTO>> paging(@RequestBody PagingReqDTO<ElStationReqDTO> reqDTO) {

        // 确保分页参数不为null
        if (reqDTO.getCurrent() == null) {
            reqDTO.setCurrent(1);
        }
        if (reqDTO.getSize() == null) {
            reqDTO.setSize(10);
        }

        // 分页查询并转换
        IPage<ElStationRespDTO> page = baseService.paging(reqDTO);

        return super.success(page);
    }

    /**
    * 根据区域代码数组查询站点信息
    * @param reqDTO 区域代码查询请求
    * @return 站点列表
    */
    @ApiOperation(value = "根据区域代码数组查询站点信息")
    @RequestMapping(value = "/listByRegionCodes", method = { RequestMethod.POST})
    public ApiRest<List<ElStationRespDTO>> listByRegionCodes(@Valid @RequestBody ElStationRegionReqDTO reqDTO) {

        // 调用服务层方法查询站点信息
        List<ElStationRespDTO> stations = baseService.listByRegionCodes(reqDTO);

        return super.success(stations);
    }
}
