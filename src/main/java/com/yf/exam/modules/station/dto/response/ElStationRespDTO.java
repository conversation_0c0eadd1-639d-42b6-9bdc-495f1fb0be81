package com.yf.exam.modules.station.dto.response;

import com.yf.exam.modules.station.dto.ElStationDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
* <p>
* 气象站点响应类
* </p>
*
* <AUTHOR>
* @since 2025-01-23
*/
@Data
@ApiModel(value="气象站点响应", description="气象站点响应")
public class ElStationRespDTO extends ElStationDTO {

    private static final long serialVersionUID = 1L;

    // 可以在这里添加额外的响应字段，比如统计信息等
}
