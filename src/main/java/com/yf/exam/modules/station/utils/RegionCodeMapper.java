package com.yf.exam.modules.station.utils;

import java.util.*;

/**
 * 区域代码映射工具类
 * 
 * <AUTHOR>
 * @since 2025-01-23
 */
public class RegionCodeMapper {

    /**
     * 区域代码映射表
     * 键：区域编号（1-9）
     * 值：对应的省份行政区划代码列表
     */
    private static final Map<String, List<String>> REGION_CODE_MAP = new HashMap<>();

    static {
        // 一区：华北区域 - 北京、天津、河北、山西、山东、河南
        REGION_CODE_MAP.put("1", Arrays.asList(
            "110000", // 北京
            "120000", // 天津
            "130000", // 河北
            "140000", // 山西
            "370000", // 山东
            "410000"  // 河南
        ));

        // 二区：东北区域 - 辽宁、吉林、黑龙江
        REGION_CODE_MAP.put("2", Arrays.asList(
            "210000", // 辽宁
            "220000", // 吉林
            "230000"  // 黑龙江
        ));

        // 三区：长江中下游区域 - 上海、江苏、浙江、安徽、湖北、湖南、江西
        REGION_CODE_MAP.put("3", Arrays.asList(
            "310000", // 上海
            "320000", // 江苏
            "330000", // 浙江
            "340000", // 安徽
            "420000", // 湖北
            "430000", // 湖南
            "360000"  // 江西
        ));

        // 四区：华南区域 - 广东、广西、海南、福建
        REGION_CODE_MAP.put("4", Arrays.asList(
            "440000", // 广东
            "450000", // 广西
            "460000", // 海南
            "350000"  // 福建
        ));

        // 五区：西南地区东部 - 重庆、四川、贵州、云南
        REGION_CODE_MAP.put("5", Arrays.asList(
            "500000", // 重庆
            "510000", // 四川
            "520000", // 贵州
            "530000"  // 云南
        ));

        // 六区：青藏高原区域 - 青海、西藏
        REGION_CODE_MAP.put("6", Arrays.asList(
            "630000", // 青海
            "540000"  // 西藏
        ));

        // 七区：新疆区域 - 新疆
        REGION_CODE_MAP.put("7", Arrays.asList(
            "650000"  // 新疆
        ));

        // 八区：西北地区东部区域 - 陕西、甘肃、宁夏
        REGION_CODE_MAP.put("8", Arrays.asList(
            "610000", // 陕西
            "620000", // 甘肃
            "640000"  // 宁夏
        ));

        // 九区：内蒙古区域 - 内蒙古
        REGION_CODE_MAP.put("9", Arrays.asList(
            "150000"  // 内蒙古
        ));
    }

    /**
     * 将区域编号转换为对应的省份行政区划代码列表
     * 
     * @param regionCode 区域编号列表（1-9）
     * @return 省份行政区划代码列表
     */
    public static List<String> convertRegionCodesToProvinceCodes(String regionCode) {
        if (regionCode == null || regionCode.isEmpty()) {
            return new ArrayList<>();
        }

        Set<String> provinceCodesSet = new HashSet<>();

        if (regionCode != null && REGION_CODE_MAP.containsKey(regionCode.trim())) {
            List<String> provinceCodes = REGION_CODE_MAP.get(regionCode.trim());
            provinceCodesSet.addAll(provinceCodes);
        }

        return new ArrayList<>(provinceCodesSet);
    }

    /**
     * 获取所有支持的区域编号
     * 
     * @return 区域编号列表
     */
    public static Set<String> getSupportedRegionCodes() {
        return REGION_CODE_MAP.keySet();
    }

    /**
     * 检查区域编号是否有效
     * 
     * @param regionCode 区域编号
     * @return 是否有效
     */
    public static boolean isValidRegionCode(String regionCode) {
        return regionCode != null && REGION_CODE_MAP.containsKey(regionCode.trim());
    }

    /**
     * 获取区域编号对应的省份列表（用于显示）
     * 
     * @param regionCode 区域编号
     * @return 省份名称列表
     */
    public static List<String> getProvinceNamesByRegionCode(String regionCode) {
        Map<String, String> provinceNames = new HashMap<>();
        provinceNames.put("110000", "北京");
        provinceNames.put("120000", "天津");
        provinceNames.put("130000", "河北");
        provinceNames.put("140000", "山西");
        provinceNames.put("150000", "内蒙古");
        provinceNames.put("210000", "辽宁");
        provinceNames.put("220000", "吉林");
        provinceNames.put("230000", "黑龙江");
        provinceNames.put("310000", "上海");
        provinceNames.put("320000", "江苏");
        provinceNames.put("330000", "浙江");
        provinceNames.put("340000", "安徽");
        provinceNames.put("350000", "福建");
        provinceNames.put("360000", "江西");
        provinceNames.put("370000", "山东");
        provinceNames.put("410000", "河南");
        provinceNames.put("420000", "湖北");
        provinceNames.put("430000", "湖南");
        provinceNames.put("440000", "广东");
        provinceNames.put("450000", "广西");
        provinceNames.put("460000", "海南");
        provinceNames.put("500000", "重庆");
        provinceNames.put("510000", "四川");
        provinceNames.put("520000", "贵州");
        provinceNames.put("530000", "云南");
        provinceNames.put("540000", "西藏");
        provinceNames.put("610000", "陕西");
        provinceNames.put("620000", "甘肃");
        provinceNames.put("630000", "青海");
        provinceNames.put("640000", "宁夏");
        provinceNames.put("650000", "新疆");

        List<String> result = new ArrayList<>();
        if (regionCode != null && REGION_CODE_MAP.containsKey(regionCode.trim())) {
            List<String> provinceCodes = REGION_CODE_MAP.get(regionCode.trim());
            for (String code : provinceCodes) {
                String name = provinceNames.get(code);
                if (name != null) {
                    result.add(name);
                }
            }
        }
        return result;
    }
}
