package com.yf.exam.modules.station.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.modules.station.dto.ElStationDTO;
import com.yf.exam.modules.station.dto.request.ElStationReqDTO;
import com.yf.exam.modules.station.dto.request.ElStationRegionReqDTO;
import com.yf.exam.modules.station.dto.response.ElStationRespDTO;
import com.yf.exam.modules.station.entity.ElStation;

import java.util.List;

/**
* <p>
* 气象站点业务类
* </p>
*
* <AUTHOR>
* @since 2025-01-23
*/
public interface ElStationService extends IService<ElStation> {

    /**
    * 分页查询数据
    * @param reqDTO 分页查询请求
    * @return 分页结果
    */
    IPage<ElStationRespDTO> paging(PagingReqDTO<ElStationReqDTO> reqDTO);

    /**
     * 保存站点信息
     * @param reqDTO 站点信息
     */
    void save(ElStationDTO reqDTO);

    /**
     * 根据区域代码数组查询站点信息
     * @param reqDTO 区域代码查询请求
     * @return 站点列表
     */
    List<ElStationRespDTO> listByRegionCodes(ElStationRegionReqDTO reqDTO);
}
