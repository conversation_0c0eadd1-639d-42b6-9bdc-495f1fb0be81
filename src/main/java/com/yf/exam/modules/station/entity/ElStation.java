package com.yf.exam.modules.station.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
* <p>
* 气象站点实体类
* </p>
*
* <AUTHOR>
* @since 2025-01-23
*/
@Data
@TableName("el_station")
public class ElStation extends Model<ElStation> {

    private static final long serialVersionUID = 1L;

    /**
     * 站点ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 行政区划代码中文
     */
    @TableField("admin_code_chn")
    private String adminCodeChn;

    /**
     * 海拔高度
     */
    private Float alti;

    /**
     * 城市
     */
    private String city;

    /**
     * 县
     */
    private String cnty;

    /**
     * 纬度
     */
    private Float lat;

    /**
     * 经度
     */
    private Double lon;

    /**
     * 站点识别码
     */
    @TableField("station_id_c")
    private String stationIdC;

    /**
     * 站点等级
     */
    @TableField("station_levl")
    private String stationLevl;

    /**
     * 站点名称
     */
    @TableField("station_name")
    private String stationName;

    /**
     * 别名
     */
    private String alias;

    /**
     * 是否在线
     */
    private Integer online;

    /**
     * 是否绘制到地图
     */
    @TableField("draw_town")
    private Integer drawTown;

    /**
     * 排序
     */
    private Integer sort;
}
