package com.yf.exam.modules.station.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yf.exam.modules.station.dto.request.ElStationReqDTO;
import com.yf.exam.modules.station.dto.request.ElStationRegionReqDTO;
import com.yf.exam.modules.station.dto.response.ElStationRespDTO;
import com.yf.exam.modules.station.entity.ElStation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <p>
* 气象站点Mapper
* </p>
*
* <AUTHOR>
* @since 2025-01-23
*/
public interface ElStationMapper extends BaseMapper<ElStation> {

    /**
     * 分页查询气象站点
     * @param page 分页对象
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<ElStationRespDTO> paging(Page page, @Param("query") ElStationReqDTO query);

    /**
     * 根据区域代码数组查询站点信息
     * @param regionCodes 处理后的区域代码列表（已添加%用于模糊查询）
     * @param query 查询条件
     * @return 站点列表
     */
    List<ElStationRespDTO> listByRegionCodes(@Param("regionCodes") List<String> regionCodes,
                                           @Param("query") ElStationRegionReqDTO query);
}
