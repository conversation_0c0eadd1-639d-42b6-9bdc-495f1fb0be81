package com.yf.exam.modules.station.dto.request;

import com.yf.exam.modules.station.dto.ElStationDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* <p>
* 气象站点查询请求类
* </p>
*
* <AUTHOR>
* @since 2025-01-23
*/
@Data
@ApiModel(value="气象站点查询请求", description="气象站点查询请求")
public class ElStationReqDTO extends ElStationDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "行政区划代码中文（模糊查询）")
    private String adminCodeChn;

    @ApiModelProperty(value = "站点等级")
    private String stationLevl;

    @ApiModelProperty(value = "站点名称（模糊查询）")
    private String stationName;

    @ApiModelProperty(value = "城市（模糊查询）")
    private String city;

    @ApiModelProperty(value = "县（模糊查询）")
    private String cnty;

    @ApiModelProperty(value = "是否在线")
    private Integer online;

    @ApiModelProperty(value = "是否绘制到地图")
    private Integer drawTown;
}
