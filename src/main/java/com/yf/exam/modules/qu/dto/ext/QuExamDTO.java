package com.yf.exam.modules.qu.dto.ext;

import com.yf.exam.modules.qu.dto.QuAnswerExamDTO;
import com.yf.exam.modules.qu.dto.QuDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
* <p>
* 考试专用题目详情DTO（不包含正确答案信息）
* </p>
*
* <AUTHOR>
* @since 2024-12-19
*/
@Data
@ApiModel(value="考试题目详情", description="考试题目详情（不包含正确答案）")
public class QuExamDTO extends QuDTO {

    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "备选项列表（不包含正确答案标识）", required=true)
    private List<QuAnswerExamDTO> answerList;

    @ApiModelProperty(value = "题库列表", required=true)
    private List<String> repoIds;
    private String precipitationRegion;
    
}
