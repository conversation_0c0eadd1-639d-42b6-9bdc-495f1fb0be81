package com.yf.exam.modules.qu.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
* <p>
* 考试专用候选答案DTO（不包含正确答案标识）
* </p>
*
* <AUTHOR>
* @since 2024-12-19
*/
@Data
@ApiModel(value="考试候选答案", description="考试候选答案（不包含正确答案标识）")
public class QuAnswerExamDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "答案ID", required=true)
    private String id;

    @ApiModelProperty(value = "问题ID", required=true)
    private String quId;

    @ApiModelProperty(value = "选项图片", required=true)
    private String image;

    @ApiModelProperty(value = "答案内容", required=true)
    private String content;

    // 注意：这里故意不包含 isRight 字段和 analysis 字段
    // 这些信息只在结果查看时提供
    
}
