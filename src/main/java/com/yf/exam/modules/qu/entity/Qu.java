package com.yf.exam.modules.qu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
* <p>
* 问题题目实体类
* </p>
*
* <AUTHOR>
* @since 2020-05-25 13:23
*/
@Data
@TableName("el_qu")
public class Qu extends Model<Qu> {

    private static final long serialVersionUID = 1L;

    /**
     * 题目ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 题目标题
     */
    private String title;

    /**
     * 预报起报日期（历史个例题目专用）
     */
    @TableField("forecast_date")
    private String forecastDate;

    /**
     * 预报起报时次（历史个例题目专用）
     */
    @TableField("forecast_time")
    private String forecastTime;

    /**
     * 题目类型
     */
    @TableField("qu_type")
    private Integer quType;

    /**
     * 1普通,2较难
     */
    private Integer level;

    /**
     * 题目图片
     */
    private String image;

    /**
     * 题目内容
     */
    private String content;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 题目备注
     */
    private String remark;

    /**
     * 整题解析
     */
    private String analysis;

    /**
     * 天气预报表格配置ID（仅当quType=6时使用）
     */
    @TableField("weather_config_id")
    private String weatherConfigId;

    /**
     * 关联的天气预报表格配置ID
     */
    @TableField("table_config_id")
    private String tableConfigId;

    /**
     * 天气情景描述和背景材料
     */
    @TableField("weather_scenario")
    private String weatherScenario;

    /**
     * 情景相关数据（观测数据、历史资料等）
     */
    @TableField("scenario_data")
    private String scenarioData;

}
