package com.yf.exam.modules.qu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.exam.ability.upload.config.UploadConfig;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.core.exception.ServiceException;
import com.yf.exam.core.utils.BeanMapper;
import com.yf.exam.modules.qu.dto.QuAnswerDTO;
import com.yf.exam.modules.qu.dto.QuAnswerExamDTO;
import com.yf.exam.modules.qu.dto.QuDTO;
import com.yf.exam.modules.qu.dto.export.QuExportDTO;
import com.yf.exam.modules.qu.dto.ext.QuDetailDTO;
import com.yf.exam.modules.qu.dto.ext.QuExamDTO;
import com.yf.exam.modules.qu.dto.request.QuQueryReqDTO;
import com.yf.exam.modules.qu.entity.Qu;
import com.yf.exam.modules.qu.entity.QuAnswer;
import com.yf.exam.modules.qu.entity.QuRepo;
import com.yf.exam.modules.qu.enums.QuType;
import com.yf.exam.modules.qu.mapper.QuMapper;
import com.yf.exam.modules.qu.service.QuAnswerService;
import com.yf.exam.modules.qu.service.QuRepoService;
import com.yf.exam.modules.qu.service.QuService;
import com.yf.exam.modules.qu.utils.ImageCheckUtils;
import com.yf.exam.modules.repo.service.RepoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 语言设置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-25 10:17
 */
@Service
public class QuServiceImpl extends ServiceImpl<QuMapper, Qu> implements QuService {

    @Autowired
    private QuAnswerService quAnswerService;

    @Autowired
    private QuRepoService quRepoService;

    @Autowired
    private ImageCheckUtils imageCheckUtils;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public IPage<QuDTO> paging(PagingReqDTO<QuQueryReqDTO> reqDTO) {

        //创建分页对象
        Page page = new Page<>(reqDTO.getCurrent(), reqDTO.getSize());

        //转换结果
        IPage<QuDTO> pageData = baseMapper.paging(page, reqDTO.getParams());
        return pageData;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        // 移除题目
        this.removeByIds(ids);

        // 移除选项
        QueryWrapper<QuAnswer> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(QuAnswer::getQuId, ids);
        quAnswerService.remove(wrapper);

        // 移除题库绑定
        QueryWrapper<QuRepo> wrapper1 = new QueryWrapper<>();
        wrapper1.lambda().in(QuRepo::getQuId, ids);
        quRepoService.remove(wrapper1);
    }

    @Override
    public List<Qu> listByRandom(String repoId, Integer quType, List<String> excludes, Integer size) {
        return baseMapper.listByRandom(repoId, quType, excludes, size);
    }

    @Override
    public QuDetailDTO detail(String id) {

        QuDetailDTO respDTO = new QuDetailDTO();
        Qu qu = this.getById(id);
        BeanMapper.copy(qu, respDTO);

        List<QuAnswerDTO> answerList = quAnswerService.listByQu(id);
        respDTO.setAnswerList(answerList);

        List<String> repoIds = quRepoService.listByQu(id);
        respDTO.setRepoIds(repoIds);

        return respDTO;
    }

    @Override
    public QuExamDTO examDetail(String id) {
        QuExamDTO respDTO = new QuExamDTO();
        Qu qu = this.getById(id);
        BeanMapper.copy(qu, respDTO);

        // 历史个例题目（quType=6）的答案存储在scenario_data中，不需要从qu_answer表获取
        if (qu.getQuType() != null && qu.getQuType() == 6) {
            // 天气预报表格题，答案在scenario_data中，这里返回空的答案列表
            respDTO.setAnswerList(new ArrayList<>());

            // 过滤scenarioData中的答案信息，但保留其他题目相关信息
            String filteredScenarioData = filterScenarioDataForExam(qu.getScenarioData());
            respDTO.setScenarioData(filteredScenarioData);
        } else {
            // 其他类型题目，从qu_answer表获取答案列表，但不包含正确答案标识
            List<QuAnswerDTO> fullAnswerList = quAnswerService.listByQu(id);
            List<QuAnswerExamDTO> examAnswerList = new ArrayList<>();

            for (QuAnswerDTO fullAnswer : fullAnswerList) {
                QuAnswerExamDTO examAnswer = new QuAnswerExamDTO();
                examAnswer.setId(fullAnswer.getId());
                examAnswer.setQuId(fullAnswer.getQuId());
                examAnswer.setImage(fullAnswer.getImage());
                examAnswer.setContent(fullAnswer.getContent());
                // 故意不设置 isRight 和 analysis 字段
                examAnswerList.add(examAnswer);
            }

            respDTO.setAnswerList(examAnswerList);
        }

        List<String> repoIds = quRepoService.listByQu(id);
        respDTO.setRepoIds(repoIds);

        return respDTO;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(QuDetailDTO reqDTO) {


        // 校验数据
        if (!this.checkData(reqDTO, "")){
            return;
        }

        Qu qu = new Qu();
        BeanMapper.copy(reqDTO, qu);

        // 如果是新建试题，需要生成ID
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(qu.getId())) {
            qu.setId(IdWorker.getIdStr());
        }

        // 校验图片地址
//        imageCheckUtils.checkImage(qu.getImage(), "题干图片地址错误！");

        // 更新
        this.saveOrUpdate(qu);

        // 将生成的ID设置回DTO，这样控制器就能获取到ID
        reqDTO.setId(qu.getId());

        // 保存全部问题
        quAnswerService.saveAll(qu.getId(), reqDTO.getAnswerList());

        // 保存到题库
        quRepoService.saveAll(qu.getId(), qu.getQuType(), reqDTO.getRepoIds());

    }

    @Override
    public List<QuExportDTO> listForExport(QuQueryReqDTO query) {
        return baseMapper.listForExport(query);
    }

    @Override
    public int importExcel(List<QuExportDTO> dtoList) {

        //根据题目名称分组
        Map<Integer, List<QuExportDTO>> anMap = new HashMap<>(16);

        //题目本体信息
        Map<Integer, QuExportDTO> quMap = new HashMap<>(16);

        //数据分组
        for (QuExportDTO item : dtoList) {

            // 空白的ID
            if (StringUtils.isEmpty(item.getNo())) {
                continue;
            }

            Integer key;
            //序号
            try {
                key = Integer.parseInt(item.getNo());
            } catch (Exception e) {
                continue;
            }

            //如果已经有题目了，直接处理选项
            if (anMap.containsKey(key)) {
                anMap.get(key).add(item);
            } else {
                //如果没有，将题目内容和选项一起
                List<QuExportDTO> subList = new ArrayList<>();
                subList.add(item);
                anMap.put(key, subList);
                quMap.put(key, item);
            }
        }

        int count = 0;
        try {

            //循环题目插入
            for (Integer key : quMap.keySet()) {

                QuExportDTO im = quMap.get(key);

                //题目基本信息
                QuDetailDTO qu = new QuDetailDTO();
                qu.setContent(im.getQContent());
                qu.setAnalysis(im.getQAnalysis());
                qu.setQuType(Integer.parseInt(im.getQuType()));
                qu.setCreateTime(new Date());
                qu.setImage(im.getQImage());
                //设置回答列表
                List<QuAnswerDTO> answerList = this.processAnswerList(anMap.get(key));
                //设置题目
                qu.setAnswerList(answerList);
                //设置引用题库
                qu.setRepoIds(im.getRepoList());
                // 保存答案
                this.save(qu);
                count++;
            }

        } catch (ServiceException e) {
            e.printStackTrace();
            throw new ServiceException(1, "导入出现问题，行：" + count + "，" + e.getMessage());
        }

        return count;
    }

    /**
     * 处理回答列表
     *
     * @param importList
     * @return
     */
    private List<QuAnswerDTO> processAnswerList(List<QuExportDTO> importList) {

        List<QuAnswerDTO> list = new ArrayList<>(16);
        for (QuExportDTO item : importList) {
            QuAnswerDTO a = new QuAnswerDTO();
            a.setIsRight("1".equals(item.getAIsRight()));
            a.setContent(item.getAContent());
            a.setAnalysis(item.getAAnalysis());
            a.setId("");
            list.add(a);
        }
        return list;
    }

    /**
     * 校验题目信息
     *
     * @param qu
     * @param no
     * @throws Exception
     */
    public boolean checkData(QuDetailDTO qu, String no) {


        if (StringUtils.isEmpty(qu.getContent())) {
            log.error(qu + "题目内容不能为空！");
            return false;
        }


        // 历史个例题型（天气预报表格题）和强对流试题不需要传统的题库和答案列表
        if (qu.getQuType() != null && (qu.getQuType().equals(6) || qu.getQuType().equals(7))) {
            // 历史个例题型（6）的答案在scenarioData中
            // 强对流试题（7）的答案也在scenarioData中
            // 这两种题型都不需要传统的题库和答案列表，跳过相关检查
            return true;
        }

        if (CollectionUtils.isEmpty(qu.getRepoIds())) {
            log.error(qu + "至少要选择一个题库！");
            return false;
        }

        List<QuAnswerDTO> answers = qu.getAnswerList();

        if (CollectionUtils.isEmpty(answers)) {
            log.error(qu + "至少要包含一个备选答案！");
            return false;
        }


            int trueCount = 0;
            for (QuAnswerDTO a : answers) {

                if (a.getIsRight() == null) {
                    log.error(qu + "必须定义选项是否正确项！");
            return false;
                }

                if (StringUtils.isEmpty(a.getContent())) {
                    log.error(qu + "选项内容不为空！");
            return false;
                }

                if (a.getIsRight()) {
                    trueCount += 1;
                }
            }

            if (trueCount == 0) {
                log.error(qu + "至少要包含一个正确项！");
            return false;
            }


            //单选题
            if (qu.getQuType().equals(QuType.RADIO) && trueCount > 1) {
                log.error(qu + "单选题不能包含多个正确项！");
            return false;
            }
        return true;
    }

    /**
     * 过滤scenarioData中的答案信息，保留题目相关信息
     *
     * @param scenarioData 原始的scenarioData JSON字符串
     * @return 过滤后的scenarioData JSON字符串
     */
    private String filterScenarioDataForExam(String scenarioData) {
        if (StringUtils.isEmpty(scenarioData)) {
            return null;
        }

        try {
            // 解析JSON数据
            Map<String, Object> dataMap = objectMapper.readValue(scenarioData, new TypeReference<Map<String, Object>>() {});

            // 创建过滤后的数据Map
            Map<String, Object> filteredMap = new HashMap<>();

            // 保留题目相关信息，过滤掉答案信息
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                // 过滤掉答案相关的字段
                if (!"answers".equals(key) &&
                    !"standardAnswer".equals(key) &&
                    !"scoringConfig".equals(key)) {
                    filteredMap.put(key, value);
                }
            }

            // 将过滤后的数据转换回JSON字符串
            return objectMapper.writeValueAsString(filteredMap);

        } catch (Exception e) {
            log.warn("过滤scenarioData时发生异常");
            return null;
        }
    }
}
