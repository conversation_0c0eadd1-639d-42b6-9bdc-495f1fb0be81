package com.yf.exam.modules.weather.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 历史个例考试答案DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@ApiModel(value = "WeatherHistoryExamAnswerDTO", description = "历史个例考试答案DTO")
public class WeatherHistoryExamAnswerDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "考试ID")
    private String examId;

    @ApiModelProperty(value = "题目ID")
    private String questionId;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "第一部分：降水分级落区预报答案")
    private Map<String, Object> precipitationAnswer;

    @ApiModelProperty(value = "第二部分：灾害性天气预报答案")
    private Map<String, Object> weatherAnswer;

    @ApiModelProperty(value = "整体进度百分比")
    private Integer overallProgress;

    @ApiModelProperty(value = "答题状态：0-答题中，1-已提交")
    private Integer answerStatus;

    @ApiModelProperty(value = "答题时间（最后保存时间）")
    private Date answerTime;

    @ApiModelProperty(value = "已用时间（秒）")
    private Integer timeSpent;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "总得分")
    private BigDecimal totalScore;

    @ApiModelProperty(value = "得分详情")
    private Map<String, Object> scoreDetails;

    @ApiModelProperty(value = "批改状态：0-未批改，1-已批改")
    private Integer gradingStatus;

    @ApiModelProperty(value = "批改时间")
    private Date gradingTime;

    @ApiModelProperty(value = "批改人ID")
    private String graderId;

    @ApiModelProperty(value = "批改备注")
    private String gradingRemark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
