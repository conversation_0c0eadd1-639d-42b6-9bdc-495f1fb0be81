package com.yf.exam.modules.weather.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.exam.modules.weather.dto.WeatherHistoryExamAnswerDTO;
import com.yf.exam.modules.weather.dto.request.WeatherHistoryExamAnswerReqDTO;
import com.yf.exam.modules.weather.dto.request.WeatherHistoryExamAnswerSaveReqDTO;
import com.yf.exam.modules.weather.dto.request.WeatherHistoryExamSubmitReqDTO;
import com.yf.exam.modules.weather.entity.WeatherHistoryExamAnswer;

/**
 * <p>
 * 历史个例考试答案表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface WeatherHistoryExamAnswerService extends IService<WeatherHistoryExamAnswer> {

    /**
     * 保存历史个例考试答案
     *
     * @param reqDTO 保存请求DTO
     * @return 答案DTO
     */
    WeatherHistoryExamAnswerDTO saveAnswer(WeatherHistoryExamAnswerSaveReqDTO reqDTO);

    /**
     * 获取历史个例考试答案
     *
     * @param reqDTO 获取请求DTO
     * @return 答案DTO
     */
    WeatherHistoryExamAnswerDTO getAnswer(WeatherHistoryExamAnswerReqDTO reqDTO);

    /**
     * 提交历史个例考试
     *
     * @param reqDTO 提交请求DTO
     * @return 是否成功
     */
    boolean submitExam(WeatherHistoryExamSubmitReqDTO reqDTO);

    /**
     * 获取考试答题进度
     *
     * @param reqDTO 获取请求DTO
     * @return 进度信息
     */
    WeatherHistoryExamAnswerDTO getProgress(WeatherHistoryExamAnswerReqDTO reqDTO);

    /**
     * 检查用户是否已经参加过指定考试
     *
     * @param examId 考试ID
     * @return 考试状态信息，如果没有参加过返回null
     */
    WeatherHistoryExamAnswerDTO checkExamStatus(String examId);
}
