package com.yf.exam.modules.weather.micaps;

import java.util.List;

/**
 * MICAPS第一类数据：地面全要素填图数据
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */
public class MicapsType1Data extends MicapsData {
    
    /** 总站点数 */
    private int totalStations;
    
    /** 站点数据列表 */
    private List<MicapsStation> stations;
    
    public int getTotalStations() {
        return totalStations;
    }
    
    public void setTotalStations(int totalStations) {
        this.totalStations = totalStations;
    }
    
    public List<MicapsStation> getStations() {
        return stations;
    }
    
    public void setStations(List<MicapsStation> stations) {
        this.stations = stations;
    }
    
    /**
     * 根据站号获取站点数据
     * 
     * @param stationId 站号
     * @return 站点数据，如果不存在返回null
     */
    public MicapsStation getStationById(long stationId) {
        if (stations == null) {
            return null;
        }
        
        return stations.stream()
                .filter(station -> station.getStationId() == stationId)
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 获取指定经纬度范围内的站点
     * 
     * @param minLon 最小经度
     * @param maxLon 最大经度
     * @param minLat 最小纬度
     * @param maxLat 最大纬度
     * @return 范围内的站点列表
     */
    public List<MicapsStation> getStationsInRange(double minLon, double maxLon, 
                                                  double minLat, double maxLat) {
        if (stations == null) {
            return null;
        }
        
        return stations.stream()
                .filter(station -> station.getLongitude() >= minLon && 
                                 station.getLongitude() <= maxLon &&
                                 station.getLatitude() >= minLat && 
                                 station.getLatitude() <= maxLat)
                .collect(java.util.stream.Collectors.toList());
    }
    
    @Override
    public String toString() {
        return "MicapsType1Data{" +
                "dataType=" + getDataType() +
                ", description='" + getDescription() + '\'' +
                ", year=" + getYear() +
                ", month=" + getMonth() +
                ", day=" + getDay() +
                ", hour=" + getHour() +
                ", totalStations=" + totalStations +
                ", actualStations=" + (stations != null ? stations.size() : 0) +
                '}';
    }
}
