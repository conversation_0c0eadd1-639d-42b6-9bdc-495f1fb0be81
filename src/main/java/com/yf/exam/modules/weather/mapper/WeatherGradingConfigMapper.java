package com.yf.exam.modules.weather.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yf.exam.modules.weather.entity.WeatherGradingConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 天气预报判卷配置Mapper接口
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Mapper
public interface WeatherGradingConfigMapper extends BaseMapper<WeatherGradingConfig> {

    /**
     * 根据配置键获取配置
     *
     * @param configKey 配置键
     * @return 配置对象
     */
    @Select("SELECT * FROM el_weather_grading_config WHERE config_key = #{configKey} AND is_enabled = 1")
    WeatherGradingConfig selectByKey(@Param("configKey") String configKey);

    /**
     * 根据配置组获取配置列表
     *
     * @param configGroup 配置组
     * @return 配置列表
     */
    @Select("SELECT * FROM el_weather_grading_config WHERE config_group = #{configGroup} AND is_enabled = 1 ORDER BY sort_order ASC")
    List<WeatherGradingConfig> selectByGroup(@Param("configGroup") String configGroup);

    /**
     * 获取所有有效配置
     *
     * @return 配置列表
     */
    @Select("SELECT * FROM el_weather_grading_config WHERE is_enabled = 1 ORDER BY config_group, sort_order ASC")
    List<WeatherGradingConfig> selectAllEnabled();

    /**
     * 更新配置值
     *
     * @param configKey 配置键
     * @param configValue 配置值
     * @param modifierId 修改人ID
     * @return 影响行数
     */
    @Update("UPDATE el_weather_grading_config SET config_value = #{configValue}, " +
            "update_time = NOW(), modifier_id = #{modifierId} " +
            "WHERE config_key = #{configKey}")
    int updateValueByKey(@Param("configKey") String configKey, 
                        @Param("configValue") String configValue,
                        @Param("modifierId") String modifierId);

    /**
     * 批量更新配置启用状态
     *
     * @param configKeys 配置键列表
     * @param isEnabled 是否启用
     * @param modifierId 修改人ID
     * @return 影响行数
     */
    @Update("<script>" +
            "UPDATE el_weather_grading_config SET is_enabled = #{isEnabled}, " +
            "update_time = NOW(), modifier_id = #{modifierId} " +
            "WHERE config_key IN " +
            "<foreach collection='configKeys' item='key' open='(' separator=',' close=')'>" +
            "#{key}" +
            "</foreach>" +
            "</script>")
    int batchUpdateEnabled(@Param("configKeys") List<String> configKeys, 
                          @Param("isEnabled") Boolean isEnabled,
                          @Param("modifierId") String modifierId);

    /**
     * 检查配置键是否存在
     *
     * @param configKey 配置键
     * @return 存在数量
     */
    @Select("SELECT COUNT(*) FROM el_weather_grading_config WHERE config_key = #{configKey}")
    int countByKey(@Param("configKey") String configKey);

    /**
     * 获取配置组统计
     *
     * @return 配置组统计信息
     */
    @Select("SELECT config_group, COUNT(*) as config_count, " +
            "COUNT(CASE WHEN is_enabled = 1 THEN 1 END) as enabled_count " +
            "FROM el_weather_grading_config " +
            "GROUP BY config_group")
    List<java.util.Map<String, Object>> selectGroupStatistics();

    /**
     * 重置配置为默认值
     *
     * @param configKey 配置键
     * @param modifierId 修改人ID
     * @return 影响行数
     */
    @Update("UPDATE el_weather_grading_config SET config_value = default_value, " +
            "update_time = NOW(), modifier_id = #{modifierId} " +
            "WHERE config_key = #{configKey}")
    int resetToDefault(@Param("configKey") String configKey, @Param("modifierId") String modifierId);
} 