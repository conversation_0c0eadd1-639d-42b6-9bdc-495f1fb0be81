package com.yf.exam.modules.weather.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <p>
 * 提交历史个例考试请求DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@ApiModel(value = "WeatherHistoryExamSubmitReqDTO", description = "提交历史个例考试请求DTO")
public class WeatherHistoryExamSubmitReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "考试ID", required = true)
    @NotBlank(message = "考试ID不能为空")
    private String examId;

    @ApiModelProperty(value = "题目ID", required = true)
    @NotBlank(message = "题目ID不能为空")
    private String questionId;

    @ApiModelProperty(value = "答案ID")
    private String answerId;

    @ApiModelProperty(value = "最终进度百分比")
    private Integer finalProgress;

    @ApiModelProperty(value = "提交时间")
    private String submitTime;

    @ApiModelProperty(value = "总用时（秒）")
    private Integer totalTimeSpent;

    @ApiModelProperty(value = "答案数据（当answerId为空时使用）")
    private Object answerData;
}
