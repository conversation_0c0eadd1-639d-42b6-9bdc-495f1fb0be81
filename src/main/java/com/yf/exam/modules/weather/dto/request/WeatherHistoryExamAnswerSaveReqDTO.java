package com.yf.exam.modules.weather.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 保存历史个例考试答案请求DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@ApiModel(value = "WeatherHistoryExamAnswerSaveReqDTO", description = "保存历史个例考试答案请求DTO")
public class WeatherHistoryExamAnswerSaveReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "答案ID（更新时传入）")
    private String answerId;

    @ApiModelProperty(value = "考试ID", required = true)
    @NotBlank(message = "考试ID不能为空")
    private String examId;

    @ApiModelProperty(value = "题目ID", required = true)
    @NotBlank(message = "题目ID不能为空")
    private String questionId;

    @ApiModelProperty(value = "第一部分：降水分级落区预报答案")
    private Map<String, Object> precipitationAnswer;

    @ApiModelProperty(value = "第二部分：灾害性天气预报答案")
    private Map<String, Object> weatherAnswer;

    @ApiModelProperty(value = "整体进度百分比")
    private Integer overallProgress;

    @ApiModelProperty(value = "答题时间")
    private String answerTime;

    @ApiModelProperty(value = "已用时间（秒）")
    private Integer timeSpent;
}
