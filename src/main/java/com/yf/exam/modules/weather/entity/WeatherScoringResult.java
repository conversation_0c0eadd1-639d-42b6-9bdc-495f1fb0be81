package com.yf.exam.modules.weather.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yf.exam.config.CustomJacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 天气预报评分结果实体类
 * 
 * <AUTHOR>
 * @since 2024-12-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("el_weather_scoring_result")
public class WeatherScoringResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 结果ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 答案ID
     */
    @TableField("answer_id")
    private String answerId;

    /**
     * 考试ID
     */
    @TableField("exam_id")
    private String examId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 题目ID
     */
    @TableField("question_id")
    private String questionId;

    /**
     * 是否评分成功
     */
    @TableField("is_success")
    private Boolean isSuccess;

    /**
     * 最终得分
     */
    @TableField("final_score")
    private Double finalScore;

    /**
     * 详细结果JSON
     */
    @TableField(value = "detail_results", typeHandler = CustomJacksonTypeHandler.class)
    private Map<String, Object> detailResults;

    /**
     * 总得分
     */
    @TableField("total_score")
    private BigDecimal totalScore;

    /**
     * 满分
     */
    @TableField("max_score")
    private BigDecimal maxScore;

    /**
     * 得分率
     */
    @TableField("score_percentage")
    private BigDecimal scorePercentage;

    /**
     * 评分站点数量
     */
    @TableField("station_count")
    private Integer stationCount;

    /**
     * 分站得分详情(JSON格式)
     */
    @TableField(value = "station_scores", typeHandler = CustomJacksonTypeHandler.class)
    private Map<String, Object> stationScores;

    /**
     * 分要素得分详情(JSON格式)
     */
    @TableField(value = "element_scores", typeHandler = CustomJacksonTypeHandler.class)
    private Map<String, Object> elementScores;

    /**
     * 错误分析(JSON格式)
     */
    @TableField(value = "error_analysis", typeHandler = CustomJacksonTypeHandler.class)
    private Map<String, Object> errorAnalysis;

    /**
     * 评分时间
     */
    @TableField("scoring_time")
    private Date scoringTime;

    /**
     * 使用的配置版本
     */
    @TableField("config_version")
    private String configVersion;

    /**
     * 使用的配置ID
     */
    @TableField("config_id")
    private String configId;

    /**
     * 评分耗时(毫秒)
     */
    @TableField("scoring_duration")
    private Integer scoringDuration;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 计算得分率
     */
    public void calculateScorePercentage() {
        if (totalScore != null && maxScore != null && maxScore.compareTo(BigDecimal.ZERO) > 0) {
            this.scorePercentage = totalScore.divide(maxScore, 4, BigDecimal.ROUND_HALF_UP)
                                             .multiply(new BigDecimal("100"));
        } else {
            this.scorePercentage = BigDecimal.ZERO;
        }
    }

    /**
     * 获取特定要素的得分
     * 
     * @param elementName 要素名称
     * @return 要素得分
     */
    public BigDecimal getElementScore(String elementName) {
        if (elementScores == null) {
            return BigDecimal.ZERO;
        }
        
        Object score = elementScores.get(elementName);
        if (score instanceof Number) {
            return new BigDecimal(score.toString());
        }
        return BigDecimal.ZERO;
    }

    /**
     * 设置特定要素的得分
     * 
     * @param elementName 要素名称
     * @param score 要素得分
     */
    public void setElementScore(String elementName, BigDecimal score) {
        if (elementScores == null) {
            elementScores = new java.util.HashMap<>();
        }
        elementScores.put(elementName, score);
    }

    /**
     * 添加错误分析信息
     * 
     * @param category 错误类别
     * @param message 错误信息
     */
    public void addErrorAnalysis(String category, String message) {
        if (errorAnalysis == null) {
            errorAnalysis = new java.util.HashMap<>();
        }
        
        @SuppressWarnings("unchecked")
        List<String> errors = (List<String>) errorAnalysis.computeIfAbsent(category, 
                                                            k -> new java.util.ArrayList<>());
        errors.add(message);
    }

    /**
     * 获取评分等级
     * 
     * @return 评分等级（优秀、良好、及格、不及格）
     */
    public String getGrade() {
        if (scorePercentage == null) {
            calculateScorePercentage();
        }
        
        if (scorePercentage == null) {
            return "未评分";
        }
        
        BigDecimal percentage = scorePercentage;
        if (percentage.compareTo(new BigDecimal("90")) >= 0) {
            return "优秀";
        } else if (percentage.compareTo(new BigDecimal("80")) >= 0) {
            return "良好";
        } else if (percentage.compareTo(new BigDecimal("60")) >= 0) {
            return "及格";
        } else {
            return "不及格";
        }
    }

    /**
     * 是否通过评分
     * 
     * @return 是否通过（得分率>=60%）
     */
    public boolean isPassed() {
        if (scorePercentage == null) {
            calculateScorePercentage();
        }
        
        return scorePercentage != null && scorePercentage.compareTo(new BigDecimal("60")) >= 0;
    }
} 