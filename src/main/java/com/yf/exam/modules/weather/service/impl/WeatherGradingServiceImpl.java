package com.yf.exam.modules.weather.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yf.exam.modules.user.UserUtils;
import com.yf.exam.modules.weather.entity.WeatherHistoryExamAnswer;
import com.yf.exam.modules.weather.entity.WeatherScoringResult;
import com.yf.exam.modules.weather.mapper.WeatherHistoryExamAnswerMapper;
import com.yf.exam.modules.weather.scoring.engine.ScoringEngineResult;
import com.yf.exam.modules.weather.scoring.engine.WeatherScoringEngine;
import com.yf.exam.modules.weather.service.WeatherGradingConfigService;
import com.yf.exam.modules.weather.service.WeatherGradingService;
import com.yf.exam.modules.weather.service.WeatherHistoryExamAnswerService;
import com.yf.exam.modules.weather.scoring.service.WeatherScoringBatchTaskService;
import com.yf.exam.modules.weather.scoring.service.WeatherScoringResultService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 天气预报判卷管理服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Slf4j
@Service
public class WeatherGradingServiceImpl implements WeatherGradingService {

    @Autowired
    private WeatherGradingConfigService gradingConfigService;

    @Autowired
    private WeatherScoringEngine scoringEngine;

    @Autowired
    private WeatherScoringResultService resultService;

    @Autowired
    private WeatherScoringBatchTaskService batchTaskService;

    @Autowired
    private WeatherHistoryExamAnswerService answerService;

    @Autowired
    private WeatherHistoryExamAnswerMapper weatherHistoryExamAnswerMapper;

    // ==================== 单个判卷功能 ====================

    @Override
    public ScoringEngineResult gradeAnswer(String answerId) {
        return gradeAnswer(answerId, null);
    }

    @Override
    public ScoringEngineResult gradeAnswer(String answerId, String configId) {
        if (!StringUtils.hasText(answerId)) {
            return ScoringEngineResult.failure("答案ID不能为空");
        }

        try {
            log.info("开始单个判卷，答案ID：{}，配置ID：{}", answerId, configId);
            
            // 检查答案是否存在且已提交
            WeatherHistoryExamAnswer answer = answerService.getById(answerId);
            if (answer == null) {
                return ScoringEngineResult.failure("答案记录不存在：" + answerId);
            }
            
            if (answer.getAnswerStatus() == null || answer.getAnswerStatus() != 1) {
                return ScoringEngineResult.failure("答案未提交，无法判卷");
            }

            // 检查是否已有判卷结果（非强制重新判卷）
            if (isAnswerGraded(answerId)) {
                WeatherScoringResult existingResult = resultService.getByAnswerId(answerId);
                if (existingResult != null) {
                    log.info("答案已有判卷结果，返回现有结果，答案ID：{}", answerId);
                    return ScoringEngineResult.success(
                        existingResult.getFinalScore(), 
                        existingResult.getId(), 
                        null
                    );
                }
            }

            // 调用评分引擎
            ScoringEngineResult result = scoringEngine.calculateSingleScore(answerId, configId);
            
            log.info("单个判卷完成，答案ID：{}，成功：{}，得分：{}", 
                    answerId, result.isSuccess(), result.getScore());
            
            return result;
            
        } catch (Exception e) {
            log.error("单个判卷异常，答案ID：{}", answerId, e);
            return ScoringEngineResult.failure("判卷异常：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ScoringEngineResult regradeAnswer(String answerId, String configId) {
        if (!StringUtils.hasText(answerId)) {
            return ScoringEngineResult.failure("答案ID不能为空");
        }

        try {
            log.info("开始重新判卷，答案ID：{}，配置ID：{}", answerId, configId);
            
            // 检查答案是否存在且已提交
            WeatherHistoryExamAnswer answer = answerService.getById(answerId);
            if (answer == null) {
                return ScoringEngineResult.failure("答案记录不存在：" + answerId);
            }
            
            if (answer.getAnswerStatus() == null || answer.getAnswerStatus() != 1) {
                return ScoringEngineResult.failure("答案未提交，无法重新判卷");
            }

            // 删除现有的判卷结果（如果有）
            boolean deletedExisting = resultService.deleteByAnswerId(answerId);
            if (deletedExisting) {
                log.info("删除现有判卷结果，答案ID：{}", answerId);
            }

            // 执行重新判卷
            ScoringEngineResult result = scoringEngine.calculateSingleScore(answerId, configId);
            
            log.info("重新判卷完成，答案ID：{}，成功：{}，得分：{}", 
                    answerId, result.isSuccess(), result.getScore());
            
            return result;
            
        } catch (Exception e) {
            log.error("重新判卷异常，答案ID：{}", answerId, e);
            return ScoringEngineResult.failure("重新判卷异常：" + e.getMessage());
        }
    }

    @Override
    public boolean isAnswerGraded(String answerId) {
        if (!StringUtils.hasText(answerId)) {
            return false;
        }

        try {
            WeatherScoringResult result = resultService.getByAnswerId(answerId);
            return result != null && Boolean.TRUE.equals(result.getIsSuccess());
        } catch (Exception e) {
            log.error("检查判卷状态异常，答案ID：{}", answerId, e);
            return false;
        }
    }

    // ==================== 批量判卷功能 ====================

    @Override
    public String batchGradeByExam(String examId, String configId, boolean onlyUngraded) {
        if (!StringUtils.hasText(examId)) {
            log.warn("考试ID不能为空");
            return null;
        }

        try {
            log.info("开始按考试批量判卷，考试ID：{}，配置ID：{}，仅未判卷：{}", examId, configId, onlyUngraded);
            
            // 构建批量任务名称
            String taskName = "按考试批量判卷-" + examId;
            if (onlyUngraded) {
                taskName += "-仅未判卷";
            }
            
            // 查询答案ID列表（这里简化实现，实际应该从数据库查询）
            List<String> answerIds = getAnswerIdsByExam(examId, onlyUngraded);
            
            if (answerIds.isEmpty()) {
                log.warn("未找到符合条件的答案，考试ID：{}", examId);
                return null;
            }

            // 调用批量任务
            return scoringEngine.calculateBatchScore(answerIds, configId, taskName);
            
        } catch (Exception e) {
            log.error("按考试批量判卷失败，考试ID：{}", examId, e);
            return null;
        }
    }

    @Override
    public String batchGradeByQuestion(String questionId, String configId, boolean onlyUngraded) {
        if (!StringUtils.hasText(questionId)) {
            log.warn("题目ID不能为空");
            return null;
        }

        try {
            log.info("开始按题目批量判卷，题目ID：{}，配置ID：{}，仅未判卷：{}", questionId, configId, onlyUngraded);
            
            String taskName = "按题目批量判卷-" + questionId;
            if (onlyUngraded) {
                taskName += "-仅未判卷";
            }
            
            List<String> answerIds = getAnswerIdsByQuestion(questionId, onlyUngraded);
            
            if (answerIds.isEmpty()) {
                log.warn("未找到符合条件的答案，题目ID：{}", questionId);
                return null;
            }

            return scoringEngine.calculateBatchScore(answerIds, configId, taskName);
            
        } catch (Exception e) {
            log.error("按题目批量判卷失败，题目ID：{}", questionId, e);
            return null;
        }
    }

    @Override
    public String batchGradeByUsers(List<String> userIds, String examId, String configId, boolean onlyUngraded) {
        if (userIds == null || userIds.isEmpty()) {
            log.warn("用户ID列表不能为空");
            return null;
        }

        try {
            log.info("开始按用户批量判卷，用户数：{}，考试ID：{}，配置ID：{}，仅未判卷：{}", 
                    userIds.size(), examId, configId, onlyUngraded);
            
            String taskName = "按用户批量判卷-" + userIds.size() + "人";
            if (StringUtils.hasText(examId)) {
                taskName += "-考试" + examId;
            }
            if (onlyUngraded) {
                taskName += "-仅未判卷";
            }
            
            List<String> answerIds = getAnswerIdsByUsers(userIds, examId, onlyUngraded);
            
            if (answerIds.isEmpty()) {
                log.warn("未找到符合条件的答案，用户数：{}", userIds.size());
                return null;
            }

            return scoringEngine.calculateBatchScore(answerIds, configId, taskName);
            
        } catch (Exception e) {
            log.error("按用户批量判卷失败，用户数：{}", userIds.size(), e);
            return null;
        }
    }

    @Override
    public String batchGradeByAnswers(List<String> answerIds, String configId, boolean forceRegrade) {
        if (answerIds == null || answerIds.isEmpty()) {
            log.warn("答案ID列表不能为空");
            return null;
        }

        try {
            log.info("开始按答案列表批量判卷，答案数：{}，配置ID：{}，强制重判：{}", 
                    answerIds.size(), configId, forceRegrade);
            
            String taskName = "按答案列表批量判卷-" + answerIds.size() + "个";
            if (forceRegrade) {
                taskName += "-强制重判";
            }
            
            // 如果不是强制重判，过滤掉已判卷的答案
            List<String> targetAnswerIds = answerIds;
            if (!forceRegrade) {
                targetAnswerIds = answerIds.stream()
                        .filter(answerId -> !isAnswerGraded(answerId))
                        .collect(Collectors.toList());
                        
                log.info("过滤已判卷答案后，待判卷数量：{}", targetAnswerIds.size());
            }
            
            if (targetAnswerIds.isEmpty()) {
                log.warn("没有需要判卷的答案");
                return null;
            }

            return scoringEngine.calculateBatchScore(targetAnswerIds, configId, taskName);
            
        } catch (Exception e) {
            log.error("按答案列表批量判卷失败，答案数：{}", answerIds.size(), e);
            return null;
        }
    }

    @Override
    public String batchGradeByTimeRange(LocalDateTime startTime, LocalDateTime endTime, 
                                       String examId, String configId, boolean onlyUngraded) {
        if (startTime == null || endTime == null) {
            log.warn("开始时间和结束时间不能为空");
            return null;
        }

        try {
            log.info("开始按时间范围批量判卷，时间：{} - {}，考试ID：{}，配置ID：{}，仅未判卷：{}", 
                    startTime, endTime, examId, configId, onlyUngraded);
            
            String taskName = String.format("按时间范围批量判卷-%s至%s", 
                    startTime.toLocalDate(), endTime.toLocalDate());
            if (StringUtils.hasText(examId)) {
                taskName += "-考试" + examId;
            }
            if (onlyUngraded) {
                taskName += "-仅未判卷";
            }
            
            List<String> answerIds = getAnswerIdsByTimeRange(startTime, endTime, examId, onlyUngraded);
            
            if (answerIds.isEmpty()) {
                log.warn("指定时间范围内未找到符合条件的答案");
                return null;
            }

            return scoringEngine.calculateBatchScore(answerIds, configId, taskName);
            
        } catch (Exception e) {
            log.error("按时间范围批量判卷失败", e);
            return null;
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 根据考试ID获取答案ID列表
     */
    private List<String> getAnswerIdsByExam(String examId, boolean onlyUngraded) {
        // TODO: 实现具体的查询逻辑
        // 这里应该调用相应的Mapper方法查询数据库
        log.info("查询考试答案ID列表，考试ID：{}，仅未判卷：{}", examId, onlyUngraded);
        return new ArrayList<>();
    }

    /**
     * 根据题目ID获取答案ID列表
     */
    private List<String> getAnswerIdsByQuestion(String questionId, boolean onlyUngraded) {
        log.info("查询题目答案ID列表，题目ID：{}，仅未判卷：{}", questionId, onlyUngraded);
        return new ArrayList<>();
    }

    /**
     * 根据用户ID列表获取答案ID列表
     */
    private List<String> getAnswerIdsByUsers(List<String> userIds, String examId, boolean onlyUngraded) {
        log.info("查询用户答案ID列表，用户数：{}，考试ID：{}，仅未判卷：{}", userIds.size(), examId, onlyUngraded);
        return new ArrayList<>();
    }

    /**
     * 根据时间范围获取答案ID列表
     */
    private List<String> getAnswerIdsByTimeRange(LocalDateTime startTime, LocalDateTime endTime, 
                                                String examId, boolean onlyUngraded) {
        log.info("查询时间范围答案ID列表，时间：{} - {}，考试ID：{}，仅未判卷：{}", 
                startTime, endTime, examId, onlyUngraded);
        return new ArrayList<>();
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        try {
            return UserUtils.getUserId(false);
        } catch (Exception e) {
            return "system";
        }
    }

    // ==================== 任务管理功能（基础实现） ====================

    @Override
    public Map<String, Object> getBatchTaskStatus(String taskId) {
        Map<String, Object> status = new HashMap<>();
        try {
            // TODO: 调用batchTaskService获取任务状态
            status.put("taskId", taskId);
            status.put("status", "UNKNOWN");
            status.put("message", "功能开发中");
        } catch (Exception e) {
            log.error("获取批量任务状态失败，任务ID：{}", taskId, e);
            status.put("error", e.getMessage());
        }
        return status;
    }

    @Override
    public Map<String, Object> getBatchTaskProgress(String taskId) {
        Map<String, Object> progress = new HashMap<>();
        try {
            // TODO: 调用batchTaskService获取详细进度
            progress.put("taskId", taskId);
            progress.put("totalCount", 0);
            progress.put("processedCount", 0);
            progress.put("successCount", 0);
            progress.put("failCount", 0);
            progress.put("message", "功能开发中");
        } catch (Exception e) {
            log.error("获取批量任务进度失败，任务ID：{}", taskId, e);
            progress.put("error", e.getMessage());
        }
        return progress;
    }

    @Override
    public boolean stopBatchTask(String taskId) {
        try {
            log.info("停止批量任务，任务ID：{}", taskId);
            // TODO: 调用batchTaskService停止任务
            return false; // 功能开发中
        } catch (Exception e) {
            log.error("停止批量任务失败，任务ID：{}", taskId, e);
            return false;
        }
    }

    @Override
    public boolean restartBatchTask(String taskId) {
        try {
            log.info("重启批量任务，任务ID：{}", taskId);
            // TODO: 调用batchTaskService重启任务
            return false; // 功能开发中
        } catch (Exception e) {
            log.error("重启批量任务失败，任务ID：{}", taskId, e);
            return false;
        }
    }

    @Override
    public List<Map<String, Object>> getRunningBatchTasks() {
        try {
            // TODO: 调用batchTaskService获取运行中的任务
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("获取运行中批量任务失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getUserTaskHistory(String userId, int limit) {
        try {
            // TODO: 调用batchTaskService获取用户任务历史
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("获取用户任务历史失败，用户ID：{}", userId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<String> getBatchTaskLogs(String taskId) {
        try {
            // TODO: 调用batchTaskService获取任务日志
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("获取批量任务日志失败，任务ID：{}", taskId, e);
            return new ArrayList<>();
        }
    }

    // ==================== 结果查询功能（基础实现） ====================

    @Override
    public Map<String, Object> getGradingResult(String answerId) {
        Map<String, Object> result = new HashMap<>();
        try {
            // TODO: 调用resultService获取判卷结果详情
            result.put("answerId", answerId);
            result.put("message", "功能开发中");
        } catch (Exception e) {
            log.error("获取判卷结果失败，答案ID：{}", answerId, e);
            result.put("error", e.getMessage());
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> getUserGradingResults(String userId, String examId) {
        try {
            // TODO: 调用resultService获取用户判卷结果列表
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("获取用户判卷结果失败，用户ID：{}，考试ID：{}", userId, examId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> getExamGradingStatistics(String examId) {
        Map<String, Object> stats = new HashMap<>();
        try {
            // TODO: 调用相关服务获取考试判卷统计
            stats.put("examId", examId);
            stats.put("message", "功能开发中");
        } catch (Exception e) {
            log.error("获取考试判卷统计失败，考试ID：{}", examId, e);
            stats.put("error", e.getMessage());
        }
        return stats;
    }

    @Override
    public Map<String, Object> getQuestionGradingStatistics(String questionId) {
        Map<String, Object> stats = new HashMap<>();
        try {
            // TODO: 调用相关服务获取题目判卷统计
            stats.put("questionId", questionId);
            stats.put("message", "功能开发中");
        } catch (Exception e) {
            log.error("获取题目判卷统计失败，题目ID：{}", questionId, e);
            stats.put("error", e.getMessage());
        }
        return stats;
    }

    @Override
    public Map<String, Object> getSystemGradingStatistics() {
        Map<String, Object> stats = new HashMap<>();
        try {
            // TODO: 调用相关服务获取系统级判卷统计
            stats.put("message", "功能开发中");
        } catch (Exception e) {
            log.error("获取系统判卷统计失败", e);
            stats.put("error", e.getMessage());
        }
        return stats;
    }

    // ==================== 查询和筛选功能（基础实现） ====================

    @Override
    public List<Map<String, Object>> getUngradedAnswers(String examId, String questionId, int limit) {
        try {
            log.info("查询未判卷答案，考试ID：{}，题目ID：{}，限制数量：{}", examId, questionId, limit);

            // 构建查询条件
            QueryWrapper<WeatherHistoryExamAnswer> queryWrapper = new QueryWrapper<>();

            // 查询未判卷的答案（grading_status = 0 或 null）
            queryWrapper.and(wrapper -> wrapper.eq("grading_status", 0).or().isNull("grading_status"));

            // 如果指定了考试ID
            if (StringUtils.hasText(examId)) {
                queryWrapper.eq("exam_id", examId);
            }

            // 如果指定了题目ID
            if (StringUtils.hasText(questionId)) {
                queryWrapper.eq("question_id", questionId);
            }

            // 按创建时间倒序排列
            queryWrapper.orderByDesc("create_time");

            // 限制返回数量
            queryWrapper.last("LIMIT " + limit);

            List<WeatherHistoryExamAnswer> answers = weatherHistoryExamAnswerMapper.selectList(queryWrapper);

            // 转换为Map格式
            List<Map<String, Object>> result = new ArrayList<>();
            for (WeatherHistoryExamAnswer answer : answers) {
                Map<String, Object> answerMap = new HashMap<>();
                answerMap.put("id", answer.getId());
                answerMap.put("examId", answer.getExamId());
                answerMap.put("questionId", answer.getQuestionId());
                answerMap.put("userId", answer.getUserId());
                answerMap.put("answerStatus", answer.getAnswerStatus());
                answerMap.put("answerTime", answer.getAnswerTime());
                answerMap.put("submitTime", answer.getSubmitTime());
                answerMap.put("gradingStatus", answer.getGradingStatus() != null ? answer.getGradingStatus() : 0);
                answerMap.put("totalScore", answer.getTotalScore());
                answerMap.put("createTime", answer.getCreateTime());
                answerMap.put("updateTime", answer.getUpdateTime());

                // 添加用户信息（如果需要的话）
                // TODO: 可以通过userId查询用户名等信息

                result.add(answerMap);
            }

            log.info("查询到未判卷答案数量：{}", result.size());
            return result;

        } catch (Exception e) {
            log.error("查询未判卷答案失败，考试ID：{}，题目ID：{}", examId, questionId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getFailedGradingAnswers(String examId, int limit) {
        try {
            log.info("查询判卷失败答案，考试ID：{}，限制数量：{}", examId, limit);

            // 构建查询条件
            QueryWrapper<WeatherHistoryExamAnswer> queryWrapper = new QueryWrapper<>();

            // 查询判卷失败的答案（grading_status = 3）
            queryWrapper.eq("grading_status", 3);

            // 如果指定了考试ID
            if (StringUtils.hasText(examId)) {
                queryWrapper.eq("exam_id", examId);
            }

            // 按创建时间倒序排列
            queryWrapper.orderByDesc("create_time");

            // 限制返回数量
            queryWrapper.last("LIMIT " + limit);

            List<WeatherHistoryExamAnswer> answers = weatherHistoryExamAnswerMapper.selectList(queryWrapper);

            // 转换为Map格式
            List<Map<String, Object>> result = new ArrayList<>();
            for (WeatherHistoryExamAnswer answer : answers) {
                Map<String, Object> answerMap = new HashMap<>();
                answerMap.put("id", answer.getId());
                answerMap.put("examId", answer.getExamId());
                answerMap.put("questionId", answer.getQuestionId());
                answerMap.put("userId", answer.getUserId());
                answerMap.put("answerStatus", answer.getAnswerStatus());
                answerMap.put("answerTime", answer.getAnswerTime());
                answerMap.put("submitTime", answer.getSubmitTime());
                answerMap.put("gradingStatus", answer.getGradingStatus());
                answerMap.put("totalScore", answer.getTotalScore());
                answerMap.put("gradingRemark", answer.getGradingRemark());
                answerMap.put("createTime", answer.getCreateTime());
                answerMap.put("updateTime", answer.getUpdateTime());

                result.add(answerMap);
            }

            log.info("查询到判卷失败答案数量：{}", result.size());
            return result;

        } catch (Exception e) {
            log.error("查询判卷失败答案失败，考试ID：{}", examId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getAnswersNeedRegrade(String examId, String configId, int limit) {
        try {
            // TODO: 查询需要重新判卷的答案
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("查询需要重新判卷答案失败，考试ID：{}，配置ID：{}", examId, configId, e);
            return new ArrayList<>();
        }
    }

    // ==================== 系统维护功能（基础实现） ====================

    @Override
    public int cleanupExpiredResults(int retentionDays) {
        try {
            // TODO: 调用相关服务清理过期结果
            log.info("清理过期判卷结果，保留天数：{}", retentionDays);
            return 0; // 功能开发中
        } catch (Exception e) {
            log.error("清理过期判卷结果失败", e);
            return 0;
        }
    }

    @Override
    public int cleanupExpiredTasks(int retentionDays) {
        try {
            // TODO: 调用batchTaskService清理过期任务
            log.info("清理过期批量任务，保留天数：{}", retentionDays);
            return 0; // 功能开发中
        } catch (Exception e) {
            log.error("清理过期批量任务失败", e);
            return 0;
        }
    }

    @Override
    public Map<String, Object> rebuildGradingStatistics(String examId) {
        Map<String, Object> result = new HashMap<>();
        try {
            // TODO: 重建判卷统计数据
            result.put("success", false);
            result.put("message", "功能开发中");
        } catch (Exception e) {
            log.error("重建判卷统计失败，考试ID：{}", examId, e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        return result;
    }

    @Override
    public Map<String, Object> checkGradingSystemHealth() {
        Map<String, Object> health = new HashMap<>();
        try {
            // TODO: 检查判卷系统健康状态
            health.put("status", "UP");
            health.put("message", "系统运行正常");
            health.put("timestamp", new Date());
        } catch (Exception e) {
            log.error("检查判卷系统健康状态失败", e);
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
        }
        return health;
    }

    // ==================== 配置和管理功能（基础实现） ====================

    @Override
    public Map<String, Object> getGradingConfigSummary() {
        Map<String, Object> summary = new HashMap<>();
        try {
            // 获取核心配置
            summary.put("autoGradingEnabled", gradingConfigService.isAutoGradingEnabled());
            summary.put("maxBatchSize", gradingConfigService.getMaxBatchSize());
            summary.put("gradingTimeout", gradingConfigService.getGradingTimeout());
            summary.put("retryAttempts", gradingConfigService.getRetryAttempts());
            summary.put("asyncThreshold", gradingConfigService.getAsyncThreshold());
            summary.put("threadPoolSize", gradingConfigService.getThreadPoolSize());
            summary.put("defaultScoringConfigId", gradingConfigService.getDefaultScoringConfigId());
        } catch (Exception e) {
            log.error("获取判卷配置摘要失败", e);
            summary.put("error", e.getMessage());
        }
        return summary;
    }

    @Override
    public boolean updateGradingConfig(String configKey, String configValue) {
        try {
            return gradingConfigService.setConfigValue(configKey, configValue, getCurrentUserId());
        } catch (Exception e) {
            log.error("更新判卷配置失败，配置键：{}，值：{}", configKey, configValue, e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getGradingPerformanceMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        try {
            // TODO: 获取判卷性能监控数据
            metrics.put("message", "功能开发中");
            metrics.put("timestamp", new Date());
        } catch (Exception e) {
            log.error("获取判卷性能监控数据失败", e);
            metrics.put("error", e.getMessage());
        }
        return metrics;
    }
} 