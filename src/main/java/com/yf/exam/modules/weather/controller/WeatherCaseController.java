package com.yf.exam.modules.weather.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.core.api.dto.BaseIdReqDTO;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.modules.qu.dto.QuDTO;
import com.yf.exam.modules.qu.dto.ext.QuDetailDTO;
import com.yf.exam.modules.qu.dto.ext.QuExamDTO;
import com.yf.exam.modules.qu.dto.request.QuQueryReqDTO;
import com.yf.exam.modules.qu.service.QuService;
import com.yf.exam.modules.weather.dto.request.ResultDetailReqDTO;
import com.yf.exam.modules.weather.service.WeatherExamParticipationService;
import com.yf.exam.core.exception.ServiceException;
import com.yf.exam.modules.user.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 历史个例考试题目控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Api(tags = {"历史个例考试题目"})
@RestController
@RequestMapping("/exam/api/weather/case")
public class WeatherCaseController extends BaseController {

    @Autowired
    private QuService quService;

    @Autowired
    private WeatherExamParticipationService participationService;

    /**
     * 分页查询历史个例题目
     */
    @ApiOperation(value = "分页查询历史个例题目")
    @PostMapping("/paging")
    public ApiRest<IPage<QuDTO>> paging(@RequestBody PagingReqDTO<QuQueryReqDTO> reqDTO) {
        // 限制查询天气预报表格题
        if (reqDTO.getParams() == null) {
            reqDTO.setParams(new QuQueryReqDTO());
        }
        reqDTO.getParams().setQuType(6); // 天气预报表格题

        IPage<QuDTO> page = quService.paging(reqDTO);
        return super.success(page);
    }

    /**
     * 保存历史个例题目
     */
    @ApiOperation(value = "保存历史个例题目")
    @PostMapping("/save")
    public ApiRest saveWeatherCase(@RequestBody QuDetailDTO reqDTO) {
        // 强制设置为天气预报表格题
        reqDTO.setQuType(6);
        quService.save(reqDTO);
        return super.success();
    }

    /**
     * 获取历史个例题目详情（管理端，包含正确答案）
     */
    @ApiOperation(value = "获取历史个例题目详情（管理端）")
    @PostMapping("/detail")
    public ApiRest<QuDetailDTO> getDetail(@RequestBody BaseIdReqDTO reqDTO) {
        QuDetailDTO detail = quService.detail(reqDTO.getId());
        return super.success(detail);
    }

    /**
     * 获取历史个例题目详情（考试专用，不包含正确答案）
     */
    @ApiOperation(value = "获取历史个例题目详情（考试专用）")
    @PostMapping("/exam-detail")
    public ApiRest<QuExamDTO> getExamDetail(@RequestBody BaseIdReqDTO reqDTO) {
        QuExamDTO detail = quService.examDetail(reqDTO.getId());
        return super.success(detail);
    }

    /**
     * 获取历史个例题目详情（结果查看专用，包含正确答案，需验证参与状态）
     */
    @ApiOperation(value = "获取历史个例题目详情（结果查看专用）")
    @PostMapping("/result-detail")
    public ApiRest<QuDetailDTO> getResultDetail(@RequestBody ResultDetailReqDTO reqDTO) {
        String currentUserId = UserUtils.getUserId(true);

        // 验证用户是否已参加该考试
        boolean hasParticipated = participationService.hasUserParticipated(reqDTO.getExamId(), currentUserId);
        if (!hasParticipated) {
            throw new ServiceException("您尚未参加该考试，无法查看结果");
        }

        // 验证用户是否已提交该考试
        boolean hasSubmitted = participationService.hasUserSubmitted(reqDTO.getExamId(), currentUserId);
        if (!hasSubmitted) {
            throw new ServiceException("您尚未提交该考试，无法查看结果");
        }

        // 获取题目详情（包含正确答案）
        QuDetailDTO detail = quService.detail(reqDTO.getQuestionId());
        return super.success(detail);
    }

    /**
     * 删除历史个例题目
     */
    @ApiOperation(value = "删除历史个例题目")
    @PostMapping("/delete")
    public ApiRest deleteWeatherCase(@RequestBody BaseIdReqDTO reqDTO) {
        quService.removeById(reqDTO.getId());
        return super.success();
    }
}
