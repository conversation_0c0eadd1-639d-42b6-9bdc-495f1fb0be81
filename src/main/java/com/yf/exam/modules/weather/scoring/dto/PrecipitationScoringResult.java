package com.yf.exam.modules.weather.scoring.dto;

import lombok.Data;

import java.util.Map;

/**
 * 降水落区评分结果
 *
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
@Data
public class PrecipitationScoringResult {
    
    /** 是否成功 */
    private boolean success;
    
    /** 消息 */
    private String message;
    
    /** 学生TS评分 */
    private Map<String, Double> studentTSScores;
    
    /** CMA-MESO TS评分 */
    private Map<String, Double> cmaMesoTSScores;
    
    /** 基础分 */
    private Map<String, Double> baseScores;
    
    /** 技巧评分 */
    private Map<String, Double> skillScores;
    
    /** 最终评分 */
    private double finalScore;
    
    /** 总站点数 */
    private int totalStations;
    
    /** 详细统计信息 */
    private Map<String, Object> detailStats;

    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Map<String, Double> getStudentTSScores() {
        return studentTSScores;
    }

    public void setStudentTSScores(Map<String, Double> studentTSScores) {
        this.studentTSScores = studentTSScores;
    }

    public Map<String, Double> getCmaMesoTSScores() {
        return cmaMesoTSScores;
    }

    public void setCmaMesoTSScores(Map<String, Double> cmaMesoTSScores) {
        this.cmaMesoTSScores = cmaMesoTSScores;
    }

    public Map<String, Double> getBaseScores() {
        return baseScores;
    }

    public void setBaseScores(Map<String, Double> baseScores) {
        this.baseScores = baseScores;
    }

    public Map<String, Double> getSkillScores() {
        return skillScores;
    }

    public void setSkillScores(Map<String, Double> skillScores) {
        this.skillScores = skillScores;
    }

    public double getFinalScore() {
        return finalScore;
    }

    public void setFinalScore(double finalScore) {
        this.finalScore = finalScore;
    }

    public int getTotalStations() {
        return totalStations;
    }

    public void setTotalStations(int totalStations) {
        this.totalStations = totalStations;
    }

    public Map<String, Object> getDetailStats() {
        return detailStats;
    }

    public void setDetailStats(Map<String, Object> detailStats) {
        this.detailStats = detailStats;
    }

    /**
     * 获取评分详情摘要
     */
    public String getScoringSummary() {
        if (!success) {
            return "评分失败：" + message;
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append("降水落区评分结果：\n");
        summary.append(String.format("最终得分：%.2f分（满分40分）\n", finalScore));
        summary.append(String.format("总站点数：%d个\n", totalStations));
        
        if (studentTSScores != null) {
            summary.append("\n各量级TS评分：\n");
            studentTSScores.forEach((level, score) -> 
                summary.append(String.format("  %s：%.3f\n", level, score)));
        }
        
        if (skillScores != null) {
            summary.append("\n各量级技巧评分：\n");
            skillScores.forEach((level, score) -> 
                summary.append(String.format("  %s：%.3f\n", level, score)));
        }
        
        return summary.toString();
    }
}
