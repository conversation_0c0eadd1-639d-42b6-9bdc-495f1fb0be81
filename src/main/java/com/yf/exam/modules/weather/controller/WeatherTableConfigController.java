package com.yf.exam.modules.weather.controller;

import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.core.api.dto.BaseIdReqDTO;
import com.yf.exam.modules.weather.dto.WeatherTableConfigDTO;
import com.yf.exam.modules.weather.dto.WeatherTableDTO;
import com.yf.exam.modules.weather.service.WeatherTableConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 天气预报表格配置控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Api(tags = {"天气预报表格配置"})
@RestController
@RequestMapping("/exam/api/weather/config")
public class WeatherTableConfigController extends BaseController {

    @Autowired
    private WeatherTableConfigService weatherTableConfigService;

    /**
     * 获取配置详情
     */
    @ApiOperation(value = "获取配置详情")
    @PostMapping("/detail")
    public ApiRest<WeatherTableConfigDTO> getConfigDetail(@RequestBody BaseIdReqDTO reqDTO) {
        WeatherTableConfigDTO config = weatherTableConfigService.getConfigDetail(reqDTO.getId());
        return super.success(config);
    }

    /**
     * 获取表格数据结构
     */
    @ApiOperation(value = "获取表格数据结构")
    @GetMapping("/table-data/{id}")
    public ApiRest<WeatherTableDTO> getTableData(@PathVariable String id) {
        WeatherTableDTO tableData = weatherTableConfigService.getTableData(id);
        return super.success(tableData);
    }

    /**
     * 获取所有启用的配置
     */
    @ApiOperation(value = "获取所有启用的配置")
    @GetMapping("/active")
    public ApiRest<List<WeatherTableConfigDTO>> getActiveConfigs() {
        List<WeatherTableConfigDTO> configs = weatherTableConfigService.getActiveConfigs();
        return super.success(configs);
    }

    /**
     * 根据类型获取配置列表
     */
    @ApiOperation(value = "根据类型获取配置列表")
    @GetMapping("/type/{templateType}")
    public ApiRest<List<WeatherTableConfigDTO>> getConfigsByType(@PathVariable String templateType) {
        List<WeatherTableConfigDTO> configs = weatherTableConfigService.getConfigsByType(templateType);
        return super.success(configs);
    }

    /**
     * 保存配置
     */
    @ApiOperation(value = "保存配置")
    @PostMapping("/save")
    public ApiRest<Boolean> saveConfig(@RequestBody WeatherTableConfigDTO configDTO) {
        boolean result = weatherTableConfigService.saveConfig(configDTO);
        return super.success(result);
    }

    /**
     * 更新配置
     */
    @ApiOperation(value = "更新配置")
    @PostMapping("/update")
    public ApiRest<Boolean> updateConfig(@RequestBody WeatherTableConfigDTO configDTO) {
        boolean result = weatherTableConfigService.updateConfig(configDTO);
        return super.success(result);
    }

    /**
     * 删除配置
     */
    @ApiOperation(value = "删除配置")
    @PostMapping("/delete")
    public ApiRest<Boolean> deleteConfig(@RequestBody BaseIdReqDTO reqDTO) {
        boolean result = weatherTableConfigService.deleteConfig(reqDTO.getId());
        return super.success(result);
    }

    /**
     * 启用/禁用配置
     */
    @ApiOperation(value = "启用/禁用配置")
    @PostMapping("/toggle-active")
    public ApiRest<Boolean> toggleActive(@RequestBody WeatherTableConfigDTO configDTO) {
        boolean result = weatherTableConfigService.toggleActive(configDTO.getId(), configDTO.getIsActive());
        return super.success(result);
    }
}
