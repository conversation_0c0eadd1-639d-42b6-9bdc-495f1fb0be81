package com.yf.exam.modules.weather.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yf.exam.modules.weather.entity.WeatherHistoryExamAnswer;
import com.yf.exam.modules.weather.service.WeatherExamParticipationService;
import com.yf.exam.modules.weather.service.WeatherHistoryExamAnswerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 历史个例考试参与状态验证服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Service
public class WeatherExamParticipationServiceImpl implements WeatherExamParticipationService {

    @Autowired
    private WeatherHistoryExamAnswerService weatherHistoryExamAnswerService;

    @Override
    public boolean hasUserParticipated(String examId, String userId) {
        QueryWrapper<WeatherHistoryExamAnswer> wrapper = new QueryWrapper<>();
        wrapper.eq("exam_id", examId)
               .eq("user_id", userId);
        
        return weatherHistoryExamAnswerService.count(wrapper) > 0;
    }

    @Override
    public boolean hasUserSubmitted(String examId, String userId) {
        QueryWrapper<WeatherHistoryExamAnswer> wrapper = new QueryWrapper<>();
        wrapper.eq("exam_id", examId)
               .eq("user_id", userId)
               .eq("answer_status", 1); // 1表示已提交
        
        return weatherHistoryExamAnswerService.count(wrapper) > 0;
    }

    @Override
    public String getUserAnswerId(String examId, String userId) {
        QueryWrapper<WeatherHistoryExamAnswer> wrapper = new QueryWrapper<>();
        wrapper.eq("exam_id", examId)
               .eq("user_id", userId)
               .orderByDesc("create_time")
               .last("LIMIT 1");
        
        WeatherHistoryExamAnswer answer = weatherHistoryExamAnswerService.getOne(wrapper);
        return answer != null ? answer.getId() : null;
    }
}
