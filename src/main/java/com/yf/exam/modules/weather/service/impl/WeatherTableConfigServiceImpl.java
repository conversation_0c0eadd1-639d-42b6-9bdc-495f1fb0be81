package com.yf.exam.modules.weather.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.exam.modules.weather.dto.WeatherTableConfigDTO;
import com.yf.exam.modules.weather.dto.WeatherTableDTO;
import com.yf.exam.modules.weather.entity.WeatherTableConfig;
import com.yf.exam.modules.weather.mapper.WeatherTableConfigMapper;
import com.yf.exam.modules.weather.service.WeatherTableConfigService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 天气预报表格配置服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Service
public class WeatherTableConfigServiceImpl extends ServiceImpl<WeatherTableConfigMapper, WeatherTableConfig> 
        implements WeatherTableConfigService {

    @Override
    public WeatherTableConfigDTO getConfigDetail(String id) {
        WeatherTableConfig config = this.getById(id);
        if (config == null) {
            return null;
        }
        
        WeatherTableConfigDTO dto = new WeatherTableConfigDTO();
        BeanUtils.copyProperties(config, dto);
        return dto;
    }

    @Override
    public WeatherTableDTO getTableData(String id) {
        WeatherTableConfig config = this.getById(id);
        if (config == null) {
            return null;
        }
        
        WeatherTableDTO tableDTO = new WeatherTableDTO();
        tableDTO.setConfigId(config.getId());
        tableDTO.setTitle(config.getTitle());
        tableDTO.setDescription(config.getDescription());
        tableDTO.setTemplateType(config.getTemplateType());
        tableDTO.setValidationRules(config.getValidationRules());
        tableDTO.setScoringConfig(config.getScoringConfig());
        
        // 解析表格结构
        if (config.getTableSchema() != null) {
            Map<String, Object> schema = config.getTableSchema();
            if (schema.containsKey("columns")) {
                tableDTO.setColumns((List<Map<String, Object>>) schema.get("columns"));
            }
            if (schema.containsKey("rows")) {
                tableDTO.setRows((List<Map<String, Object>>) schema.get("rows"));
            }
        }
        
        return tableDTO;
    }

    @Override
    public List<WeatherTableConfigDTO> getActiveConfigs() {
        QueryWrapper<WeatherTableConfig> wrapper = new QueryWrapper<>();
        wrapper.eq("is_active", true);
        wrapper.orderByDesc("create_time");
        
        List<WeatherTableConfig> configs = this.list(wrapper);
        return convertToDTO(configs);
    }

    @Override
    public List<WeatherTableConfigDTO> getConfigsByType(String templateType) {
        QueryWrapper<WeatherTableConfig> wrapper = new QueryWrapper<>();
        wrapper.eq("template_type", templateType);
        wrapper.eq("is_active", true);
        wrapper.orderByDesc("create_time");
        
        List<WeatherTableConfig> configs = this.list(wrapper);
        return convertToDTO(configs);
    }

    @Override
    public boolean saveConfig(WeatherTableConfigDTO configDTO) {
        WeatherTableConfig config = new WeatherTableConfig();
        BeanUtils.copyProperties(configDTO, config);
        config.setCreateTime(new Date());
        config.setUpdateTime(new Date());
        
        return this.save(config);
    }

    @Override
    public boolean updateConfig(WeatherTableConfigDTO configDTO) {
        WeatherTableConfig config = new WeatherTableConfig();
        BeanUtils.copyProperties(configDTO, config);
        config.setUpdateTime(new Date());
        
        return this.updateById(config);
    }

    @Override
    public boolean deleteConfig(String id) {
        return this.removeById(id);
    }

    @Override
    public boolean toggleActive(String id, Boolean isActive) {
        WeatherTableConfig config = new WeatherTableConfig();
        config.setId(id);
        config.setIsActive(isActive);
        config.setUpdateTime(new Date());
        
        return this.updateById(config);
    }

    /**
     * 转换为DTO列表
     */
    private List<WeatherTableConfigDTO> convertToDTO(List<WeatherTableConfig> configs) {
        List<WeatherTableConfigDTO> dtoList = new ArrayList<>();
        for (WeatherTableConfig config : configs) {
            WeatherTableConfigDTO dto = new WeatherTableConfigDTO();
            BeanUtils.copyProperties(config, dto);
            dtoList.add(dto);
        }
        return dtoList;
    }
}
