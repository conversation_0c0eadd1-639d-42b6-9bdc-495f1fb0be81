package com.yf.exam.modules.weather.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 天气预报表格配置DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WeatherTableConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 配置名称
     */
    private String name;

    /**
     * 模板类型
     */
    private String templateType;

    /**
     * 表格标题
     */
    private String title;

    /**
     * 表格描述
     */
    private String description;

    /**
     * 表格结构配置JSON
     */
    private Map<String, Object> tableSchema;

    /**
     * 数据验证规则JSON
     */
    private Map<String, Object> validationRules;

    /**
     * 评分配置JSON
     */
    private Map<String, Object> scoringConfig;

    /**
     * 是否启用
     */
    private Boolean isActive;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
