package com.yf.exam.modules.weather.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 天气预报判卷配置实体
 *
 * 用于控制自动判卷和批量判卷的各种配置参数
 * 
 * <AUTHOR>
 * @since 2024-12-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("el_weather_grading_config")
public class WeatherGradingConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 配置项键名
     */
    @TableField("config_key")
    private String configKey;

    /**
     * 配置项名称
     */
    @TableField("config_name")
    private String configName;

    /**
     * 配置值
     */
    @TableField("config_value")
    private String configValue;

    /**
     * 配置值类型：STRING, BOOLEAN, INTEGER, DOUBLE
     */
    @TableField("value_type")
    private String valueType;

    /**
     * 配置描述
     */
    @TableField("description")
    private String description;

    /**
     * 配置分组：AUTO_GRADING, BATCH_PROCESSING, SYSTEM
     */
    @TableField("config_group")
    private String configGroup;

    /**
     * 是否启用：0-禁用，1-启用
     */
    @TableField("is_enabled")
    private Boolean isEnabled;

    /**
     * 是否只读：0-可修改，1-只读
     */
    @TableField("is_readonly")
    private Boolean isReadonly;

    /**
     * 默认值
     */
    @TableField("default_value")
    private String defaultValue;

    /**
     * 配置值约束规则（JSON格式）
     */
    @TableField("validation_rules")
    private String validationRules;

    /**
     * 排序顺序
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 最后修改人
     */
    @TableField("modifier_id")
    private String modifierId;

    // ==================== 便捷方法 ====================

    /**
     * 获取布尔类型的配置值
     */
    public Boolean getBooleanValue() {
        if ("BOOLEAN".equals(valueType) && configValue != null) {
            return Boolean.parseBoolean(configValue);
        }
        return null;
    }

    /**
     * 获取整数类型的配置值
     */
    public Integer getIntegerValue() {
        if ("INTEGER".equals(valueType) && configValue != null) {
            try {
                return Integer.parseInt(configValue);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * 获取双精度类型的配置值
     */
    public Double getDoubleValue() {
        if ("DOUBLE".equals(valueType) && configValue != null) {
            try {
                return Double.parseDouble(configValue);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * 获取字符串类型的配置值
     */
    public String getStringValue() {
        return configValue;
    }

    /**
     * 设置配置值（自动处理类型转换）
     */
    public void setTypedValue(Object value) {
        if (value == null) {
            this.configValue = null;
            return;
        }

        switch (valueType) {
            case "BOOLEAN":
                this.configValue = String.valueOf(value);
                break;
            case "INTEGER":
                this.configValue = String.valueOf(value);
                break;
            case "DOUBLE":
                this.configValue = String.valueOf(value);
                break;
            case "STRING":
            default:
                this.configValue = String.valueOf(value);
                break;
        }
    }

    /**
     * 是否为有效配置
     */
    public boolean isValid() {
        return isEnabled != null && isEnabled && configValue != null && !configValue.trim().isEmpty();
    }
} 