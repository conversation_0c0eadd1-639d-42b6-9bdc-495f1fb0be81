package com.yf.exam.modules.weather.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.exam.modules.weather.dto.WeatherTableConfigDTO;
import com.yf.exam.modules.weather.dto.WeatherTableDTO;
import com.yf.exam.modules.weather.entity.WeatherTableConfig;

import java.util.List;

/**
 * <p>
 * 天气预报表格配置服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface WeatherTableConfigService extends IService<WeatherTableConfig> {

    /**
     * 根据ID获取配置详情
     * 
     * @param id 配置ID
     * @return 配置详情
     */
    WeatherTableConfigDTO getConfigDetail(String id);

    /**
     * 根据ID获取表格数据结构
     * 
     * @param id 配置ID
     * @return 表格数据结构
     */
    WeatherTableDTO getTableData(String id);

    /**
     * 获取所有启用的配置
     * 
     * @return 启用的配置列表
     */
    List<WeatherTableConfigDTO> getActiveConfigs();

    /**
     * 根据模板类型获取配置列表
     * 
     * @param templateType 模板类型
     * @return 配置列表
     */
    List<WeatherTableConfigDTO> getConfigsByType(String templateType);

    /**
     * 保存配置
     * 
     * @param configDTO 配置数据
     * @return 保存结果
     */
    boolean saveConfig(WeatherTableConfigDTO configDTO);

    /**
     * 更新配置
     * 
     * @param configDTO 配置数据
     * @return 更新结果
     */
    boolean updateConfig(WeatherTableConfigDTO configDTO);

    /**
     * 删除配置
     * 
     * @param id 配置ID
     * @return 删除结果
     */
    boolean deleteConfig(String id);

    /**
     * 启用/禁用配置
     * 
     * @param id 配置ID
     * @param isActive 是否启用
     * @return 操作结果
     */
    boolean toggleActive(String id, Boolean isActive);
}
