package com.yf.exam.modules.weather.scoring.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.exam.modules.weather.entity.WeatherScoringResult;
import com.yf.exam.modules.weather.mapper.WeatherScoringResultMapper;
import com.yf.exam.modules.weather.scoring.service.WeatherScoringResultService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 天气预报评分结果Service实现类
 * 
 * <AUTHOR>
 * @since 2024-12-21
 */
@Slf4j
@Service
public class WeatherScoringResultServiceImpl extends ServiceImpl<WeatherScoringResultMapper, WeatherScoringResult> 
        implements WeatherScoringResultService {

    @Autowired
    private WeatherScoringResultMapper resultMapper;

    @Override
    public WeatherScoringResult getByAnswerId(String answerId) {
        if (!StringUtils.hasText(answerId)) {
            log.warn("答案ID不能为空");
            return null;
        }

        try {
            return resultMapper.selectByAnswerId(answerId);
        } catch (Exception e) {
            log.error("根据答案ID获取评分结果失败，答案ID：{}", answerId, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByAnswerId(String answerId) {
        if (!StringUtils.hasText(answerId)) {
            log.warn("答案ID不能为空");
            return false;
        }

        try {
            LambdaQueryWrapper<WeatherScoringResult> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(WeatherScoringResult::getAnswerId, answerId);
            
            int count = count(wrapper);
            if (count > 0) {
                boolean removed = remove(wrapper);
                log.info("删除答案相关评分结果，答案ID：{}，删除数量：{}", answerId, count);
                return removed;
            }
            return true;
        } catch (Exception e) {
            log.error("根据答案ID删除评分结果失败，答案ID：{}", answerId, e);
            return false;
        }
    }

    @Override
    public List<WeatherScoringResult> listByBatchTaskId(String batchTaskId) {
        if (!StringUtils.hasText(batchTaskId)) {
            return new ArrayList<>();
        }

        try {
            // 暂时返回空列表，因为WeatherScoringResult实体中暂未有batchTaskId字段
            // 需要根据实际业务需求决定如何关联批量任务和评分结果
            log.warn("listByBatchTaskId功能需要根据实际业务需求完善，批量任务ID：{}", batchTaskId);
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("根据批量任务ID获取评分结果失败，任务ID：{}", batchTaskId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<WeatherScoringResult> listByUserId(String userId) {
        if (!StringUtils.hasText(userId)) {
            return new ArrayList<>();
        }

        try {
            LambdaQueryWrapper<WeatherScoringResult> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(WeatherScoringResult::getUserId, userId)
                   .orderByDesc(WeatherScoringResult::getScoringTime);
            
            return list(wrapper);
        } catch (Exception e) {
            log.error("根据用户ID获取评分结果失败，用户ID：{}", userId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<WeatherScoringResult> listByExamId(String examId) {
        if (!StringUtils.hasText(examId)) {
            return new ArrayList<>();
        }

        try {
            LambdaQueryWrapper<WeatherScoringResult> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(WeatherScoringResult::getExamId, examId)
                   .orderByDesc(WeatherScoringResult::getScoringTime);
            
            return list(wrapper);
        } catch (Exception e) {
            log.error("根据考试ID获取评分结果失败，考试ID：{}", examId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> getUserScoringStats(String userId) {
        Map<String, Object> stats = new HashMap<>();
        
        if (!StringUtils.hasText(userId)) {
            stats.put("error", "用户ID不能为空");
            return stats;
        }

        try {
            List<WeatherScoringResult> results = listByUserId(userId);
            
            if (results.isEmpty()) {
                stats.put("totalCount", 0);
                stats.put("averageScore", 0.0);
                stats.put("maxScore", 0.0);
                stats.put("minScore", 0.0);
                stats.put("successRate", 0.0);
                return stats;
            }

            // 基本统计
            int totalCount = results.size();
            int successCount = (int) results.stream().filter(r -> Boolean.TRUE.equals(r.getIsSuccess())).count();
            
            List<Double> scores = results.stream()
                                        .filter(r -> Boolean.TRUE.equals(r.getIsSuccess()) && r.getFinalScore() != null)
                                        .map(WeatherScoringResult::getFinalScore)
                                        .collect(Collectors.toList());

            stats.put("totalCount", totalCount);
            stats.put("successCount", successCount);
            stats.put("failureCount", totalCount - successCount);
            stats.put("successRate", totalCount > 0 ? (double) successCount / totalCount * 100 : 0.0);

            if (!scores.isEmpty()) {
                double avgScore = scores.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                double maxScore = scores.stream().mapToDouble(Double::doubleValue).max().orElse(0.0);
                double minScore = scores.stream().mapToDouble(Double::doubleValue).min().orElse(0.0);

                stats.put("averageScore", new BigDecimal(avgScore).setScale(2, RoundingMode.HALF_UP).doubleValue());
                stats.put("maxScore", maxScore);
                stats.put("minScore", minScore);
            } else {
                stats.put("averageScore", 0.0);
                stats.put("maxScore", 0.0);
                stats.put("minScore", 0.0);
            }

            // 最近30天的趋势统计
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DAY_OF_MONTH, -30);
            Date thirtyDaysAgo = cal.getTime();

            List<WeatherScoringResult> recentResults = results.stream()
                    .filter(r -> r.getScoringTime() != null && r.getScoringTime().after(thirtyDaysAgo))
                    .collect(Collectors.toList());

            stats.put("recentCount", recentResults.size());
            stats.put("recentSuccessCount", (int) recentResults.stream()
                    .filter(r -> Boolean.TRUE.equals(r.getIsSuccess())).count());

        } catch (Exception e) {
            log.error("获取用户评分统计失败，用户ID：{}", userId, e);
            stats.put("error", "获取统计数据失败：" + e.getMessage());
        }

        return stats;
    }

    @Override
    public Map<String, Object> getExamScoringStats(String examId) {
        Map<String, Object> stats = new HashMap<>();
        
        if (!StringUtils.hasText(examId)) {
            stats.put("error", "考试ID不能为空");
            return stats;
        }

        try {
            // 获取详细结果进行统计
            List<WeatherScoringResult> results = listByExamId(examId);
            
            // 基本统计
            int totalCount = results.size();
            int successCount = (int) results.stream().filter(r -> Boolean.TRUE.equals(r.getIsSuccess())).count();
            
            stats.put("totalCount", totalCount);
            stats.put("successCount", successCount);
            stats.put("failureCount", totalCount - successCount);
            
            if (totalCount > 0) {
                stats.put("successRate", (double) successCount / totalCount * 100);
            } else {
                stats.put("successRate", 0.0);
            }
            
            if (!results.isEmpty()) {
                // 分数分布统计
                Map<String, Integer> scoreDistribution = new HashMap<>();
                scoreDistribution.put("excellent", 0); // >= 90
                scoreDistribution.put("good", 0);      // 80-89
                scoreDistribution.put("fair", 0);      // 70-79
                scoreDistribution.put("poor", 0);      // 60-69
                scoreDistribution.put("fail", 0);      // < 60

                for (WeatherScoringResult result : results) {
                    if (Boolean.TRUE.equals(result.getIsSuccess()) && result.getFinalScore() != null) {
                        double score = result.getFinalScore();
                        if (score >= 90) {
                            scoreDistribution.put("excellent", scoreDistribution.get("excellent") + 1);
                        } else if (score >= 80) {
                            scoreDistribution.put("good", scoreDistribution.get("good") + 1);
                        } else if (score >= 70) {
                            scoreDistribution.put("fair", scoreDistribution.get("fair") + 1);
                        } else if (score >= 60) {
                            scoreDistribution.put("poor", scoreDistribution.get("poor") + 1);
                        } else {
                            scoreDistribution.put("fail", scoreDistribution.get("fail") + 1);
                        }
                    }
                }

                stats.put("scoreDistribution", scoreDistribution);

                // 评分配置版本分布
                Map<String, Integer> configVersionStats = new HashMap<>();
                for (WeatherScoringResult result : results) {
                    String version = result.getConfigVersion();
                    if (StringUtils.hasText(version)) {
                        configVersionStats.put(version, configVersionStats.getOrDefault(version, 0) + 1);
                    }
                }
                stats.put("configVersionStats", configVersionStats);
            }

        } catch (Exception e) {
            log.error("获取考试评分统计失败，考试ID：{}", examId, e);
            stats.put("error", "获取统计数据失败：" + e.getMessage());
        }

        return stats;
    }

    @Override
    public Map<String, Object> getStationScoringStats(String stationCode) {
        Map<String, Object> stats = new HashMap<>();
        
        if (!StringUtils.hasText(stationCode)) {
            stats.put("error", "站点代码不能为空");
            return stats;
        }

        try {
            // 这里应该根据站点代码查询相关的评分结果
            // 由于WeatherScoringResult中暂时没有stationCode字段，先返回空统计
            stats.put("totalCount", 0);
            stats.put("message", "站点统计功能需要根据实际业务需求完善");
            
        } catch (Exception e) {
            log.error("获取站点评分统计失败，站点代码：{}", stationCode, e);
            stats.put("error", "获取统计数据失败：" + e.getMessage());
        }

        return stats;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchSave(List<WeatherScoringResult> results) {
        if (CollectionUtils.isEmpty(results)) {
            return 0;
        }

        try {
            // 验证并设置默认值
            for (WeatherScoringResult result : results) {
                if (result.getScoringTime() == null) {
                    result.setScoringTime(new Date());
                }
                if (result.getIsSuccess() == null) {
                    result.setIsSuccess(false);
                }
            }

            boolean saved = saveBatch(results);
            int savedCount = saved ? results.size() : 0;
            
            log.info("批量保存评分结果完成，总数：{}，成功数：{}", results.size(), savedCount);
            return savedCount;
            
        } catch (Exception e) {
            log.error("批量保存评分结果失败", e);
            return 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(String resultId, boolean isSuccess, String errorMessage) {
        if (!StringUtils.hasText(resultId)) {
            log.warn("结果ID不能为空");
            return false;
        }

        try {
            WeatherScoringResult result = getById(resultId);
            if (result == null) {
                log.warn("评分结果不存在：{}", resultId);
                return false;
            }

            result.setIsSuccess(isSuccess);
            if (!isSuccess && StringUtils.hasText(errorMessage)) {
                // 将错误信息保存到错误分析字段
                Map<String, Object> errorAnalysis = result.getErrorAnalysis();
                if (errorAnalysis == null) {
                    errorAnalysis = new HashMap<>();
                }
                errorAnalysis.put("errorMessage", errorMessage);
                errorAnalysis.put("errorTime", new Date());
                result.setErrorAnalysis(errorAnalysis);
            }
            result.setUpdateTime(new Date());

            boolean updated = updateById(result);
            log.info("更新评分结果状态：{}，成功：{}，错误信息：{}", resultId, isSuccess, errorMessage);
            return updated;
            
        } catch (Exception e) {
            log.error("更新评分结果状态失败，结果ID：{}", resultId, e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getResultDetail(String resultId) {
        Map<String, Object> detail = new HashMap<>();
        
        if (!StringUtils.hasText(resultId)) {
            detail.put("error", "结果ID不能为空");
            return detail;
        }

        try {
            WeatherScoringResult result = getById(resultId);
            if (result == null) {
                detail.put("error", "评分结果不存在");
                return detail;
            }

            // 基本信息
            detail.put("result", result);
            
            // 计算额外信息
            if (result.getTotalScore() != null && result.getMaxScore() != null) {
                if (result.getMaxScore().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal percentage = result.getTotalScore()
                            .divide(result.getMaxScore(), 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100"));
                    detail.put("scorePercentage", percentage.setScale(2, RoundingMode.HALF_UP).doubleValue());
                }
            }

            // 评分等级
            if (Boolean.TRUE.equals(result.getIsSuccess()) && result.getFinalScore() != null) {
                String grade = calculateGrade(result.getFinalScore());
                detail.put("grade", grade);
            }

            // 评分耗时分析
            if (result.getScoringDuration() != null) {
                String durationAnalysis = analyzeDuration(result.getScoringDuration());
                detail.put("durationAnalysis", durationAnalysis);
            }

            return detail;
            
        } catch (Exception e) {
            log.error("获取评分结果详情失败，结果ID：{}", resultId, e);
            detail.put("error", "获取详情失败：" + e.getMessage());
            return detail;
        }
    }

    /**
     * 计算评分等级
     */
    private String calculateGrade(double score) {
        if (score >= 90) {
            return "优秀";
        } else if (score >= 80) {
            return "良好";
        } else if (score >= 70) {
            return "中等";
        } else if (score >= 60) {
            return "及格";
        } else {
            return "不及格";
        }
    }

    /**
     * 分析评分耗时
     */
    private String analyzeDuration(int durationMs) {
        if (durationMs < 1000) {
            return "极快";
        } else if (durationMs < 3000) {
            return "较快";
        } else if (durationMs < 5000) {
            return "正常";
        } else if (durationMs < 10000) {
            return "较慢";
        } else {
            return "很慢";
        }
    }
} 