package com.yf.exam.modules.weather.service;

/**
 * <p>
 * 历史个例考试参与状态验证服务
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface WeatherExamParticipationService {

    /**
     * 验证用户是否已参加指定考试
     * @param examId 考试ID
     * @param userId 用户ID
     * @return true-已参加，false-未参加
     */
    boolean hasUserParticipated(String examId, String userId);

    /**
     * 验证用户是否已提交指定考试
     * @param examId 考试ID
     * @param userId 用户ID
     * @return true-已提交，false-未提交
     */
    boolean hasUserSubmitted(String examId, String userId);

    /**
     * 获取用户在指定考试中的答案ID
     * @param examId 考试ID
     * @param userId 用户ID
     * @return 答案ID，如果未参加则返回null
     */
    String getUserAnswerId(String examId, String userId);
}
