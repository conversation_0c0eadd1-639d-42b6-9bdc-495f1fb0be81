package com.yf.exam.modules.weather.micaps;

import lombok.Data;

/**
 * MDFS文件头信息
 * 用于存储MICAPS MDFS文件的头部信息
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
@Data
public class MdfsFileHeader {
    
    /**
     * 数据类型（1=站点数据，4=格点数据）
     */
    private int dataType;
    
    /**
     * 数据描述
     */
    private String description;
    
    /**
     * 文件编码
     */
    private String encoding;
    
    /**
     * 原始文件头行
     */
    private String rawHeader;
    
    /**
     * 文件头长度（字节数）
     */
    private int headerLength;
    
    /**
     * 数据开始位置（字节偏移）
     */
    private long dataStartOffset;
    
    /**
     * 是否为有效的MDFS文件头
     */
    public boolean isValid() {
        return dataType > 0 && description != null && !description.trim().isEmpty();
    }
    
    /**
     * 获取格式化的头部信息
     */
    public String getFormattedInfo() {
        return String.format("MDFS文件头[类型=%d, 描述=%s, 编码=%s]", 
            dataType, description, encoding);
    }
}
