package com.yf.exam.modules.weather.scoring.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yf.exam.modules.weather.entity.WeatherScoringConfig;
import com.yf.exam.modules.weather.mapper.WeatherScoringConfigMapper;
import com.yf.exam.modules.weather.scoring.service.ScoringConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 天气预报评分配置Service实现类
 * 
 * <AUTHOR>
 * @since 2024-12-21
 */
@Slf4j
@Service
public class ScoringConfigServiceImpl extends ServiceImpl<WeatherScoringConfigMapper, WeatherScoringConfig> 
        implements ScoringConfigService {

    @Autowired
    private WeatherScoringConfigMapper configMapper;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public WeatherScoringConfig getActiveConfig() {
        try {
            return configMapper.selectActiveConfig();
        } catch (Exception e) {
            log.error("获取活跃配置失败", e);
            return null;
        }
    }

    @Override
    public WeatherScoringConfig getConfigByVersion(String version) {
        if (!StringUtils.hasText(version)) {
            log.warn("配置版本号不能为空");
            return null;
        }
        
        try {
            return configMapper.selectByVersion(version);
        } catch (Exception e) {
            log.error("根据版本获取配置失败，版本：{}", version, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveConfig(WeatherScoringConfig config) {
        if (config == null) {
            log.warn("配置对象不能为空");
            return false;
        }

        // 验证配置有效性
        Map<String, Object> validationResult = validateConfig(config);
        Boolean isValid = (Boolean) validationResult.get("valid");
        if (!isValid) {
            log.warn("配置验证失败：{}", validationResult.get("errors"));
            return false;
        }

        try {
            // 设置默认值
            if (config.getId() == null) {
                config.setId(IdWorker.getIdStr());
            }
            if (config.getConfigVersion() == null) {
                config.setConfigVersion("1.0");
            }
            if (config.getIsActive() == null) {
                config.setIsActive(false);
            }
            if (config.getTotalScore() == null) {
                config.setTotalScore(10);
            }
            if (config.getStationWeight() == null) {
                config.setStationWeight(10);
            }

            // 保存或更新
            return saveOrUpdate(config);
        } catch (Exception e) {
            log.error("保存配置失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean activateConfig(String configId) {
        if (!StringUtils.hasText(configId)) {
            log.warn("配置ID不能为空");
            return false;
        }

        try {
            // 检查配置是否存在
            WeatherScoringConfig config = getById(configId);
            if (config == null) {
                log.warn("指定的配置不存在：{}", configId);
                return false;
            }

            // 激活指定配置，同时将其他配置设为非活跃
            int result = configMapper.activateConfig(configId);
            log.info("激活配置成功，ID：{}，影响行数：{}", configId, result);
            return result > 0;
        } catch (Exception e) {
            log.error("激活配置失败，ID：{}", configId, e);
            return false;
        }
    }

    @Override
    public List<WeatherScoringConfig> getConfigHistory(String configName) {
        try {
            if (StringUtils.hasText(configName)) {
                return configMapper.selectConfigHistory(configName);
            } else {
                // 返回所有配置历史
                LambdaQueryWrapper<WeatherScoringConfig> wrapper = new LambdaQueryWrapper<>();
                wrapper.orderByDesc(WeatherScoringConfig::getUpdateTime);
                return list(wrapper);
            }
        } catch (Exception e) {
            log.error("获取配置历史失败，配置名称：{}", configName, e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WeatherScoringConfig createNewVersion(String sourceConfigId, String newVersion, 
                                                String newConfigName, String createBy) {
        if (!StringUtils.hasText(sourceConfigId) || !StringUtils.hasText(newVersion)) {
            log.warn("源配置ID和新版本号不能为空");
            return null;
        }

        try {
            // 获取源配置
            WeatherScoringConfig sourceConfig = getById(sourceConfigId);
            if (sourceConfig == null) {
                log.warn("源配置不存在：{}", sourceConfigId);
                return null;
            }

            // 创建新配置
            WeatherScoringConfig newConfig = new WeatherScoringConfig();
            newConfig.setId(IdWorker.getIdStr());
            newConfig.setConfigName(StringUtils.hasText(newConfigName) ? newConfigName : sourceConfig.getConfigName());
            newConfig.setConfigVersion(newVersion);
            newConfig.setTotalScore(sourceConfig.getTotalScore());
            newConfig.setStationWeight(sourceConfig.getStationWeight());
            newConfig.setElementWeights(sourceConfig.getElementWeights());
            newConfig.setToleranceConfig(sourceConfig.getToleranceConfig());
            newConfig.setIsActive(false); // 新版本默认不激活
            newConfig.setCreateBy(createBy);

            // 检查版本是否已存在
            if (configExists(newConfig.getConfigName(), newVersion)) {
                log.warn("配置版本已存在：{} - {}", newConfig.getConfigName(), newVersion);
                return null;
            }

            // 保存新配置
            boolean saved = saveConfig(newConfig);
            if (saved) {
                log.info("创建新版本配置成功：{} -> {}", sourceConfigId, newConfig.getId());
                return newConfig;
            } else {
                log.error("保存新版本配置失败");
                return null;
            }
        } catch (Exception e) {
            log.error("创建新版本配置失败，源配置：{}，新版本：{}", sourceConfigId, newVersion, e);
            return null;
        }
    }

    @Override
    public Map<String, Object> validateConfig(WeatherScoringConfig config) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        
        try {
            if (config == null) {
                errors.add("配置对象不能为空");
                result.put("valid", false);
                result.put("errors", errors);
                return result;
            }

            // 验证配置名称
            if (!StringUtils.hasText(config.getConfigName())) {
                errors.add("配置名称不能为空");
            }

            // 验证配置版本
            if (!StringUtils.hasText(config.getConfigVersion())) {
                errors.add("配置版本不能为空");
            }

            // 验证总分
            if (config.getTotalScore() == null || config.getTotalScore() <= 0) {
                errors.add("总分必须大于0");
            }

            // 验证要素权重配置
            if (config.getElementWeights() == null || config.getElementWeights().isEmpty()) {
                errors.add("要素权重配置不能为空");
            } else {
                // 验证要素权重JSON格式
                try {
                    String jsonStr = objectMapper.writeValueAsString(config.getElementWeights());
                    objectMapper.readTree(jsonStr); // 验证JSON格式
                    
                    // 验证必要的要素是否存在
                    String[] requiredElements = {"windForce", "windDirection", "minTemperature", 
                                               "maxTemperature", "precipitation", "disasterWeather"};
                    for (String element : requiredElements) {
                        if (!config.getElementWeights().containsKey(element)) {
                            errors.add("缺少必要的评分要素：" + element);
                        }
                    }
                } catch (JsonProcessingException e) {
                    errors.add("要素权重配置JSON格式无效");
                }
            }

            // 验证容差配置
            if (config.getToleranceConfig() != null) {
                try {
                    String jsonStr = objectMapper.writeValueAsString(config.getToleranceConfig());
                    objectMapper.readTree(jsonStr); // 验证JSON格式
                } catch (JsonProcessingException e) {
                    errors.add("容差配置JSON格式无效");
                }
            }

            result.put("valid", errors.isEmpty());
            result.put("errors", errors);
            return result;
            
        } catch (Exception e) {
            log.error("验证配置时发生异常", e);
            errors.add("验证过程中发生异常：" + e.getMessage());
            result.put("valid", false);
            result.put("errors", errors);
            return result;
        }
    }

    @Override
    public Map<String, Object> getConfigUsageStats(String configId) {
        if (!StringUtils.hasText(configId)) {
            return new HashMap<>();
        }

        try {
            // 这里应该调用扩展的Mapper方法来获取使用统计
            // 由于XML中已定义，直接调用即可
            return (Map<String, Object>) configMapper.selectOne(
                new LambdaQueryWrapper<WeatherScoringConfig>()
                    .eq(WeatherScoringConfig::getId, configId)
            );
        } catch (Exception e) {
            log.error("获取配置使用统计失败，配置ID：{}", configId, e);
            return new HashMap<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateStatus(List<String> configIds, Boolean isActive) {
        if (configIds == null || configIds.isEmpty()) {
            log.warn("配置ID列表不能为空");
            return 0;
        }

        try {
            return configMapper.batchUpdateStatus(configIds, isActive);
        } catch (Exception e) {
            log.error("批量更新配置状态失败", e);
            return 0;
        }
    }

    @Override
    public List<String> getAllVersions() {
        try {
            return configMapper.selectAllVersions();
        } catch (Exception e) {
            log.error("获取所有版本失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean configExists(String configName, String configVersion) {
        if (!StringUtils.hasText(configName) || !StringUtils.hasText(configVersion)) {
            return false;
        }

        try {
            int count = configMapper.checkConfigExists(configName, configVersion);
            return count > 0;
        } catch (Exception e) {
            log.error("检查配置是否存在失败，配置名：{}，版本：{}", configName, configVersion, e);
            return false;
        }
    }

    @Override
    public Map<String, Object> compareConfigs(String configId1, String configId2) {
        Map<String, Object> result = new HashMap<>();
        
        if (!StringUtils.hasText(configId1) || !StringUtils.hasText(configId2)) {
            result.put("error", "配置ID不能为空");
            return result;
        }

        try {
            WeatherScoringConfig config1 = getById(configId1);
            WeatherScoringConfig config2 = getById(configId2);

            if (config1 == null) {
                result.put("error", "配置1不存在：" + configId1);
                return result;
            }
            if (config2 == null) {
                result.put("error", "配置2不存在：" + configId2);
                return result;
            }

            result.put("config1", buildConfigSummary(config1));
            result.put("config2", buildConfigSummary(config2));

            // 比较主要字段
            List<String> differences = new ArrayList<>();
            if (!Objects.equals(config1.getTotalScore(), config2.getTotalScore())) {
                differences.add("总分不同");
            }
            if (!Objects.equals(config1.getStationWeight(), config2.getStationWeight())) {
                differences.add("站点权重不同");
            }
            if (!Objects.equals(config1.getElementWeights(), config2.getElementWeights())) {
                differences.add("要素权重配置不同");
            }
            if (!Objects.equals(config1.getToleranceConfig(), config2.getToleranceConfig())) {
                differences.add("容差配置不同");
            }

            result.put("differences", differences);
            result.put("identical", differences.isEmpty());

            return result;
        } catch (Exception e) {
            log.error("比较配置失败，配置1：{}，配置2：{}", configId1, configId2, e);
            result.put("error", "比较配置时发生异常：" + e.getMessage());
            return result;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanupUnusedConfigs(int retentionDays) {
        if (retentionDays <= 0) {
            log.warn("保留天数必须大于0");
            return 0;
        }

        try {
            // 这里需要在Mapper XML中实现cleanupUnusedConfigs方法
            // 暂时使用基本的删除逻辑
            LambdaQueryWrapper<WeatherScoringConfig> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(WeatherScoringConfig::getIsActive, false)
                   .le(WeatherScoringConfig::getCreateTime, 
                       new Date(System.currentTimeMillis() - retentionDays * 24L * 60 * 60 * 1000));
            
            List<WeatherScoringConfig> toDelete = list(wrapper);
            if (toDelete.isEmpty()) {
                return 0;
            }

            boolean removed = removeByIds(toDelete.stream().map(WeatherScoringConfig::getId).collect(Collectors.toList()));
            int cleanupCount = removed ? toDelete.size() : 0;
            log.info("清理无效配置完成，清理数量：{}", cleanupCount);
            return cleanupCount;
        } catch (Exception e) {
            log.error("清理无效配置失败", e);
            return 0;
        }
    }

    @Override
    public String exportConfigAsJson(String configId) {
        if (!StringUtils.hasText(configId)) {
            log.warn("配置ID不能为空");
            return null;
        }

        try {
            WeatherScoringConfig config = getById(configId);
            if (config == null) {
                log.warn("配置不存在：{}", configId);
                return null;
            }

            return objectMapper.writeValueAsString(config);
        } catch (JsonProcessingException e) {
            log.error("导出配置为JSON失败，配置ID：{}", configId, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WeatherScoringConfig importConfigFromJson(String configJson, String createBy) {
        if (!StringUtils.hasText(configJson)) {
            log.warn("配置JSON不能为空");
            return null;
        }

        try {
            WeatherScoringConfig config = objectMapper.readValue(configJson, WeatherScoringConfig.class);
            
            // 重新设置ID和创建信息
            config.setId(IdWorker.getIdStr());
            config.setCreateBy(createBy);
            config.setIsActive(false); // 导入的配置默认不激活

            // 验证并保存配置
            boolean saved = saveConfig(config);
            if (saved) {
                log.info("从JSON导入配置成功，ID：{}", config.getId());
                return config;
            } else {
                log.error("保存导入的配置失败");
                return null;
            }
        } catch (JsonProcessingException e) {
            log.error("从JSON导入配置失败，JSON解析异常", e);
            return null;
        } catch (Exception e) {
            log.error("从JSON导入配置失败", e);
            return null;
        }
    }

    /**
     * 构建配置摘要信息
     */
    private Map<String, Object> buildConfigSummary(WeatherScoringConfig config) {
        Map<String, Object> summary = new HashMap<>();
        summary.put("id", config.getId());
        summary.put("configName", config.getConfigName());
        summary.put("configVersion", config.getConfigVersion());
        summary.put("totalScore", config.getTotalScore());
        summary.put("stationWeight", config.getStationWeight());
        summary.put("isActive", config.getIsActive());
        summary.put("createTime", config.getCreateTime());
        summary.put("updateTime", config.getUpdateTime());
        return summary;
    }
} 