package com.yf.exam.modules.weather.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 天气预报答案表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("el_weather_answer")
public class WeatherAnswer implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 试卷题目ID
     */
    @TableField("paper_qu_id")
    private String paperQuId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 表格配置ID
     */
    @TableField("table_config_id")
    private String tableConfigId;

    /**
     * 表格单元格数据JSON
     */
    @TableField(value = "cell_data", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> cellData;

    /**
     * 答题状态：0-未完成，1-已完成
     */
    @TableField("answer_status")
    private Boolean answerStatus;

    /**
     * 数据验证结果JSON
     */
    @TableField(value = "validation_result", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> validationResult;

    /**
     * 总得分
     */
    @TableField("total_score")
    private BigDecimal totalScore;

    /**
     * 得分详情JSON
     */
    @TableField(value = "score_detail", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> scoreDetails;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;
}
