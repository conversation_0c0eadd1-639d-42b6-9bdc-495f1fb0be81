package com.yf.exam.modules.weather.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.core.api.dto.BaseIdReqDTO;
import com.yf.exam.core.api.dto.BaseStateReqDTO;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.modules.exam.dto.ExamDTO;
import com.yf.exam.modules.exam.dto.request.ExamSaveReqDTO;
import com.yf.exam.modules.exam.dto.response.ExamOnlineRespDTO;
import com.yf.exam.modules.exam.entity.Exam;
import com.yf.exam.modules.exam.service.ExamService;
import com.yf.exam.modules.weather.dto.WeatherHistoryExamAnswerDTO;
import com.yf.exam.modules.weather.dto.request.WeatherHistoryExamAnswerReqDTO;
import com.yf.exam.modules.weather.dto.request.WeatherHistoryExamAnswerSaveReqDTO;
import com.yf.exam.modules.weather.dto.request.WeatherHistoryExamSubmitReqDTO;
import com.yf.exam.modules.weather.dto.request.WeatherExamResultReqDTO;
import com.yf.exam.modules.weather.service.WeatherHistoryExamAnswerService;
import com.yf.exam.modules.weather.service.WeatherHistoryExamResultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 历史个例考试管理控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Api(tags = {"历史个例考试管理"})
@RestController
@RequestMapping("/exam/api/weather/exam")
public class WeatherExamController extends BaseController {

    @Autowired
    private ExamService examService;

    @Autowired
    private WeatherHistoryExamAnswerService weatherHistoryExamAnswerService;

    @Autowired
    private WeatherHistoryExamResultService weatherHistoryExamResultService;

    /**
     * 分页查询历史个例考试
     */
    @ApiOperation(value = "分页查询历史个例考试")
    @PostMapping("/paging")
    public ApiRest<IPage<ExamDTO>> paging(@RequestBody PagingReqDTO<ExamDTO> reqDTO) {
        IPage<ExamDTO> page = examService.paging(reqDTO);
        return super.success(page);
    }

    /**
     * 学生端分页查询历史个例考试
     */
    @ApiOperation(value = "学生端分页查询历史个例考试")
    @PostMapping("/online-paging")
    public ApiRest<IPage<ExamOnlineRespDTO>> onlinePaging(@RequestBody PagingReqDTO<ExamDTO> reqDTO) {
        IPage<ExamOnlineRespDTO> page = examService.onlinePaging(reqDTO);
        return super.success(page);
    }

    /**
     * 保存历史个例考试
     */
    @ApiOperation(value = "保存历史个例考试")
    @PostMapping("/save")
    public ApiRest saveWeatherExam(@RequestBody ExamSaveReqDTO reqDTO) {
        examService.save(reqDTO);
        return super.success();
    }

    /**
     * 获取历史个例考试详情
     */
    @ApiOperation(value = "获取历史个例考试详情")
    @PostMapping("/detail")
    public ApiRest<ExamSaveReqDTO> getDetail(@RequestBody BaseIdReqDTO reqDTO) {
        ExamSaveReqDTO detail = examService.findDetail(reqDTO.getId());
        return super.success(detail);
    }

    /**
     * 删除历史个例考试
     */
    @ApiOperation(value = "删除历史个例考试")
    @PostMapping("/delete")
    public ApiRest deleteWeatherExam(@RequestBody BaseIdReqDTO reqDTO) {
        examService.removeById(reqDTO.getId());
        return super.success();
    }

    /**
     * 获取考试基本信息
     */
    @ApiOperation(value = "获取考试基本信息")
    @PostMapping("/info")
    public ApiRest<ExamDTO> getExamInfo(@RequestBody BaseIdReqDTO reqDTO) {
        ExamDTO exam = examService.findById(reqDTO.getId());
        return super.success(exam);
    }

    /**
     * 更新历史个例考试状态
     */
    @ApiOperation(value = "更新历史个例考试状态")
    @PostMapping("/state")
    public ApiRest updateState(@RequestBody BaseStateReqDTO reqDTO) {
        QueryWrapper<Exam> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(Exam::getId, reqDTO.getIds());
        Exam exam = new Exam();
        exam.setState(reqDTO.getState());
        exam.setUpdateTime(new Date());

        examService.update(exam, wrapper);
        return super.success();
    }

    // ==================== 历史个例考试答案管理 ====================

    /**
     * 保存历史个例考试答案
     */
    @ApiOperation(value = "保存历史个例考试答案")
    @PostMapping("/answer/save")
    public ApiRest<WeatherHistoryExamAnswerDTO> saveAnswer(@Valid @RequestBody WeatherHistoryExamAnswerSaveReqDTO reqDTO) {
        WeatherHistoryExamAnswerDTO result = weatherHistoryExamAnswerService.saveAnswer(reqDTO);
        return super.success(result);
    }

    /**
     * 获取历史个例考试答案
     */
    @ApiOperation(value = "获取历史个例考试答案")
    @PostMapping("/answer/detail")
    public ApiRest<WeatherHistoryExamAnswerDTO> getAnswer(@Valid @RequestBody WeatherHistoryExamAnswerReqDTO reqDTO) {
        WeatherHistoryExamAnswerDTO result = weatherHistoryExamAnswerService.getAnswer(reqDTO);
        return super.success(result);
    }

    /**
     * 提交历史个例考试
     */
    @ApiOperation(value = "提交历史个例考试")
    @PostMapping("/submit")
    public ApiRest<Boolean> submitExam(@Valid @RequestBody WeatherHistoryExamSubmitReqDTO reqDTO) {
        boolean result = weatherHistoryExamAnswerService.submitExam(reqDTO);
        return super.success(result);
    }

    /**
     * 获取考试答题进度
     */
    @ApiOperation(value = "获取考试答题进度")
    @PostMapping("/progress")
    public ApiRest<WeatherHistoryExamAnswerDTO> getProgress(@Valid @RequestBody WeatherHistoryExamAnswerReqDTO reqDTO) {
        WeatherHistoryExamAnswerDTO result = weatherHistoryExamAnswerService.getProgress(reqDTO);
        return super.success(result);
    }

    /**
     * 检查用户是否已经参加过指定考试
     */
    @ApiOperation(value = "检查考试状态")
    @PostMapping("/check-status")
    public ApiRest<WeatherHistoryExamAnswerDTO> checkExamStatus(@Valid @RequestBody BaseIdReqDTO reqDTO) {
        WeatherHistoryExamAnswerDTO result = weatherHistoryExamAnswerService.checkExamStatus(reqDTO.getId());
        return super.success(result);
    }

    // ==================== 历史个例考试结果管理 ====================

    /**
     * 获取历史个例考试结果详情
     */
    @ApiOperation(value = "获取历史个例考试结果详情")
    @PostMapping("/result/detail")
    public ApiRest<Map<String, Object>> getExamResult(@Valid @RequestBody WeatherExamResultReqDTO reqDTO) {
        Map<String, Object> result = weatherHistoryExamResultService.getExamResult(reqDTO.getExamId());
        return super.success(result);
    }

    /**
     * 导出历史个例考试结果
     */
    @ApiOperation(value = "导出历史个例考试结果")
    @PostMapping("/result/export")
    public ApiRest<Map<String, Object>> exportExamResult(@Valid @RequestBody WeatherExamResultReqDTO reqDTO) {
        Map<String, Object> result = weatherHistoryExamResultService.exportExamResult(reqDTO.getExamId());
        return super.success(result);
    }
}
