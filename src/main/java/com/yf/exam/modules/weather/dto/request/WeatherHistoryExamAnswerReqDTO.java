package com.yf.exam.modules.weather.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <p>
 * 获取历史个例考试答案请求DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@ApiModel(value = "WeatherHistoryExamAnswerReqDTO", description = "获取历史个例考试答案请求DTO")
public class WeatherHistoryExamAnswerReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "考试ID", required = true)
    @NotBlank(message = "考试ID不能为空")
    private String examId;

    @ApiModelProperty(value = "题目ID", required = true)
    @NotBlank(message = "题目ID不能为空")
    private String questionId;
}
