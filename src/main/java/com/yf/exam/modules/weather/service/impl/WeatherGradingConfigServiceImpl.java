package com.yf.exam.modules.weather.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.exam.modules.user.UserUtils;
import com.yf.exam.modules.weather.entity.WeatherGradingConfig;
import com.yf.exam.modules.weather.mapper.WeatherGradingConfigMapper;
import com.yf.exam.modules.weather.service.WeatherGradingConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 天气预报判卷配置服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Slf4j
@Service
public class WeatherGradingConfigServiceImpl extends ServiceImpl<WeatherGradingConfigMapper, WeatherGradingConfig>
        implements WeatherGradingConfigService {

    @Autowired
    private WeatherGradingConfigMapper configMapper;

    // 配置缓存，提高性能
    private final Map<String, WeatherGradingConfig> configCache = new ConcurrentHashMap<>();
    
    // 缓存最后更新时间
    private volatile long lastCacheUpdate = 0;
    
    // 缓存有效期（毫秒）
    private static final long CACHE_EXPIRE_TIME = 5 * 60 * 1000; // 5分钟

    @PostConstruct
    public void init() {
        // 系统启动时初始化默认配置
        try {
            initializeDefaultConfigs();
            refreshConfigCache();
            log.info("天气预报判卷配置服务初始化完成");
        } catch (Exception e) {
            log.error("初始化判卷配置服务失败", e);
        }
    }

    // ==================== 核心配置获取方法 ====================

    @Override
    public boolean isAutoGradingEnabled() {
        return getBooleanConfig(AUTO_GRADING_ENABLED, true);
    }

    @Override
    public int getMaxBatchSize() {
        return getIntegerConfig(MAX_BATCH_SIZE, 100);
    }

    @Override
    public int getGradingTimeout() {
        return getIntegerConfig(GRADING_TIMEOUT, 300);
    }

    @Override
    public int getRetryAttempts() {
        return getIntegerConfig(RETRY_ATTEMPTS, 3);
    }

    @Override
    public int getAsyncThreshold() {
        return getIntegerConfig(ASYNC_THRESHOLD, 50);
    }

    @Override
    public int getThreadPoolSize() {
        return getIntegerConfig(THREAD_POOL_SIZE, 10);
    }

    @Override
    public String getDefaultScoringConfigId() {
        return getConfigValue(DEFAULT_SCORING_CONFIG);
    }

    // ==================== 通用配置操作方法 ====================

    @Override
    public String getConfigValue(String configKey) {
        if (!StringUtils.hasText(configKey)) {
            return null;
        }

        WeatherGradingConfig config = getConfigFromCache(configKey);
        return config != null && config.isValid() ? config.getConfigValue() : null;
    }

    @Override
    public boolean getBooleanConfig(String configKey, boolean defaultValue) {
        WeatherGradingConfig config = getConfigFromCache(configKey);
        if (config != null && config.isValid()) {
            Boolean value = config.getBooleanValue();
            return value != null ? value : defaultValue;
        }
        return defaultValue;
    }

    @Override
    public int getIntegerConfig(String configKey, int defaultValue) {
        WeatherGradingConfig config = getConfigFromCache(configKey);
        if (config != null && config.isValid()) {
            Integer value = config.getIntegerValue();
            return value != null ? value : defaultValue;
        }
        return defaultValue;
    }

    @Override
    public double getDoubleConfig(String configKey, double defaultValue) {
        WeatherGradingConfig config = getConfigFromCache(configKey);
        if (config != null && config.isValid()) {
            Double value = config.getDoubleValue();
            return value != null ? value : defaultValue;
        }
        return defaultValue;
    }

    @Override
    public boolean setConfigValue(String configKey, String configValue) {
        return setConfigValue(configKey, configValue, getCurrentUserId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setConfigValue(String configKey, String configValue, String modifierId) {
        if (!StringUtils.hasText(configKey)) {
            log.warn("配置键不能为空");
            return false;
        }

        try {
            // 先验证配置值
            Map<String, Object> validation = validateConfigValue(configKey, configValue);
            if (!Boolean.TRUE.equals(validation.get("valid"))) {
                log.warn("配置值验证失败，配置键：{}，值：{}，错误：{}", 
                        configKey, configValue, validation.get("error"));
                return false;
            }

            int updated = configMapper.updateValueByKey(configKey, configValue, modifierId);
            if (updated > 0) {
                // 清除缓存，强制重新加载
                configCache.remove(configKey);
                log.info("更新配置成功，配置键：{}，新值：{}，修改人：{}", configKey, configValue, modifierId);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            log.error("更新配置失败，配置键：{}，值：{}", configKey, configValue, e);
            throw new RuntimeException("更新配置失败：" + e.getMessage());
        }
    }

    // ==================== 配置组管理方法 ====================

    @Override
    public List<WeatherGradingConfig> getConfigsByGroup(String configGroup) {
        if (!StringUtils.hasText(configGroup)) {
            return new ArrayList<>();
        }

        try {
            return configMapper.selectByGroup(configGroup);
        } catch (Exception e) {
            log.error("根据配置组获取配置失败，组名：{}", configGroup, e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, List<WeatherGradingConfig>> getAllConfigsGrouped() {
        try {
            List<WeatherGradingConfig> allConfigs = configMapper.selectAllEnabled();
            return allConfigs.stream()
                    .collect(Collectors.groupingBy(WeatherGradingConfig::getConfigGroup));
        } catch (Exception e) {
            log.error("获取分组配置失败", e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> getConfigGroupStatistics() {
        try {
            List<Map<String, Object>> statistics = configMapper.selectGroupStatistics();
            
            Map<String, Object> result = new HashMap<>();
            result.put("groups", statistics);
            result.put("totalGroups", statistics.size());
            result.put("totalConfigs", statistics.stream()
                    .mapToInt(stat -> ((Number) stat.get("config_count")).intValue())
                    .sum());
            result.put("enabledConfigs", statistics.stream()
                    .mapToInt(stat -> ((Number) stat.get("enabled_count")).intValue())
                    .sum());
            
            return result;
        } catch (Exception e) {
            log.error("获取配置组统计失败", e);
            return new HashMap<>();
        }
    }

    // ==================== 批量操作方法 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchSetConfigs(Map<String, String> configs, String modifierId) {
        if (configs == null || configs.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        for (Map.Entry<String, String> entry : configs.entrySet()) {
            try {
                if (setConfigValue(entry.getKey(), entry.getValue(), modifierId)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量设置配置失败，配置键：{}，值：{}", entry.getKey(), entry.getValue(), e);
            }
        }

        log.info("批量设置配置完成，成功：{}个，总数：{}个", successCount, configs.size());
        return successCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchSetEnabled(List<String> configKeys, boolean isEnabled, String modifierId) {
        if (configKeys == null || configKeys.isEmpty()) {
            return 0;
        }

        try {
            int updated = configMapper.batchUpdateEnabled(configKeys, isEnabled, modifierId);
            
            // 清除相关缓存
            configKeys.forEach(configCache::remove);
            
            log.info("批量{}配置完成，影响{}个配置", isEnabled ? "启用" : "禁用", updated);
            return updated;
        } catch (Exception e) {
            log.error("批量设置配置启用状态失败，启用：{}，配置数：{}", isEnabled, configKeys.size(), e);
            throw new RuntimeException("批量设置配置状态失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetToDefault(String configKey, String modifierId) {
        if (!StringUtils.hasText(configKey)) {
            return false;
        }

        try {
            int updated = configMapper.resetToDefault(configKey, modifierId);
            if (updated > 0) {
                // 清除缓存
                configCache.remove(configKey);
                log.info("重置配置为默认值成功，配置键：{}，修改人：{}", configKey, modifierId);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("重置配置为默认值失败，配置键：{}", configKey, e);
            throw new RuntimeException("重置配置失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int resetGroupToDefault(String configGroup, String modifierId) {
        if (!StringUtils.hasText(configGroup)) {
            return 0;
        }

        try {
            List<WeatherGradingConfig> configs = getConfigsByGroup(configGroup);
            int resetCount = 0;
            
            for (WeatherGradingConfig config : configs) {
                if (resetToDefault(config.getConfigKey(), modifierId)) {
                    resetCount++;
                }
            }
            
            log.info("重置配置组为默认值完成，组名：{}，重置{}个配置", configGroup, resetCount);
            return resetCount;
        } catch (Exception e) {
            log.error("重置配置组为默认值失败，组名：{}", configGroup, e);
            throw new RuntimeException("重置配置组失败：" + e.getMessage());
        }
    }

    // ==================== 配置验证方法 ====================

    @Override
    public Map<String, Object> validateConfigValue(String configKey, String configValue) {
        Map<String, Object> result = new HashMap<>();
        result.put("valid", false);

        if (!StringUtils.hasText(configKey)) {
            result.put("error", "配置键不能为空");
            return result;
        }

        // 获取配置约束
        Map<String, Object> constraints = getConfigConstraints(configKey);
        if (constraints.isEmpty()) {
            // 没有约束规则，认为有效
            result.put("valid", true);
            return result;
        }

        try {
            // 根据不同的约束类型进行验证
            String valueType = (String) constraints.get("valueType");
            if (StringUtils.hasText(valueType)) {
                switch (valueType) {
                    case "BOOLEAN":
                        Boolean.parseBoolean(configValue);
                        break;
                    case "INTEGER":
                        int intValue = Integer.parseInt(configValue);
                        // 检查范围约束
                        if (constraints.containsKey("min")) {
                            int min = (Integer) constraints.get("min");
                            if (intValue < min) {
                                result.put("error", "值不能小于 " + min);
                                return result;
                            }
                        }
                        if (constraints.containsKey("max")) {
                            int max = (Integer) constraints.get("max");
                            if (intValue > max) {
                                result.put("error", "值不能大于 " + max);
                                return result;
                            }
                        }
                        break;
                    case "DOUBLE":
                        double doubleValue = Double.parseDouble(configValue);
                        // 检查范围约束
                        if (constraints.containsKey("min")) {
                            double min = (Double) constraints.get("min");
                            if (doubleValue < min) {
                                result.put("error", "值不能小于 " + min);
                                return result;
                            }
                        }
                        if (constraints.containsKey("max")) {
                            double max = (Double) constraints.get("max");
                            if (doubleValue > max) {
                                result.put("error", "值不能大于 " + max);
                                return result;
                            }
                        }
                        break;
                    case "STRING":
                        // 检查长度约束
                        if (constraints.containsKey("maxLength")) {
                            int maxLength = (Integer) constraints.get("maxLength");
                            if (configValue.length() > maxLength) {
                                result.put("error", "字符长度不能超过 " + maxLength);
                                return result;
                            }
                        }
                        break;
                }
            }

            result.put("valid", true);
        } catch (NumberFormatException e) {
            result.put("error", "数值格式不正确");
        } catch (Exception e) {
            result.put("error", "验证失败：" + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getConfigConstraints(String configKey) {
        WeatherGradingConfig config = getConfigFromCache(configKey);
        if (config == null || !StringUtils.hasText(config.getValidationRules())) {
            return new HashMap<>();
        }

        try {
            return JSON.parseObject(config.getValidationRules(), Map.class);
        } catch (Exception e) {
            log.warn("解析配置约束规则失败，配置键：{}", configKey, e);
            return new HashMap<>();
        }
    }

    // ==================== 系统初始化方法 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initializeDefaultConfigs() {
        log.info("开始初始化默认判卷配置");

        List<DefaultConfigItem> defaultConfigs = Arrays.asList(
            new DefaultConfigItem(AUTO_GRADING_ENABLED, "自动判卷启用", "true", 
                    "BOOLEAN", "是否在考试提交后自动触发判卷", "AUTO_GRADING"),
            new DefaultConfigItem(MAX_BATCH_SIZE, "最大批量处理数量", "100", 
                    "INTEGER", "单次批量判卷的最大处理数量", "BATCH_PROCESSING"),
            new DefaultConfigItem(GRADING_TIMEOUT, "判卷超时时间", "300", 
                    "INTEGER", "单个答案判卷的超时时间（秒）", "SYSTEM"),
            new DefaultConfigItem(RETRY_ATTEMPTS, "失败重试次数", "3", 
                    "INTEGER", "判卷失败时的重试次数", "SYSTEM"),
            new DefaultConfigItem(ASYNC_THRESHOLD, "异步处理阈值", "50", 
                    "INTEGER", "超过此数量的批量任务将使用异步处理", "BATCH_PROCESSING"),
            new DefaultConfigItem(THREAD_POOL_SIZE, "并发线程池大小", "10", 
                    "INTEGER", "判卷任务的并发线程数", "SYSTEM"),
            new DefaultConfigItem(DEFAULT_SCORING_CONFIG, "默认评分配置ID", "", 
                    "STRING", "系统默认使用的评分配置ID", "AUTO_GRADING")
        );

        int initializedCount = 0;
        for (DefaultConfigItem item : defaultConfigs) {
            try {
                if (configMapper.countByKey(item.configKey) == 0) {
                    WeatherGradingConfig config = new WeatherGradingConfig();
                    config.setConfigKey(item.configKey);
                    config.setConfigName(item.configName);
                    config.setConfigValue(item.defaultValue);
                    config.setValueType(item.valueType);
                    config.setDescription(item.description);
                    config.setConfigGroup(item.configGroup);
                    config.setIsEnabled(true);
                    config.setIsReadonly(false);
                    config.setDefaultValue(item.defaultValue);
                    config.setSortOrder(initializedCount + 1);
                    config.setCreateTime(new Date());
                    config.setUpdateTime(new Date());
                    config.setModifierId("system");

                    // 设置验证规则
                    config.setValidationRules(createValidationRules(item));

                    save(config);
                    initializedCount++;
                    log.info("初始化配置：{} = {}", item.configKey, item.defaultValue);
                }
            } catch (Exception e) {
                log.error("初始化配置失败：{}", item.configKey, e);
            }
        }

        log.info("默认判卷配置初始化完成，新增{}个配置", initializedCount);
    }

    @Override
    public void refreshConfigCache() {
        try {
            configCache.clear();
            List<WeatherGradingConfig> allConfigs = configMapper.selectAllEnabled();
            
            for (WeatherGradingConfig config : allConfigs) {
                configCache.put(config.getConfigKey(), config);
            }
            
            lastCacheUpdate = System.currentTimeMillis();
            log.info("配置缓存刷新完成，加载{}个配置", allConfigs.size());
        } catch (Exception e) {
            log.error("刷新配置缓存失败", e);
        }
    }

    // ==================== 配置导入导出方法 ====================

    @Override
    public String exportConfigs(String configGroup) {
        try {
            List<WeatherGradingConfig> configs;
            if (StringUtils.hasText(configGroup)) {
                configs = getConfigsByGroup(configGroup);
            } else {
                configs = configMapper.selectAllEnabled();
            }

            Map<String, Object> exportData = new HashMap<>();
            exportData.put("exportTime", new Date());
            exportData.put("configGroup", configGroup);
            exportData.put("configCount", configs.size());
            exportData.put("configs", configs);

            return JSON.toJSONString(exportData, true);
        } catch (Exception e) {
            log.error("导出配置失败，组名：{}", configGroup, e);
            throw new RuntimeException("导出配置失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> importConfigs(String configData, String modifierId) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("importCount", 0);
        result.put("failureCount", 0);
        result.put("errors", new ArrayList<String>());

        try {
            JSONObject importJson = JSON.parseObject(configData);
            List<WeatherGradingConfig> configs = JSON.parseArray(
                    importJson.getString("configs"), WeatherGradingConfig.class);

            int importCount = 0;
            int failureCount = 0;
            List<String> errors = new ArrayList<>();

            for (WeatherGradingConfig config : configs) {
                try {
                    // 验证配置值
                    Map<String, Object> validation = validateConfigValue(
                            config.getConfigKey(), config.getConfigValue());
                    
                    if (Boolean.TRUE.equals(validation.get("valid"))) {
                        if (setConfigValue(config.getConfigKey(), config.getConfigValue(), modifierId)) {
                            importCount++;
                        } else {
                            failureCount++;
                            errors.add("配置 " + config.getConfigKey() + " 设置失败");
                        }
                    } else {
                        failureCount++;
                        errors.add("配置 " + config.getConfigKey() + " 验证失败：" + validation.get("error"));
                    }
                } catch (Exception e) {
                    failureCount++;
                    errors.add("配置 " + config.getConfigKey() + " 导入异常：" + e.getMessage());
                }
            }

            result.put("success", true);
            result.put("importCount", importCount);
            result.put("failureCount", failureCount);
            result.put("errors", errors);

            log.info("导入配置完成，成功：{}个，失败：{}个", importCount, failureCount);
        } catch (Exception e) {
            log.error("导入配置失败", e);
            ((List<String>) result.get("errors")).add("解析配置数据失败：" + e.getMessage());
        }

        return result;
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 从缓存获取配置
     */
    private WeatherGradingConfig getConfigFromCache(String configKey) {
        // 检查缓存是否过期
        if (System.currentTimeMillis() - lastCacheUpdate > CACHE_EXPIRE_TIME) {
            refreshConfigCache();
        }

        WeatherGradingConfig config = configCache.get(configKey);
        if (config == null) {
            // 缓存中没有，从数据库查询
            config = configMapper.selectByKey(configKey);
            if (config != null) {
                configCache.put(configKey, config);
            }
        }

        return config;
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        try {
            return UserUtils.getUserId(false);
        } catch (Exception e) {
            return "system";
        }
    }

    /**
     * 创建验证规则
     */
    private String createValidationRules(DefaultConfigItem item) {
        Map<String, Object> rules = new HashMap<>();
        rules.put("valueType", item.valueType);

        switch (item.configKey) {
            case MAX_BATCH_SIZE:
            case ASYNC_THRESHOLD:
            case THREAD_POOL_SIZE:
                rules.put("min", 1);
                rules.put("max", 1000);
                break;
            case GRADING_TIMEOUT:
                rules.put("min", 60);  // 最少60秒
                rules.put("max", 3600); // 最多1小时
                break;
            case RETRY_ATTEMPTS:
                rules.put("min", 0);
                rules.put("max", 10);
                break;
            case DEFAULT_SCORING_CONFIG:
                rules.put("maxLength", 50);
                break;
        }

        return JSON.toJSONString(rules);
    }

    /**
     * 默认配置项
     */
    private static class DefaultConfigItem {
        final String configKey;
        final String configName;
        final String defaultValue;
        final String valueType;
        final String description;
        final String configGroup;

        public DefaultConfigItem(String configKey, String configName, String defaultValue,
                               String valueType, String description, String configGroup) {
            this.configKey = configKey;
            this.configName = configName;
            this.defaultValue = defaultValue;
            this.valueType = valueType;
            this.description = description;
            this.configGroup = configGroup;
        }
    }
} 