package com.yf.exam.modules.weather.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yf.exam.modules.weather.entity.WeatherScoringBatchTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * 天气预报批量评分任务Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-12-21
 */
@Mapper
public interface WeatherScoringBatchTaskMapper extends BaseMapper<WeatherScoringBatchTask> {

    /**
     * 根据状态查询任务列表
     * 
     * @param status 任务状态
     * @return 任务列表
     */
    @Select("SELECT * FROM el_weather_scoring_batch_task WHERE status = #{status} ORDER BY create_time DESC")
    List<WeatherScoringBatchTask> selectByStatus(@Param("status") String status);

    /**
     * 查询正在运行的任务
     * 
     * @return 正在运行的任务列表
     */
    @Select("SELECT * FROM el_weather_scoring_batch_task WHERE status = 'running' ORDER BY start_time ASC")
    List<WeatherScoringBatchTask> selectRunningTasks();

    /**
     * 根据考试ID查询任务分页
     * 
     * @param page 分页对象
     * @param examId 考试ID
     * @return 任务分页
     */
    @Select("SELECT * FROM el_weather_scoring_batch_task WHERE exam_id = #{examId} ORDER BY create_time DESC")
    IPage<WeatherScoringBatchTask> selectPageByExamId(Page<WeatherScoringBatchTask> page, @Param("examId") String examId);

    /**
     * 更新任务进度
     * 
     * @param taskId 任务ID
     * @param processedAnswers 已处理答案数
     * @param successCount 成功数量
     * @param failCount 失败数量
     * @return 影响行数
     */
    @Update("UPDATE el_weather_scoring_batch_task SET " +
            "processed_answers = #{processedAnswers}, " +
            "success_count = #{successCount}, " +
            "fail_count = #{failCount}, " +
            "update_time = NOW() " +
            "WHERE id = #{taskId}")
    int updateTaskProgress(@Param("taskId") String taskId, 
                          @Param("processedAnswers") Integer processedAnswers, 
                          @Param("successCount") Integer successCount, 
                          @Param("failCount") Integer failCount);

    /**
     * 更新任务状态
     * 
     * @param taskId 任务ID
     * @param status 新状态
     * @param errorMessage 错误信息（可选）
     * @return 影响行数
     */
    @Update("UPDATE el_weather_scoring_batch_task SET " +
            "status = #{status}, " +
            "error_message = #{errorMessage}, " +
            "update_time = NOW(), " +
            "end_time = CASE WHEN #{status} IN ('completed', 'failed', 'cancelled') THEN NOW() ELSE end_time END " +
            "WHERE id = #{taskId}")
    int updateTaskStatus(@Param("taskId") String taskId, 
                        @Param("status") String status, 
                        @Param("errorMessage") String errorMessage);

    /**
     * 标记任务开始
     * 
     * @param taskId 任务ID
     * @return 影响行数
     */
    @Update("UPDATE el_weather_scoring_batch_task SET " +
            "status = 'running', " +
            "start_time = NOW(), " +
            "update_time = NOW() " +
            "WHERE id = #{taskId}")
    int startTask(@Param("taskId") String taskId);

    /**
     * 查询超时的运行中任务
     * 
     * @param timeoutMinutes 超时分钟数
     * @return 超时任务列表
     */
    @Select("SELECT * FROM el_weather_scoring_batch_task " +
            "WHERE status = 'running' " +
            "AND start_time < DATE_SUB(NOW(), INTERVAL #{timeoutMinutes} MINUTE)")
    List<WeatherScoringBatchTask> selectTimeoutTasks(@Param("timeoutMinutes") int timeoutMinutes);

    /**
     * 统计任务数量按状态
     * 
     * @param examId 考试ID（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 统计结果
     */
    @Select("<script>" +
            "SELECT status, COUNT(1) as count FROM el_weather_scoring_batch_task " +
            "WHERE 1=1 " +
            "<if test='examId != null'> AND exam_id = #{examId} </if>" +
            "<if test='startDate != null'> AND create_time &gt;= #{startDate} </if>" +
            "<if test='endDate != null'> AND create_time &lt;= #{endDate} </if>" +
            "GROUP BY status" +
            "</script>")
    List<java.util.Map<String, Object>> countTasksByStatus(@Param("examId") String examId, 
                                                           @Param("startDate") Date startDate, 
                                                           @Param("endDate") Date endDate);

    /**
     * 查询用户创建的任务
     * 
     * @param createBy 创建人
     * @param limit 限制数量
     * @return 任务列表
     */
    @Select("SELECT * FROM el_weather_scoring_batch_task WHERE create_by = #{createBy} ORDER BY create_time DESC LIMIT #{limit}")
    List<WeatherScoringBatchTask> selectUserTasks(@Param("createBy") String createBy, @Param("limit") int limit);

    /**
     * 删除指定天数前的已完成任务
     * 
     * @param days 天数
     * @return 删除的记录数
     */
    @Update("DELETE FROM el_weather_scoring_batch_task " +
            "WHERE status IN ('completed', 'failed') " +
            "AND end_time < DATE_SUB(NOW(), INTERVAL #{days} DAY)")
    int deleteOldCompletedTasks(@Param("days") int days);

    /**
     * 查询任务执行效率统计
     * 
     * @param examId 考试ID（可选）
     * @return 效率统计
     */
    @Select("<script>" +
            "SELECT " +
            "AVG(total_answers) as avgTotalAnswers, " +
            "AVG(TIMESTAMPDIFF(SECOND, start_time, end_time)) as avgDurationSeconds, " +
            "AVG(success_count * 1.0 / total_answers * 100) as avgSuccessRate, " +
            "COUNT(CASE WHEN status = 'completed' THEN 1 END) as completedCount, " +
            "COUNT(CASE WHEN status = 'failed' THEN 1 END) as failedCount " +
            "FROM el_weather_scoring_batch_task " +
            "WHERE status IN ('completed', 'failed') " +
            "AND start_time IS NOT NULL AND end_time IS NOT NULL " +
            "<if test='examId != null'> AND exam_id = #{examId} </if>" +
            "</script>")
    java.util.Map<String, Object> selectTaskEfficiencyStats(@Param("examId") String examId);
} 