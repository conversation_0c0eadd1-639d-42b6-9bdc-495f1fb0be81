package com.yf.exam.modules.weather.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 天气预报答案DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WeatherAnswerDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 试卷题目ID
     */
    private String paperQuId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 表格配置ID
     */
    private String tableConfigId;

    /**
     * 表格单元格数据JSON
     */
    private Map<String, Object> cellData;

    /**
     * 答题状态：0-未完成，1-已完成
     */
    private Boolean answerStatus;

    /**
     * 数据验证结果JSON
     */
    private Map<String, Object> validationResult;

    /**
     * 总得分
     */
    private BigDecimal totalScore;

    /**
     * 得分详情JSON
     */
    private Map<String, Object> scoreDetails;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
