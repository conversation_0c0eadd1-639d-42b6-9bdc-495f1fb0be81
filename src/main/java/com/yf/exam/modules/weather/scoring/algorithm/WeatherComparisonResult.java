package com.yf.exam.modules.weather.scoring.algorithm;

import lombok.Data;
import java.util.Map;

/**
 * 天气数据综合比较结果
 * 
 * <AUTHOR>
 * @since 2024-12-21
 */
@Data
public class WeatherComparisonResult {

    /**
     * 总体得分（0.0 - 1.0）
     */
    private double overallScore;

    /**
     * 总体是否匹配
     */
    private boolean overallMatch;

    /**
     * 各要素详细比较结果
     * key: 要素名称 (windForce, windDirection, maxTemperature, minTemperature, precipitation, disasterWeather)
     * value: 该要素的比较结果
     */
    private Map<String, ComparisonResult> detailResults;

    /**
     * 总权重
     */
    private double totalWeight;

    /**
     * 加权得分
     */
    private double weightedScore;

    /**
     * 构造函数
     */
    public WeatherComparisonResult() {
        this.overallScore = 0.0;
        this.overallMatch = false;
        this.totalWeight = 0.0;
        this.weightedScore = 0.0;
    }

    /**
     * 构造函数
     * 
     * @param overallScore 总体得分
     * @param overallMatch 总体匹配状态
     */
    public WeatherComparisonResult(double overallScore, boolean overallMatch) {
        this.overallScore = overallScore;
        this.overallMatch = overallMatch;
        this.totalWeight = 0.0;
        this.weightedScore = 0.0;
    }

    /**
     * 获取指定要素的比较结果
     * 
     * @param element 要素名称
     * @return 比较结果，如果不存在则返回null
     */
    public ComparisonResult getElementResult(String element) {
        return detailResults != null ? detailResults.get(element) : null;
    }

    /**
     * 检查指定要素是否匹配
     * 
     * @param element 要素名称
     * @return 是否匹配，如果要素不存在则返回false
     */
    public boolean isElementMatched(String element) {
        ComparisonResult result = getElementResult(element);
        return result != null && result.isMatch();
    }

    /**
     * 获取指定要素的得分
     * 
     * @param element 要素名称
     * @return 得分，如果要素不存在则返回0.0
     */
    public double getElementScore(String element) {
        ComparisonResult result = getElementResult(element);
        return result != null ? result.getScore() : 0.0;
    }

    /**
     * 计算匹配要素数量
     * 
     * @return 匹配的要素数量
     */
    public int getMatchedElementCount() {
        if (detailResults == null) {
            return 0;
        }
        
        return (int) detailResults.values().stream()
                                  .filter(ComparisonResult::isMatch)
                                  .count();
    }

    /**
     * 计算总要素数量
     * 
     * @return 总要素数量
     */
    public int getTotalElementCount() {
        return detailResults != null ? detailResults.size() : 0;
    }

    /**
     * 获取匹配率
     * 
     * @return 匹配率（匹配要素数 / 总要素数）
     */
    public double getMatchRate() {
        int totalCount = getTotalElementCount();
        if (totalCount == 0) {
            return 0.0;
        }
        return (double) getMatchedElementCount() / totalCount;
    }
} 