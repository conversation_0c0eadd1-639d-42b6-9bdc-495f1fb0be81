package com.yf.exam.modules.weather.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.exam.modules.weather.entity.WeatherGradingConfig;

import java.util.List;
import java.util.Map;

/**
 * 天气预报判卷配置服务接口
 *
 * 提供判卷系统的配置管理功能，包括：
 * - 自动判卷开关配置
 * - 批量处理参数配置
 * - 系统性能参数配置
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface WeatherGradingConfigService extends IService<WeatherGradingConfig> {

    // ==================== 配置键常量 ====================
    
    /** 自动判卷启用开关 */
    String AUTO_GRADING_ENABLED = "auto_grading_enabled";
    
    /** 最大批量处理数量 */
    String MAX_BATCH_SIZE = "max_batch_size";
    
    /** 判卷超时时间（秒） */
    String GRADING_TIMEOUT = "grading_timeout_seconds";
    
    /** 失败重试次数 */
    String RETRY_ATTEMPTS = "retry_attempts";
    
    /** 异步处理阈值 */
    String ASYNC_THRESHOLD = "async_threshold";
    
    /** 并发线程池大小 */
    String THREAD_POOL_SIZE = "thread_pool_size";

    /** 默认评分配置ID */
    String DEFAULT_SCORING_CONFIG = "default_scoring_config_id";

    // ==================== 核心配置获取方法 ====================

    /**
     * 是否启用自动判卷
     *
     * @return true-启用，false-禁用
     */
    boolean isAutoGradingEnabled();

    /**
     * 获取最大批量处理数量
     *
     * @return 批量处理数量，默认100
     */
    int getMaxBatchSize();

    /**
     * 获取判卷超时时间（秒）
     *
     * @return 超时时间，默认300秒
     */
    int getGradingTimeout();

    /**
     * 获取失败重试次数
     *
     * @return 重试次数，默认3次
     */
    int getRetryAttempts();

    /**
     * 获取异步处理阈值
     *
     * @return 阈值数量，超过此数量使用异步处理，默认50
     */
    int getAsyncThreshold();

    /**
     * 获取线程池大小
     *
     * @return 线程池大小，默认10
     */
    int getThreadPoolSize();

    /**
     * 获取默认评分配置ID
     *
     * @return 默认评分配置ID
     */
    String getDefaultScoringConfigId();

    // ==================== 通用配置操作方法 ====================

    /**
     * 根据配置键获取配置值
     *
     * @param configKey 配置键
     * @return 配置值，不存在或未启用返回null
     */
    String getConfigValue(String configKey);

    /**
     * 根据配置键获取布尔配置值
     *
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    boolean getBooleanConfig(String configKey, boolean defaultValue);

    /**
     * 根据配置键获取整数配置值
     *
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    int getIntegerConfig(String configKey, int defaultValue);

    /**
     * 根据配置键获取双精度配置值
     *
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    double getDoubleConfig(String configKey, double defaultValue);

    /**
     * 设置配置值
     *
     * @param configKey 配置键
     * @param configValue 配置值
     * @return 是否设置成功
     */
    boolean setConfigValue(String configKey, String configValue);

    /**
     * 设置配置值（带修改人）
     *
     * @param configKey 配置键
     * @param configValue 配置值
     * @param modifierId 修改人ID
     * @return 是否设置成功
     */
    boolean setConfigValue(String configKey, String configValue, String modifierId);

    // ==================== 配置组管理方法 ====================

    /**
     * 获取指定组的所有配置
     *
     * @param configGroup 配置组名
     * @return 配置列表
     */
    List<WeatherGradingConfig> getConfigsByGroup(String configGroup);

    /**
     * 获取所有有效配置（按组分类）
     *
     * @return 按组分类的配置Map
     */
    Map<String, List<WeatherGradingConfig>> getAllConfigsGrouped();

    /**
     * 获取配置组统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getConfigGroupStatistics();

    // ==================== 批量操作方法 ====================

    /**
     * 批量设置配置值
     *
     * @param configs 配置键值对
     * @param modifierId 修改人ID
     * @return 成功设置的配置数量
     */
    int batchSetConfigs(Map<String, String> configs, String modifierId);

    /**
     * 批量启用/禁用配置
     *
     * @param configKeys 配置键列表
     * @param isEnabled 是否启用
     * @param modifierId 修改人ID
     * @return 影响的配置数量
     */
    int batchSetEnabled(List<String> configKeys, boolean isEnabled, String modifierId);

    /**
     * 重置配置为默认值
     *
     * @param configKey 配置键
     * @param modifierId 修改人ID
     * @return 是否重置成功
     */
    boolean resetToDefault(String configKey, String modifierId);

    /**
     * 重置指定组的所有配置为默认值
     *
     * @param configGroup 配置组名
     * @param modifierId 修改人ID
     * @return 重置的配置数量
     */
    int resetGroupToDefault(String configGroup, String modifierId);

    // ==================== 配置验证方法 ====================

    /**
     * 验证配置值的有效性
     *
     * @param configKey 配置键
     * @param configValue 配置值
     * @return 验证结果，包含是否有效和错误信息
     */
    Map<String, Object> validateConfigValue(String configKey, String configValue);

    /**
     * 获取配置的约束规则
     *
     * @param configKey 配置键
     * @return 约束规则Map
     */
    Map<String, Object> getConfigConstraints(String configKey);

    // ==================== 系统初始化方法 ====================

    /**
     * 初始化默认配置
     * 
     * 在系统启动时调用，确保必要的配置项存在
     */
    void initializeDefaultConfigs();

    /**
     * 刷新配置缓存
     * 
     * 重新从数据库加载配置，清除缓存
     */
    void refreshConfigCache();

    // ==================== 配置导入导出方法 ====================

    /**
     * 导出配置（JSON格式）
     *
     * @param configGroup 配置组名，null表示全部
     * @return JSON格式的配置数据
     */
    String exportConfigs(String configGroup);

    /**
     * 导入配置（JSON格式）
     *
     * @param configData JSON格式的配置数据
     * @param modifierId 修改人ID
     * @return 导入结果，包含成功和失败的统计
     */
    Map<String, Object> importConfigs(String configData, String modifierId);
} 