package com.yf.exam.modules.weather.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yf.exam.config.CustomJacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 天气预报评分配置实体类
 * 
 * <AUTHOR>
 * @since 2024-12-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("el_weather_scoring_config")
public class WeatherScoringConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 配置名称
     */
    @TableField("config_name")
    private String configName;

    /**
     * 配置版本
     */
    @TableField("config_version")
    private String configVersion;

    /**
     * 总分
     */
    @TableField("total_score")
    private Integer totalScore;

    /**
     * 单站权重
     */
    @TableField("station_weight")
    private Integer stationWeight;

    /**
     * 要素权重配置(JSON格式)
     */
    @TableField(value = "element_weights", typeHandler = CustomJacksonTypeHandler.class)
    private Map<String, Object> elementWeights;

    /**
     * 容差配置(JSON格式)
     */
    @TableField(value = "tolerance_config", typeHandler = CustomJacksonTypeHandler.class)
    private Map<String, Object> toleranceConfig;

    /**
     * 是否启用
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 获取特定要素的最大分数
     * 
     * @param elementName 要素名称
     * @return 最大分数
     */
    public Double getElementMaxScore(String elementName) {
        if (elementWeights == null) {
            return 0.0;
        }
        
        Object element = elementWeights.get(elementName);
        if (element instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> elementConfig = (Map<String, Object>) element;
            Object maxScore = elementConfig.get("maxScore");
            if (maxScore instanceof Number) {
                return ((Number) maxScore).doubleValue();
            }
        }
        return 0.0;
    }

    /**
     * 获取特定要素的评分类型
     * 
     * @param elementName 要素名称
     * @return 评分类型
     */
    public String getElementScoringType(String elementName) {
        if (elementWeights == null) {
            return null;
        }
        
        Object element = elementWeights.get(elementName);
        if (element instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> elementConfig = (Map<String, Object>) element;
            Object scoringType = elementConfig.get("scoringType");
            return scoringType != null ? scoringType.toString() : null;
        }
        return null;
    }

    /**
     * 获取温度容差范围
     * 
     * @return 容差范围（摄氏度）
     */
    public Integer getTemperatureTolerance() {
        if (toleranceConfig == null) {
            return 2; // 默认±2℃
        }
        
        Object temperature = toleranceConfig.get("temperature");
        if (temperature instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> tempConfig = (Map<String, Object>) temperature;
            Object maxDiff = tempConfig.get("maxDifference");
            if (maxDiff instanceof Number) {
                return ((Number) maxDiff).intValue();
            }
        }
        return 2; // 默认±2℃
    }

    /**
     * 获取风向角度范围配置
     * 
     * @return 角度范围配置
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getWindDirectionRanges() {
        if (toleranceConfig == null) {
            return null;
        }
        
        Object windDirection = toleranceConfig.get("windDirection");
        if (windDirection instanceof Map) {
            Map<String, Object> windConfig = (Map<String, Object>) windDirection;
            Object angleRanges = windConfig.get("angleRanges");
            if (angleRanges instanceof Map) {
                return (Map<String, Object>) angleRanges;
            }
        }
        return null;
    }
} 