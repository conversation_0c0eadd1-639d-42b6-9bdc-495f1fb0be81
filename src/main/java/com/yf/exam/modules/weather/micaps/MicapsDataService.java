package com.yf.exam.modules.weather.micaps;

import org.springframework.stereotype.Service;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * MICAPS数据服务类
 * 提供MICAPS数据解析和处理的业务方法
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */
@Service
public class MicapsDataService {
    
    /**
     * 解析MICAPS文件
     * 
     * @param filePath 文件路径
     * @return 解析结果
     * @throws IOException 文件读取异常
     */
    public MicapsData parseMicapsFile(String filePath) throws IOException {
        try {
            // 首先尝试文本格式解析
            return MicapsParser.parseMicapsFile(filePath);
        } catch (Exception e) {
            // 如果文本格式解析失败，尝试二进制格式
            try {
                return MicapsBinaryParser.parseBinaryMicapsFile(filePath);
            } catch (Exception binaryException) {
                throw new IOException("无法解析MICAPS文件: " + filePath + 
                    ". 文本格式错误: " + e.getMessage() + 
                    ". 二进制格式错误: " + binaryException.getMessage());
            }
        }
    }
    
    /**
     * 获取第一类数据中指定区域的站点信息
     * 
     * @param data MICAPS数据
     * @param minLon 最小经度
     * @param maxLon 最大经度
     * @param minLat 最小纬度
     * @param maxLat 最大纬度
     * @return 区域内的站点列表
     */
    public List<MicapsStation> getStationsInRegion(MicapsData data, 
                                                   double minLon, double maxLon,
                                                   double minLat, double maxLat) {
        if (!(data instanceof MicapsType1Data)) {
            throw new IllegalArgumentException("只有第一类数据支持站点查询");
        }
        
        MicapsType1Data type1Data = (MicapsType1Data) data;
        return type1Data.getStationsInRange(minLon, maxLon, minLat, maxLat);
    }
    
    /**
     * 获取第一类数据中有温度数据的站点
     * 
     * @param data MICAPS数据
     * @return 有温度数据的站点列表
     */
    public List<MicapsStation> getStationsWithTemperature(MicapsData data) {
        if (!(data instanceof MicapsType1Data)) {
            throw new IllegalArgumentException("只有第一类数据支持站点查询");
        }
        
        MicapsType1Data type1Data = (MicapsType1Data) data;
        return type1Data.getStations().stream()
                .filter(station -> station.getTemperature() != null)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取第一类数据中有降水数据的站点
     * 
     * @param data MICAPS数据
     * @return 有降水数据的站点列表
     */
    public List<MicapsStation> getStationsWithPrecipitation(MicapsData data) {
        if (!(data instanceof MicapsType1Data)) {
            throw new IllegalArgumentException("只有第一类数据支持站点查询");
        }
        
        MicapsType1Data type1Data = (MicapsType1Data) data;
        return type1Data.getStations().stream()
                .filter(station -> station.getPrecipitation6h() != null && 
                                 station.getPrecipitation6h() > 0)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取第四类数据中指定位置的插值数据
     * 
     * @param data MICAPS数据
     * @param lon 经度
     * @param lat 纬度
     * @return 插值后的数据值
     */
    public Double getGridValueAtPosition(MicapsData data, double lon, double lat) {
        if (!(data instanceof MicapsType4Data)) {
            throw new IllegalArgumentException("只有第四类数据支持格点查询");
        }
        
        MicapsType4Data type4Data = (MicapsType4Data) data;
        return type4Data.getValueAtPosition(lon, lat);
    }
    
    /**
     * 获取第四类数据的统计信息
     * 
     * @param data MICAPS数据
     * @return 统计信息
     */
    public GridStatistics getGridStatistics(MicapsData data) {
        if (!(data instanceof MicapsType4Data)) {
            throw new IllegalArgumentException("只有第四类数据支持统计分析");
        }
        
        MicapsType4Data type4Data = (MicapsType4Data) data;
        List<Double> values = type4Data.getGridValues();
        
        if (values == null || values.isEmpty()) {
            return null;
        }
        
        double min = values.stream().mapToDouble(Double::doubleValue).min().orElse(0);
        double max = values.stream().mapToDouble(Double::doubleValue).max().orElse(0);
        double avg = values.stream().mapToDouble(Double::doubleValue).average().orElse(0);
        
        return new GridStatistics(min, max, avg, values.size());
    }
    
    /**
     * 验证MICAPS数据的完整性
     * 
     * @param data MICAPS数据
     * @return 验证结果
     */
    public ValidationResult validateMicapsData(MicapsData data) {
        ValidationResult result = new ValidationResult();
        
        if (data == null) {
            result.addError("数据对象为空");
            return result;
        }
        
        // 验证基本信息
        if (data.getYear() < 1900 || data.getYear() > 2100) {
            result.addWarning("年份可能不正确: " + data.getYear());
        }
        
        if (data.getMonth() < 1 || data.getMonth() > 12) {
            result.addError("月份错误: " + data.getMonth());
        }
        
        if (data.getDay() < 1 || data.getDay() > 31) {
            result.addError("日期错误: " + data.getDay());
        }
        
        if (data.getHour() < 0 || data.getHour() > 23) {
            result.addError("小时错误: " + data.getHour());
        }
        
        // 根据数据类型进行特定验证
        if (data instanceof MicapsType1Data) {
            validateType1Data((MicapsType1Data) data, result);
        } else if (data instanceof MicapsType4Data) {
            validateType4Data((MicapsType4Data) data, result);
        }
        
        return result;
    }
    
    /**
     * 验证第一类数据
     */
    private void validateType1Data(MicapsType1Data data, ValidationResult result) {
        if (data.getStations() == null) {
            result.addError("站点数据为空");
            return;
        }
        
        if (data.getTotalStations() != data.getStations().size()) {
            result.addWarning("声明的站点数(" + data.getTotalStations() + 
                            ")与实际站点数(" + data.getStations().size() + ")不匹配");
        }
        
        // 检查站点数据的合理性
        for (MicapsStation station : data.getStations()) {
            if (station.getLongitude() < -180 || station.getLongitude() > 180) {
                result.addWarning("站点" + station.getStationId() + "经度超出范围: " + 
                                station.getLongitude());
            }
            
            if (station.getLatitude() < -90 || station.getLatitude() > 90) {
                result.addWarning("站点" + station.getStationId() + "纬度超出范围: " + 
                                station.getLatitude());
            }
        }
    }
    
    /**
     * 验证第四类数据
     */
    private void validateType4Data(MicapsType4Data data, ValidationResult result) {
        if (data.getGridValues() == null) {
            result.addError("格点数据为空");
            return;
        }
        
        int expectedPoints = data.getLatGridNum() * data.getLonGridNum();
        if (data.getGridValues().size() != expectedPoints) {
            result.addWarning("声明的格点数(" + expectedPoints + 
                            ")与实际格点数(" + data.getGridValues().size() + ")不匹配");
        }
        
        if (data.getStartLon() >= data.getEndLon()) {
            result.addError("起始经度应小于终止经度");
        }
        
        if (data.getStartLat() >= data.getEndLat()) {
            result.addError("起始纬度应小于终止纬度");
        }
    }
    
    /**
     * 格点数据统计信息
     */
    public static class GridStatistics {
        private final double min;
        private final double max;
        private final double average;
        private final int count;
        
        public GridStatistics(double min, double max, double average, int count) {
            this.min = min;
            this.max = max;
            this.average = average;
            this.count = count;
        }
        
        public double getMin() { return min; }
        public double getMax() { return max; }
        public double getAverage() { return average; }
        public int getCount() { return count; }
        
        @Override
        public String toString() {
            return String.format("GridStatistics{min=%.2f, max=%.2f, avg=%.2f, count=%d}", 
                               min, max, average, count);
        }
    }
    
    /**
     * 数据验证结果
     */
    public static class ValidationResult {
        private final List<String> errors = new java.util.ArrayList<>();
        private final List<String> warnings = new java.util.ArrayList<>();
        
        public void addError(String error) {
            errors.add(error);
        }
        
        public void addWarning(String warning) {
            warnings.add(warning);
        }
        
        public boolean isValid() {
            return errors.isEmpty();
        }
        
        public List<String> getErrors() { return errors; }
        public List<String> getWarnings() { return warnings; }
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("ValidationResult{valid=").append(isValid());
            if (!errors.isEmpty()) {
                sb.append(", errors=").append(errors);
            }
            if (!warnings.isEmpty()) {
                sb.append(", warnings=").append(warnings);
            }
            sb.append('}');
            return sb.toString();
        }
    }
}
