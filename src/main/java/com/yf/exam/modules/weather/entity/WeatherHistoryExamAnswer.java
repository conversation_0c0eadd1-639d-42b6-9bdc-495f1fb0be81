package com.yf.exam.modules.weather.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yf.exam.config.CustomJacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 历史个例考试答案表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("el_weather_history_exam_answer")
public class WeatherHistoryExamAnswer implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 考试ID
     */
    @TableField("exam_id")
    private String examId;

    /**
     * 题目ID
     */
    @TableField("question_id")
    private String questionId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 第一部分：降水分级落区预报答案JSON
     */
    @TableField(value = "precipitation_answer", typeHandler = com.yf.exam.config.CustomJacksonTypeHandler.class)
    private Map<String, Object> precipitationAnswer;

    /**
     * 第二部分：灾害性天气预报答案JSON
     */
    @TableField(value = "weather_answer", typeHandler = com.yf.exam.config.CustomJacksonTypeHandler.class)
    private Map<String, Object> weatherAnswer;

    /**
     * 整体进度百分比
     */
    @TableField("overall_progress")
    private Integer overallProgress;

    /**
     * 答题状态：0-答题中，1-已提交
     */
    @TableField("answer_status")
    private Integer answerStatus;

    /**
     * 答题时间（最后保存时间）
     */
    @TableField("answer_time")
    private Date answerTime;

    /**
     * 已用时间（秒）
     */
    @TableField("time_spent")
    private Integer timeSpent;

    /**
     * 提交时间
     */
    @TableField("submit_time")
    private Date submitTime;

    /**
     * 总得分
     */
    @TableField("total_score")
    private BigDecimal totalScore;

    /**
     * 得分详情JSON
     */
    @TableField(value = "score_details", typeHandler = com.yf.exam.config.CustomJacksonTypeHandler.class)
    private Map<String, Object> scoreDetails;

    /**
     * 批改状态：0-未批改，1-已批改
     */
    @TableField("grading_status")
    private Integer gradingStatus;

    /**
     * 批改时间
     */
    @TableField("grading_time")
    private Date gradingTime;

    /**
     * 批改人ID
     */
    @TableField("grader_id")
    private String graderId;

    /**
     * 批改备注
     */
    @TableField("grading_remark")
    private String gradingRemark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;
}
