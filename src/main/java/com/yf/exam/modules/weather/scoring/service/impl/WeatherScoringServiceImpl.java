package com.yf.exam.modules.weather.scoring.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yf.exam.modules.weather.entity.WeatherScoringBatchTask;
import com.yf.exam.modules.weather.entity.WeatherScoringConfig;
import com.yf.exam.modules.weather.entity.WeatherScoringResult;
import com.yf.exam.modules.weather.scoring.engine.ScoringEngineResult;
import com.yf.exam.modules.weather.scoring.engine.WeatherScoringEngine;
import com.yf.exam.modules.weather.scoring.service.ScoringConfigService;
import com.yf.exam.modules.weather.scoring.service.WeatherScoringBatchTaskService;
import com.yf.exam.modules.weather.scoring.service.WeatherScoringResultService;
import com.yf.exam.modules.weather.scoring.service.WeatherScoringService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 天气预报评分系统统一业务入口Service实现类
 * 
 * 整合所有评分系统组件，提供统一的业务接口
 * 
 * <AUTHOR>
 * @since 2024-12-21
 */
@Slf4j
@Service
public class WeatherScoringServiceImpl implements WeatherScoringService {

    @Autowired
    private ScoringConfigService configService;

    @Autowired
    private WeatherScoringEngine scoringEngine;

    @Autowired
    private WeatherScoringResultService resultService;

    @Autowired
    private WeatherScoringBatchTaskService batchTaskService;

    @Autowired
    private ObjectMapper objectMapper;

    // ==================== 评分配置管理 ====================

    @Override
    public WeatherScoringConfig getActiveConfig() {
        try {
            return configService.getActiveConfig();
        } catch (Exception e) {
            log.error("获取活跃配置失败", e);
            return null;
        }
    }

    @Override
    public WeatherScoringConfig getConfigByVersion(String version) {
        if (!StringUtils.hasText(version)) {
            log.warn("配置版本不能为空");
            return null;
        }

        try {
            return configService.getConfigByVersion(version);
        } catch (Exception e) {
            log.error("根据版本获取配置失败，版本：{}", version, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WeatherScoringConfig saveConfig(WeatherScoringConfig config) {
        if (config == null) {
            throw new IllegalArgumentException("评分配置不能为空");
        }

        try {
            // 验证配置
            Map<String, String> validationResult = validateConfig(config);
            if (!validationResult.isEmpty()) {
                log.warn("配置验证失败：{}", validationResult);
                throw new IllegalArgumentException("配置验证失败：" + validationResult);
            }

            boolean saved = configService.saveConfig(config);
            if (saved) {
                return config;
            } else {
                throw new RuntimeException("保存配置失败");
            }
        } catch (Exception e) {
            log.error("保存评分配置失败", e);
            throw new RuntimeException("保存配置失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean activateConfig(String version) {
        if (!StringUtils.hasText(version)) {
            log.warn("配置版本不能为空");
            return false;
        }

        try {
            return configService.activateConfig(version);
        } catch (Exception e) {
            log.error("激活配置失败，版本：{}", version, e);
            return false;
        }
    }

    @Override
    public List<WeatherScoringConfig> getConfigHistory() {
        try {
            return configService.getConfigHistory(null);
        } catch (Exception e) {
            log.error("获取配置历史失败", e);
            return new ArrayList<>();
        }
    }

    // ==================== 评分计算功能 ====================

    @Override
    public ScoringEngineResult calculateScore(String answerId) {
        return calculateScore(answerId, null);
    }

    @Override
    public ScoringEngineResult calculateScore(String answerId, String configId) {
        if (!StringUtils.hasText(answerId)) {
            return ScoringEngineResult.failure("答案ID不能为空");
        }

        try {
            return scoringEngine.calculateSingleScore(answerId, configId);
        } catch (Exception e) {
            log.error("单个评分计算失败，答案ID：{}，配置ID：{}", answerId, configId, e);
            return ScoringEngineResult.failure("评分计算失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchCalculateScore(List<String> answerIds, String batchName) {
        return batchCalculateScore(answerIds, null, batchName);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchCalculateScore(List<String> answerIds, String configId, String batchName) {
        if (CollectionUtils.isEmpty(answerIds)) {
            throw new IllegalArgumentException("答案ID列表不能为空");
        }

        if (!StringUtils.hasText(batchName)) {
            batchName = "批量评分任务_" + System.currentTimeMillis();
        }

        try {
            return scoringEngine.calculateBatchScore(answerIds, configId, batchName);
        } catch (Exception e) {
            log.error("批量评分失败，任务名称：{}，答案数量：{}", batchName, answerIds.size(), e);
            throw new RuntimeException("批量评分失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchCalculateScoreByCondition(String examId, LocalDateTime startDate, LocalDateTime endDate, String batchName) {
        if (!StringUtils.hasText(batchName)) {
            batchName = "条件批量评分_" + System.currentTimeMillis();
        }

        try {
            return scoringEngine.calculateScoreByCondition(examId, startDate, endDate, null, batchName);
        } catch (Exception e) {
            log.error("条件批量评分失败，考试ID：{}，任务名称：{}", examId, batchName, e);
            throw new RuntimeException("条件批量评分失败：" + e.getMessage());
        }
    }

    @Override
    public ScoringEngineResult recalculateScore(String resultId, String configId) {
        if (!StringUtils.hasText(resultId)) {
            return ScoringEngineResult.failure("结果ID不能为空");
        }

        try {
            return scoringEngine.recalculateScore(resultId, configId);
        } catch (Exception e) {
            log.error("重新计算评分失败，结果ID：{}，配置ID：{}", resultId, configId, e);
            return ScoringEngineResult.failure("重新计算失败：" + e.getMessage());
        }
    }

    // ==================== 评分结果查询 ====================

    @Override
    public WeatherScoringResult getScoringResult(String answerId) {
        if (!StringUtils.hasText(answerId)) {
            log.warn("答案ID不能为空");
            return null;
        }

        try {
            return resultService.getByAnswerId(answerId);
        } catch (Exception e) {
            log.error("获取评分结果失败，答案ID：{}", answerId, e);
            return null;
        }
    }

    @Override
    public List<WeatherScoringResult> getUserScoringResults(String userId) {
        if (!StringUtils.hasText(userId)) {
            return new ArrayList<>();
        }

        try {
            return resultService.listByUserId(userId);
        } catch (Exception e) {
            log.error("获取用户评分结果失败，用户ID：{}", userId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<WeatherScoringResult> getExamScoringResults(String examId) {
        if (!StringUtils.hasText(examId)) {
            return new ArrayList<>();
        }

        try {
            return resultService.listByExamId(examId);
        } catch (Exception e) {
            log.error("获取考试评分结果失败，考试ID：{}", examId, e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteScoringResult(String answerId) {
        if (!StringUtils.hasText(answerId)) {
            log.warn("答案ID不能为空");
            return false;
        }

        try {
            return resultService.deleteByAnswerId(answerId);
        } catch (Exception e) {
            log.error("删除评分结果失败，答案ID：{}", answerId, e);
            return false;
        }
    }

    // ==================== 统计分析功能 ====================

    @Override
    public Map<String, Object> getUserScoringStats(String userId) {
        if (!StringUtils.hasText(userId)) {
            Map<String, Object> result = new HashMap<>();
            result.put("error", "用户ID不能为空");
            return result;
        }

        try {
            return resultService.getUserScoringStats(userId);
        } catch (Exception e) {
            log.error("获取用户评分统计失败，用户ID：{}", userId, e);
            Map<String, Object> result = new HashMap<>();
            result.put("error", "获取统计失败：" + e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> getExamScoringStats(String examId) {
        if (!StringUtils.hasText(examId)) {
            Map<String, Object> result = new HashMap<>();
            result.put("error", "考试ID不能为空");
            return result;
        }

        try {
            return resultService.getExamScoringStats(examId);
        } catch (Exception e) {
            log.error("获取考试评分统计失败，考试ID：{}", examId, e);
            Map<String, Object> result = new HashMap<>();
            result.put("error", "获取统计失败：" + e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> getStationScoringStats(String stationCode) {
        if (!StringUtils.hasText(stationCode)) {
            Map<String, Object> result = new HashMap<>();
            result.put("error", "站点代码不能为空");
            return result;
        }

        try {
            return resultService.getStationScoringStats(stationCode);
        } catch (Exception e) {
            log.error("获取站点评分统计失败，站点代码：{}", stationCode, e);
            Map<String, Object> result = new HashMap<>();
            result.put("error", "获取统计失败：" + e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> getSystemScoringStats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 获取基本统计
            List<WeatherScoringResult> allResults = resultService.list();
            List<WeatherScoringBatchTask> allTasks = batchTaskService.list();
            List<WeatherScoringConfig> allConfigs = configService.list();

            stats.put("totalResults", allResults.size());
            stats.put("totalTasks", allTasks.size());
            stats.put("totalConfigs", allConfigs.size());

            // 结果统计
            if (!allResults.isEmpty()) {
                int successCount = (int) allResults.stream().filter(r -> Boolean.TRUE.equals(r.getIsSuccess())).count();
                stats.put("successResults", successCount);
                stats.put("failureResults", allResults.size() - successCount);
                stats.put("overallSuccessRate", allResults.size() > 0 ? (double) successCount / allResults.size() * 100 : 0.0);

                // 平均分计算
                double avgScore = allResults.stream()
                    .filter(r -> Boolean.TRUE.equals(r.getIsSuccess()) && r.getFinalScore() != null)
                    .mapToDouble(WeatherScoringResult::getFinalScore)
                    .average()
                    .orElse(0.0);
                stats.put("averageScore", new BigDecimal(avgScore).setScale(2, RoundingMode.HALF_UP).doubleValue());
            }

            // 任务统计
            if (!allTasks.isEmpty()) {
                Map<String, Integer> taskStatusStats = new HashMap<>();
                for (WeatherScoringBatchTask task : allTasks) {
                    String status = task.getStatus();
                    taskStatusStats.put(status, taskStatusStats.getOrDefault(status, 0) + 1);
                }
                stats.put("taskStatusStats", taskStatusStats);
            }

            // 配置统计
            if (!allConfigs.isEmpty()) {
                int activeConfigs = (int) allConfigs.stream().filter(WeatherScoringConfig::getIsActive).count();
                stats.put("activeConfigs", activeConfigs);
                stats.put("inactiveConfigs", allConfigs.size() - activeConfigs);
            }

            // 最近30天统计
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DAY_OF_MONTH, -30);
            Date thirtyDaysAgo = cal.getTime();

            int recentResults = (int) allResults.stream()
                .filter(r -> r.getCreateTime() != null && r.getCreateTime().after(thirtyDaysAgo))
                .count();
            stats.put("recentResults", recentResults);

            stats.put("lastUpdateTime", new Date());

        } catch (Exception e) {
            log.error("获取系统评分统计失败", e);
            stats.put("error", "获取系统统计失败：" + e.getMessage());
        }

        return stats;
    }

    // ==================== 批量任务管理 ====================

    @Override
    public WeatherScoringBatchTask getBatchTask(String taskId) {
        if (!StringUtils.hasText(taskId)) {
            return null;
        }

        try {
            return batchTaskService.getById(taskId);
        } catch (Exception e) {
            log.error("获取批量任务失败，任务ID：{}", taskId, e);
            return null;
        }
    }

    @Override
    public List<WeatherScoringBatchTask> getRunningBatchTasks() {
        try {
            return batchTaskService.getRunningTasks();
        } catch (Exception e) {
            log.error("获取运行中任务失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<WeatherScoringBatchTask> getUserTaskHistory(String userId) {
        if (!StringUtils.hasText(userId)) {
            return new ArrayList<>();
        }

        try {
            return batchTaskService.getUserTaskHistory(userId);
        } catch (Exception e) {
            log.error("获取用户任务历史失败，用户ID：{}", userId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> getBatchTaskStats(String taskId) {
        if (!StringUtils.hasText(taskId)) {
            Map<String, Object> result = new HashMap<>();
            result.put("error", "任务ID不能为空");
            return result;
        }

        try {
            return batchTaskService.getTaskStatistics(taskId);
        } catch (Exception e) {
            log.error("获取批量任务统计失败，任务ID：{}", taskId, e);
            Map<String, Object> result = new HashMap<>();
            result.put("error", "获取任务统计失败：" + e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> getBatchTaskProgress(String taskId) {
        return getBatchTaskStats(taskId); // 复用统计方法
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean stopBatchTask(String taskId, String reason) {
        if (!StringUtils.hasText(taskId)) {
            log.warn("任务ID不能为空");
            return false;
        }

        try {
            return batchTaskService.stopTask(taskId, reason);
        } catch (Exception e) {
            log.error("停止批量任务失败，任务ID：{}", taskId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean restartBatchTask(String taskId) {
        if (!StringUtils.hasText(taskId)) {
            log.warn("任务ID不能为空");
            return false;
        }

        try {
            return batchTaskService.restartTask(taskId);
        } catch (Exception e) {
            log.error("重启批量任务失败，任务ID：{}", taskId, e);
            return false;
        }
    }

    @Override
    public List<String> getBatchTaskLogs(String taskId) {
        if (!StringUtils.hasText(taskId)) {
            return new ArrayList<>();
        }

        try {
            return batchTaskService.getTaskLogs(taskId);
        } catch (Exception e) {
            log.error("获取批量任务日志失败，任务ID：{}", taskId, e);
            return new ArrayList<>();
        }
    }

    // ==================== 系统管理功能 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanupExpiredResults(int retentionDays) {
        if (retentionDays <= 0) {
            log.warn("保留天数必须大于0");
            return 0;
        }

        try {
            // 这里需要在resultService中添加清理过期结果的方法
            // 暂时返回0，表示功能待完善
            log.warn("清理过期评分结果功能需要在resultService中实现");
            return 0;
        } catch (Exception e) {
            log.error("清理过期评分结果失败", e);
            return 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanupExpiredTasks(int retentionDays) {
        if (retentionDays <= 0) {
            log.warn("保留天数必须大于0");
            return 0;
        }

        try {
            return batchTaskService.cleanupExpiredTasks(retentionDays);
        } catch (Exception e) {
            log.error("清理过期任务失败", e);
            return 0;
        }
    }

    @Override
    public Map<String, String> validateConfig(WeatherScoringConfig config) {
        Map<String, String> errors = new HashMap<>();

        if (config == null) {
            errors.put("config", "配置不能为空");
            return errors;
        }

        try {
            Map<String, Object> validationResult = configService.validateConfig(config);
            Map<String, String> stringErrors = new HashMap<>();

            // 转换Map<String, Object>为Map<String, String>
            for (Map.Entry<String, Object> entry : validationResult.entrySet()) {
                stringErrors.put(entry.getKey(), entry.getValue() != null ? entry.getValue().toString() : "");
            }

            return stringErrors;
        } catch (Exception e) {
            log.error("配置验证失败", e);
            errors.put("validation", "验证过程出错：" + e.getMessage());
            return errors;
        }
    }

    @Override
    public Map<String, Object> getSystemHealth() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            // 检查各个组件状态
            boolean configServiceHealth = checkConfigServiceHealth();
            boolean resultServiceHealth = checkResultServiceHealth();
            boolean taskServiceHealth = checkTaskServiceHealth();
            boolean engineHealth = checkEngineHealth();

            health.put("configService", configServiceHealth ? "UP" : "DOWN");
            health.put("resultService", resultServiceHealth ? "UP" : "DOWN");
            health.put("taskService", taskServiceHealth ? "UP" : "DOWN");
            health.put("scoringEngine", engineHealth ? "UP" : "DOWN");

            boolean overallHealth = configServiceHealth && resultServiceHealth && taskServiceHealth && engineHealth;
            health.put("status", overallHealth ? "UP" : "DOWN");
            
            health.put("checkTime", new Date());

        } catch (Exception e) {
            log.error("获取系统健康状态失败", e);
            health.put("status", "DOWN");
            health.put("error", "健康检查失败：" + e.getMessage());
        }

        return health;
    }

    @Override
    public Map<String, Object> getPerformanceMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        
        try {
            // 获取基本性能指标
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;

            metrics.put("memoryTotal", totalMemory);
            metrics.put("memoryUsed", usedMemory);
            metrics.put("memoryFree", freeMemory);
            metrics.put("memoryUsagePercent", (double) usedMemory / totalMemory * 100);

            // 获取任务执行指标
            List<WeatherScoringBatchTask> runningTasks = getRunningBatchTasks();
            metrics.put("runningTaskCount", runningTasks.size());

            // 计算平均任务执行时间等
            metrics.put("collectTime", new Date());

        } catch (Exception e) {
            log.error("获取性能指标失败", e);
            metrics.put("error", "获取性能指标失败：" + e.getMessage());
        }

        return metrics;
    }

    // ==================== 数据导入导出 ====================

    @Override
    public String exportConfigs(List<String> configIds) {
        if (CollectionUtils.isEmpty(configIds)) {
            return "{}";
        }

        try {
            // 由于ScoringConfigService只支持单个配置导出，这里需要循环处理
            StringBuilder result = new StringBuilder("{\"configs\":[");
            for (int i = 0; i < configIds.size(); i++) {
                if (i > 0) {
                    result.append(",");
                }
                String configJson = configService.exportConfigAsJson(configIds.get(i));
                result.append(configJson);
            }
            result.append("]}");
            return result.toString();
        } catch (Exception e) {
            log.error("导出配置失败", e);
            return "{\"error\":\"导出失败：" + e.getMessage() + "\"}";
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> importConfigs(String configJson) {
        Map<String, Object> result = new HashMap<>();

        if (!StringUtils.hasText(configJson)) {
            result.put("success", false);
            result.put("message", "配置JSON不能为空");
            return result;
        }

        try {
            // 使用系统用户作为创建人
            WeatherScoringConfig importedConfig = configService.importConfigFromJson(configJson, "system");
            if (importedConfig != null) {
                result.put("success", true);
                result.put("message", "导入成功");
                result.put("configId", importedConfig.getId());
                result.put("configName", importedConfig.getConfigName());
            } else {
                result.put("success", false);
                result.put("message", "导入失败：返回配置为空");
            }
            return result;
        } catch (Exception e) {
            log.error("导入配置失败", e);
            result.put("success", false);
            result.put("message", "导入失败：" + e.getMessage());
            return result;
        }
    }

    @Override
    public String exportScoringResults(String examId, LocalDateTime startDate, LocalDateTime endDate) {
        try {
            // 根据条件获取评分结果
            List<WeatherScoringResult> results;
            if (StringUtils.hasText(examId)) {
                results = getExamScoringResults(examId);
            } else {
                results = resultService.list();
            }

            // 时间过滤
            if (startDate != null || endDate != null) {
                results = results.stream()
                    .filter(r -> {
                        if (r.getCreateTime() == null) return false;
                        Date createTime = r.getCreateTime();
                        if (startDate != null && createTime.before(java.sql.Timestamp.valueOf(startDate))) return false;
                        if (endDate != null && createTime.after(java.sql.Timestamp.valueOf(endDate))) return false;
                        return true;
                    })
                    .collect(java.util.stream.Collectors.toList());
            }

            return objectMapper.writeValueAsString(results);

        } catch (Exception e) {
            log.error("导出评分结果失败", e);
            return "{\"error\":\"导出失败：" + e.getMessage() + "\"}";
        }
    }

    // ==================== 私有辅助方法 ====================

    private boolean checkConfigServiceHealth() {
        try {
            configService.getActiveConfig();
            return true;
        } catch (Exception e) {
            log.debug("配置服务健康检查失败", e);
            return false;
        }
    }

    private boolean checkResultServiceHealth() {
        try {
            resultService.count();
            return true;
        } catch (Exception e) {
            log.debug("结果服务健康检查失败", e);
            return false;
        }
    }

    private boolean checkTaskServiceHealth() {
        try {
            batchTaskService.getRunningTasks();
            return true;
        } catch (Exception e) {
            log.debug("任务服务健康检查失败", e);
            return false;
        }
    }

    private boolean checkEngineHealth() {
        try {
            // 简单检查评分引擎是否可用
            return scoringEngine != null;
        } catch (Exception e) {
            log.debug("评分引擎健康检查失败", e);
            return false;
        }
    }
} 