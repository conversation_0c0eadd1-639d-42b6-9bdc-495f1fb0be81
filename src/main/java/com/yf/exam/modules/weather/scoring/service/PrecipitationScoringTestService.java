package com.yf.exam.modules.weather.scoring.service;

import com.yf.exam.modules.weather.scoring.dto.PrecipitationScoringResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 降水落区评分测试服务
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
@Slf4j
@Service
public class PrecipitationScoringTestService {

    @Autowired
    private PrecipitationAreaScoringService precipitationAreaScoringService;

    /**
     * 执行完整的降水落区评分测试
     */
    public PrecipitationScoringResult runFullTest() {
        log.info("开始执行降水落区评分完整测试");
        
        try {
            // 1. 准备测试数据
            String actualFilePath = "sample_actual.000";
            String cmaMesoFilePath = "sample_cma_meso.004";
            Map<String, Object> studentAnswer = createTestStudentAnswer();
            
            log.info("测试数据准备完成");
            log.info("实况文件：{}", actualFilePath);
            log.info("CMA-MESO文件：{}", cmaMesoFilePath);
            log.info("考生答案：{}", studentAnswer);
            
            // 2. 执行评分
            PrecipitationScoringResult result = precipitationAreaScoringService.calculatePrecipitationScore(
                actualFilePath, cmaMesoFilePath, studentAnswer);
            
            // 3. 输出详细结果
            if (result.isSuccess()) {
                log.info("=== 降水落区评分测试结果 ===");
                log.info("最终得分：{}/40", result.getFinalScore());
                log.info("总站点数：{}", result.getTotalStations());
                
                if (result.getStudentTSScores() != null) {
                    log.info("学生TS评分：");
                    result.getStudentTSScores().forEach((level, score) -> 
                        log.info("  {}：{}", level, String.format("%.3f", score)));
                }
                
                if (result.getCmaMesoTSScores() != null) {
                    log.info("CMA-MESO TS评分：");
                    result.getCmaMesoTSScores().forEach((level, score) -> 
                        log.info("  {}：{}", level, String.format("%.3f", score)));
                }
                
                if (result.getSkillScores() != null) {
                    log.info("技巧评分：");
                    result.getSkillScores().forEach((level, score) -> 
                        log.info("  {}：{}", level, String.format("%.3f", score)));
                }
                
                log.info("评分摘要：\n{}", result.getScoringSummary());
            } else {
                log.error("降水落区评分测试失败：{}", result.getMessage());
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("降水落区评分测试异常", e);
            
            PrecipitationScoringResult errorResult = new PrecipitationScoringResult();
            errorResult.setSuccess(false);
            errorResult.setMessage("测试异常：" + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 创建测试用的考生答案
     * 模拟考生绘制的降水落区
     */
    private Map<String, Object> createTestStudentAnswer() {
        Map<String, Object> answer = new HashMap<>();
        Map<String, Object> content = new HashMap<>();
        
        // 小雨区域（覆盖部分站点）
        content.put("小雨", Arrays.asList(
            createPolygonArea(Arrays.asList(
                Arrays.asList(115.5, 39.0),
                Arrays.asList(117.0, 39.0),
                Arrays.asList(117.0, 40.5),
                Arrays.asList(115.5, 40.5),
                Arrays.asList(115.5, 39.0)
            ))
        ));

        // 中雨区域（覆盖部分站点）
        content.put("中雨", Arrays.asList(
            createPolygonArea(Arrays.asList(
                Arrays.asList(116.5, 40.0),
                Arrays.asList(118.0, 40.0),
                Arrays.asList(118.0, 41.0),
                Arrays.asList(116.5, 41.0),
                Arrays.asList(116.5, 40.0)
            ))
        ));

        // 大雨区域（覆盖少数站点）
        content.put("大雨", Arrays.asList(
            createPolygonArea(Arrays.asList(
                Arrays.asList(117.0, 39.5),
                Arrays.asList(118.0, 39.5),
                Arrays.asList(118.0, 40.5),
                Arrays.asList(117.0, 40.5),
                Arrays.asList(117.0, 39.5)
            ))
        ));
        
        answer.put("content", content);
        return answer;
    }

    /**
     * 创建多边形区域
     */
    private Map<String, Object> createPolygonArea(List<List<Double>> coordinates) {
        Map<String, Object> area = new HashMap<>();
        Map<String, Object> geometry = new HashMap<>();
        
        geometry.put("type", "Polygon");
        geometry.put("coordinates", Arrays.asList(coordinates));
        
        area.put("geometry", geometry);
        return area;
    }

    /**
     * 创建简单的测试答案（只有小雨区域）
     */
    public Map<String, Object> createSimpleTestAnswer() {
        Map<String, Object> answer = new HashMap<>();
        Map<String, Object> content = new HashMap<>();
        
        // 只创建一个小雨区域，覆盖大部分测试站点
        content.put("小雨", Arrays.asList(
            createPolygonArea(Arrays.asList(
                Arrays.asList(115.0, 39.0),
                Arrays.asList(119.0, 39.0),
                Arrays.asList(119.0, 41.0),
                Arrays.asList(115.0, 41.0),
                Arrays.asList(115.0, 39.0)
            ))
        ));
        
        answer.put("content", content);
        return answer;
    }

    /**
     * 测试特定降水等级的评分
     */
    public void testSpecificLevel(String level) {
        log.info("测试{}等级的评分", level);
        
        Map<String, Object> answer = new HashMap<>();
        Map<String, Object> content = new HashMap<>();
        
        // 创建覆盖所有站点的指定等级区域
        content.put(level, Arrays.asList(
            createPolygonArea(Arrays.asList(
                Arrays.asList(115.0, 39.0),
                Arrays.asList(119.0, 39.0),
                Arrays.asList(119.0, 41.0),
                Arrays.asList(115.0, 41.0),
                Arrays.asList(115.0, 39.0)
            ))
        ));
        
        answer.put("content", content);
        
        try {
            PrecipitationScoringResult result = precipitationAreaScoringService.calculatePrecipitationScore(
                "sample_actual.000", "sample_cma_meso.004", answer);
            
            if (result.isSuccess()) {
                log.info("{}等级测试结果：", level);
                log.info("最终得分：{}", result.getFinalScore());
                if (result.getStudentTSScores() != null) {
                    log.info("{}的TS评分：{}", level, result.getStudentTSScores().get(level));
                }
            } else {
                log.error("{}等级测试失败：{}", level, result.getMessage());
            }
        } catch (Exception e) {
            log.error("{}等级测试异常", level, e);
        }
    }

    /**
     * 批量测试所有降水等级
     */
    public void testAllLevels() {
        String[] levels = {"小雨", "中雨", "大雨", "暴雨", "大暴雨"};
        
        log.info("开始批量测试所有降水等级");
        for (String level : levels) {
            testSpecificLevel(level);
        }
        log.info("批量测试完成");
    }
}
