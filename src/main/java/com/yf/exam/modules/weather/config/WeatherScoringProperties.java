package com.yf.exam.modules.weather.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 天气预报评分配置属性类
 * 从application.yml中读取评分配置
 * 
 * <AUTHOR>
 * @since 2024-12-21
 */
@Data
@Component
@ConfigurationProperties(prefix = "weather.scoring")
public class WeatherScoringProperties {

    /**
     * 单站总分
     */
    private Double totalScore = 10.0;

    /**
     * 各要素评分配置
     */
    private Elements elements = new Elements();

    @Data
    public static class Elements {
        /**
         * 风力评分配置
         */
        private WindForce windForce = new WindForce();

        /**
         * 风向评分配置
         */
        private WindDirection windDirection = new WindDirection();

        /**
         * 最低气温评分配置
         */
        private Temperature minTemperature = new Temperature();

        /**
         * 最高气温评分配置
         */
        private Temperature maxTemperature = new Temperature();

        /**
         * 降水评分配置
         */
        private Precipitation precipitation = new Precipitation();

        /**
         * 灾害天气评分配置
         */
        private DisasterWeather disasterWeather = new DisasterWeather();
    }

    @Data
    public static class WindForce {
        private Double maxScore = 1.0;
        private String scoringType = "level_based";
        private Levels levels = new Levels();
        private List<SpecialRule> specialRules;

        @Data
        public static class Levels {
            private Double exact = 1.0;
            private Double adjacent = 0.8;
            private Double wrong = 0.0;
        }

        @Data
        public static class SpecialRule {
            private Integer student;
            private List<Integer> standard;
            private Double score;
        }
    }

    @Data
    public static class WindDirection {
        private Double maxScore = 1.0;
        private Map<String, DirectionConfig> directions;

        @Data
        public static class DirectionConfig {
            private List<String> perfectRanges; // 1.0分的角度范围
            private List<String> goodRanges;    // 0.6分的角度范围
        }
    }

    @Data
    public static class Temperature {
        private Double maxScore = 2.0;
        private String scoringType = "tolerance_based";
        private Boolean toleranceEnabled = true;
        private Integer toleranceRange = 2;
        private String unit = "℃";
        private Levels levels = new Levels();

        @Data
        public static class Levels {
            private Double withinTolerance = 2.0;
            private Double outsideTolerance = 0.0;
        }
    }

    @Data
    public static class Precipitation {
        private Double maxScore = 2.0;
        private String scoringType = "matrix_based";
        private Levels levels = new Levels();

        // 使用更简单的配置方式，在代码中初始化评分矩阵
        public Map<String, Map<String, Double>> getScoreMatrix() {
            return initializePrecipitationMatrix();
        }

        @Data
        public static class Levels {
            private Double exactMatch = 2.0;
            private Double adjacentLevel = 1.2;
            private Double wrong = 0.0;
        }

        /**
         * 初始化降水评分矩阵
         */
        private Map<String, Map<String, Double>> initializePrecipitationMatrix() {
            Map<String, Map<String, Double>> matrix = new HashMap<>();

            // 无雨雪
            Map<String, Double> noRain = new HashMap<>();
            noRain.put("无雨雪", 2.0);
            noRain.put("雨夹雪", 0.0);
            noRain.put("小雨", 0.0);
            noRain.put("小雪", 0.0);
            noRain.put("中雨", 0.0);
            noRain.put("中雪", 0.0);
            noRain.put("大雨", 0.0);
            noRain.put("暴雨", 0.0);
            noRain.put("大雪", 0.0);
            noRain.put("大暴雨以上", 0.0);
            noRain.put("暴雪", 0.0);
            matrix.put("无雨雪", noRain);

            // 雨夹雪
            Map<String, Double> sleet = new HashMap<>();
            sleet.put("无雨雪", 0.0);
            sleet.put("雨夹雪", 2.0);
            sleet.put("小雨", 2.0);
            sleet.put("小雪", 2.0);
            sleet.put("中雨", 1.2);
            sleet.put("中雪", 1.2);
            sleet.put("大雨", 0.0);
            sleet.put("暴雨", 0.0);
            sleet.put("大雪", 0.0);
            sleet.put("大暴雨以上", 0.0);
            sleet.put("暴雪", 0.0);
            matrix.put("雨夹雪", sleet);

            // 小雨
            Map<String, Double> lightRain = new HashMap<>();
            lightRain.put("无雨雪", 0.0);
            lightRain.put("雨夹雪", 2.0);
            lightRain.put("小雨", 2.0);
            lightRain.put("小雪", 0.0);
            lightRain.put("中雨", 1.2);
            lightRain.put("中雪", 0.0);
            lightRain.put("大雨", 0.0);
            lightRain.put("暴雨", 0.0);
            lightRain.put("大雪", 0.0);
            lightRain.put("大暴雨以上", 0.0);
            lightRain.put("暴雪", 0.0);
            matrix.put("小雨", lightRain);

            // 小雪
            Map<String, Double> lightSnow = new HashMap<>();
            lightSnow.put("无雨雪", 0.0);
            lightSnow.put("雨夹雪", 2.0);
            lightSnow.put("小雨", 0.0);
            lightSnow.put("小雪", 2.0);
            lightSnow.put("中雨", 0.0);
            lightSnow.put("中雪", 1.2);
            lightSnow.put("大雨", 0.0);
            lightSnow.put("暴雨", 0.0);
            lightSnow.put("大雪", 0.0);
            lightSnow.put("大暴雨以上", 0.0);
            lightSnow.put("暴雪", 0.0);
            matrix.put("小雪", lightSnow);

            // 中雨
            Map<String, Double> moderateRain = new HashMap<>();
            moderateRain.put("无雨雪", 0.0);
            moderateRain.put("雨夹雪", 1.2);
            moderateRain.put("小雨", 1.2);
            moderateRain.put("小雪", 0.0);
            moderateRain.put("中雨", 2.0);
            moderateRain.put("中雪", 0.0);
            moderateRain.put("大雨", 1.2);
            moderateRain.put("暴雨", 0.0);
            moderateRain.put("大雪", 0.0);
            moderateRain.put("大暴雨以上", 0.0);
            moderateRain.put("暴雪", 0.0);
            matrix.put("中雨", moderateRain);

            // 中雪
            Map<String, Double> moderateSnow = new HashMap<>();
            moderateSnow.put("无雨雪", 0.0);
            moderateSnow.put("雨夹雪", 1.2);
            moderateSnow.put("小雨", 0.0);
            moderateSnow.put("小雪", 1.2);
            moderateSnow.put("中雨", 0.0);
            moderateSnow.put("中雪", 2.0);
            moderateSnow.put("大雨", 0.0);
            moderateSnow.put("暴雨", 0.0);
            moderateSnow.put("大雪", 1.2);
            moderateSnow.put("大暴雨以上", 0.0);
            moderateSnow.put("暴雪", 0.0);
            matrix.put("中雪", moderateSnow);

            // 大雨
            Map<String, Double> heavyRain = new HashMap<>();
            heavyRain.put("无雨雪", 0.0);
            heavyRain.put("雨夹雪", 0.0);
            heavyRain.put("小雨", 0.0);
            heavyRain.put("小雪", 0.0);
            heavyRain.put("中雨", 1.2);
            heavyRain.put("中雪", 0.0);
            heavyRain.put("大雨", 2.0);
            heavyRain.put("暴雨", 1.2);
            heavyRain.put("大雪", 0.0);
            heavyRain.put("大暴雨以上", 0.0);
            heavyRain.put("暴雪", 0.0);
            matrix.put("大雨", heavyRain);

            // 暴雨
            Map<String, Double> stormRain = new HashMap<>();
            stormRain.put("无雨雪", 0.0);
            stormRain.put("雨夹雪", 0.0);
            stormRain.put("小雨", 0.0);
            stormRain.put("小雪", 0.0);
            stormRain.put("中雨", 0.0);
            stormRain.put("中雪", 0.0);
            stormRain.put("大雨", 1.2);
            stormRain.put("暴雨", 2.0);
            stormRain.put("大雪", 0.0);
            stormRain.put("大暴雨以上", 1.2);
            stormRain.put("暴雪", 0.0);
            matrix.put("暴雨", stormRain);

            // 大雪
            Map<String, Double> heavySnow = new HashMap<>();
            heavySnow.put("无雨雪", 0.0);
            heavySnow.put("雨夹雪", 0.0);
            heavySnow.put("小雨", 0.0);
            heavySnow.put("小雪", 0.0);
            heavySnow.put("中雨", 0.0);
            heavySnow.put("中雪", 1.2);
            heavySnow.put("大雨", 0.0);
            heavySnow.put("暴雨", 0.0);
            heavySnow.put("大雪", 2.0);
            heavySnow.put("大暴雨以上", 0.0);
            heavySnow.put("暴雪", 1.2);
            matrix.put("大雪", heavySnow);

            // 大暴雨以上
            Map<String, Double> extremeRain = new HashMap<>();
            extremeRain.put("无雨雪", 0.0);
            extremeRain.put("雨夹雪", 0.0);
            extremeRain.put("小雨", 0.0);
            extremeRain.put("小雪", 0.0);
            extremeRain.put("中雨", 0.0);
            extremeRain.put("中雪", 0.0);
            extremeRain.put("大雨", 0.0);
            extremeRain.put("暴雨", 1.2);
            extremeRain.put("大雪", 0.0);
            extremeRain.put("大暴雨以上", 2.0);
            extremeRain.put("暴雪", 0.0);
            matrix.put("大暴雨以上", extremeRain);

            // 暴雪
            Map<String, Double> stormSnow = new HashMap<>();
            stormSnow.put("无雨雪", 0.0);
            stormSnow.put("雨夹雪", 0.0);
            stormSnow.put("小雨", 0.0);
            stormSnow.put("小雪", 0.0);
            stormSnow.put("中雨", 0.0);
            stormSnow.put("中雪", 0.0);
            stormSnow.put("大雨", 0.0);
            stormSnow.put("暴雨", 0.0);
            stormSnow.put("大雪", 1.2);
            stormSnow.put("大暴雨以上", 0.0);
            stormSnow.put("暴雪", 2.0);
            matrix.put("暴雪", stormSnow);

            return matrix;
        }
    }

    @Data
    public static class DisasterWeather {
        private Double maxScore = 2.0;
        private String scoringType = "exact_match_with_proportion";
        private String algorithm = "proportion_based";
        private List<String> validTypes;
        private Rules rules = new Rules();
        private SpecialRules specialRules = new SpecialRules();

        @Data
        public static class Rules {
            private Double exactMatch = 2.0;
            private Boolean proportionMatch = true;
            private Boolean noExtraPenalty = true;
            private Boolean noneSpecialHandling = true;
        }

        @Data
        public static class SpecialRules {
            private NoneMatch noneMatch = new NoneMatch();
            private String proportionFormula = "匹配数量 / 实况总数量 * 2.0";

            @Data
            public static class NoneMatch {
                private Double bothNone = 2.0;
                private Double oneNone = 0.0;
            }
        }
    }
}
