package com.yf.exam.modules.weather.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yf.exam.core.exception.ServiceException;
import com.yf.exam.modules.exam.entity.Exam;
import com.yf.exam.modules.exam.service.ExamService;
import com.yf.exam.modules.qu.entity.Qu;
import com.yf.exam.modules.qu.service.QuService;
import com.yf.exam.modules.user.UserUtils;
import com.yf.exam.modules.weather.entity.WeatherHistoryExamAnswer;
import com.yf.exam.modules.weather.service.WeatherHistoryExamAnswerService;
import com.yf.exam.modules.weather.service.WeatherHistoryExamResultService;
import com.yf.exam.modules.weather.scoring.service.WeatherScoringResultService;
import com.yf.exam.modules.weather.entity.WeatherScoringResult;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 历史个例考试结果服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Service
public class WeatherHistoryExamResultServiceImpl implements WeatherHistoryExamResultService {

    @Autowired
    private ExamService examService;

    @Autowired
    private QuService quService;

    @Autowired
    private WeatherHistoryExamAnswerService weatherHistoryExamAnswerService;

    @Autowired
    private WeatherScoringResultService weatherScoringResultService;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public Map<String, Object> getExamResult(String examId) {
        String currentUserId = UserUtils.getUserId(true);
        
        // 1. 验证考试是否存在
        Exam exam = examService.getById(examId);
        if (exam == null) {
            throw new ServiceException("考试不存在");
        }

        // 2. 获取用户答案记录
        QueryWrapper<WeatherHistoryExamAnswer> wrapper = new QueryWrapper<>();
        wrapper.eq("exam_id", examId)
               .eq("user_id", currentUserId)
               .orderByDesc("create_time")
               .last("LIMIT 1");
        
        WeatherHistoryExamAnswer userAnswer = weatherHistoryExamAnswerService.getOne(wrapper);
        if (userAnswer == null) {
            throw new ServiceException("您尚未参加该考试");
        }

        // 3. 验证是否已提交
        if (userAnswer.getAnswerStatus() == null || userAnswer.getAnswerStatus() != 1) {
            throw new ServiceException("您尚未提交该考试，无法查看结果");
        }

        // 4. 获取题目信息
        Qu question = quService.getById(exam.getQuestionId());
        if (question == null) {
            throw new ServiceException("题目不存在");
        }

        // 5. 构建结果数据
        Map<String, Object> result = new HashMap<>();
        
        // 基本信息
        result.put("examId", examId);
        result.put("examTitle", exam.getTitle());
        result.put("questionId", question.getId());
        result.put("questionTitle", question.getTitle());
        result.put("submitTime", userAnswer.getSubmitTime());
        result.put("totalScore", userAnswer.getTotalScore());
        result.put("maxScore", 100); // 默认满分100分
        
        // 用户答案数据
        Map<String, Object> userAnswerData = new HashMap<>();
        userAnswerData.put("precipitationAnswer", userAnswer.getPrecipitationAnswer());
        userAnswerData.put("weatherAnswer", userAnswer.getWeatherAnswer());
        result.put("userAnswer", userAnswerData);

        // 标准答案数据（从题目的scenarioData字段中获取）
        Map<String, Object> standardAnswerData = getStandardAnswerFromQuestion(question);
        result.put("standardAnswer", standardAnswerData);

        // 评分详情（从用户答案的得分详情中获取）
        Map<String, Object> scoringDetails = userAnswer.getScoreDetails();
        if (scoringDetails == null) {
            scoringDetails = new HashMap<>();
        }
        result.put("scoringDetails", scoringDetails);

        // 6. 获取批卷详细信息（从el_weather_scoring_result表中获取）
        Map<String, Object> gradingDetails = getGradingDetails(userAnswer.getId());
        result.put("gradingDetails", gradingDetails);

        // 题目场景数据
        result.put("scenarioData", question.getScenarioData());

        return result;
    }

    @Override
    public Map<String, Object> exportExamResult(String examId) {
        // 获取考试结果
        Map<String, Object> examResult = getExamResult(examId);
        
        // 构建导出数据
        Map<String, Object> exportData = new HashMap<>();
        exportData.put("examResult", examResult);
        exportData.put("exportTime", System.currentTimeMillis());
        exportData.put("exportFormat", "JSON");
        
        return exportData;
    }

    /**
     * 从题目的scenarioData字段中获取标准答案
     *
     * @param question 题目实体
     * @return 标准答案数据
     */
    private Map<String, Object> getStandardAnswerFromQuestion(Qu question) {
        Map<String, Object> standardAnswerData = new HashMap<>();

        try {
            String scenarioData = question.getScenarioData();
            if (!StringUtils.hasText(scenarioData)) {
                // 如果没有scenarioData，返回空的标准答案结构
                standardAnswerData.put("precipitationAnswer", new HashMap<>());
                standardAnswerData.put("weatherAnswer", new HashMap<>());
                return standardAnswerData;
            }

            // 解析scenarioData中的JSON数据
            Map<String, Object> scenarioMap = objectMapper.readValue(scenarioData, Map.class);

            // 提取标准答案数据
            if (scenarioMap.containsKey("answers")) {
                Map<String, Object> answers = (Map<String, Object>) scenarioMap.get("answers");

                // 提取降水预报标准答案
                if (answers.containsKey("precipitation")) {
                    standardAnswerData.put("precipitationAnswer", answers.get("precipitation"));
                } else {
                    standardAnswerData.put("precipitationAnswer", new HashMap<>());
                }

                // 提取天气预报标准答案
                if (answers.containsKey("weather")) {
                    standardAnswerData.put("weatherAnswer", answers.get("weather"));
                } else {
                    standardAnswerData.put("weatherAnswer", new HashMap<>());
                }
            } else {
                // 兼容旧的数据结构，直接从scenarioMap中提取
                if (scenarioMap.containsKey("precipitation")) {
                    standardAnswerData.put("precipitationAnswer", scenarioMap.get("precipitation"));
                } else {
                    standardAnswerData.put("precipitationAnswer", new HashMap<>());
                }

                if (scenarioMap.containsKey("weather")) {
                    standardAnswerData.put("weatherAnswer", scenarioMap.get("weather"));
                } else {
                    standardAnswerData.put("weatherAnswer", new HashMap<>());
                }
            }

        } catch (Exception e) {
            // 解析失败时，返回空的标准答案结构
            standardAnswerData.put("precipitationAnswer", new HashMap<>());
            standardAnswerData.put("weatherAnswer", new HashMap<>());
        }

        return standardAnswerData;
    }

    /**
     * 获取批卷详细信息
     *
     * @param answerId 答案ID
     * @return 批卷详细信息
     */
    private Map<String, Object> getGradingDetails(String answerId) {
        Map<String, Object> gradingDetails = new HashMap<>();

        try {
            // 根据答案ID获取评分结果
            WeatherScoringResult scoringResult = weatherScoringResultService.getByAnswerId(answerId);

            if (scoringResult == null) {
                // 如果没有评分结果，返回空的批卷信息
                gradingDetails.put("hasGrading", false);
                gradingDetails.put("message", "暂无批卷信息");
                return gradingDetails;
            }

            // 基本批卷信息
            gradingDetails.put("hasGrading", true);
            gradingDetails.put("resultId", scoringResult.getId());
            gradingDetails.put("isSuccess", scoringResult.getIsSuccess());
            gradingDetails.put("finalScore", scoringResult.getFinalScore());
            gradingDetails.put("totalScore", scoringResult.getTotalScore());
            gradingDetails.put("maxScore", scoringResult.getMaxScore());
            gradingDetails.put("scorePercentage", scoringResult.getScorePercentage());
            gradingDetails.put("scoringTime", scoringResult.getScoringTime());
            gradingDetails.put("configVersion", scoringResult.getConfigVersion());
            gradingDetails.put("scoringDuration", scoringResult.getScoringDuration());

            // 详细结果信息（detail_results字段）
            Map<String, Object> detailResults = scoringResult.getDetailResults();
            if (detailResults != null) {
                gradingDetails.put("detailResults", detailResults);
            } else {
                gradingDetails.put("detailResults", new HashMap<>());
            }

            // 分站得分详情
            Map<String, Object> stationScores = scoringResult.getStationScores();
            if (stationScores != null) {
                gradingDetails.put("stationScores", stationScores);
            } else {
                gradingDetails.put("stationScores", new HashMap<>());
            }

            // 分要素得分详情
            Map<String, Object> elementScores = scoringResult.getElementScores();
            if (elementScores != null) {
                gradingDetails.put("elementScores", elementScores);
            } else {
                gradingDetails.put("elementScores", new HashMap<>());
            }

            // 错误分析
            Map<String, Object> errorAnalysis = scoringResult.getErrorAnalysis();
            if (errorAnalysis != null) {
                gradingDetails.put("errorAnalysis", errorAnalysis);
            } else {
                gradingDetails.put("errorAnalysis", new HashMap<>());
            }

            // 计算评分等级
            if (scoringResult.getScorePercentage() != null) {
                String grade = scoringResult.getGrade();
                gradingDetails.put("grade", grade);
                gradingDetails.put("isPassed", scoringResult.isPassed());
            }

        } catch (Exception e) {
            // 获取批卷信息失败时，返回错误信息
            gradingDetails.put("hasGrading", false);
            gradingDetails.put("error", "获取批卷信息失败: " + e.getMessage());
        }

        return gradingDetails;
    }
}
