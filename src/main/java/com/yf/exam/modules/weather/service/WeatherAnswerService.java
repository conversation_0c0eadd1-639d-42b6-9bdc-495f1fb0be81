package com.yf.exam.modules.weather.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.exam.modules.weather.dto.WeatherAnswerDTO;
import com.yf.exam.modules.weather.entity.WeatherAnswer;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <p>
 * 天气预报答案服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface WeatherAnswerService extends IService<WeatherAnswer> {

    /**
     * 保存答题数据
     * 
     * @param answerDTO 答题数据
     * @return 保存结果
     */
    boolean saveAnswer(WeatherAnswerDTO answerDTO);

    /**
     * 根据试卷题目ID获取答题数据
     * 
     * @param paperQuId 试卷题目ID
     * @return 答题数据
     */
    WeatherAnswerDTO getAnswerByPaperQuId(String paperQuId);

    /**
     * 根据用户ID和试卷题目ID获取答题数据
     * 
     * @param userId 用户ID
     * @param paperQuId 试卷题目ID
     * @return 答题数据
     */
    WeatherAnswerDTO getAnswerByUserAndPaperQu(String userId, String paperQuId);

    /**
     * 验证答案数据
     * 
     * @param answerDTO 答案数据
     * @return 验证结果
     */
    Map<String, Object> validateAnswer(WeatherAnswerDTO answerDTO);

    /**
     * 计算答案得分
     * 
     * @param answerDTO 答案数据
     * @param standardAnswer 标准答案
     * @return 得分结果
     */
    Map<String, Object> calculateScore(WeatherAnswerDTO answerDTO, WeatherAnswerDTO standardAnswer);

    /**
     * 更新答题状态
     * 
     * @param paperQuId 试卷题目ID
     * @param answerStatus 答题状态
     * @return 更新结果
     */
    boolean updateAnswerStatus(String paperQuId, Boolean answerStatus);

    /**
     * 更新答案得分
     * 
     * @param paperQuId 试卷题目ID
     * @param totalScore 总得分
     * @param scoreDetails 得分详情
     * @return 更新结果
     */
    boolean updateScore(String paperQuId, BigDecimal totalScore, Map<String, Object> scoreDetails);

    /**
     * 删除答题数据
     * 
     * @param paperQuId 试卷题目ID
     * @return 删除结果
     */
    boolean deleteAnswer(String paperQuId);
}
