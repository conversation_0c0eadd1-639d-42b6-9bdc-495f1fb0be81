package com.yf.exam.modules.weather.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.exam.modules.weather.dto.WeatherAnswerDTO;
import com.yf.exam.modules.weather.entity.WeatherAnswer;
import com.yf.exam.modules.weather.mapper.WeatherAnswerMapper;
import com.yf.exam.modules.weather.service.WeatherAnswerService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 天气预报答案服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Service
public class WeatherAnswerServiceImpl extends ServiceImpl<WeatherAnswerMapper, WeatherAnswer> 
        implements WeatherAnswerService {

    @Override
    public boolean saveAnswer(WeatherAnswerDTO answerDTO) {
        // 先查询是否已存在
        QueryWrapper<WeatherAnswer> wrapper = new QueryWrapper<>();
        wrapper.eq("paper_qu_id", answerDTO.getPaperQuId());
        wrapper.eq("user_id", answerDTO.getUserId());
        
        WeatherAnswer existingAnswer = this.getOne(wrapper);
        
        if (existingAnswer != null) {
            // 更新现有答案
            BeanUtils.copyProperties(answerDTO, existingAnswer);
            existingAnswer.setUpdateTime(new Date());
            return this.updateById(existingAnswer);
        } else {
            // 创建新答案
            WeatherAnswer answer = new WeatherAnswer();
            BeanUtils.copyProperties(answerDTO, answer);
            answer.setCreateTime(new Date());
            answer.setUpdateTime(new Date());
            return this.save(answer);
        }
    }

    @Override
    public WeatherAnswerDTO getAnswerByPaperQuId(String paperQuId) {
        QueryWrapper<WeatherAnswer> wrapper = new QueryWrapper<>();
        wrapper.eq("paper_qu_id", paperQuId);
        
        WeatherAnswer answer = this.getOne(wrapper);
        if (answer == null) {
            return null;
        }
        
        WeatherAnswerDTO dto = new WeatherAnswerDTO();
        BeanUtils.copyProperties(answer, dto);
        return dto;
    }

    @Override
    public WeatherAnswerDTO getAnswerByUserAndPaperQu(String userId, String paperQuId) {
        QueryWrapper<WeatherAnswer> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        wrapper.eq("paper_qu_id", paperQuId);
        
        WeatherAnswer answer = this.getOne(wrapper);
        if (answer == null) {
            return null;
        }
        
        WeatherAnswerDTO dto = new WeatherAnswerDTO();
        BeanUtils.copyProperties(answer, dto);
        return dto;
    }

    @Override
    public Map<String, Object> validateAnswer(WeatherAnswerDTO answerDTO) {
        Map<String, Object> result = new HashMap<>();
        result.put("valid", true);
        result.put("errors", new HashMap<>());
        
        // 基本验证逻辑
        if (answerDTO.getCellData() == null || answerDTO.getCellData().isEmpty()) {
            result.put("valid", false);
            ((Map<String, String>) result.get("errors")).put("cellData", "答题数据不能为空");
        }
        
        // TODO: 根据表格配置进行更详细的验证
        
        return result;
    }

    @Override
    public Map<String, Object> calculateScore(WeatherAnswerDTO answerDTO, WeatherAnswerDTO standardAnswer) {
        Map<String, Object> result = new HashMap<>();
        
        // 简单的评分逻辑示例
        BigDecimal totalScore = BigDecimal.ZERO;
        Map<String, Object> scoreDetails = new HashMap<>();
        
        if (answerDTO.getCellData() != null && standardAnswer.getCellData() != null) {
            // TODO: 实现具体的评分算法
            // 这里只是示例，实际需要根据业务规则计算
            totalScore = new BigDecimal("80"); // 示例分数
            scoreDetails.put("accuracy", "80%");
            scoreDetails.put("completeness", "100%");
        }
        
        result.put("totalScore", totalScore);
        result.put("scoreDetails", scoreDetails);
        
        return result;
    }

    @Override
    public boolean updateAnswerStatus(String paperQuId, Boolean answerStatus) {
        QueryWrapper<WeatherAnswer> wrapper = new QueryWrapper<>();
        wrapper.eq("paper_qu_id", paperQuId);
        
        WeatherAnswer answer = this.getOne(wrapper);
        if (answer != null) {
            answer.setAnswerStatus(answerStatus);
            answer.setUpdateTime(new Date());
            return this.updateById(answer);
        }
        
        return false;
    }

    @Override
    public boolean updateScore(String paperQuId, BigDecimal totalScore, Map<String, Object> scoreDetails) {
        QueryWrapper<WeatherAnswer> wrapper = new QueryWrapper<>();
        wrapper.eq("paper_qu_id", paperQuId);
        
        WeatherAnswer answer = this.getOne(wrapper);
        if (answer != null) {
            answer.setTotalScore(totalScore);
            answer.setScoreDetails(scoreDetails);
            answer.setUpdateTime(new Date());
            return this.updateById(answer);
        }
        
        return false;
    }

    @Override
    public boolean deleteAnswer(String paperQuId) {
        QueryWrapper<WeatherAnswer> wrapper = new QueryWrapper<>();
        wrapper.eq("paper_qu_id", paperQuId);
        
        return this.remove(wrapper);
    }
}
