package com.yf.exam.modules.weather.service;

import com.yf.exam.modules.weather.scoring.engine.ScoringEngineResult;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 天气预报判卷管理服务接口
 *
 * 提供统一的判卷管理功能，包括：
 * - 单个答案判卷
 * - 批量答案判卷
 * - 判卷任务管理
 * - 判卷结果查询
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface WeatherGradingService {

    // ==================== 单个判卷功能 ====================

    /**
     * 对单个答案进行判卷
     *
     * @param answerId 答案ID
     * @return 判卷结果
     */
    ScoringEngineResult gradeAnswer(String answerId);

    /**
     * 对单个答案进行判卷（指定评分配置）
     *
     * @param answerId 答案ID
     * @param configId 评分配置ID，为空时使用默认配置
     * @return 判卷结果
     */
    ScoringEngineResult gradeAnswer(String answerId, String configId);

    /**
     * 重新判卷（强制重新评分，即使已有评分结果）
     *
     * @param answerId 答案ID
     * @param configId 评分配置ID，为空时使用默认配置
     * @return 重新判卷结果
     */
    ScoringEngineResult regradeAnswer(String answerId, String configId);

    /**
     * 检查答案是否已被判卷
     *
     * @param answerId 答案ID
     * @return true-已判卷，false-未判卷
     */
    boolean isAnswerGraded(String answerId);

    // ==================== 批量判卷功能 ====================

    /**
     * 批量判卷（按考试ID）
     *
     * @param examId 考试ID
     * @param configId 评分配置ID，为空时使用默认配置
     * @param onlyUngraded 是否仅处理未判卷的答案
     * @return 批量任务ID
     */
    String batchGradeByExam(String examId, String configId, boolean onlyUngraded);

    /**
     * 批量判卷（按题目ID）
     *
     * @param questionId 题目ID
     * @param configId 评分配置ID，为空时使用默认配置
     * @param onlyUngraded 是否仅处理未判卷的答案
     * @return 批量任务ID
     */
    String batchGradeByQuestion(String questionId, String configId, boolean onlyUngraded);

    /**
     * 批量判卷（按用户列表）
     *
     * @param userIds 用户ID列表
     * @param examId 考试ID，可选
     * @param configId 评分配置ID，为空时使用默认配置
     * @param onlyUngraded 是否仅处理未判卷的答案
     * @return 批量任务ID
     */
    String batchGradeByUsers(List<String> userIds, String examId, String configId, boolean onlyUngraded);

    /**
     * 批量判卷（按答案ID列表）
     *
     * @param answerIds 答案ID列表
     * @param configId 评分配置ID，为空时使用默认配置
     * @param forceRegrade 是否强制重新判卷
     * @return 批量任务ID
     */
    String batchGradeByAnswers(List<String> answerIds, String configId, boolean forceRegrade);

    /**
     * 批量判卷（按时间范围）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param examId 考试ID，可选
     * @param configId 评分配置ID，为空时使用默认配置
     * @param onlyUngraded 是否仅处理未判卷的答案
     * @return 批量任务ID
     */
    String batchGradeByTimeRange(LocalDateTime startTime, LocalDateTime endTime, 
                                String examId, String configId, boolean onlyUngraded);

    // ==================== 任务管理功能 ====================

    /**
     * 获取批量任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    Map<String, Object> getBatchTaskStatus(String taskId);

    /**
     * 获取批量任务详细进度
     *
     * @param taskId 任务ID
     * @return 任务详细进度信息
     */
    Map<String, Object> getBatchTaskProgress(String taskId);

    /**
     * 停止批量任务
     *
     * @param taskId 任务ID
     * @return 是否成功停止
     */
    boolean stopBatchTask(String taskId);

    /**
     * 重启批量任务
     *
     * @param taskId 任务ID
     * @return 是否成功重启
     */
    boolean restartBatchTask(String taskId);

    /**
     * 获取正在运行的批量任务列表
     *
     * @return 正在运行的任务列表
     */
    List<Map<String, Object>> getRunningBatchTasks();

    /**
     * 获取用户的任务历史
     *
     * @param userId 用户ID
     * @param limit 返回数量限制
     * @return 任务历史列表
     */
    List<Map<String, Object>> getUserTaskHistory(String userId, int limit);

    /**
     * 获取批量任务日志
     *
     * @param taskId 任务ID
     * @return 任务日志列表
     */
    List<String> getBatchTaskLogs(String taskId);

    // ==================== 结果查询功能 ====================

    /**
     * 获取判卷结果
     *
     * @param answerId 答案ID
     * @return 判卷结果详情
     */
    Map<String, Object> getGradingResult(String answerId);

    /**
     * 获取用户的判卷结果列表
     *
     * @param userId 用户ID
     * @param examId 考试ID，可选
     * @return 判卷结果列表
     */
    List<Map<String, Object>> getUserGradingResults(String userId, String examId);

    /**
     * 获取考试的判卷结果统计
     *
     * @param examId 考试ID
     * @return 判卷统计信息
     */
    Map<String, Object> getExamGradingStatistics(String examId);

    /**
     * 获取题目的判卷结果统计
     *
     * @param questionId 题目ID
     * @return 判卷统计信息
     */
    Map<String, Object> getQuestionGradingStatistics(String questionId);

    /**
     * 获取系统判卷总体统计
     *
     * @return 系统级判卷统计信息
     */
    Map<String, Object> getSystemGradingStatistics();

    // ==================== 查询和筛选功能 ====================

    /**
     * 查询未判卷的答案
     *
     * @param examId 考试ID，可选
     * @param questionId 题目ID，可选
     * @param limit 返回数量限制
     * @return 未判卷答案列表
     */
    List<Map<String, Object>> getUngradedAnswers(String examId, String questionId, int limit);

    /**
     * 查询判卷失败的答案
     *
     * @param examId 考试ID，可选
     * @param limit 返回数量限制
     * @return 判卷失败答案列表
     */
    List<Map<String, Object>> getFailedGradingAnswers(String examId, int limit);

    /**
     * 查询需要重新判卷的答案
     *
     * @param examId 考试ID，可选
     * @param configId 评分配置ID，用于查找使用旧配置的答案
     * @param limit 返回数量限制
     * @return 需要重新判卷的答案列表
     */
    List<Map<String, Object>> getAnswersNeedRegrade(String examId, String configId, int limit);

    // ==================== 系统维护功能 ====================

    /**
     * 清理过期的判卷结果
     *
     * @param retentionDays 保留天数
     * @return 清理的结果数量
     */
    int cleanupExpiredResults(int retentionDays);

    /**
     * 清理过期的批量任务
     *
     * @param retentionDays 保留天数
     * @return 清理的任务数量
     */
    int cleanupExpiredTasks(int retentionDays);

    /**
     * 重建判卷统计数据
     *
     * @param examId 考试ID，为空时重建全部
     * @return 重建结果信息
     */
    Map<String, Object> rebuildGradingStatistics(String examId);

    /**
     * 验证判卷系统健康状态
     *
     * @return 健康检查结果
     */
    Map<String, Object> checkGradingSystemHealth();

    // ==================== 配置和管理功能 ====================

    /**
     * 获取判卷配置摘要
     *
     * @return 当前判卷配置信息
     */
    Map<String, Object> getGradingConfigSummary();

    /**
     * 更新判卷配置
     *
     * @param configKey 配置键
     * @param configValue 配置值
     * @return 是否更新成功
     */
    boolean updateGradingConfig(String configKey, String configValue);

    /**
     * 获取判卷性能监控数据
     *
     * @return 性能监控数据
     */
    Map<String, Object> getGradingPerformanceMetrics();
} 