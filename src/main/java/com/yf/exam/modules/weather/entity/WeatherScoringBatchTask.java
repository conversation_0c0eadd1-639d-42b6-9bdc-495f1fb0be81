package com.yf.exam.modules.weather.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yf.exam.config.CustomJacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 天气预报批量评分任务实体类
 * 
 * <AUTHOR>
 * @since 2024-12-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("el_weather_scoring_batch_task")
public class WeatherScoringBatchTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 任务名称
     */
    @TableField("task_name")
    private String taskName;

    /**
     * 考试ID
     */
    @TableField("exam_id")
    private String examId;

    /**
     * 题目ID
     */
    @TableField("question_id")
    private String questionId;

    /**
     * 评分配置ID
     */
    @TableField("config_id")
    private String configId;

    /**
     * 总答案数
     */
    @TableField("total_answers")
    private Integer totalAnswers;

    /**
     * 已处理答案数
     */
    @TableField("processed_answers")
    private Integer processedAnswers;

    /**
     * 成功评分数
     */
    @TableField("success_count")
    private Integer successCount;

    /**
     * 失败评分数
     */
    @TableField("fail_count")
    private Integer failCount;

    /**
     * 任务状态
     */
    @TableField("status")
    private String status;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 结果摘要(JSON格式)
     */
    @TableField(value = "result_summary", typeHandler = CustomJacksonTypeHandler.class)
    private Map<String, Object> resultSummary;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 任务状态枚举
     */
    public static class TaskStatus {
        public static final String PENDING = "pending";
        public static final String RUNNING = "running";
        public static final String COMPLETED = "completed";
        public static final String FAILED = "failed";
        public static final String CANCELLED = "cancelled";
    }

    /**
     * 获取进度百分比
     * 
     * @return 进度百分比
     */
    public Double getProgressPercentage() {
        if (totalAnswers == null || totalAnswers == 0) {
            return 0.0;
        }
        
        int processed = processedAnswers != null ? processedAnswers : 0;
        return (double) processed / totalAnswers * 100;
    }

    /**
     * 获取成功率
     * 
     * @return 成功率百分比
     */
    public Double getSuccessRate() {
        if (processedAnswers == null || processedAnswers == 0) {
            return 0.0;
        }
        
        int success = successCount != null ? successCount : 0;
        return (double) success / processedAnswers * 100;
    }

    /**
     * 获取任务耗时(秒)
     * 
     * @return 任务耗时
     */
    public Long getDurationSeconds() {
        if (startTime == null || endTime == null) {
            return null;
        }
        
        return (endTime.getTime() - startTime.getTime()) / 1000;
    }

    /**
     * 是否已完成
     * 
     * @return 是否已完成
     */
    public boolean isCompleted() {
        return TaskStatus.COMPLETED.equals(status) || TaskStatus.FAILED.equals(status);
    }

    /**
     * 是否正在运行
     * 
     * @return 是否正在运行
     */
    public boolean isRunning() {
        return TaskStatus.RUNNING.equals(status);
    }

    /**
     * 是否成功完成
     * 
     * @return 是否成功完成
     */
    public boolean isSuccessCompleted() {
        return TaskStatus.COMPLETED.equals(status) && 
               (failCount == null || failCount == 0);
    }
} 