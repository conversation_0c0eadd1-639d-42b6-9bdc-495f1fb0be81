package com.yf.exam.modules.weather.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yf.exam.modules.weather.entity.WeatherAnswer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * 天气预报答案表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Mapper
public interface WeatherAnswerMapper extends BaseMapper<WeatherAnswer> {

    /**
     * 根据试卷题目ID和用户ID查询答案
     * 
     * @param paperQuId 试卷题目ID
     * @param userId 用户ID
     * @return 天气预报答案
     */
    WeatherAnswer selectByPaperQuAndUser(@Param("paperQuId") String paperQuId, @Param("userId") String userId);

    /**
     * 根据试卷题目ID查询答案列表
     * 
     * @param paperQuId 试卷题目ID
     * @return 答案列表
     */
    java.util.List<WeatherAnswer> selectByPaperQu(@Param("paperQuId") String paperQuId);

    /**
     * 根据用户ID查询答案列表
     * 
     * @param userId 用户ID
     * @return 答案列表
     */
    java.util.List<WeatherAnswer> selectByUser(@Param("userId") String userId);

    /**
     * 根据表格配置ID查询答案列表
     * 
     * @param tableConfigId 表格配置ID
     * @return 答案列表
     */
    java.util.List<WeatherAnswer> selectByTableConfig(@Param("tableConfigId") String tableConfigId);

    /**
     * 获取答案统计信息
     * 
     * @param paperQuId 试卷题目ID
     * @param tableConfigId 表格配置ID
     * @return 统计信息
     */
    Map<String, Object> selectAnswerStatistics(@Param("paperQuId") String paperQuId, @Param("tableConfigId") String tableConfigId);
}
