package com.yf.exam.modules.weather.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 天气评分配置测试类
 * 用于验证配置文件是否正确加载
 */
@Slf4j
@Component
public class WeatherScoringConfigTest implements CommandLineRunner {

    @Autowired
    private WeatherScoringProperties scoringProperties;

    @Override
    public void run(String... args) throws Exception {
        // 只在开发环境下运行测试
        if (!"dev".equals(System.getProperty("spring.profiles.active", "dev"))) {
            return;
        }
    }
}
