package com.yf.exam.modules.weather.scoring.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.exam.modules.weather.entity.WeatherScoringBatchTask;
import com.yf.exam.modules.weather.mapper.WeatherScoringBatchTaskMapper;
import com.yf.exam.modules.weather.scoring.service.WeatherScoringBatchTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 天气预报批量评分任务Service实现类
 * 
 * <AUTHOR>
 * @since 2024-12-21
 */
@Slf4j
@Service
public class WeatherScoringBatchTaskServiceImpl extends ServiceImpl<WeatherScoringBatchTaskMapper, WeatherScoringBatchTask> 
        implements WeatherScoringBatchTaskService {

    @Autowired
    private WeatherScoringBatchTaskMapper batchTaskMapper;

    // 内存中维护任务日志，实际生产环境可以使用Redis或数据库
    private final Map<String, List<String>> taskLogs = new ConcurrentHashMap<>();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WeatherScoringBatchTask createBatchTask(String batchName, String configId, int totalCount) {
        if (!StringUtils.hasText(batchName) || totalCount <= 0) {
            throw new IllegalArgumentException("任务名称不能为空且总数量必须大于0");
        }

        try {
            WeatherScoringBatchTask task = new WeatherScoringBatchTask();
            task.setId(IdWorker.getIdStr());
            task.setTaskName(batchName);
            task.setConfigId(configId);
            task.setTotalAnswers(totalCount);
            task.setProcessedAnswers(0);
            task.setSuccessCount(0);
            task.setFailCount(0);
            task.setStatus("CREATED");
            task.setCreateTime(new Date());
            task.setUpdateTime(new Date());

            boolean saved = save(task);
            if (saved) {
                log.info("创建批量评分任务成功，任务ID：{}，任务名称：{}，总数量：{}", 
                        task.getId(), batchName, totalCount);
                
                // 初始化任务日志
                taskLogs.put(task.getId(), new ArrayList<>());
                addTaskLog(task.getId(), "任务创建成功，总数量：" + totalCount);
                
                return task;
            } else {
                throw new RuntimeException("保存批量任务失败");
            }
        } catch (Exception e) {
            log.error("创建批量评分任务失败", e);
            throw new RuntimeException("创建批量任务失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProgress(String taskId, int completedCount, int successCount, int failureCount) {
        if (!StringUtils.hasText(taskId)) {
            log.warn("任务ID不能为空");
            return false;
        }

        try {
            WeatherScoringBatchTask task = getById(taskId);
            if (task == null) {
                log.warn("批量任务不存在：{}", taskId);
                return false;
            }

            task.setProcessedAnswers(completedCount);
            task.setSuccessCount(successCount);
            task.setFailCount(failureCount);
            task.setUpdateTime(new Date());

            // 计算进度百分比
            if (task.getTotalAnswers() > 0) {
                double progress = (double) completedCount / task.getTotalAnswers() * 100;
                // 可以在这里添加进度字段到实体类
                addTaskLog(taskId, String.format("进度更新：%d/%d (%.1f%%), 成功:%d, 失败:%d", 
                        completedCount, task.getTotalAnswers(), progress, successCount, failureCount));
            }

            boolean updated = updateById(task);
            log.debug("更新任务进度：{}，已处理：{}，成功：{}，失败：{}", 
                    taskId, completedCount, successCount, failureCount);
            
            return updated;
        } catch (Exception e) {
            log.error("更新任务进度失败，任务ID：{}", taskId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean finalize(String taskId, String status, LocalDateTime endTime) {
        if (!StringUtils.hasText(taskId) || !StringUtils.hasText(status)) {
            log.warn("任务ID和状态不能为空");
            return false;
        }

        try {
            WeatherScoringBatchTask task = getById(taskId);
            if (task == null) {
                log.warn("批量任务不存在：{}", taskId);
                return false;
            }

            task.setStatus(status);
            task.setEndTime(Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant()));
            task.setUpdateTime(new Date());

            boolean updated = updateById(task);
            if (updated) {
                addTaskLog(taskId, "任务完成，最终状态：" + status);
                log.info("完成批量任务：{}，状态：{}，结束时间：{}", taskId, status, endTime);
            }
            
            return updated;
        } catch (Exception e) {
            log.error("完成批量任务失败，任务ID：{}", taskId, e);
            return false;
        }
    }

    @Override
    public List<WeatherScoringBatchTask> getRunningTasks() {
        try {
            LambdaQueryWrapper<WeatherScoringBatchTask> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(WeatherScoringBatchTask::getStatus, Arrays.asList("CREATED", "RUNNING", "PAUSED"))
                   .orderByDesc(WeatherScoringBatchTask::getCreateTime);
            
            return list(wrapper);
        } catch (Exception e) {
            log.error("获取正在运行的任务失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<WeatherScoringBatchTask> getUserTaskHistory(String userId) {
        if (!StringUtils.hasText(userId)) {
            return new ArrayList<>();
        }

        try {
            LambdaQueryWrapper<WeatherScoringBatchTask> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(WeatherScoringBatchTask::getCreateBy, userId)
                   .orderByDesc(WeatherScoringBatchTask::getCreateTime);
            
            return list(wrapper);
        } catch (Exception e) {
            log.error("获取用户任务历史失败，用户ID：{}", userId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> getTaskStatistics(String taskId) {
        Map<String, Object> stats = new HashMap<>();
        
        if (!StringUtils.hasText(taskId)) {
            stats.put("error", "任务ID不能为空");
            return stats;
        }

        try {
            WeatherScoringBatchTask task = getById(taskId);
            if (task == null) {
                stats.put("error", "任务不存在");
                return stats;
            }

            stats.put("task", task);
            stats.put("totalAnswers", task.getTotalAnswers());
            stats.put("processedAnswers", task.getProcessedAnswers());
            stats.put("successCount", task.getSuccessCount());
            stats.put("failCount", task.getFailCount());
            stats.put("status", task.getStatus());

            // 计算进度百分比
            if (task.getTotalAnswers() > 0) {
                double progress = (double) task.getProcessedAnswers() / task.getTotalAnswers() * 100;
                stats.put("progressPercent", Math.round(progress * 100.0) / 100.0);
            } else {
                stats.put("progressPercent", 0.0);
            }

            // 计算成功率
            if (task.getProcessedAnswers() > 0) {
                double successRate = (double) task.getSuccessCount() / task.getProcessedAnswers() * 100;
                stats.put("successRate", Math.round(successRate * 100.0) / 100.0);
            } else {
                stats.put("successRate", 0.0);
            }

            // 计算执行时间
            if (task.getStartTime() != null) {
                Date endTime = task.getEndTime() != null ? task.getEndTime() : new Date();
                long duration = endTime.getTime() - task.getStartTime().getTime();
                stats.put("durationMs", duration);
                stats.put("durationSeconds", duration / 1000);
                stats.put("durationMinutes", duration / (1000 * 60));
            }

            // 估算剩余时间
            if (task.getStartTime() != null && task.getProcessedAnswers() > 0 && 
                !"COMPLETED".equals(task.getStatus()) && !"FAILED".equals(task.getStatus())) {
                
                long elapsed = System.currentTimeMillis() - task.getStartTime().getTime();
                int remaining = task.getTotalAnswers() - task.getProcessedAnswers();
                if (remaining > 0) {
                    long avgTimePerItem = elapsed / task.getProcessedAnswers();
                    long estimatedRemainingMs = avgTimePerItem * remaining;
                    stats.put("estimatedRemainingMs", estimatedRemainingMs);
                    stats.put("estimatedRemainingMinutes", estimatedRemainingMs / (1000 * 60));
                }
            }

        } catch (Exception e) {
            log.error("获取任务统计失败，任务ID：{}", taskId, e);
            stats.put("error", "获取统计失败：" + e.getMessage());
        }

        return stats;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean stopTask(String taskId, String reason) {
        if (!StringUtils.hasText(taskId)) {
            log.warn("任务ID不能为空");
            return false;
        }

        try {
            WeatherScoringBatchTask task = getById(taskId);
            if (task == null) {
                log.warn("批量任务不存在：{}", taskId);
                return false;
            }

            // 检查任务状态是否可以停止
            if (!canStopTask(taskId)) {
                log.warn("任务不能停止，当前状态：{}", task.getStatus());
                return false;
            }

            task.setStatus("CANCELLED");
            task.setErrorMessage(StringUtils.hasText(reason) ? reason : "用户手动停止");
            task.setEndTime(new Date());
            task.setUpdateTime(new Date());

            boolean updated = updateById(task);
            if (updated) {
                addTaskLog(taskId, "任务被停止，原因：" + (StringUtils.hasText(reason) ? reason : "用户手动停止"));
                log.info("停止批量任务成功，任务ID：{}，原因：{}", taskId, reason);
            }
            
            return updated;
        } catch (Exception e) {
            log.error("停止批量任务失败，任务ID：{}", taskId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean restartTask(String taskId) {
        if (!StringUtils.hasText(taskId)) {
            log.warn("任务ID不能为空");
            return false;
        }

        try {
            WeatherScoringBatchTask task = getById(taskId);
            if (task == null) {
                log.warn("批量任务不存在：{}", taskId);
                return false;
            }

            // 只有失败或取消的任务可以重启
            if (!"FAILED".equals(task.getStatus()) && !"CANCELLED".equals(task.getStatus())) {
                log.warn("任务不能重启，当前状态：{}", task.getStatus());
                return false;
            }

            // 重置任务状态
            task.setStatus("CREATED");
            task.setProcessedAnswers(0);
            task.setSuccessCount(0);
            task.setFailCount(0);
            task.setStartTime(null);
            task.setEndTime(null);
            task.setErrorMessage(null);
            task.setUpdateTime(new Date());

            boolean updated = updateById(task);
            if (updated) {
                // 清空并重新初始化日志
                taskLogs.put(taskId, new ArrayList<>());
                addTaskLog(taskId, "任务重启，重置所有计数器");
                log.info("重启批量任务成功，任务ID：{}", taskId);
            }
            
            return updated;
        } catch (Exception e) {
            log.error("重启批量任务失败，任务ID：{}", taskId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanupExpiredTasks(int retentionDays) {
        if (retentionDays <= 0) {
            log.warn("保留天数必须大于0");
            return 0;
        }

        try {
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DAY_OF_MONTH, -retentionDays);
            Date expiredDate = cal.getTime();

            LambdaQueryWrapper<WeatherScoringBatchTask> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(WeatherScoringBatchTask::getStatus, Arrays.asList("COMPLETED", "FAILED", "CANCELLED"))
                   .le(WeatherScoringBatchTask::getEndTime, expiredDate);

            List<WeatherScoringBatchTask> expiredTasks = list(wrapper);
            if (expiredTasks.isEmpty()) {
                return 0;
            }

            // 删除任务记录和日志
            List<String> taskIds = expiredTasks.stream().map(WeatherScoringBatchTask::getId).collect(Collectors.toList());
            boolean removed = removeByIds(taskIds);
            
            if (removed) {
                // 清理内存中的日志
                for (String taskId : taskIds) {
                    taskLogs.remove(taskId);
                }
                
                int cleanupCount = expiredTasks.size();
                log.info("清理过期任务完成，清理数量：{}，保留天数：{}", cleanupCount, retentionDays);
                return cleanupCount;
            }
            
            return 0;
        } catch (Exception e) {
            log.error("清理过期任务失败", e);
            return 0;
        }
    }

    @Override
    public Map<String, Object> getTaskProgress(String taskId) {
        return getTaskStatistics(taskId); // 复用统计方法
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateStatus(List<String> taskIds, String status) {
        if (CollectionUtils.isEmpty(taskIds) || !StringUtils.hasText(status)) {
            return 0;
        }

        try {
            LambdaQueryWrapper<WeatherScoringBatchTask> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(WeatherScoringBatchTask::getId, taskIds);

            WeatherScoringBatchTask updateTask = new WeatherScoringBatchTask();
            updateTask.setStatus(status);
            updateTask.setUpdateTime(new Date());

            boolean updated = update(updateTask, wrapper);
            int updateCount = updated ? taskIds.size() : 0;
            
            log.info("批量更新任务状态完成，更新数量：{}，新状态：{}", updateCount, status);
            return updateCount;
        } catch (Exception e) {
            log.error("批量更新任务状态失败", e);
            return 0;
        }
    }

    @Override
    public boolean canStopTask(String taskId) {
        if (!StringUtils.hasText(taskId)) {
            return false;
        }

        try {
            WeatherScoringBatchTask task = getById(taskId);
            if (task == null) {
                return false;
            }

            // 只有正在运行、已创建或暂停的任务可以停止
            String status = task.getStatus();
            return "RUNNING".equals(status) || "CREATED".equals(status) || "PAUSED".equals(status);
        } catch (Exception e) {
            log.error("检查任务是否可停止失败，任务ID：{}", taskId, e);
            return false;
        }
    }

    @Override
    public List<String> getTaskLogs(String taskId) {
        if (!StringUtils.hasText(taskId)) {
            return new ArrayList<>();
        }

        List<String> logs = taskLogs.get(taskId);
        return logs != null ? new ArrayList<>(logs) : new ArrayList<>();
    }

    /**
     * 添加任务日志
     */
    private void addTaskLog(String taskId, String message) {
        if (!StringUtils.hasText(taskId) || !StringUtils.hasText(message)) {
            return;
        }

        try {
            List<String> logs = taskLogs.computeIfAbsent(taskId, k -> new ArrayList<>());
            String logEntry = String.format("[%tF %<tT] %s", new Date(), message);
            logs.add(logEntry);

            // 限制日志数量，避免内存溢出
            if (logs.size() > 1000) {
                logs.remove(0);
            }
        } catch (Exception e) {
            log.error("添加任务日志失败，任务ID：{}，消息：{}", taskId, message, e);
        }
    }
} 