package com.yf.exam.modules.weather.micaps;

/**
 * MICAPS站点数据模型
 * 用于第一类数据中的站点信息
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */
public class MicapsStation {
    
    /** 区站号 */
    private long stationId;
    
    /** 经度 */
    private double longitude;
    
    /** 纬度 */
    private double latitude;
    
    /** 拔海高度 */
    private double elevation;
    
    /** 站点级别 */
    private int level;
    
    /** 总云量 */
    private Integer totalCloudCover;
    
    /** 风向 */
    private Integer windDirection;
    
    /** 风速 */
    private Integer windSpeed;
    
    /** 海平面气压（或本站气压） */
    private Double pressure;
    
    /** 3小时变压 */
    private Double pressureChange3h;
    
    /** 过去天气1 */
    private Integer pastWeather1;
    
    /** 过去天气2 */
    private Integer pastWeather2;
    
    /** 6小时降水 */
    private Double precipitation6h;
    
    /** 低云状 */
    private Integer lowCloudType;
    
    /** 低云量 */
    private Integer lowCloudAmount;
    
    /** 低云高 */
    private Double lowCloudHeight;
    
    /** 露点 */
    private Double dewPoint;
    
    /** 能见度 */
    private Double visibility;
    
    /** 现在天气 */
    private Integer presentWeather;
    
    /** 温度 */
    private Double temperature;
    
    /** 中云状 */
    private Integer middleCloudType;
    
    /** 高云状 */
    private Integer highCloudType;
    
    /** 标志1 */
    private Integer flag1;
    
    /** 标志2 */
    private Integer flag2;
    
    /** 24小时变温 */
    private Double temperatureChange24h;
    
    /** 24小时变压 */
    private Double pressureChange24h;
    
    // Getters and Setters
    
    public long getStationId() {
        return stationId;
    }
    
    public void setStationId(long stationId) {
        this.stationId = stationId;
    }
    
    public double getLongitude() {
        return longitude;
    }
    
    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }
    
    public double getLatitude() {
        return latitude;
    }
    
    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }
    
    public double getElevation() {
        return elevation;
    }
    
    public void setElevation(double elevation) {
        this.elevation = elevation;
    }
    
    public int getLevel() {
        return level;
    }
    
    public void setLevel(int level) {
        this.level = level;
    }
    
    public Integer getTotalCloudCover() {
        return totalCloudCover;
    }
    
    public void setTotalCloudCover(Integer totalCloudCover) {
        this.totalCloudCover = totalCloudCover;
    }
    
    public Integer getWindDirection() {
        return windDirection;
    }
    
    public void setWindDirection(Integer windDirection) {
        this.windDirection = windDirection;
    }
    
    public Integer getWindSpeed() {
        return windSpeed;
    }
    
    public void setWindSpeed(Integer windSpeed) {
        this.windSpeed = windSpeed;
    }
    
    public Double getPressure() {
        return pressure;
    }
    
    public void setPressure(Double pressure) {
        this.pressure = pressure;
    }
    
    public Double getPressureChange3h() {
        return pressureChange3h;
    }
    
    public void setPressureChange3h(Double pressureChange3h) {
        this.pressureChange3h = pressureChange3h;
    }
    
    public Integer getPastWeather1() {
        return pastWeather1;
    }
    
    public void setPastWeather1(Integer pastWeather1) {
        this.pastWeather1 = pastWeather1;
    }
    
    public Integer getPastWeather2() {
        return pastWeather2;
    }
    
    public void setPastWeather2(Integer pastWeather2) {
        this.pastWeather2 = pastWeather2;
    }
    
    public Double getPrecipitation6h() {
        return precipitation6h;
    }
    
    public void setPrecipitation6h(Double precipitation6h) {
        this.precipitation6h = precipitation6h;
    }
    
    public Integer getLowCloudType() {
        return lowCloudType;
    }
    
    public void setLowCloudType(Integer lowCloudType) {
        this.lowCloudType = lowCloudType;
    }
    
    public Integer getLowCloudAmount() {
        return lowCloudAmount;
    }
    
    public void setLowCloudAmount(Integer lowCloudAmount) {
        this.lowCloudAmount = lowCloudAmount;
    }
    
    public Double getLowCloudHeight() {
        return lowCloudHeight;
    }
    
    public void setLowCloudHeight(Double lowCloudHeight) {
        this.lowCloudHeight = lowCloudHeight;
    }
    
    public Double getDewPoint() {
        return dewPoint;
    }
    
    public void setDewPoint(Double dewPoint) {
        this.dewPoint = dewPoint;
    }
    
    public Double getVisibility() {
        return visibility;
    }
    
    public void setVisibility(Double visibility) {
        this.visibility = visibility;
    }
    
    public Integer getPresentWeather() {
        return presentWeather;
    }
    
    public void setPresentWeather(Integer presentWeather) {
        this.presentWeather = presentWeather;
    }
    
    public Double getTemperature() {
        return temperature;
    }
    
    public void setTemperature(Double temperature) {
        this.temperature = temperature;
    }
    
    public Integer getMiddleCloudType() {
        return middleCloudType;
    }
    
    public void setMiddleCloudType(Integer middleCloudType) {
        this.middleCloudType = middleCloudType;
    }
    
    public Integer getHighCloudType() {
        return highCloudType;
    }
    
    public void setHighCloudType(Integer highCloudType) {
        this.highCloudType = highCloudType;
    }
    
    public Integer getFlag1() {
        return flag1;
    }
    
    public void setFlag1(Integer flag1) {
        this.flag1 = flag1;
    }
    
    public Integer getFlag2() {
        return flag2;
    }
    
    public void setFlag2(Integer flag2) {
        this.flag2 = flag2;
    }
    
    public Double getTemperatureChange24h() {
        return temperatureChange24h;
    }
    
    public void setTemperatureChange24h(Double temperatureChange24h) {
        this.temperatureChange24h = temperatureChange24h;
    }
    
    public Double getPressureChange24h() {
        return pressureChange24h;
    }
    
    public void setPressureChange24h(Double pressureChange24h) {
        this.pressureChange24h = pressureChange24h;
    }
    
    /**
     * 获取风力等级（根据风速计算）
     * 
     * @return 风力等级（0-12级）
     */
    public Integer getWindForceLevel() {
        if (windSpeed == null) {
            return null;
        }
        
        // 根据蒲福风级标准计算风力等级
        if (windSpeed < 1) return 0;
        else if (windSpeed <= 5) return 1;
        else if (windSpeed <= 11) return 2;
        else if (windSpeed <= 19) return 3;
        else if (windSpeed <= 28) return 4;
        else if (windSpeed <= 38) return 5;
        else if (windSpeed <= 49) return 6;
        else if (windSpeed <= 61) return 7;
        else if (windSpeed <= 74) return 8;
        else if (windSpeed <= 88) return 9;
        else if (windSpeed <= 102) return 10;
        else if (windSpeed <= 117) return 11;
        else return 12;
    }
    
    /**
     * 获取八方位风向描述
     * 
     * @return 风向描述（如"北风"、"东北风"等）
     */
    public String getWindDirectionDescription() {
        if (windDirection == null) {
            return null;
        }
        
        // 将360度风向转换为8方位
        int direction = windDirection % 360;
        if (direction < 0) direction += 360;
        
        if (direction >= 337.5 || direction < 22.5) return "北风";
        else if (direction < 67.5) return "东北风";
        else if (direction < 112.5) return "东风";
        else if (direction < 157.5) return "东南风";
        else if (direction < 202.5) return "南风";
        else if (direction < 247.5) return "西南风";
        else if (direction < 292.5) return "西风";
        else return "西北风";
    }
    
    @Override
    public String toString() {
        return "MicapsStation{" +
                "stationId=" + stationId +
                ", longitude=" + longitude +
                ", latitude=" + latitude +
                ", elevation=" + elevation +
                ", temperature=" + temperature +
                ", pressure=" + pressure +
                ", windDirection=" + windDirection +
                ", windSpeed=" + windSpeed +
                '}';
    }
}
