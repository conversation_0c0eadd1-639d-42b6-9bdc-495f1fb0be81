package com.yf.exam.modules.weather.micaps;

/**
 * MICAPS站点数据
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
public class MicapsStation {
    
    /** 区站号 */
    private long stationId;
    
    /** 经度 */
    private double longitude;
    
    /** 纬度 */
    private double latitude;
    
    /** 海拔高度 */
    private double elevation;
    
    /** 温度 */
    private Double temperature;
    
    /** 气压 */
    private Double pressure;
    
    /** 风向 */
    private Integer windDirection;
    
    /** 风速 */
    private Integer windSpeed;
    
    /** 6小时降水量 */
    private Double precipitation6h;
    
    /** 能见度 */
    private Double visibility;
    
    public long getStationId() {
        return stationId;
    }
    
    public void setStationId(long stationId) {
        this.stationId = stationId;
    }
    
    public double getLongitude() {
        return longitude;
    }
    
    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }
    
    public double getLatitude() {
        return latitude;
    }
    
    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }
    
    public double getElevation() {
        return elevation;
    }
    
    public void setElevation(double elevation) {
        this.elevation = elevation;
    }
    
    public Double getTemperature() {
        return temperature;
    }
    
    public void setTemperature(Double temperature) {
        this.temperature = temperature;
    }
    
    public Double getPressure() {
        return pressure;
    }
    
    public void setPressure(Double pressure) {
        this.pressure = pressure;
    }
    
    public Integer getWindDirection() {
        return windDirection;
    }
    
    public void setWindDirection(Integer windDirection) {
        this.windDirection = windDirection;
    }
    
    public Integer getWindSpeed() {
        return windSpeed;
    }
    
    public void setWindSpeed(Integer windSpeed) {
        this.windSpeed = windSpeed;
    }
    
    public Double getPrecipitation6h() {
        return precipitation6h;
    }
    
    public void setPrecipitation6h(Double precipitation6h) {
        this.precipitation6h = precipitation6h;
    }
    
    public Double getVisibility() {
        return visibility;
    }
    
    public void setVisibility(Double visibility) {
        this.visibility = visibility;
    }
    
    @Override
    public String toString() {
        return "MicapsStation{" +
                "stationId=" + stationId +
                ", longitude=" + longitude +
                ", latitude=" + latitude +
                ", elevation=" + elevation +
                ", temperature=" + temperature +
                ", pressure=" + pressure +
                ", windDirection=" + windDirection +
                ", windSpeed=" + windSpeed +
                ", precipitation6h=" + precipitation6h +
                ", visibility=" + visibility +
                '}';
    }
}
