package com.yf.exam.modules.weather.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 天气预报表格配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("el_weather_table_config")
public class WeatherTableConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 配置名称
     */
    @TableField("name")
    private String name;

    /**
     * 模板类型
     */
    @TableField("template_type")
    private String templateType;

    /**
     * 表格标题
     */
    @TableField("title")
    private String title;

    /**
     * 表格描述
     */
    @TableField("description")
    private String description;

    /**
     * 表格结构配置JSON
     */
    @TableField(value = "table_schema", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> tableSchema;

    /**
     * 数据验证规则JSON
     */
    @TableField(value = "validation_rules", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> validationRules;

    /**
     * 评分配置JSON
     */
    @TableField(value = "scoring_config", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> scoringConfig;

    /**
     * 是否启用
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;
}
