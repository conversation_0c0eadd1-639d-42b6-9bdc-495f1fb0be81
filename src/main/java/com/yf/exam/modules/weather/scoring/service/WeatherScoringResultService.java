package com.yf.exam.modules.weather.scoring.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.exam.modules.weather.entity.WeatherScoringResult;

import java.util.List;
import java.util.Map;

/**
 * 天气预报评分结果Service接口
 * 
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface WeatherScoringResultService extends IService<WeatherScoringResult> {

    /**
     * 根据答案ID获取评分结果
     * 
     * @param answerId 答案ID
     * @return 评分结果
     */
    WeatherScoringResult getByAnswerId(String answerId);

    /**
     * 根据答案ID删除评分结果
     * 
     * @param answerId 答案ID
     * @return 删除是否成功
     */
    boolean deleteByAnswerId(String answerId);

    /**
     * 根据批量任务ID获取评分结果列表
     * 
     * @param batchTaskId 批量任务ID
     * @return 评分结果列表
     */
    List<WeatherScoringResult> listByBatchTaskId(String batchTaskId);

    /**
     * 根据用户ID获取评分结果列表
     * 
     * @param userId 用户ID
     * @return 评分结果列表
     */
    List<WeatherScoringResult> listByUserId(String userId);

    /**
     * 根据考试ID获取评分结果列表
     * 
     * @param examId 考试ID
     * @return 评分结果列表
     */
    List<WeatherScoringResult> listByExamId(String examId);

    /**
     * 获取用户的评分统计
     * 
     * @param userId 用户ID
     * @return 统计结果
     */
    Map<String, Object> getUserScoringStats(String userId);

    /**
     * 获取考试的评分统计
     * 
     * @param examId 考试ID
     * @return 统计结果
     */
    Map<String, Object> getExamScoringStats(String examId);

    /**
     * 获取站点的评分统计
     * 
     * @param stationCode 站点代码
     * @return 统计结果
     */
    Map<String, Object> getStationScoringStats(String stationCode);

    /**
     * 批量保存评分结果
     * 
     * @param results 评分结果列表
     * @return 保存成功的数量
     */
    int batchSave(List<WeatherScoringResult> results);

    /**
     * 更新评分结果状态
     * 
     * @param resultId 结果ID
     * @param isSuccess 是否成功
     * @param errorMessage 错误信息
     * @return 更新是否成功
     */
    boolean updateStatus(String resultId, boolean isSuccess, String errorMessage);

    /**
     * 获取评分结果详情（包含关联信息）
     * 
     * @param resultId 结果ID
     * @return 详情信息
     */
    Map<String, Object> getResultDetail(String resultId);
} 