package com.yf.exam.modules.weather.scoring.algorithm;

import java.util.*;

/**
 * WeatherDataComparator修复验证工具
 * 用于验证修复后的数据结构处理是否正确
 */
public class WeatherDataComparatorTestRunner {

    public static void main(String[] args) {
        System.out.println("开始验证WeatherDataComparator修复...");
        
        WeatherDataComparator comparator = new WeatherDataComparator();
        
        try {
            // 测试1：嵌套结构数据
            testNestedStructure(comparator);
            System.out.println("✓ 嵌套结构数据测试通过");
            
            // 测试2：平铺结构数据（向后兼容）
            testFlatStructure(comparator);
            System.out.println("✓ 平铺结构数据测试通过");
            
            // 测试3：空数据处理
            testEmptyData(comparator);
            System.out.println("✓ 空数据处理测试通过");
            
            System.out.println("\n🎉 所有测试通过！修复成功。");
            System.out.println("现在compareWeatherData方法能够正确处理嵌套的站点数据结构。");
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void testNestedStructure(WeatherDataComparator comparator) {
        // 构造嵌套结构的预测数据
        Map<String, Object> predictedData = new HashMap<>();
        List<Map<String, Object>> predictedStations = new ArrayList<>();
        
        Map<String, Object> predictedStation = new HashMap<>();
        predictedStation.put("stationId", "station001");
        predictedStation.put("stationName", "测试站点");
        predictedStation.put("windForce", "3级");
        predictedStation.put("windDirection", "东北");
        predictedStation.put("maxTemperature", 25);
        predictedStation.put("minTemperature", 15);
        predictedStation.put("precipitation", "小雨");
        predictedStation.put("disasterWeather", "无");
        
        predictedStations.add(predictedStation);
        predictedData.put("stations", predictedStations);

        // 构造嵌套结构的实际数据
        Map<String, Object> actualData = new HashMap<>();
        List<Map<String, Object>> actualStations = new ArrayList<>();
        
        Map<String, Object> actualStation = new HashMap<>();
        actualStation.put("stationId", "station001");
        actualStation.put("stationName", "测试站点");
        actualStation.put("windForce", "3级");
        actualStation.put("windDirection", "东北");
        actualStation.put("maxTemperature", 26);
        actualStation.put("minTemperature", 14);
        actualStation.put("precipitation", "小雨");
        actualStation.put("disasterWeather", "无");
        
        actualStations.add(actualStation);
        actualData.put("stations", actualStations);

        // 执行比较（现在使用配置文件中的权重和容差配置）
        WeatherComparisonResult result = comparator.compareWeatherData(
            predictedData, actualData
        );

        // 验证结果
        if (result == null) {
            throw new RuntimeException("比较结果为null");
        }
        if (result.getOverallScore() <= 0.0) {
            throw new RuntimeException("总体得分应该大于0，实际得分：" + result.getOverallScore());
        }
        if (result.getDetailResults() == null || result.getDetailResults().isEmpty()) {
            throw new RuntimeException("详细结果为空");
        }
        
        // 验证各要素都有比较结果
        String[] expectedElements = {"windForce", "windDirection", "maxTemperature", "minTemperature", "precipitation", "disasterWeather"};
        for (String element : expectedElements) {
            if (!result.getDetailResults().containsKey(element)) {
                throw new RuntimeException("缺少" + element + "比较结果");
            }
        }
        
        System.out.println("  - 嵌套结构数据总体得分：" + String.format("%.2f", result.getOverallScore()));
        System.out.println("  - 详细结果数量：" + result.getDetailResults().size());
    }

    private static void testFlatStructure(WeatherDataComparator comparator) {
        // 构造平铺结构的数据（向后兼容）
        Map<String, Object> predictedData = new HashMap<>();
        predictedData.put("windForce", "4级");
        predictedData.put("windDirection", "东");
        predictedData.put("maxTemperature", 28);
        predictedData.put("minTemperature", 18);
        predictedData.put("precipitation", "中雨");
        predictedData.put("disasterWeather", "雷暴");

        Map<String, Object> actualData = new HashMap<>();
        actualData.put("windForce", "4级");
        actualData.put("windDirection", "东");
        actualData.put("maxTemperature", 27);
        actualData.put("minTemperature", 17);
        actualData.put("precipitation", "中雨");
        actualData.put("disasterWeather", "雷暴");

        // 执行比较（现在使用配置文件中的权重和容差配置）
        WeatherComparisonResult result = comparator.compareWeatherData(
            predictedData, actualData
        );

        // 验证结果
        if (result == null) {
            throw new RuntimeException("比较结果为null");
        }
        if (result.getOverallScore() <= 0.0) {
            throw new RuntimeException("总体得分应该大于0，实际得分：" + result.getOverallScore());
        }
        if (result.getDetailResults() == null) {
            throw new RuntimeException("详细结果为null");
        }
        
        System.out.println("  - 平铺结构数据总体得分：" + String.format("%.2f", result.getOverallScore()));
    }

    private static void testEmptyData(WeatherDataComparator comparator) {
        // 测试空站点数据的处理
        Map<String, Object> predictedData = new HashMap<>();
        predictedData.put("stations", new ArrayList<>());

        Map<String, Object> actualData = new HashMap<>();
        actualData.put("stations", new ArrayList<>());

        WeatherComparisonResult result = comparator.compareWeatherData(
            predictedData, actualData
        );

        if (result == null) {
            throw new RuntimeException("比较结果为null");
        }
        if (result.getOverallScore() != 0.0) {
            throw new RuntimeException("空站点数据的得分应该为0，实际得分：" + result.getOverallScore());
        }
        if (result.isOverallMatch()) {
            throw new RuntimeException("空站点数据不应该匹配");
        }
        
        System.out.println("  - 空数据处理正常，得分：" + result.getOverallScore());
    }
}
