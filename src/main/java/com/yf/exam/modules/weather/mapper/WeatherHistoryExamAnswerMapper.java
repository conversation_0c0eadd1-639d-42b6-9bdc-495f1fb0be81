package com.yf.exam.modules.weather.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yf.exam.modules.weather.entity.WeatherHistoryExamAnswer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 历史个例考试答案表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Mapper
public interface WeatherHistoryExamAnswerMapper extends BaseMapper<WeatherHistoryExamAnswer> {

    /**
     * 根据考试ID、题目ID和用户ID查询答案
     *
     * @param examId     考试ID
     * @param questionId 题目ID
     * @param userId     用户ID
     * @return 答案实体
     */
    WeatherHistoryExamAnswer findByExamAndQuestionAndUser(@Param("examId") String examId,
                                                          @Param("questionId") String questionId,
                                                          @Param("userId") String userId);

    /**
     * 更新评分信息
     *
     * @param answerId 答案ID
     * @param scoringResultId 评分结果ID（存储在gradingRemark字段中）
     * @param score 总分
     * @param gradingStatus 批改状态描述
     * @return 影响行数
     */
    @Update("UPDATE el_weather_history_exam_answer SET " +
            "total_score = #{score}, " +
            "grading_status = 1, " +
            "grading_time = NOW(), " +
            "grading_remark = #{gradingStatus}, " +
            "update_time = NOW() " +
            "WHERE id = #{answerId}")
    int updateScoringInfo(@Param("answerId") String answerId, 
                         @Param("scoringResultId") String scoringResultId,
                         @Param("score") double score, 
                         @Param("gradingStatus") String gradingStatus);

    /**
     * 根据条件查询答案ID列表
     *
     * @param examId 考试ID（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 答案ID列表
     */
    @Select("<script>" +
            "SELECT id FROM el_weather_history_exam_answer WHERE 1=1 " +
            "<if test='examId != null and examId != \"\"'>" +
            "AND exam_id = #{examId} " +
            "</if>" +
            "<if test='startDate != null'>" +
            "AND create_time >= #{startDate} " +
            "</if>" +
            "<if test='endDate != null'>" +
            "AND create_time &lt;= #{endDate} " +
            "</if>" +
            "ORDER BY create_time DESC" +
            "</script>")
    List<String> queryAnswerIdsByCondition(@Param("examId") String examId,
                                          @Param("startDate") LocalDateTime startDate,
                                          @Param("endDate") LocalDateTime endDate);

    /**
     * 根据答案ID列表查询用户答案和正确答案
     *
     * @param answerIds 答案ID列表
     * @return 答案列表
     */
    @Select("<script>" +
            "SELECT id, exam_id, question_id, user_id, precipitation_answer, weather_answer " +
            "FROM el_weather_history_exam_answer WHERE id IN " +
            "<foreach collection='answerIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    List<WeatherHistoryExamAnswer> selectBatchAnswers(@Param("answerIds") List<String> answerIds);

    /**
     * 查询已提交但未评分的答案
     *
     * @param examId 考试ID（可选）
     * @param limit 限制数量
     * @return 答案列表
     */
    @Select("<script>" +
            "SELECT * FROM el_weather_history_exam_answer WHERE answer_status = 1 AND grading_status = 0 " +
            "<if test='examId != null and examId != \"\"'>" +
            "AND exam_id = #{examId} " +
            "</if>" +
            "ORDER BY submit_time ASC LIMIT #{limit}" +
            "</script>")
    List<WeatherHistoryExamAnswer> selectUnScoredAnswers(@Param("examId") String examId, @Param("limit") int limit);

    /**
     * 统计答案评分状态
     *
     * @param examId 考试ID
     * @return 统计结果
     */
    @Select("SELECT " +
            "COUNT(*) as totalCount, " +
            "COUNT(CASE WHEN answer_status = 1 THEN 1 END) as submittedCount, " +
            "COUNT(CASE WHEN grading_status = 1 THEN 1 END) as gradedCount, " +
            "AVG(CASE WHEN total_score IS NOT NULL THEN total_score END) as avgScore " +
            "FROM el_weather_history_exam_answer WHERE exam_id = #{examId}")
    java.util.Map<String, Object> selectScoringStatistics(@Param("examId") String examId);

    /**
     * 更新批改状态
     *
     * @param answerIds 答案ID列表
     * @param status 批改状态（0-未批改，1-已批改）
     * @return 影响行数
     */
    @Update("<script>" +
            "UPDATE el_weather_history_exam_answer SET " +
            "grading_status = #{status}, " +
            "update_time = NOW() " +
            "WHERE id IN " +
            "<foreach collection='answerIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int batchUpdateGradingStatus(@Param("answerIds") List<String> answerIds, @Param("status") int status);

    /**
     * 清理测试数据
     *
     * @param beforeDate 清理此日期之前的数据
     * @return 清理的记录数
     */
    @Update("DELETE FROM el_weather_history_exam_answer WHERE grading_status = 0 AND create_time < #{beforeDate}")
    int cleanupTestData(@Param("beforeDate") LocalDateTime beforeDate);
}
