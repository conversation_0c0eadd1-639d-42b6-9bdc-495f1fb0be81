package com.yf.exam.modules.weather.scoring.service;

import com.yf.exam.modules.weather.entity.WeatherScoringBatchTask;
import com.yf.exam.modules.weather.entity.WeatherScoringConfig;
import com.yf.exam.modules.weather.entity.WeatherScoringResult;
import com.yf.exam.modules.weather.scoring.engine.ScoringEngineResult;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 天气预报评分系统统一业务入口Service接口
 * 
 * 提供评分系统的所有业务功能的统一入口，包括：
 * - 评分配置管理
 * - 单个和批量评分计算
 * - 评分结果查询和统计
 * - 批量任务管理
 * - 系统监控和管理
 * 
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface WeatherScoringService {

    // ==================== 评分配置管理 ====================

    /**
     * 获取当前活跃的评分配置
     * 
     * @return 活跃的评分配置，如果没有则返回null
     */
    WeatherScoringConfig getActiveConfig();

    /**
     * 根据版本获取评分配置
     * 
     * @param version 配置版本
     * @return 指定版本的配置，如果不存在则返回null
     */
    WeatherScoringConfig getConfigByVersion(String version);

    /**
     * 保存评分配置
     * 
     * @param config 评分配置
     * @return 保存后的配置
     */
    WeatherScoringConfig saveConfig(WeatherScoringConfig config);

    /**
     * 激活指定版本的配置
     * 
     * @param version 要激活的配置版本
     * @return 是否激活成功
     */
    boolean activateConfig(String version);

    /**
     * 获取配置版本历史
     * 
     * @return 配置版本历史列表
     */
    List<WeatherScoringConfig> getConfigHistory();

    // ==================== 评分计算功能 ====================

    /**
     * 对单个答案进行评分
     * 
     * @param answerId 答案ID
     * @return 评分结果
     */
    ScoringEngineResult calculateScore(String answerId);

    /**
     * 对单个答案使用指定配置进行评分
     * 
     * @param answerId 答案ID
     * @param configId 评分配置ID
     * @return 评分结果
     */
    ScoringEngineResult calculateScore(String answerId, String configId);

    /**
     * 批量评分
     * 
     * @param answerIds 答案ID列表
     * @param batchName 批量任务名称
     * @return 批量任务ID
     */
    String batchCalculateScore(List<String> answerIds, String batchName);

    /**
     * 批量评分使用指定配置
     * 
     * @param answerIds 答案ID列表
     * @param configId 评分配置ID
     * @param batchName 批量任务名称
     * @return 批量任务ID
     */
    String batchCalculateScore(List<String> answerIds, String configId, String batchName);

    /**
     * 根据条件批量评分
     * 
     * @param examId 考试ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param batchName 批量任务名称
     * @return 批量任务ID
     */
    String batchCalculateScoreByCondition(String examId, LocalDateTime startDate, LocalDateTime endDate, String batchName);

    /**
     * 重新计算评分
     * 
     * @param resultId 评分结果ID
     * @param configId 新的配置ID（可选）
     * @return 重新计算的结果
     */
    ScoringEngineResult recalculateScore(String resultId, String configId);

    // ==================== 评分结果查询 ====================

    /**
     * 根据答案ID获取评分结果
     * 
     * @param answerId 答案ID
     * @return 评分结果
     */
    WeatherScoringResult getScoringResult(String answerId);

    /**
     * 获取用户的评分结果列表
     * 
     * @param userId 用户ID
     * @return 评分结果列表
     */
    List<WeatherScoringResult> getUserScoringResults(String userId);

    /**
     * 获取考试的评分结果列表
     * 
     * @param examId 考试ID
     * @return 评分结果列表
     */
    List<WeatherScoringResult> getExamScoringResults(String examId);

    /**
     * 删除评分结果
     * 
     * @param answerId 答案ID
     * @return 是否删除成功
     */
    boolean deleteScoringResult(String answerId);

    // ==================== 统计分析功能 ====================

    /**
     * 获取用户评分统计
     * 
     * @param userId 用户ID
     * @return 统计结果
     */
    Map<String, Object> getUserScoringStats(String userId);

    /**
     * 获取考试评分统计
     * 
     * @param examId 考试ID
     * @return 统计结果
     */
    Map<String, Object> getExamScoringStats(String examId);

    /**
     * 获取站点评分统计
     * 
     * @param stationCode 站点代码
     * @return 统计结果
     */
    Map<String, Object> getStationScoringStats(String stationCode);

    /**
     * 获取系统整体评分统计
     * 
     * @return 系统统计结果
     */
    Map<String, Object> getSystemScoringStats();

    // ==================== 批量任务管理 ====================

    /**
     * 获取批量任务详情
     * 
     * @param taskId 任务ID
     * @return 批量任务
     */
    WeatherScoringBatchTask getBatchTask(String taskId);

    /**
     * 获取正在运行的批量任务
     * 
     * @return 运行中的任务列表
     */
    List<WeatherScoringBatchTask> getRunningBatchTasks();

    /**
     * 获取用户的任务历史
     * 
     * @param userId 用户ID
     * @return 任务历史列表
     */
    List<WeatherScoringBatchTask> getUserTaskHistory(String userId);

    /**
     * 获取批量任务统计
     * 
     * @param taskId 任务ID
     * @return 任务统计
     */
    Map<String, Object> getBatchTaskStats(String taskId);

    /**
     * 获取批量任务进度
     * 
     * @param taskId 任务ID
     * @return 任务进度
     */
    Map<String, Object> getBatchTaskProgress(String taskId);

    /**
     * 停止批量任务
     * 
     * @param taskId 任务ID
     * @param reason 停止原因
     * @return 是否停止成功
     */
    boolean stopBatchTask(String taskId, String reason);

    /**
     * 重启批量任务
     * 
     * @param taskId 任务ID
     * @return 是否重启成功
     */
    boolean restartBatchTask(String taskId);

    /**
     * 获取批量任务日志
     * 
     * @param taskId 任务ID
     * @return 任务日志
     */
    List<String> getBatchTaskLogs(String taskId);

    // ==================== 系统管理功能 ====================

    /**
     * 清理过期的评分结果
     * 
     * @param retentionDays 保留天数
     * @return 清理的记录数
     */
    int cleanupExpiredResults(int retentionDays);

    /**
     * 清理过期的批量任务
     * 
     * @param retentionDays 保留天数
     * @return 清理的任务数
     */
    int cleanupExpiredTasks(int retentionDays);

    /**
     * 验证评分配置
     * 
     * @param config 评分配置
     * @return 验证结果，key为字段名，value为错误信息
     */
    Map<String, String> validateConfig(WeatherScoringConfig config);

    /**
     * 获取系统健康状态
     * 
     * @return 系统健康状态
     */
    Map<String, Object> getSystemHealth();

    /**
     * 获取性能监控数据
     * 
     * @return 性能监控数据
     */
    Map<String, Object> getPerformanceMetrics();

    // ==================== 数据导入导出 ====================

    /**
     * 导出评分配置
     * 
     * @param configIds 配置ID列表
     * @return 导出的配置数据JSON字符串
     */
    String exportConfigs(List<String> configIds);

    /**
     * 导入评分配置
     * 
     * @param configJson 配置JSON字符串
     * @return 导入结果
     */
    Map<String, Object> importConfigs(String configJson);

    /**
     * 导出评分结果
     * 
     * @param examId 考试ID
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 导出的结果数据JSON字符串
     */
    String exportScoringResults(String examId, LocalDateTime startDate, LocalDateTime endDate);
} 