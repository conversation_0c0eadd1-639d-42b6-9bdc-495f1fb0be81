# MICAPS数据解析器

本模块提供了完整的MICAPS（气象信息综合分析处理系统）数据解析功能，支持解析第一类（地面全要素填图数据）和第四类（格点数据）MICAPS文件。

## 功能特性

- ✅ 支持MICAPS第一类数据（地面全要素填图数据）解析
- ✅ 支持MICAPS第四类数据（格点数据）解析
- ✅ 支持文本格式和二进制格式文件
- ✅ 提供数据验证和统计分析功能
- ✅ 支持地理位置查询和插值计算
- ✅ 完整的单元测试覆盖

## 核心类说明

### 1. 解析器类
- `MicapsParser` - 主要的文本格式解析器
- `MicapsBinaryParser` - 二进制格式解析器
- `MicapsDataService` - 业务服务类，提供高级功能

### 2. 数据模型类
- `MicapsData` - 数据基类
- `MicapsType1Data` - 第一类数据模型
- `MicapsType4Data` - 第四类数据模型
- `MicapsStation` - 站点数据模型

## 快速开始

### 1. 基本使用

```java
import com.yf.exam.modules.weather.micaps.*;

// 创建服务实例
MicapsDataService service = new MicapsDataService();

// 解析MICAPS文件
try {
    MicapsData data = service.parseMicapsFile("path/to/micaps/file.000");
    System.out.println("解析成功: " + data);
} catch (IOException e) {
    System.err.println("解析失败: " + e.getMessage());
}
```

### 2. 第一类数据处理

```java
// 解析第一类数据（地面站点数据）
MicapsData data = service.parseMicapsFile("第一类.000");

if (data instanceof MicapsType1Data) {
    MicapsType1Data stationData = (MicapsType1Data) data;
    
    // 获取所有站点
    List<MicapsStation> stations = stationData.getStations();
    
    // 查询指定区域内的站点
    List<MicapsStation> regionStations = service.getStationsInRegion(
        data, 100.0, 120.0, 30.0, 50.0); // 经度100-120°, 纬度30-50°
    
    // 获取有温度数据的站点
    List<MicapsStation> tempStations = service.getStationsWithTemperature(data);
    
    // 获取有降水的站点
    List<MicapsStation> precipStations = service.getStationsWithPrecipitation(data);
    
    // 访问站点详细信息
    for (MicapsStation station : stations) {
        System.out.printf("站点%d: 位置(%.2f, %.2f), 温度=%.1f°C, %s, 风力%d级\n",
            station.getStationId(),
            station.getLongitude(), station.getLatitude(),
            station.getTemperature(),
            station.getWindDirectionDescription(),
            station.getWindForceLevel()
        );
    }
}
```

### 3. 第四类数据处理

```java
// 解析第四类数据（格点数据）
MicapsData data = service.parseMicapsFile("第四类.024");

if (data instanceof MicapsType4Data) {
    MicapsType4Data gridData = (MicapsType4Data) data;
    
    // 获取网格信息
    System.out.printf("网格范围: 经度%.1f-%.1f°, 纬度%.1f-%.1f°\n",
        gridData.getStartLon(), gridData.getEndLon(),
        gridData.getStartLat(), gridData.getEndLat());
    
    // 查询指定位置的插值数据
    Double value = service.getGridValueAtPosition(data, 105.0, 35.0);
    System.out.println("位置(105°E, 35°N)的数值: " + value);
    
    // 获取统计信息
    MicapsDataService.GridStatistics stats = service.getGridStatistics(data);
    System.out.println("格点数据统计: " + stats);
    
    // 直接访问格点数据
    Double gridValue = gridData.getGridValue(10, 15); // 第10行第15列的格点
    List<Double> allValues = gridData.getGridValues(); // 所有格点数据
}
```

### 4. 数据验证

```java
// 验证数据完整性
MicapsDataService.ValidationResult result = service.validateMicapsData(data);

if (result.isValid()) {
    System.out.println("数据验证通过");
} else {
    System.out.println("数据验证失败:");
    result.getErrors().forEach(error -> System.out.println("  错误: " + error));
}

if (!result.getWarnings().isEmpty()) {
    System.out.println("警告信息:");
    result.getWarnings().forEach(warning -> System.out.println("  警告: " + warning));
}
```

## 数据格式说明

### 第一类数据格式（地面全要素填图数据）

文件头格式：
```
diamond 1 数据说明 年 月 日 时次
总站点数
```

站点数据格式：
```
区站号 经度 纬度 拔海高度 站点级别 总云量 风向 风速 海平面气压 3小时变压 
过去天气1 过去天气2 6小时降水 低云状 低云量 低云高 露点 能见度 现在天气 温度 
中云状 高云状 标志1 标志2 [24小时变温] [24小时变压]
```

### 第四类数据格式（格点数据）

文件头格式：
```
diamond 4 数据说明 年 月 日 时次 时效 层次
经度格距 纬度格距 起始经度 终止经度 起始纬度 终止纬度 纬向格点数 经向格点数 
等值线间隔 等值线起始值 终止值 平滑系数 加粗线值
```

数据格式：
```
格点数据按先纬向后经向排列，均为浮点数
```

## 业务应用示例

### 1. 气象预警应用

```java
// 查找高温站点（温度>35°C）
List<MicapsStation> hotStations = stationData.getStations().stream()
    .filter(station -> station.getTemperature() != null && 
                     station.getTemperature() > 35.0)
    .collect(Collectors.toList());

// 查找大风站点（风力≥6级）
List<MicapsStation> windyStations = stationData.getStations().stream()
    .filter(station -> station.getWindForceLevel() != null && 
                     station.getWindForceLevel() >= 6)
    .collect(Collectors.toList());
```

### 2. 区域统计分析

```java
// 计算指定区域的平均温度
double avgTemp = stationData.getStations().stream()
    .filter(station -> station.getLongitude() >= 100 && station.getLongitude() <= 120 &&
                      station.getLatitude() >= 30 && station.getLatitude() <= 50)
    .filter(station -> station.getTemperature() != null)
    .mapToDouble(MicapsStation::getTemperature)
    .average()
    .orElse(0.0);
```

### 3. 格点数据插值

```java
// 获取任意位置的插值数据
Double interpolatedValue = gridData.getValueAtPosition(105.5, 35.5);

// 批量查询多个位置
double[][] positions = {{105.0, 35.0}, {110.0, 40.0}, {115.0, 45.0}};
for (double[] pos : positions) {
    Double value = gridData.getValueAtPosition(pos[0], pos[1]);
    System.out.printf("位置(%.1f, %.1f): %.2f\n", pos[0], pos[1], value);
}
```

## 注意事项

1. **文件编码**: 文本格式文件默认使用UTF-8编码
2. **缺值处理**: 数值9999表示缺值，解析后转换为null
3. **坐标系统**: 经纬度采用十进制度数表示
4. **内存使用**: 大型格点数据文件可能占用较多内存
5. **异常处理**: 建议使用try-catch处理IOException和IllegalArgumentException

## 测试

运行单元测试：
```bash
mvn test -Dtest=MicapsParserTest
```

## 扩展开发

如需支持其他类型的MICAPS数据格式，可以：

1. 继承`MicapsData`创建新的数据模型类
2. 在`MicapsParser`中添加相应的解析方法
3. 在`MicapsDataService`中添加业务处理方法
4. 编写相应的单元测试

## 依赖要求

- Java 8+
- Spring Boot 2.1.4+
- JUnit 5（用于测试）

## 更新日志

- v1.0.0 (2025-01-22): 初始版本，支持第一类和第四类数据解析
