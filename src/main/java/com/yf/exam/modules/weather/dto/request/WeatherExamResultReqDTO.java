package com.yf.exam.modules.weather.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <p>
 * 历史个例考试结果请求DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@ApiModel(value = "历史个例考试结果请求", description = "历史个例考试结果请求")
public class WeatherExamResultReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "考试ID", required = true)
    @NotBlank(message = "考试ID不能为空")
    private String examId;
}
