package com.yf.exam.modules.weather.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yf.exam.modules.weather.entity.WeatherTableConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 天气预报表格配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Mapper
public interface WeatherTableConfigMapper extends BaseMapper<WeatherTableConfig> {

    /**
     * 根据模板类型查询配置列表
     * 
     * @param templateType 模板类型
     * @return 配置列表
     */
    List<WeatherTableConfig> selectByTemplateType(@Param("templateType") String templateType);

    /**
     * 查询所有启用的配置
     * 
     * @return 启用的配置列表
     */
    List<WeatherTableConfig> selectActiveConfigs();

    /**
     * 根据名称查询配置
     * 
     * @param name 配置名称
     * @return 配置信息
     */
    WeatherTableConfig selectByName(@Param("name") String name);

    /**
     * 获取配置的基本信息（不包含JSON字段）
     * 
     * @param id 配置ID
     * @return 基本信息
     */
    WeatherTableConfig selectBasicInfo(@Param("id") String id);

    /**
     * 批量更新配置状态
     * 
     * @param ids 配置ID列表
     * @param isActive 是否启用
     * @return 更新数量
     */
    int updateStatusBatch(@Param("ids") List<String> ids, @Param("isActive") Boolean isActive);
}
