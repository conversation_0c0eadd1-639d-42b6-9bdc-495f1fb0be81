package com.yf.exam.modules.weather.micaps;

import java.io.IOException;
import java.util.List;

/**
 * MICAPS数据解析使用示例
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */
public class MicapsExample {
    
    public static void main(String[] args) {
        MicapsDataService service = new MicapsDataService();
        
        // 示例1：解析第一类数据文件
        try {
            System.out.println("=== 解析第一类数据示例 ===");
            String type1FilePath = "第一类.000"; // 替换为实际文件路径
            
            MicapsData data = service.parseMicapsFile(type1FilePath);
            System.out.println("解析成功: " + data);
            
            if (data instanceof MicapsType1Data) {
                MicapsType1Data type1Data = (MicapsType1Data) data;
                
                // 获取所有站点
                List<MicapsStation> allStations = type1Data.getStations();
                System.out.println("总站点数: " + allStations.size());
                
                // 获取有温度数据的站点
                List<MicapsStation> tempStations = service.getStationsWithTemperature(data);
                System.out.println("有温度数据的站点数: " + tempStations.size());
                
                // 显示前5个站点的详细信息
                System.out.println("\n前5个站点信息:");
                allStations.stream().limit(5).forEach(station -> {
                    System.out.printf("站点%d: 位置(%.2f, %.2f), 温度=%.1f°C, %s, 风力%d级\n",
                        station.getStationId(),
                        station.getLongitude(), station.getLatitude(),
                        station.getTemperature() != null ? station.getTemperature() : -999,
                        station.getWindDirectionDescription() != null ? station.getWindDirectionDescription() : "无风向",
                        station.getWindForceLevel() != null ? station.getWindForceLevel() : -1
                    );
                });
                
                // 查询指定区域内的站点
                List<MicapsStation> regionStations = service.getStationsInRegion(
                    data, 100.0, 120.0, 30.0, 50.0);
                System.out.println("\n指定区域(100-120°E, 30-50°N)内的站点数: " + regionStations.size());
                
                // 获取有降水的站点
                List<MicapsStation> precipStations = service.getStationsWithPrecipitation(data);
                System.out.println("有降水的站点数: " + precipStations.size());
            }
            
        } catch (IOException e) {
            System.err.println("解析第一类数据失败: " + e.getMessage());
        }
        
        // 示例2：解析第四类数据文件
        try {
            System.out.println("\n=== 解析第四类数据示例 ===");
            String type4FilePath = "第四类.024"; // 替换为实际文件路径
            
            MicapsData data = service.parseMicapsFile(type4FilePath);
            System.out.println("解析成功: " + data);
            
            if (data instanceof MicapsType4Data) {
                MicapsType4Data type4Data = (MicapsType4Data) data;
                
                // 显示网格信息
                System.out.printf("网格范围: 经度%.1f-%.1f°, 纬度%.1f-%.1f°\n",
                    type4Data.getStartLon(), type4Data.getEndLon(),
                    type4Data.getStartLat(), type4Data.getEndLat());
                System.out.printf("网格分辨率: 经度%.2f°, 纬度%.2f°\n",
                    type4Data.getLonInterval(), type4Data.getLatInterval());
                System.out.printf("网格大小: %d×%d = %d个格点\n",
                    type4Data.getLonGridNum(), type4Data.getLatGridNum(),
                    type4Data.getLonGridNum() * type4Data.getLatGridNum());
                
                // 获取统计信息
                MicapsDataService.GridStatistics stats = service.getGridStatistics(data);
                if (stats != null) {
                    System.out.println("格点数据统计: " + stats);
                }
                
                // 查询指定位置的数值
                double[] testPositions = {
                    {105.0, 35.0},
                    {110.0, 40.0},
                    {115.0, 45.0}
                };
                
                System.out.println("\n指定位置的插值结果:");
                for (double[] pos : testPositions) {
                    Double value = service.getGridValueAtPosition(data, pos[0], pos[1]);
                    System.out.printf("位置(%.1f°E, %.1f°N): %.2f\n", 
                        pos[0], pos[1], value != null ? value : -999.0);
                }
                
                // 显示部分格点数据
                List<Double> gridValues = type4Data.getGridValues();
                if (gridValues != null && !gridValues.isEmpty()) {
                    System.out.println("\n前10个格点数值:");
                    gridValues.stream().limit(10).forEach(value -> 
                        System.out.printf("%.2f ", value));
                    System.out.println();
                }
            }
            
        } catch (IOException e) {
            System.err.println("解析第四类数据失败: " + e.getMessage());
        }
        
        // 示例3：数据验证
        try {
            System.out.println("\n=== 数据验证示例 ===");
            String filePath = "第一类.000"; // 替换为实际文件路径
            
            MicapsData data = service.parseMicapsFile(filePath);
            MicapsDataService.ValidationResult result = service.validateMicapsData(data);
            
            System.out.println("验证结果: " + (result.isValid() ? "通过" : "失败"));
            
            if (!result.getErrors().isEmpty()) {
                System.out.println("错误信息:");
                result.getErrors().forEach(error -> System.out.println("  - " + error));
            }
            
            if (!result.getWarnings().isEmpty()) {
                System.out.println("警告信息:");
                result.getWarnings().forEach(warning -> System.out.println("  - " + warning));
            }
            
        } catch (IOException e) {
            System.err.println("数据验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 演示如何在业务代码中使用MICAPS数据
     */
    public static void businessExample() {
        MicapsDataService service = new MicapsDataService();
        
        try {
            // 解析气象数据文件
            MicapsData data = service.parseMicapsFile("weather_data.000");
            
            if (data instanceof MicapsType1Data) {
                MicapsType1Data stationData = (MicapsType1Data) data;
                
                // 业务场景1：查找高温站点
                List<MicapsStation> hotStations = stationData.getStations().stream()
                    .filter(station -> station.getTemperature() != null && 
                                     station.getTemperature() > 35.0)
                    .collect(java.util.stream.Collectors.toList());
                
                System.out.println("高温站点数量: " + hotStations.size());
                
                // 业务场景2：查找大风站点
                List<MicapsStation> windyStations = stationData.getStations().stream()
                    .filter(station -> station.getWindForceLevel() != null && 
                                     station.getWindForceLevel() >= 6)
                    .collect(java.util.stream.Collectors.toList());
                
                System.out.println("大风站点数量: " + windyStations.size());
                
                // 业务场景3：统计不同风向的站点数量
                java.util.Map<String, Long> windDirectionCount = stationData.getStations().stream()
                    .filter(station -> station.getWindDirectionDescription() != null)
                    .collect(java.util.stream.Collectors.groupingBy(
                        MicapsStation::getWindDirectionDescription,
                        java.util.stream.Collectors.counting()));
                
                System.out.println("各风向站点统计:");
                windDirectionCount.forEach((direction, count) -> 
                    System.out.println("  " + direction + ": " + count + "个站点"));
            }
            
            if (data instanceof MicapsType4Data) {
                MicapsType4Data gridData = (MicapsType4Data) data;
                
                // 业务场景4：查找数值异常的区域
                List<Double> values = gridData.getGridValues();
                double threshold = 100.0; // 设定阈值
                
                long abnormalCount = values.stream()
                    .filter(value -> Math.abs(value) > threshold)
                    .count();
                
                System.out.println("异常数值格点数量: " + abnormalCount);
                
                // 业务场景5：计算区域平均值
                double regionAvg = calculateRegionAverage(gridData, 
                    105.0, 115.0, 30.0, 40.0);
                System.out.println("指定区域平均值: " + regionAvg);
            }
            
        } catch (IOException e) {
            System.err.println("业务处理失败: " + e.getMessage());
        }
    }
    
    /**
     * 计算指定区域的平均值
     */
    private static double calculateRegionAverage(MicapsType4Data data, 
                                               double minLon, double maxLon,
                                               double minLat, double maxLat) {
        double sum = 0.0;
        int count = 0;
        
        for (int latIdx = 0; latIdx < data.getLatGridNum(); latIdx++) {
            for (int lonIdx = 0; lonIdx < data.getLonGridNum(); lonIdx++) {
                double lon = data.getStartLon() + lonIdx * data.getLonInterval();
                double lat = data.getStartLat() + latIdx * data.getLatInterval();
                
                if (lon >= minLon && lon <= maxLon && lat >= minLat && lat <= maxLat) {
                    Double value = data.getGridValue(latIdx, lonIdx);
                    if (value != null) {
                        sum += value;
                        count++;
                    }
                }
            }
        }
        
        return count > 0 ? sum / count : 0.0;
    }
}
