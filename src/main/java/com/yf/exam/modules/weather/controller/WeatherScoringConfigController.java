package com.yf.exam.modules.weather.controller;

import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.modules.weather.entity.WeatherScoringConfig;
import com.yf.exam.modules.weather.scoring.service.WeatherScoringService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * 天气预报评分配置管理控制器
 * 
 * 提供评分配置的REST API接口，包括：
 * - 配置的增删改查
 * - 版本管理和激活
 * - 配置导入导出
 * - 配置验证和统计
 * 
 * <AUTHOR>
 * @since 2024-12-21
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/exam/api/weather/scoring/config")
@Api(tags = "天气预报评分配置管理")
public class WeatherScoringConfigController extends BaseController {

    @Autowired
    private WeatherScoringService scoringService;

    // ==================== 配置基本操作 ====================

    /**
     * 获取当前活跃的评分配置
     */
    @GetMapping("/active")
    @ApiOperation(value = "获取当前活跃配置", notes = "获取系统当前正在使用的评分配置")
    public ApiRest<WeatherScoringConfig> getActiveConfig() {
        try {
            WeatherScoringConfig config = scoringService.getActiveConfig();
            if (config == null) {
                return super.failure("当前没有活跃的评分配置");
            }
            return super.success(config);
        } catch (Exception e) {
            log.error("获取活跃配置失败", e);
            return super.failure("获取活跃配置失败：" + e.getMessage());
        }
    }

    /**
     * 根据版本获取评分配置
     */
    @GetMapping("/version/{version}")
    @ApiOperation(value = "根据版本获取配置", notes = "根据指定版本获取评分配置详情")
    public ApiRest<WeatherScoringConfig> getConfigByVersion(
            @ApiParam(value = "配置版本", required = true)
            @PathVariable @NotBlank(message = "版本不能为空") String version) {
        try {
            WeatherScoringConfig config = scoringService.getConfigByVersion(version);
            if (config == null) {
                return super.failure("指定版本的配置不存在：" + version);
            }
            return super.success(config);
        } catch (Exception e) {
            log.error("根据版本获取配置失败，版本：{}", version, e);
            return super.failure("获取配置失败：" + e.getMessage());
        }
    }

    /**
     * 保存评分配置
     */
    @PostMapping
    @ApiOperation(value = "保存评分配置", notes = "创建新的评分配置或更新现有配置")
    public ApiRest<WeatherScoringConfig> saveConfig(
            @ApiParam(value = "评分配置", required = true)
            @RequestBody @Valid WeatherScoringConfig config) {
        try {
            // 参数验证
            Map<String, String> validationErrors = scoringService.validateConfig(config);
            if (!validationErrors.isEmpty()) {
                return super.failure("配置验证失败");
            }

            WeatherScoringConfig savedConfig = scoringService.saveConfig(config);
            return super.success("配置保存成功", savedConfig);
        } catch (IllegalArgumentException e) {
            log.warn("配置参数错误", e);
            return super.failure("配置参数错误：" + e.getMessage());
        } catch (Exception e) {
            log.error("保存配置失败", e);
            return super.failure("保存配置失败：" + e.getMessage());
        }
    }

    /**
     * 激活指定版本的配置
     */
    @PostMapping("/activate/{version}")
    @ApiOperation(value = "激活配置版本", notes = "将指定版本的配置设置为活跃状态")
    public ApiRest<String> activateConfig(
            @ApiParam(value = "要激活的配置版本", required = true)
            @PathVariable @NotBlank(message = "版本不能为空") String version) {
        try {
            boolean success = scoringService.activateConfig(version);
            if (success) {
                return super.success("配置版本 " + version + " 激活成功");
            } else {
                return super.failure("配置版本激活失败，请检查版本是否存在");
            }
        } catch (Exception e) {
            log.error("激活配置失败，版本：{}", version, e);
            return super.failure("激活配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取配置版本历史
     */
    @GetMapping("/history")
    @ApiOperation(value = "获取配置历史", notes = "获取所有版本的配置历史记录")
    public ApiRest<List<WeatherScoringConfig>> getConfigHistory() {
        try {
            List<WeatherScoringConfig> history = scoringService.getConfigHistory();
            return super.success(history);
        } catch (Exception e) {
            log.error("获取配置历史失败", e);
            return super.failure("获取配置历史失败：" + e.getMessage());
        }
    }

    // ==================== 配置验证 ====================

    /**
     * 验证配置有效性
     */
    @PostMapping("/validate")
    @ApiOperation(value = "验证配置", notes = "验证配置参数的有效性，不保存配置")
    public ApiRest<Map<String, String>> validateConfig(
            @ApiParam(value = "待验证的配置", required = true)
            @RequestBody @Valid WeatherScoringConfig config) {
        try {
            Map<String, String> validationResult = scoringService.validateConfig(config);
            if (validationResult.isEmpty()) {
                return super.success("配置验证通过", validationResult);
            } else {
                return super.failure("配置验证失败");
            }
        } catch (Exception e) {
            log.error("配置验证失败", e);
            return super.failure("配置验证失败：" + e.getMessage());
        }
    }

    // ==================== 数据导入导出 ====================

    /**
     * 导出配置
     */
    @PostMapping("/export")
    @ApiOperation(value = "导出配置", notes = "导出指定的配置为JSON格式")
    public ApiRest<String> exportConfigs(
            @ApiParam(value = "要导出的配置ID列表", required = true)
            @RequestBody List<String> configIds) {
        try {
            if (configIds.isEmpty()) {
                return super.failure("配置ID列表不能为空");
            }

            String exportData = scoringService.exportConfigs(configIds);
            return super.success("配置导出成功", exportData);
        } catch (Exception e) {
            log.error("导出配置失败", e);
            return super.failure("导出配置失败：" + e.getMessage());
        }
    }

    /**
     * 导入配置
     */
    @PostMapping("/import")
    @ApiOperation(value = "导入配置", notes = "从JSON数据导入配置")
    public ApiRest<Map<String, Object>> importConfigs(
            @ApiParam(value = "配置JSON数据", required = true)
            @RequestBody Map<String, String> importRequest) {
        try {
            String configJson = importRequest.get("configData");
            if (configJson == null || configJson.trim().isEmpty()) {
                return super.failure("配置数据不能为空");
            }

            Map<String, Object> importResult = scoringService.importConfigs(configJson);
            Boolean success = (Boolean) importResult.get("success");
            if (Boolean.TRUE.equals(success)) {
                return super.success("配置导入成功", importResult);
            } else {
                return super.failure("配置导入失败");
            }
        } catch (Exception e) {
            log.error("导入配置失败", e);
            return super.failure("导入配置失败：" + e.getMessage());
        }
    }

    // ==================== 系统状态和统计 ====================

    /**
     * 获取系统健康状态
     */
    @GetMapping("/health")
    @ApiOperation(value = "获取系统健康状态", notes = "获取评分系统的健康检查结果")
    public ApiRest<Map<String, Object>> getSystemHealth() {
        try {
            Map<String, Object> health = scoringService.getSystemHealth();
            String status = (String) health.get("status");
            if ("UP".equals(status)) {
                return super.success("系统运行正常", health);
            } else {
                return super.failure("系统状态异常");
            }
        } catch (Exception e) {
            log.error("获取系统健康状态失败", e);
            return super.failure("获取系统健康状态失败：" + e.getMessage());
        }
    }

    /**
     * 获取性能监控数据
     */
    @GetMapping("/metrics")
    @ApiOperation(value = "获取性能指标", notes = "获取系统性能监控数据")
    public ApiRest<Map<String, Object>> getPerformanceMetrics() {
        try {
            Map<String, Object> metrics = scoringService.getPerformanceMetrics();
            return super.success(metrics);
        } catch (Exception e) {
            log.error("获取性能指标失败", e);
            return super.failure("获取性能指标失败：" + e.getMessage());
        }
    }

    /**
     * 获取系统整体评分统计
     */
    @GetMapping("/stats/system")
    @ApiOperation(value = "获取系统统计", notes = "获取系统整体的评分统计数据")
    public ApiRest<Map<String, Object>> getSystemStats() {
        try {
            Map<String, Object> stats = scoringService.getSystemScoringStats();
            if (stats.containsKey("error")) {
                return super.failure("获取系统统计失败");
            }
            return super.success(stats);
        } catch (Exception e) {
            log.error("获取系统统计失败", e);
            return super.failure("获取系统统计失败：" + e.getMessage());
        }
    }

    // ==================== 系统管理 ====================

    /**
     * 清理过期任务
     */
    @PostMapping("/cleanup/tasks")
    @ApiOperation(value = "清理过期任务", notes = "清理指定保留期之前的过期批量任务")
    public ApiRest<Integer> cleanupExpiredTasks(
            @ApiParam(value = "保留天数", required = true)
            @RequestParam(defaultValue = "30") int retentionDays) {
        try {
            if (retentionDays <= 0) {
                return super.failure("保留天数必须大于0");
            }

            int cleanedCount = scoringService.cleanupExpiredTasks(retentionDays);
            return super.success("成功清理 " + cleanedCount + " 个过期任务", cleanedCount);
        } catch (Exception e) {
            log.error("清理过期任务失败", e);
            return super.failure("清理过期任务失败：" + e.getMessage());
        }
    }

    /**
     * 清理过期结果
     */
    @PostMapping("/cleanup/results")
    @ApiOperation(value = "清理过期结果", notes = "清理指定保留期之前的过期评分结果")
    public ApiRest<Integer> cleanupExpiredResults(
            @ApiParam(value = "保留天数", required = true)
            @RequestParam(defaultValue = "90") int retentionDays) {
        try {
            if (retentionDays <= 0) {
                return super.failure("保留天数必须大于0");
            }

            int cleanedCount = scoringService.cleanupExpiredResults(retentionDays);
            return super.success("成功清理 " + cleanedCount + " 个过期结果", cleanedCount);
        } catch (Exception e) {
            log.error("清理过期结果失败", e);
            return super.failure("清理过期结果失败：" + e.getMessage());
        }
    }
} 