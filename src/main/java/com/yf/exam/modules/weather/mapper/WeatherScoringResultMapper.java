package com.yf.exam.modules.weather.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yf.exam.modules.weather.entity.WeatherScoringResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 天气预报评分结果Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-12-21
 */
@Mapper
public interface WeatherScoringResultMapper extends BaseMapper<WeatherScoringResult> {

    /**
     * 根据答案ID查询评分结果
     * 
     * @param answerId 答案ID
     * @return 评分结果
     */
    @Select("SELECT * FROM el_weather_scoring_result WHERE answer_id = #{answerId}")
    WeatherScoringResult selectByAnswerId(@Param("answerId") String answerId);

    /**
     * 根据考试ID和用户ID查询评分结果列表
     * 
     * @param examId 考试ID
     * @param userId 用户ID
     * @return 评分结果列表
     */
    @Select("SELECT * FROM el_weather_scoring_result WHERE exam_id = #{examId} AND user_id = #{userId} ORDER BY scoring_time DESC")
    List<WeatherScoringResult> selectByExamAndUser(@Param("examId") String examId, @Param("userId") String userId);

    /**
     * 根据考试ID查询评分结果分页
     * 
     * @param page 分页对象
     * @param examId 考试ID
     * @return 评分结果分页
     */
    @Select("SELECT * FROM el_weather_scoring_result WHERE exam_id = #{examId} ORDER BY scoring_time DESC")
    IPage<WeatherScoringResult> selectPageByExamId(Page<WeatherScoringResult> page, @Param("examId") String examId);

    /**
     * 查询考试评分统计信息
     * 
     * @param examId 考试ID
     * @return 统计信息
     */
    @Select("SELECT " +
            "COUNT(1) as totalCount, " +
            "AVG(total_score) as avgScore, " +
            "MAX(total_score) as maxScore, " +
            "MIN(total_score) as minScore, " +
            "AVG(score_percentage) as avgPercentage, " +
            "SUM(CASE WHEN score_percentage >= 60 THEN 1 ELSE 0 END) as passCount " +
            "FROM el_weather_scoring_result WHERE exam_id = #{examId}")
    Map<String, Object> selectScoreStatistics(@Param("examId") String examId);

    /**
     * 查询题目评分统计信息
     * 
     * @param questionId 题目ID
     * @return 统计信息
     */
    @Select("SELECT " +
            "COUNT(1) as totalCount, " +
            "AVG(total_score) as avgScore, " +
            "MAX(total_score) as maxScore, " +
            "MIN(total_score) as minScore, " +
            "AVG(score_percentage) as avgPercentage, " +
            "SUM(CASE WHEN score_percentage >= 60 THEN 1 ELSE 0 END) as passCount " +
            "FROM el_weather_scoring_result WHERE question_id = #{questionId}")
    Map<String, Object> selectQuestionStatistics(@Param("questionId") String questionId);

    /**
     * 根据分数范围查询评分结果数量
     * 
     * @param examId 考试ID
     * @param minScore 最低分数
     * @param maxScore 最高分数
     * @return 评分结果数量
     */
    @Select("SELECT COUNT(1) FROM el_weather_scoring_result WHERE exam_id = #{examId} AND total_score &gt;= #{minScore} AND total_score &lt;= #{maxScore}")
    int countByScoreRange(@Param("examId") String examId, @Param("minScore") BigDecimal minScore, @Param("maxScore") BigDecimal maxScore);

    /**
     * 查询指定时间范围内的评分结果
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 评分结果列表
     */
    @Select("SELECT * FROM el_weather_scoring_result WHERE scoring_time &gt;= #{startTime} AND scoring_time &lt;= #{endTime} ORDER BY scoring_time DESC")
    List<WeatherScoringResult> selectByTimeRange(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询用户的历史评分记录
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 评分结果列表
     */
    @Select("SELECT * FROM el_weather_scoring_result WHERE user_id = #{userId} ORDER BY scoring_time DESC LIMIT #{limit}")
    List<WeatherScoringResult> selectUserHistory(@Param("userId") String userId, @Param("limit") int limit);

    /**
     * 查询评分排名
     * 
     * @param examId 考试ID
     * @param limit 限制数量
     * @return 评分结果列表（按总分降序）
     */
    @Select("SELECT * FROM el_weather_scoring_result WHERE exam_id = #{examId} ORDER BY total_score DESC, scoring_time ASC LIMIT #{limit}")
    List<WeatherScoringResult> selectTopScores(@Param("examId") String examId, @Param("limit") int limit);

    /**
     * 统计评分耗时分布
     * 
     * @param examId 考试ID
     * @return 耗时统计
     */
    @Select("SELECT " +
            "AVG(scoring_duration) as avgDuration, " +
            "MAX(scoring_duration) as maxDuration, " +
            "MIN(scoring_duration) as minDuration, " +
            "COUNT(CASE WHEN scoring_duration <= 1000 THEN 1 END) as fastCount, " +
            "COUNT(CASE WHEN scoring_duration > 1000 AND scoring_duration <= 3000 THEN 1 END) as normalCount, " +
            "COUNT(CASE WHEN scoring_duration > 3000 THEN 1 END) as slowCount " +
            "FROM el_weather_scoring_result WHERE exam_id = #{examId} AND scoring_duration IS NOT NULL")
    Map<String, Object> selectDurationStatistics(@Param("examId") String examId);

    /**
     * 批量检查答案是否已评分
     * 
     * @param answerIds 答案ID列表
     * @return 已评分的答案ID列表
     */
    @Select("<script>" +
            "SELECT answer_id FROM el_weather_scoring_result WHERE answer_id IN " +
            "<foreach collection='answerIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    List<String> selectScoredAnswerIds(@Param("answerIds") List<String> answerIds);
} 