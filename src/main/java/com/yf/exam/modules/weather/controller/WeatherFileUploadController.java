package com.yf.exam.modules.weather.controller;

import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.modules.user.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.UUID;

/**
 * 历史个例文件上传控制器
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
@Slf4j
@RestController
@RequestMapping("/exam/api/weather/case")
@Api(tags = "历史个例文件上传")
public class WeatherFileUploadController extends BaseController {

    /**
     * 上传MICAPS文件
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "上传MICAPS文件")
    @PostMapping("/upload/micaps")
    public ApiRest<Map<String, Object>> uploadMicapsFile(@RequestParam("file") MultipartFile file) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                return super.failure("上传文件不能为空");
            }

            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                return super.failure("文件名不能为空");
            }

            // 验证文件大小（限制为100MB）
            long maxSize = 100 * 1024 * 1024; // 100MB
            if (file.getSize() > maxSize) {
                return super.failure("文件大小不能超过100MB");
            }

            // 验证文件类型（MICAPS文件格式）
            String extension = getFileExtension(originalFilename);
            if (!isMicapsFile(extension)) {
                return super.failure("不支持的MICAPS文件格式，支持格式：.000, .024, .dat, .txt, .nc, .grib, .grib2");
            }

            // 创建上传目录
            String uploadDir = System.getProperty("user.dir") + "/uploads/weather/micaps/";
            File dir = new File(uploadDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 生成唯一文件名
            String fileName = generateUniqueFileName(originalFilename);
            String filePath = uploadDir + fileName;

            // 保存文件
            Path targetPath = Paths.get(filePath);
            Files.copy(file.getInputStream(), targetPath);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("id", System.currentTimeMillis());
            result.put("name", originalFilename);
            result.put("fileName", fileName);
            result.put("size", file.getSize());
            result.put("url", "/uploads/weather/micaps/" + fileName);
            result.put("filePath", "uploads/weather/micaps/" + fileName);

            log.info("用户 {} 上传了MICAPS文件：{}", 
                UserUtils.getUser().getUserName(), originalFilename);

            return super.success(result);

        } catch (IOException e) {
            log.error("上传MICAPS文件失败", e);
            return super.failure("文件上传失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("上传MICAPS文件异常", e);
            return super.failure("文件上传异常：" + e.getMessage());
        }
    }

    /**
     * 上传历史个例实况文件
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "上传历史个例实况文件")
    @PostMapping("/upload/observation")
    public ApiRest<Map<String, Object>> uploadObservationFile(@RequestParam("file") MultipartFile file) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                return super.failure("上传文件不能为空");
            }

            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                return super.failure("文件名不能为空");
            }

            // 验证文件大小（限制为50MB）
            long maxSize = 50 * 1024 * 1024; // 50MB
            if (file.getSize() > maxSize) {
                return super.failure("文件大小不能超过50MB");
            }

            // 创建上传目录
            String uploadDir = System.getProperty("user.dir") + "/uploads/weather/observation/";
            File dir = new File(uploadDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 生成唯一文件名
            String fileName = generateUniqueFileName(originalFilename);
            String filePath = uploadDir + fileName;

            // 保存文件
            Path targetPath = Paths.get(filePath);
            Files.copy(file.getInputStream(), targetPath);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("id", System.currentTimeMillis());
            result.put("name", originalFilename);
            result.put("fileName", fileName);
            result.put("size", file.getSize());
            result.put("url", "/uploads/weather/observation/" + fileName);
            result.put("filePath", "uploads/weather/observation/" + fileName);

            log.info("用户 {} 上传了实况文件：{}", 
                UserUtils.getUser().getUserName(), originalFilename);

            return super.success(result);

        } catch (IOException e) {
            log.error("上传实况文件失败", e);
            return super.failure("文件上传失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("上传实况文件异常", e);
            return super.failure("文件上传异常：" + e.getMessage());
        }
    }

    /**
     * 上传历史个例数据文件（压缩包）
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "上传历史个例数据文件")
    @PostMapping("/upload/data")
    public ApiRest<Map<String, Object>> uploadDataFile(@RequestParam("file") MultipartFile file) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                return super.failure("上传文件不能为空");
            }

            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                return super.failure("文件名不能为空");
            }

            // 验证文件大小（限制为500MB）
            long maxSize = 500 * 1024 * 1024; // 500MB
            if (file.getSize() > maxSize) {
                return super.failure("文件大小不能超过500MB");
            }

            // 验证文件类型（压缩文件）
            String extension = getFileExtension(originalFilename);
            if (!isArchiveFile(extension)) {
                return super.failure("不支持的文件格式，支持格式：.zip, .rar, .7z, .tar, .gz, .bz2");
            }

            // 创建上传目录
            String uploadDir = System.getProperty("user.dir") + "/uploads/weather/data/";
            File dir = new File(uploadDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 生成唯一文件名
            String fileName = generateUniqueFileName(originalFilename);
            String filePath = uploadDir + fileName;

            // 保存文件
            Path targetPath = Paths.get(filePath);
            Files.copy(file.getInputStream(), targetPath);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("id", System.currentTimeMillis());
            result.put("name", originalFilename);
            result.put("fileName", fileName);
            result.put("size", file.getSize());
            result.put("url", "/uploads/weather/data/" + fileName);
            result.put("filePath", "uploads/weather/data/" + fileName);

            log.info("用户 {} 上传了数据文件：{}", 
                UserUtils.getUser().getUserName(), originalFilename);

            return super.success(result);

        } catch (IOException e) {
            log.error("上传数据文件失败", e);
            return super.failure("文件上传失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("上传数据文件异常", e);
            return super.failure("文件上传异常：" + e.getMessage());
        }
    }

    /**
     * 上传考生降水落区文件
     */
    @ApiOperation(value = "上传考生降水落区文件")
    @PostMapping("/upload/precipitation-area")
    public ApiRest<Map<String, Object>> uploadPrecipitationAreaFile(@RequestParam("file") MultipartFile file) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                return super.failure("上传文件不能为空");
            }

            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                return super.failure("文件名不能为空");
            }


            // 创建上传目录
            String uploadDir = System.getProperty("user.dir") + "/uploads/weather/precipitation-area/";
            File dir = new File(uploadDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 生成唯一文件名
            String fileName = generateUniqueFileName(originalFilename);
            String filePath = uploadDir + fileName;

            // 保存文件
            Path targetPath = Paths.get(filePath);
            Files.copy(file.getInputStream(), targetPath);

            // 读取文件内容并解析
            String content = new String(Files.readAllBytes(targetPath), StandardCharsets.UTF_8);
            Map<String, Object> parsedData = parsePrecipitationAreaFile(content);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("id", System.currentTimeMillis());
            result.put("name", originalFilename);
            result.put("fileName", fileName);
            result.put("size", file.getSize());
            result.put("url", "/uploads/weather/precipitation-area/" + fileName);
            result.put("filePath", "uploads/weather/precipitation-area/" + fileName);
            result.put("parsedData", parsedData);

            log.info("用户 {} 上传了降水落区实况文件：{}",
                UserUtils.getUser().getUserName(), originalFilename);

            return super.success(result);

        } catch (IOException e) {
            log.error("上传降水落区实况文件失败", e);
            return super.failure("文件上传失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("上传降水落区实况文件异常", e);
            return super.failure("文件上传异常：" + e.getMessage());
        }
    }

    /**
     * 解析降水落区文件内容
     */
    private Map<String, Object> parsePrecipitationAreaFile(String content) {
        Map<String, Object> result = new HashMap<>();

        try {
            String[] lines = content.split("\n");
            Map<String, List<Map<String, Object>>> precipitationData = new HashMap<>();

            boolean inContoursSection = false;
            String currentLevel = null;
            List<List<Double>> currentCoordinates = new ArrayList<>();

            for (int i = 0; i < lines.length; i++) {
                String line = lines[i].trim();

                // 检测闭合轮廓段开始
                if (line.startsWith("CLOSED_CONTOURS:")) {
                    inContoursSection = true;
                    continue;
                }


                // 解析数据行 (如: "3 55" 表示3无用，55个坐标点; "100 1" 表示降水量100，1个无用坐标点)
                Pattern levelPattern = Pattern.compile("^(\\d+)\\s+(\\d+)$");
                Matcher levelMatcher = levelPattern.matcher(line);
                if (levelMatcher.matches()) {
                    int firstNum = Integer.parseInt(levelMatcher.group(1));
                    int pointCount = Integer.parseInt(levelMatcher.group(2));

                    // 如果点数为1，这是降水量结束标识
                    if (pointCount == 1) {
                        // 保存当前轮廓，使用第一个数字作为降水量
                        if (currentLevel != null && !currentCoordinates.isEmpty()) {
                            // 使用结束标识中的降水量值更新当前轮廓的量级
                            String precipitationLevel = mapLevelToString(firstNum);
                            // 创建坐标副本，避免引用被清空
                            List<List<Double>> coordinatesCopy = new ArrayList<>(currentCoordinates);
                            addContourToData(precipitationData, precipitationLevel, coordinatesCopy);
                            currentCoordinates.clear();
                            currentLevel = null;
                        }
                        // 跳过下一行的无用坐标数据
                        if (i + 1 < lines.length) {
                            i++; // 跳过下一行
                        }
                        continue;
                    }

                    // 保存上一个轮廓（如果有的话）
                    if (currentLevel != null && !currentCoordinates.isEmpty()) {
                        // 创建坐标副本，避免引用被清空
                        List<List<Double>> coordinatesCopy = new ArrayList<>(currentCoordinates);
                        addContourToData(precipitationData, currentLevel, coordinatesCopy);
                    }

                    // 开始新轮廓，第一个数字无用，只记录要读取的坐标点数
                    currentLevel = "parsing"; // 临时标识，真正的量级在结束标识中
                    currentCoordinates = new ArrayList<>();
                    continue;
                }

                // 解析坐标行（包含多个坐标点）
                Pattern coordPattern = Pattern.compile("^\\s*[\\d.-]+\\s+[\\d.-]+\\s+[\\d.-]+");
                Matcher coordMatcher = coordPattern.matcher(line);
                if (coordMatcher.find() && currentLevel != null && !"null".equals(currentLevel)) {
                    String[] coords = line.trim().split("\\s+");

                    for (int j = 0; j < coords.length; j += 3) {
                        if (j + 1 < coords.length) {
                            try {
                                double lng = Double.parseDouble(coords[j]);
                                double lat = Double.parseDouble(coords[j + 1]);
                                List<Double> point = Arrays.asList(lng, lat);
                                currentCoordinates.add(point);
                            } catch (NumberFormatException e) {
                                log.warn("解析坐标失败: {}", Arrays.toString(coords));
                            }
                        }
                    }
                    log.debug("当前坐标列表大小: {}", currentCoordinates.size());
                }
            }

            // 保存最后一个轮廓（如果文件结束时还有未保存的轮廓）
            if (currentLevel != null && !currentCoordinates.isEmpty()) {
                // 如果没有结束标识，使用默认量级
                String defaultLevel = "level0";
                log.warn("发现没有结束标识的轮廓数据，使用默认量级: {}", defaultLevel);
                // 创建坐标副本，避免引用被清空
                List<List<Double>> coordinatesCopy = new ArrayList<>(currentCoordinates);
                addContourToData(precipitationData, defaultLevel, coordinatesCopy);
            }

            result.put("success", true);
            result.put("data", precipitationData);
            result.put("message", "文件解析成功");

            log.info("降水落区文件解析完成，共解析出 {} 个量级的数据", precipitationData.size());

        } catch (Exception e) {
            log.error("解析降水落区文件失败", e);
            result.put("success", false);
            result.put("message", "文件解析失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 添加轮廓到数据中
     */
    private void addContourToData(Map<String, List<Map<String, Object>>> precipitationData,
                                  String level, List<List<Double>> coordinates) {
        log.debug("addContourToData 被调用: level={}, coordinates.size()={}", level, coordinates.size());
        if (coordinates.isEmpty()) {
            log.warn("坐标列表为空，跳过添加轮廓");
            return;
        }

        // 确保轮廓闭合
        if (!coordinates.get(0).equals(coordinates.get(coordinates.size() - 1))) {
            coordinates.add(new ArrayList<>(coordinates.get(0)));
        }

        Map<String, Object> contour = new HashMap<>();
        contour.put("id", System.currentTimeMillis() + "_" + Math.random());

        Map<String, Object> geometry = new HashMap<>();
        geometry.put("type", "Polygon");
        // GeoJSON Polygon 格式: [[[lng, lat], [lng, lat], ...]]
        geometry.put("coordinates", Arrays.asList(coordinates));
        contour.put("geometry", geometry);

        Map<String, Object> properties = new HashMap<>();
        properties.put("precipitationLevel", level);
        properties.put("createTime", new Date().toString());
        contour.put("properties", properties);

        precipitationData.computeIfAbsent(level, k -> new ArrayList<>()).add(contour);
    }

    /**
     * 将数字量级映射为字符串
     */
    private String mapLevelToString(int level) {
        Map<Integer, String> levelMap = new HashMap<>();
        levelMap.put(0, "level0");     // 0mm - 无降水
        levelMap.put(10, "level10");   // 10mm
        levelMap.put(25, "level25");   // 25mm
        levelMap.put(50, "level50");   // 50mm
        levelMap.put(100, "level100"); // 100mm
        levelMap.put(250, "level250"); // 250mm
        levelMap.put(500, "level500"); // 500mm

        return levelMap.getOrDefault(level, "level" + level);
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf('.') == -1) {
            return "";
        }
        return filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
    }

    /**
     * 判断是否为MICAPS文件
     */
    private boolean isMicapsFile(String extension) {
        String[] micapsExtensions = {"000", "024", "dat", "txt", "nc", "grib", "grib2", "cma"};
        for (String ext : micapsExtensions) {
            if (ext.equals(extension)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否为压缩文件
     */
    private boolean isArchiveFile(String extension) {
        String[] archiveExtensions = {"zip", "rar", "7z", "tar", "gz", "bz2"};
        for (String ext : archiveExtensions) {
            if (ext.equals(extension)) {
                return true;
            }
        }
        return false;
    }



    /**
     * 生成唯一文件名
     */
    private String generateUniqueFileName(String originalFilename) {
        String extension = getFileExtension(originalFilename);
        String uuid = UUID.randomUUID().toString().replace("-", "");
        return uuid + (extension.isEmpty() ? "" : "." + extension);
    }
}
