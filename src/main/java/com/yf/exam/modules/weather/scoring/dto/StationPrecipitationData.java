package com.yf.exam.modules.weather.scoring.dto;

import lombok.Data;

/**
 * 站点降水数据
 *
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
@Data
public class StationPrecipitationData {
    
    /** 站点ID */
    private long stationId;
    
    /** 经度 */
    private double longitude;
    
    /** 纬度 */
    private double latitude;
    
    /** 实况降水量（毫米） */
    private double actualPrecipitation;
    
    /** 实况降水等级 */
    private String actualLevel;
    
    /** 预报降水量（毫米） */
    private double forecastPrecipitation;
    
    /** 预报降水等级 */
    private String forecastLevel;

    /**
     * 判断是否有实况降水
     */
    public boolean hasActualRain() {
        return actualPrecipitation >= 0.1;
    }
    
    /**
     * 判断是否有预报降水
     */
    public boolean hasForecastRain() {
        return forecastPrecipitation >= 0.1;
    }
    
    /**
     * 判断降水等级是否匹配
     */
    public boolean isLevelMatch() {
        if (actualLevel == null || forecastLevel == null) {
            return false;
        }
        return actualLevel.equals(forecastLevel);
    }
    
    /**
     * 获取站点位置描述
     */
    public String getLocationDescription() {
        return String.format("站点%d(%.2f°E, %.2f°N)", stationId, longitude, latitude);
    }
    
    @Override
    public String toString() {
        return String.format("StationPrecipitationData{stationId=%d, location=(%.2f,%.2f), " +
                           "actual=%.1fmm(%s), forecast=%.1fmm(%s)}", 
                           stationId, longitude, latitude, 
                           actualPrecipitation, actualLevel, 
                           forecastPrecipitation, forecastLevel);
    }
}
