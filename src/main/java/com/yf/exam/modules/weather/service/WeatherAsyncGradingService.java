package com.yf.exam.modules.weather.service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 天气预报异步判卷处理服务接口
 *
 * 提供异步批量判卷功能，支持：
 * - 大批量数据的异步处理
 * - 并发判卷任务管理
 * - 异步任务状态监控
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface WeatherAsyncGradingService {

    /**
     * 异步执行批量判卷
     *
     * @param batchTaskId 批量任务ID
     * @param answerIds 答案ID列表
     * @param configId 评分配置ID
     * @return 异步任务结果
     */
    CompletableFuture<Void> executeBatchGradingAsync(String batchTaskId, List<String> answerIds, String configId);

    /**
     * 异步执行单个答案判卷
     *
     * @param answerId 答案ID
     * @param configId 评分配置ID
     * @return 异步任务结果，包含判卷得分
     */
    CompletableFuture<Double> executeSingleGradingAsync(String answerId, String configId);

    /**
     * 停止正在运行的异步批量任务
     *
     * @param batchTaskId 批量任务ID
     * @return 是否成功停止
     */
    boolean stopAsyncBatchTask(String batchTaskId);

    /**
     * 检查异步任务是否正在运行
     *
     * @param batchTaskId 批量任务ID
     * @return 是否正在运行
     */
    boolean isAsyncTaskRunning(String batchTaskId);

    /**
     * 获取正在运行的异步任务数量
     *
     * @return 运行中的任务数量
     */
    int getRunningAsyncTaskCount();

    /**
     * 获取线程池状态信息
     *
     * @return 线程池状态
     */
    String getThreadPoolStatus();

    /**
     * 异步清理过期的判卷结果
     *
     * @param retentionDays 保留天数
     * @return 异步任务结果，包含清理的数量
     */
    CompletableFuture<Integer> cleanupExpiredResultsAsync(int retentionDays);

    /**
     * 异步重建判卷统计数据
     *
     * @param examId 考试ID，为空时重建全部
     * @return 异步任务结果
     */
    CompletableFuture<Boolean> rebuildStatisticsAsync(String examId);
} 