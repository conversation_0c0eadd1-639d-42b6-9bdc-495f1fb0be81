package com.yf.exam.modules.weather.scoring.algorithm;

import com.yf.exam.modules.weather.config.WeatherScoringProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 天气数据比较算法工具类
 * 提供各种天气要素的比较算法，支持容差处理和评分计算
 * 
 * <AUTHOR>
 * @since 2024-12-21
 */
@Slf4j
@Component
public class WeatherDataComparator {

    @Autowired
    private WeatherScoringProperties scoringProperties;

    /**
     * 风向映射表：将风向文字转换为角度值
     */
    private static final Map<String, Double> WIND_DIRECTION_MAP = new HashMap<>();
    
    /**
     * 风力等级映射表：风力文字描述到等级数值
     */
    private static final Map<String, Integer> WIND_FORCE_MAP = new HashMap<>();
    
    /**
     * 天气现象权重映射：不同天气现象的严重程度权重
     */
    private static final Map<String, Double> WEATHER_PHENOMENON_WEIGHT = new HashMap<>();

    static {
        // 初始化风向映射（以角度表示，正北为0度，顺时针）
        WIND_DIRECTION_MAP.put("北", 0.0);
        WIND_DIRECTION_MAP.put("北偏东", 22.5);
        WIND_DIRECTION_MAP.put("东北", 45.0);
        WIND_DIRECTION_MAP.put("东偏北", 67.5);
        WIND_DIRECTION_MAP.put("东", 90.0);
        WIND_DIRECTION_MAP.put("东偏南", 112.5);
        WIND_DIRECTION_MAP.put("东南", 135.0);
        WIND_DIRECTION_MAP.put("南偏东", 157.5);
        WIND_DIRECTION_MAP.put("南", 180.0);
        WIND_DIRECTION_MAP.put("南偏西", 202.5);
        WIND_DIRECTION_MAP.put("西南", 225.0);
        WIND_DIRECTION_MAP.put("西偏南", 247.5);
        WIND_DIRECTION_MAP.put("西", 270.0);
        WIND_DIRECTION_MAP.put("西偏北", 292.5);
        WIND_DIRECTION_MAP.put("西北", 315.0);
        WIND_DIRECTION_MAP.put("北偏西", 337.5);
        WIND_DIRECTION_MAP.put("无风", -1.0); // 特殊标记
        WIND_DIRECTION_MAP.put("偏北风", 0.0);
        WIND_DIRECTION_MAP.put("偏东风", 90.0);
        WIND_DIRECTION_MAP.put("偏南风", 180.0);
        WIND_DIRECTION_MAP.put("偏西风", 270.0);

        // 初始化风力等级映射
        WIND_FORCE_MAP.put("无风", 0);
        WIND_FORCE_MAP.put("1级", 1);
        WIND_FORCE_MAP.put("2级", 2);
        WIND_FORCE_MAP.put("3级", 3);
        WIND_FORCE_MAP.put("4级", 4);
        WIND_FORCE_MAP.put("5级", 5);
        WIND_FORCE_MAP.put("6级", 6);
        WIND_FORCE_MAP.put("7级", 7);
        WIND_FORCE_MAP.put("8级", 8);
        WIND_FORCE_MAP.put("9级", 9);
        WIND_FORCE_MAP.put("10级", 10);
        WIND_FORCE_MAP.put("11级", 11);
        WIND_FORCE_MAP.put("12级", 12);

        // 添加中文数字格式支持
        WIND_FORCE_MAP.put("一级", 1);
        WIND_FORCE_MAP.put("二级", 2);
        WIND_FORCE_MAP.put("三级", 3);
        WIND_FORCE_MAP.put("四级", 4);
        WIND_FORCE_MAP.put("五级", 5);
        WIND_FORCE_MAP.put("六级", 6);
        WIND_FORCE_MAP.put("七级", 7);
        WIND_FORCE_MAP.put("八级", 8);
        WIND_FORCE_MAP.put("九级", 9);
        WIND_FORCE_MAP.put("十级", 10);
        WIND_FORCE_MAP.put("十一级", 11);
        WIND_FORCE_MAP.put("十二级", 12);

        // 其他风力描述
        WIND_FORCE_MAP.put("小于3级", 2);
        WIND_FORCE_MAP.put("3-4级", 3);
        WIND_FORCE_MAP.put("4-5级", 4);
        WIND_FORCE_MAP.put("5-6级", 5);
        WIND_FORCE_MAP.put("微风", 1);
        WIND_FORCE_MAP.put("轻风", 2);
        WIND_FORCE_MAP.put("软风", 3);
        WIND_FORCE_MAP.put("和风", 4);
        WIND_FORCE_MAP.put("清风", 5);

        // 初始化天气现象权重
        WEATHER_PHENOMENON_WEIGHT.put("晴", 1.0);
        WEATHER_PHENOMENON_WEIGHT.put("多云", 1.2);
        WEATHER_PHENOMENON_WEIGHT.put("阴", 1.5);
        WEATHER_PHENOMENON_WEIGHT.put("小雨", 2.0);
        WEATHER_PHENOMENON_WEIGHT.put("中雨", 2.5);
        WEATHER_PHENOMENON_WEIGHT.put("大雨", 3.0);
        WEATHER_PHENOMENON_WEIGHT.put("暴雨", 3.5);
        WEATHER_PHENOMENON_WEIGHT.put("大暴雨", 4.0);
        WEATHER_PHENOMENON_WEIGHT.put("特大暴雨", 4.5);
        WEATHER_PHENOMENON_WEIGHT.put("雷阵雨", 2.8);
        WEATHER_PHENOMENON_WEIGHT.put("冰雹", 4.2);
        WEATHER_PHENOMENON_WEIGHT.put("雪", 2.5);
        WEATHER_PHENOMENON_WEIGHT.put("大雪", 3.2);
        WEATHER_PHENOMENON_WEIGHT.put("暴雪", 3.8);
        WEATHER_PHENOMENON_WEIGHT.put("雾", 2.2);
        WEATHER_PHENOMENON_WEIGHT.put("霾", 2.0);
        WEATHER_PHENOMENON_WEIGHT.put("沙尘暴", 4.0);
        WEATHER_PHENOMENON_WEIGHT.put("龙卷风", 5.0);
    }

    /**
     * 比较风力信息
     *
     * @param predictedWindForce 预测风力
     * @param actualWindForce 实际风力
     * @return 比较结果
     */
    public ComparisonResult compareWindForce(String predictedWindForce, String actualWindForce) {
        ComparisonResult result = new ComparisonResult();
        
        try {
            if (!StringUtils.hasText(predictedWindForce) || !StringUtils.hasText(actualWindForce)) {
                result.setMatch(false);
                result.setScore(0.0);
                result.setReason("风力数据不完整");
                return result;
            }

            Integer predictedLevel = parseWindForce(predictedWindForce);
            Integer actualLevel = parseWindForce(actualWindForce);

            if (predictedLevel == null || actualLevel == null) {
                result.setMatch(false);
                result.setScore(0.0);
                result.setReason("无法解析风力等级");
                return result;
            }

            // 使用配置文件中的评分规则
            WeatherScoringProperties.WindForce windForceConfig = scoringProperties.getElements().getWindForce();
            int difference = Math.abs(predictedLevel - actualLevel);

            // 计算匹配度和得分
            if (difference == 0) {
                // 完全匹配
                result.setMatch(true);
                result.setScore(windForceConfig.getLevels().getExact());
                result.setReason("风力完全匹配");
            } else if (difference == 1) {
                // 相邻等级
                result.setMatch(true);
                result.setScore(windForceConfig.getLevels().getAdjacent());
                result.setReason(String.format("风力相邻等级，差值: %d级", difference));
            } else {
                // 其他情况
                result.setMatch(false);
                result.setScore(windForceConfig.getLevels().getWrong());
                result.setReason(String.format("风力差异较大，差值: %d级", difference));
            }

            result.setDifference(difference);
            result.setPredictedValue(predictedLevel);
            result.setActualValue(actualLevel);

        } catch (Exception e) {
            log.error("比较风力时发生异常", e);
            result.setMatch(false);
            result.setScore(0.0);
            result.setReason("风力比较异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 比较风向信息
     *
     * @param predictedWindDir 预测风向
     * @param actualWindDir 实际风向
     * @return 比较结果
     */
    public ComparisonResult compareWindDirection(String predictedWindDir, String actualWindDir) {
        ComparisonResult result = new ComparisonResult();

        try {
            if (!StringUtils.hasText(predictedWindDir) || !StringUtils.hasText(actualWindDir)) {
                result.setMatch(false);
                result.setScore(0.0);
                result.setReason("风向数据不完整");
                return result;
            }

            // 清理度数符号
            String cleanedActualWindDir = actualWindDir.replace("°", "");

            // 获取配置文件中的评分规则
            WeatherScoringProperties.WindDirection windDirConfig = scoringProperties.getElements().getWindDirection();

            // predictedWindDir是方位词（如"东北"），actualWindDir是角度范围（如"45.0～67.5°"）
            String predictedDirection = predictedWindDir.trim();
            String actualAngleRange = cleanedActualWindDir.trim();

            // 从简化配置中获取得分
            Double score = getScoreFromSimplifiedConfig(predictedDirection, actualAngleRange, windDirConfig);

            if (score != null) {
                result.setScore(score);
                if (score >= 1.0) {
                    result.setMatch(true);
                    result.setReason(String.format("风向完全匹配(预测:%s, 实际:%s)", predictedDirection, actualAngleRange));
                } else if (score >= 0.6) {
                    result.setMatch(true);
                    result.setReason(String.format("风向相邻匹配(预测:%s, 实际:%s)", predictedDirection, actualAngleRange));
                } else {
                    result.setMatch(false);
                    result.setReason(String.format("风向不匹配(预测:%s, 实际:%s)", predictedDirection, actualAngleRange));
                }
            } else {
                result.setMatch(false);
                result.setScore(0.0);
                result.setReason(String.format("评分矩阵中未找到对应组合(预测:%s, 实际:%s)", predictedDirection, actualAngleRange));
            }

            result.setPredictedValue(predictedDirection);
            result.setActualValue(actualAngleRange);

        } catch (Exception e) {
            log.error("比较风向时发生异常", e);
            result.setMatch(false);
            result.setScore(0.0);
            result.setReason("风向比较异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 从简化配置中获取得分
     */
    private Double getScoreFromSimplifiedConfig(String predictedDirection, String actualAngleRange,
                                              WeatherScoringProperties.WindDirection windDirConfig) {
        try {
            // 将中文方位词转换为英文缩写
            String englishDirection = convertChineseToEnglishDirection(predictedDirection, windDirConfig);
            if (englishDirection == null) {
                log.warn("无法转换中文方位词: {}", predictedDirection);
                return null;
            }

            // 标准化实际角度范围格式
            String normalizedRange = normalizeAngleRange(actualAngleRange);
            if (normalizedRange == null) {
                log.warn("无法标准化角度范围: {}", actualAngleRange);
                return null;
            }

            // 获取该方位的配置
            Map<String, WeatherScoringProperties.WindDirection.DirectionConfig> directions = windDirConfig.getDirections();
            if (directions == null) {
                log.warn("风向配置为空");
                return null;
            }

            WeatherScoringProperties.WindDirection.DirectionConfig directionConfig = directions.get(englishDirection);
            if (directionConfig == null) {
                log.warn("未找到方位配置: {}", englishDirection);
                return 0.0;
            }

            // 检查是否在完美匹配范围内 (1.0分)
            if (directionConfig.getPerfectRanges() != null) {
                for (String range : directionConfig.getPerfectRanges()) {
                    if (isAngleInRange(normalizedRange, range)) {
                        return 1.0;
                    }
                }
            }

            // 检查是否在良好匹配范围内 (0.6分)
            if (directionConfig.getGoodRanges() != null) {
                for (String range : directionConfig.getGoodRanges()) {
                    if (isAngleInRange(normalizedRange, range)) {
                        return 0.6;
                    }
                }
            }

            // 其他情况返回0分
            return 0.0;

        } catch (Exception e) {
            log.error("从简化配置获取得分时发生异常", e);
            return null;
        }
    }

    /**
     * 将中文方位词转换为英文缩写
     */
    private String convertChineseToEnglishDirection(String chineseDirection,
                                                  WeatherScoringProperties.WindDirection windDirConfig) {
        // 硬编码映射关系，避免配置文件解析问题
        Map<String, String> directionMapping = new HashMap<>();
        directionMapping.put("北", "N");
        directionMapping.put("东北", "NE");
        directionMapping.put("东", "E");
        directionMapping.put("东南", "SE");
        directionMapping.put("南", "S");
        directionMapping.put("西南", "SW");
        directionMapping.put("西", "W");
        directionMapping.put("西北", "NW");

        String englishDirection = directionMapping.get(chineseDirection);
        if (englishDirection == null) {
            log.warn("未找到中文方位词的英文映射: {}", chineseDirection);
        }

        return englishDirection;
    }

    /**
     * 检查角度范围是否匹配
     */
    private boolean isAngleInRange(String actualRange, String configRange) {
        try {
            // 解析实际角度范围
            String[] actualParts = actualRange.split("-");
            if (actualParts.length != 2) {
                return false;
            }
            double actualStart = Double.parseDouble(actualParts[0]);
            double actualEnd = Double.parseDouble(actualParts[1]);

            // 解析配置角度范围
            String[] configParts = configRange.split("-");
            if (configParts.length != 2) {
                return false;
            }
            double configStart = Double.parseDouble(configParts[0]);
            double configEnd = Double.parseDouble(configParts[1]);

            // 检查范围是否匹配（允许一定的误差）
            return Math.abs(actualStart - configStart) < 0.1 && Math.abs(actualEnd - configEnd) < 0.1;

        } catch (Exception e) {
            log.warn("角度范围比较失败: actual={}, config={}", actualRange, configRange, e);
            return false;
        }
    }

    /**
     * 标准化角度范围格式
     */
    private String normalizeAngleRange(String angleRange) {
        if (!StringUtils.hasText(angleRange)) {
            return null;
        }

        String normalized = angleRange.trim().replace("°", "");

        // 支持多种分隔符：～、~、-
        if (normalized.contains("～")) {
            normalized = normalized.replace("～", "-");
        } else if (normalized.contains("~")) {
            normalized = normalized.replace("~", "-");
        }

        // 保持小数点格式，与配置文件中的键名格式一致
        return normalized;
    }





    /**
     * 比较温度信息
     *
     * @param predictedTemp 预测温度
     * @param actualTemp 实际温度
     * @param tempType 温度类型（maxTemperature或minTemperature）
     * @return 比较结果
     */
    public ComparisonResult compareTemperature(Object predictedTemp, Object actualTemp, String tempType) {
        ComparisonResult result = new ComparisonResult();
        
        try {
            Double predictedValue = parseTemperature(predictedTemp);
            Double actualValue = parseTemperature(actualTemp);

            if (predictedValue == null || actualValue == null) {
                result.setMatch(false);
                result.setScore(0.0);
                result.setReason("温度数据不完整或无法解析");
                return result;
            }

            double difference = Math.abs(predictedValue - actualValue);

            // 获取对应的温度配置
            WeatherScoringProperties.Temperature tempConfig;
            if ("maxTemperature".equals(tempType)) {
                tempConfig = scoringProperties.getElements().getMaxTemperature();
            } else {
                tempConfig = scoringProperties.getElements().getMinTemperature();
            }

            // 计算匹配度和得分
            if (difference <= tempConfig.getToleranceRange()) {
                result.setMatch(true);
                result.setScore(tempConfig.getLevels().getWithinTolerance());
                result.setReason(String.format("温度在容差范围内，差值: %.1f°C", difference));
            } else {
                result.setMatch(false);
                result.setScore(tempConfig.getLevels().getOutsideTolerance());
                result.setReason(String.format("温度超出容差，差值: %.1f°C", difference));
            }

            result.setDifference(difference);
            result.setPredictedValue(predictedValue);
            result.setActualValue(actualValue);

        } catch (Exception e) {
            log.error("比较温度时发生异常", e);
            result.setMatch(false);
            result.setScore(0.0);
            result.setReason("温度比较异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 比较降水信息
     *
     * @param predictedPrecip 预测降水量
     * @param actualPrecip 实际降水量
     * @return 比较结果
     */
    public ComparisonResult comparePrecipitation(String predictedPrecip, String actualPrecip) {
        ComparisonResult result = new ComparisonResult();
        
        try {
            if (!StringUtils.hasText(predictedPrecip) || !StringUtils.hasText(actualPrecip)) {
                result.setMatch(false);
                result.setScore(0.0);
                result.setReason("降水数据不完整");
                return result;
            }

            // 使用配置文件中的降水评分矩阵
            WeatherScoringProperties.Precipitation precipConfig = scoringProperties.getElements().getPrecipitation();
            Map<String, Map<String, Double>> scoreMatrix = precipConfig.getScoreMatrix();

            // 从评分矩阵中获取得分
            Double score = null;
            if (scoreMatrix != null && scoreMatrix.containsKey(predictedPrecip)) {
                Map<String, Double> predictedRow = scoreMatrix.get(predictedPrecip);
                if (predictedRow != null && predictedRow.containsKey(actualPrecip)) {
                    score = predictedRow.get(actualPrecip);
                }
            }

            if (score != null) {
                result.setScore(score);
                if (score.equals(precipConfig.getLevels().getExactMatch())) {
                    result.setMatch(true);
                    result.setReason("降水完全匹配");
                } else if (score.equals(precipConfig.getLevels().getAdjacentLevel())) {
                    result.setMatch(true);
                    result.setReason("降水相邻等级匹配");
                } else if (score > 0.0) {
                    result.setMatch(true);
                    result.setReason("降水部分匹配");
                } else {
                    result.setMatch(false);
                    result.setReason("降水不匹配");
                }
            } else {
                // 如果评分矩阵中没有找到对应的组合，给0分
                result.setMatch(false);
                result.setScore(0.0);
                result.setReason("降水类型不在评分矩阵中");
            }

            result.setPredictedValue(predictedPrecip);
            result.setActualValue(actualPrecip);

        } catch (Exception e) {
            log.error("比较降水时发生异常", e);
            result.setMatch(false);
            result.setScore(0.0);
            result.setReason("降水比较异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 比较灾害性天气
     *
     * @param predictedDisaster 预测灾害天气
     * @param actualDisaster 实际灾害天气
     * @return 比较结果
     */
    public ComparisonResult compareDisasterWeather(String predictedDisaster, String actualDisaster) {
        ComparisonResult result = new ComparisonResult();
        
        try {
            if (!StringUtils.hasText(predictedDisaster) || !StringUtils.hasText(actualDisaster)) {
                result.setMatch(false);
                result.setScore(0.0);
                result.setReason("灾害天气数据不完整");
                return result;
            }

            // 使用配置文件中的灾害天气评分规则
            WeatherScoringProperties.DisasterWeather disasterConfig = scoringProperties.getElements().getDisasterWeather();

            // 预处理天气现象字符串
            Set<String> predictedPhenomena = parseWeatherPhenomena(predictedDisaster);
            Set<String> actualPhenomena = parseWeatherPhenomena(actualDisaster);

            // 特殊处理："无"的情况
            boolean predictedIsNone = predictedPhenomena.contains("无") || predictedPhenomena.isEmpty();
            boolean actualIsNone = actualPhenomena.contains("无") || actualPhenomena.isEmpty();

            if (predictedIsNone && actualIsNone) {
                // 都为"无"
                result.setMatch(true);
                result.setScore(disasterConfig.getSpecialRules().getNoneMatch().getBothNone());
                result.setReason("都无灾害天气，完全匹配");
            } else if (predictedIsNone || actualIsNone) {
                // 一个为"无"，一个有灾害
                result.setMatch(false);
                result.setScore(disasterConfig.getSpecialRules().getNoneMatch().getOneNone());
                result.setReason("一个无灾害，一个有灾害");
            } else {
                // 都有灾害天气，按比例评分
                Set<String> intersection = new HashSet<>(predictedPhenomena);
                intersection.retainAll(actualPhenomena);

                if (intersection.size() == actualPhenomena.size() && actualPhenomena.size() == predictedPhenomena.size()) {
                    // 完全匹配
                    result.setMatch(true);
                    result.setScore(disasterConfig.getRules().getExactMatch());
                    result.setReason("灾害天气完全匹配");
                } else {
                    // 按比例评分：匹配数量 / 实况总数量 * 满分
                    double proportionScore = (double) intersection.size() / actualPhenomena.size() * disasterConfig.getMaxScore();
                    result.setMatch(intersection.size() > 0);
                    result.setScore(proportionScore);
                    result.setReason(String.format("灾害天气部分匹配，匹配%d/%d项", intersection.size(), actualPhenomena.size()));
                }
            }

            result.setPredictedValue(predictedPhenomena);
            result.setActualValue(actualPhenomena);

        } catch (Exception e) {
            log.error("比较灾害天气时发生异常", e);
            result.setMatch(false);
            result.setScore(0.0);
            result.setReason("灾害天气比较异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 综合比较天气数据
     *
     * @param predictedData 预测数据
     * @param actualData 实际数据
     * @return 综合比较结果
     */
    public WeatherComparisonResult compareWeatherData(Map<String, Object> predictedData,
                                                    Map<String, Object> actualData) {
        WeatherComparisonResult result = new WeatherComparisonResult();
        Map<String, ComparisonResult> detailResults = new HashMap<>();

        try {
            double totalScore = 0.0;
            double totalWeight = 0.0;

            // 提取站点数据
            List<Map<String, Object>> predictedStations = extractStationsData(predictedData);
            List<Map<String, Object>> actualStations = extractStationsData(actualData);

            if (predictedStations.isEmpty() || actualStations.isEmpty()) {
                log.warn("预测数据或实际数据中没有找到站点信息");
                result.setOverallScore(0.0);
                result.setOverallMatch(false);
                return result;
            }

            // 多站点评分：遍历所有站点进行比较
            Map<String, List<ComparisonResult>> allElementResults = new HashMap<>();
            int stationCount = 0;

            // 将预测站点转换为Map，便于按站点ID查找
            Map<String, Map<String, Object>> predictedStationMap = convertStationsToMap(predictedStations);
            Map<String, Map<String, Object>> actualStationMap = convertStationsToMap(actualStations);

            // 遍历所有预测站点
            for (String stationId : predictedStationMap.keySet()) {
                Map<String, Object> predictedStation = predictedStationMap.get(stationId);
                Map<String, Object> actualStation = actualStationMap.get(stationId);

                if (actualStation == null) {
                    log.warn("标准答案中未找到站点: {}", stationId);
                    continue;
                }

                stationCount++;
                log.debug("正在比较站点: {}", stationId);

                // 比较各个要素
                compareStationElements(predictedStation, actualStation, stationId, allElementResults);
            }

            // 直接累加所有站点的所有要素得分
              for (Map.Entry<String, List<ComparisonResult>> entry : allElementResults.entrySet()) {
                String elementName = entry.getKey();
                List<ComparisonResult> results = entry.getValue();

                if (results.isEmpty()) {
                    continue;
                }

                // 累加该要素在所有站点的得分
                double elementTotalScore = results.stream().mapToDouble(ComparisonResult::getScore).sum();
                totalScore += elementTotalScore;
                totalWeight += results.size(); // 权重为参与评分的站点数

                // 为了显示详细结果，计算该要素的平均得分和匹配情况
                double averageScore = elementTotalScore / results.size();
                long matchCount = results.stream().mapToLong(r -> r.isMatch() ? 1 : 0).sum();
                boolean overallMatch = matchCount > results.size() / 2;
                String reason = String.format("%d个站点，总得分%.2f，平均%.2f，%d个匹配",
                    results.size(), elementTotalScore, averageScore, matchCount);

                ComparisonResult summaryResult = new ComparisonResult(overallMatch, elementTotalScore, reason);
                detailResults.put(elementName, summaryResult);
            }

            log.info("多站点评分完成，共评分{}个站点，{}个要素", stationCount, detailResults.size());

            // 计算最终得分
            double finalScore = totalScore;

            result.setOverallScore(finalScore);
            result.setDetailResults(detailResults);
            result.setTotalWeight(totalWeight);
            result.setWeightedScore(totalScore);

            // 判断整体匹配状态（可以根据实际需求调整阈值）
            double matchThreshold = totalWeight * 0.7; // 70%的得分率作为匹配阈值
            result.setOverallMatch(finalScore >= matchThreshold);

        } catch (Exception e) {
            log.error("综合比较天气数据时发生异常", e);
            result.setOverallScore(0.0);
            result.setOverallMatch(false);
        }

        return result;
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 解析风力等级
     */
    private Integer parseWindForce(String windForce) {
        if (!StringUtils.hasText(windForce)) {
            return null;
        }

        String normalized = windForce.trim();

        // 直接映射查找
        if (WIND_FORCE_MAP.containsKey(normalized)) {
            return WIND_FORCE_MAP.get(normalized);
        }

        // 尝试提取数字
        try {
            // 匹配数字+级的模式
            if (normalized.matches("\\d+级")) {
                return Integer.parseInt(normalized.replaceAll("[^\\d]", ""));
            }

            // 尝试中文数字转换
            Integer chineseNumber = parseChineseNumber(normalized);
            if (chineseNumber != null) {
                return chineseNumber;
            }

            // 尝试直接解析数字
            return Integer.parseInt(normalized);
        } catch (NumberFormatException e) {
            log.warn("无法解析风力等级: {}", windForce);
            return null;
        }
    }

    /**
     * 解析中文数字（支持一到十二的风力等级）
     */
    private Integer parseChineseNumber(String text) {
        if (!StringUtils.hasText(text)) {
            return null;
        }

        String normalized = text.trim();

        // 处理带"级"字的中文数字
        if (normalized.endsWith("级")) {
            normalized = normalized.substring(0, normalized.length() - 1);
        }

        // 中文数字映射
        Map<String, Integer> chineseNumberMap = new HashMap<>();
        chineseNumberMap.put("一", 1);
        chineseNumberMap.put("二", 2);
        chineseNumberMap.put("三", 3);
        chineseNumberMap.put("四", 4);
        chineseNumberMap.put("五", 5);
        chineseNumberMap.put("六", 6);
        chineseNumberMap.put("七", 7);
        chineseNumberMap.put("八", 8);
        chineseNumberMap.put("九", 9);
        chineseNumberMap.put("十", 10);
        chineseNumberMap.put("十一", 11);
        chineseNumberMap.put("十二", 12);

        return chineseNumberMap.get(normalized);
    }

    /**
     * 解析风向角度
     */
    private Double parseWindDirection(String windDirection) {
        if (!StringUtils.hasText(windDirection)) {
            return null;
        }

        String normalized = windDirection.trim();

        // 直接映射查找
        if (WIND_DIRECTION_MAP.containsKey(normalized)) {
            return WIND_DIRECTION_MAP.get(normalized);
        }

        // 解析角度范围格式，如"22.5～45.0°"、"22.5~45.0°"、"22.5-45.0°"
        Double rangeAngle = parseWindDirectionRange(normalized);
        if (rangeAngle != null) {
            return rangeAngle;
        }

        // 尝试数值解析（单个角度值）
        try {
            // 移除可能的度数符号
            String angleStr = normalized.replaceAll("[°度]", "").trim();
            return Double.parseDouble(angleStr);
        } catch (NumberFormatException e) {
            log.warn("无法解析风向: {}", windDirection);
            return null;
        }
    }

    /**
     * 解析风向角度范围，返回范围的中间值
     * 支持格式：22.5～45.0°、22.5~45.0°、22.5-45.0°
     */
    private Double parseWindDirectionRange(String windDirection) {
        if (!StringUtils.hasText(windDirection)) {
            return null;
        }

        try {
            String normalized = windDirection.trim();

            // 移除度数符号
            normalized = normalized.replaceAll("[°度]", "");

            // 支持多种分隔符：～、~、-
            String[] rangeParts = null;
            if (normalized.contains("～")) {
                rangeParts = normalized.split("～");
            } else if (normalized.contains("~")) {
                rangeParts = normalized.split("~");
            } else if (normalized.contains("-")) {
                rangeParts = normalized.split("-");
            }

            if (rangeParts != null && rangeParts.length == 2) {
                double startAngle = Double.parseDouble(rangeParts[0].trim());
                double endAngle = Double.parseDouble(rangeParts[1].trim());

                // 返回范围的中间值
                double middleAngle = (startAngle + endAngle) / 2.0;

                log.debug("解析风向角度范围: {} -> 起始角度: {}, 结束角度: {}, 中间值: {}",
                    windDirection, startAngle, endAngle, middleAngle);

                return middleAngle;
            }
        } catch (NumberFormatException e) {
            log.debug("无法解析风向角度范围: {}", windDirection);
        }

        return null;
    }

    /**
     * 解析温度数值
     */
    private Double parseTemperature(Object temperature) {
        if (temperature == null) {
            return null;
        }
        
        if (temperature instanceof Number) {
            return ((Number) temperature).doubleValue();
        }
        
        try {
            String tempStr = temperature.toString().trim();
            // 移除可能的单位符号
            tempStr = tempStr.replaceAll("[°C℃]", "").trim();
            return Double.parseDouble(tempStr);
        } catch (NumberFormatException e) {
            log.warn("无法解析温度: {}", temperature);
            return null;
        }
    }

    /**
     * 解析降水量数值
     */
    private Double parsePrecipitation(Object precipitation) {
        if (precipitation == null) {
            return null;
        }
        
        if (precipitation instanceof Number) {
            return ((Number) precipitation).doubleValue();
        }
        
        try {
            String precipStr = precipitation.toString().trim();
            // 处理"无降水"、"无"等字符串
            if (precipStr.matches("(?i)(无降水|无|trace|微量)")) {
                return 0.0;
            }
            // 移除可能的单位符号
            precipStr = precipStr.replaceAll("mm|毫米", "").trim();
            return Double.parseDouble(precipStr);
        } catch (NumberFormatException e) {
            log.warn("无法解析降水量: {}", precipitation);
            return null;
        }
    }

    /**
     * 解析天气现象集合
     */
    private Set<String> parseWeatherPhenomena(String weather) {
        Set<String> phenomena = new HashSet<>();
        
        if (!StringUtils.hasText(weather)) {
            return phenomena;
        }
        
        // 分割多个天气现象
        String[] parts = weather.split("[,，、\\s]+");
        for (String part : parts) {
            String normalized = part.trim();
            if (StringUtils.hasText(normalized)) {
                phenomena.add(normalized);
            }
        }
        
        return phenomena;
    }

    /**
     * 计算加权相似度
     */
    private double calculateWeightedSimilarity(Set<String> predicted, Set<String> actual) {
        if (predicted.isEmpty() && actual.isEmpty()) {
            return 1.0;
        }
        
        double predictedWeight = predicted.stream()
                                         .mapToDouble(p -> WEATHER_PHENOMENON_WEIGHT.getOrDefault(p, 1.0))
                                         .sum();
        double actualWeight = actual.stream()
                                   .mapToDouble(a -> WEATHER_PHENOMENON_WEIGHT.getOrDefault(a, 1.0))
                                   .sum();
        
        Set<String> intersection = new HashSet<>(predicted);
        intersection.retainAll(actual);
        
        double intersectionWeight = intersection.stream()
                                              .mapToDouble(i -> WEATHER_PHENOMENON_WEIGHT.getOrDefault(i, 1.0))
                                              .sum();
        
        double totalWeight = Math.max(predictedWeight, actualWeight);
        return totalWeight > 0 ? intersectionWeight / totalWeight : 0.0;
    }



    /**
     * 从天气数据中提取站点数据列表
     *
     * @param weatherData 天气数据
     * @return 站点数据列表
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> extractStationsData(Map<String, Object> weatherData) {
        List<Map<String, Object>> stations = new ArrayList<>();

        if (weatherData == null) {
            return stations;
        }

        try {
            // 尝试从stations字段获取站点数据
            Object stationsObj = weatherData.get("stations");
            if (stationsObj instanceof List) {
                // 处理List格式的stations数据
                List<?> stationsList = (List<?>) stationsObj;
                for (Object stationObj : stationsList) {
                    if (stationObj instanceof Map) {
                        stations.add((Map<String, Object>) stationObj);
                    }
                }
            } else if (stationsObj instanceof Map) {
                // 处理Map格式的stations数据（key为站点ID，value为站点数据）
                Map<String, Object> stationsMap = (Map<String, Object>) stationsObj;
                for (Map.Entry<String, Object> entry : stationsMap.entrySet()) {
                    String stationId = entry.getKey();
                    Object stationData = entry.getValue();
                    if (stationData instanceof Map) {
                        Map<String, Object> stationDataMap = (Map<String, Object>) stationData;
                        // 确保站点数据包含stationId信息
                        if (!stationDataMap.containsKey("stationId")) {
                            stationDataMap.put("stationId", stationId);
                        }
                        stations.add(stationDataMap);
                    }
                }
            }

            // 如果没有找到stations字段，检查是否直接包含站点数据
            if (stations.isEmpty() && containsStationFields(weatherData)) {
                stations.add(weatherData);
            }

        } catch (Exception e) {
            log.warn("提取站点数据时发生异常", e);
        }

        return stations;
    }

    /**
     * 检查数据是否包含站点字段
     *
     * @param data 数据对象
     * @return 是否包含站点字段
     */
    private boolean containsStationFields(Map<String, Object> data) {
        if (data == null) {
            return false;
        }

        // 检查是否包含常见的天气要素字段
        return data.containsKey("windForce") ||
               data.containsKey("windDirection") ||
               data.containsKey("maxTemperature") ||
               data.containsKey("minTemperature") ||
               data.containsKey("precipitation") ||
               data.containsKey("disasterWeather");
    }

    /**
     * 检查站点数据是否包含指定字段且值不为空
     *
     * @param stationData 站点数据
     * @param fieldName 字段名
     * @return 是否包含有效字段
     */
    private boolean hasStationField(Map<String, Object> stationData, String fieldName) {
        if (stationData == null || !stationData.containsKey(fieldName)) {
            return false;
        }

        Object value = stationData.get(fieldName);
        return value != null && !String.valueOf(value).trim().isEmpty();
    }

    /**
     * 将站点列表转换为Map，便于按站点ID查找
     */
    private Map<String, Map<String, Object>> convertStationsToMap(List<Map<String, Object>> stations) {
        Map<String, Map<String, Object>> stationMap = new HashMap<>();

        for (int i = 0; i < stations.size(); i++) {
            Map<String, Object> station = stations.get(i);

            // 尝试获取站点ID
            String stationId = getStationId(station, i);
            stationMap.put(stationId, station);
        }

        return stationMap;
    }

    /**
     * 获取站点ID，优先使用stationId字段，否则使用索引
     */
    private String getStationId(Map<String, Object> station, int index) {
        // 尝试从多个可能的字段获取站点ID
        Object stationId = station.get("stationId");
        if (stationId != null) {
            return String.valueOf(stationId);
        }

        stationId = station.get("stationName");
        if (stationId != null) {
            return String.valueOf(stationId);
        }

        stationId = station.get("id");
        if (stationId != null) {
            return String.valueOf(stationId);
        }

        // 如果没有明确的站点ID，使用索引
        return "station_" + index;
    }

    /**
     * 比较单个站点的所有要素
     */
    private void compareStationElements(Map<String, Object> predictedStation,
                                      Map<String, Object> actualStation,
                                      String stationId,
                                      Map<String, List<ComparisonResult>> allElementResults) {

        // 比较风力
        if (hasStationField(predictedStation, "windForce") && hasStationField(actualStation, "windForce")) {
            ComparisonResult windForceResult = compareWindForce(
                String.valueOf(predictedStation.get("windForce")),
                String.valueOf(actualStation.get("windForce"))
            );
            addElementResult(allElementResults, "windForce", windForceResult);
        }

        // 比较风向
        if (hasStationField(predictedStation, "windDirection") && hasStationField(actualStation, "windDirection")) {
            ComparisonResult windDirResult = compareWindDirection(
                String.valueOf(predictedStation.get("windDirection")),
                String.valueOf(actualStation.get("windDirection"))
            );
            addElementResult(allElementResults, "windDirection", windDirResult);
        }

        // 比较最高温度
        if (hasStationField(predictedStation, "maxTemperature") && hasStationField(actualStation, "maxTemperature")) {
            ComparisonResult maxTempResult = compareTemperature(
                predictedStation.get("maxTemperature"),
                actualStation.get("maxTemperature"),
                "maxTemperature"
            );
            addElementResult(allElementResults, "maxTemperature", maxTempResult);
        }

        // 比较最低温度
        if (hasStationField(predictedStation, "minTemperature") && hasStationField(actualStation, "minTemperature")) {
            ComparisonResult minTempResult = compareTemperature(
                predictedStation.get("minTemperature"),
                actualStation.get("minTemperature"),
                "minTemperature"
            );
            addElementResult(allElementResults, "minTemperature", minTempResult);
        }

        // 比较降水量
        if (hasStationField(predictedStation, "precipitation") && hasStationField(actualStation, "precipitation")) {
            ComparisonResult precipResult = comparePrecipitation(
                String.valueOf(predictedStation.get("precipitation")),
                String.valueOf(actualStation.get("precipitation"))
            );
            addElementResult(allElementResults, "precipitation", precipResult);
        }

        // 比较灾害天气
        if (hasStationField(predictedStation, "disasterWeather") && hasStationField(actualStation, "disasterWeather")) {
            ComparisonResult disasterResult = compareDisasterWeather(
                String.valueOf(predictedStation.get("disasterWeather")),
                String.valueOf(actualStation.get("disasterWeather"))
            );
            addElementResult(allElementResults, "disasterWeather", disasterResult);
        }
    }

    /**
     * 添加要素比较结果到汇总Map中
     */
    private void addElementResult(Map<String, List<ComparisonResult>> allElementResults,
                                String elementName,
                                ComparisonResult result) {
        allElementResults.computeIfAbsent(elementName, k -> new ArrayList<>()).add(result);
    }



    /**
     * 检查两个风向是否在同一角度范围内
     *
     * @param predictedWindDir 预测风向
     * @param actualWindDir 实际风向
     * @return 是否在同一角度范围内
     */
    private boolean isInSameAngleRange(String predictedWindDir, String actualWindDir) {
        if (predictedWindDir == null || actualWindDir == null) {
            return false;
        }

        // 简化处理：如果风向字符串相同，则认为在同一角度范围内
        return predictedWindDir.trim().equals(actualWindDir.trim());
    }

    /**
     * 检查两个风向是否在相邻角度范围内
     *
     * @param predictedWindDir 预测风向
     * @param actualWindDir 实际风向
     * @return 是否在相邻角度范围内
     */
    private boolean isInAdjacentAngleRange(String predictedWindDir, String actualWindDir) {
        if (predictedWindDir == null || actualWindDir == null) {
            return false;
        }

        // 定义相邻关系映射
        Map<String, List<String>> adjacentMap = new HashMap<>();
        adjacentMap.put("北", Arrays.asList("东北", "西北"));
        adjacentMap.put("东北", Arrays.asList("北", "东"));
        adjacentMap.put("东", Arrays.asList("东北", "东南"));
        adjacentMap.put("东南", Arrays.asList("东", "南"));
        adjacentMap.put("南", Arrays.asList("东南", "西南"));
        adjacentMap.put("西南", Arrays.asList("南", "西"));
        adjacentMap.put("西", Arrays.asList("西南", "西北"));
        adjacentMap.put("西北", Arrays.asList("西", "北"));

        String predicted = predictedWindDir.trim();
        String actual = actualWindDir.trim();

        List<String> adjacentDirs = adjacentMap.get(predicted);
        return adjacentDirs != null && adjacentDirs.contains(actual);
    }
}