package com.yf.exam.modules.weather.service.impl;

import com.yf.exam.modules.weather.scoring.engine.ScoringEngineResult;
import com.yf.exam.modules.weather.scoring.engine.WeatherScoringEngine;
import com.yf.exam.modules.weather.scoring.service.WeatherScoringBatchTaskService;
import com.yf.exam.modules.weather.service.WeatherAsyncGradingService;
import com.yf.exam.modules.weather.service.WeatherGradingConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 天气预报异步判卷处理服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Slf4j
@Service
public class WeatherAsyncGradingServiceImpl implements WeatherAsyncGradingService {

    @Autowired
    private WeatherScoringEngine scoringEngine;

    @Autowired
    private WeatherScoringBatchTaskService batchTaskService;

    @Autowired
    private WeatherGradingConfigService gradingConfigService;

    // 自定义线程池，用于判卷任务
    private ThreadPoolExecutor gradingThreadPool;

    // 正在运行的异步任务跟踪
    private final ConcurrentHashMap<String, CompletableFuture<Void>> runningTasks = new ConcurrentHashMap<>();

    // 任务计数器
    private final AtomicInteger taskCounter = new AtomicInteger(0);

    @PostConstruct
    public void initThreadPool() {
        // 从配置服务获取线程池参数
        int threadPoolSize = gradingConfigService.getThreadPoolSize();
        int maxQueueSize = gradingConfigService.getMaxBatchSize() * 2; // 队列大小设为批量大小的2倍

        gradingThreadPool = new ThreadPoolExecutor(
            threadPoolSize,                    // 核心线程数
            threadPoolSize * 2,               // 最大线程数
            60L,                              // 线程空闲时间
            TimeUnit.SECONDS,                 // 时间单位
            new LinkedBlockingQueue<>(maxQueueSize), // 工作队列
            new ThreadFactory() {             // 线程工厂
                private final AtomicInteger threadNumber = new AtomicInteger(1);
                @Override
                public Thread newThread(Runnable r) {
                    Thread t = new Thread(r, "weather-grading-" + threadNumber.getAndIncrement());
                    t.setDaemon(false);
                    return t;
                }
            },
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略：调用者执行
        );

        log.info("初始化判卷线程池，核心线程数：{}，最大线程数：{}，队列大小：{}", 
                threadPoolSize, threadPoolSize * 2, maxQueueSize);
    }

    @PreDestroy
    public void shutdownThreadPool() {
        if (gradingThreadPool != null && !gradingThreadPool.isShutdown()) {
            log.info("关闭判卷线程池");
            gradingThreadPool.shutdown();
            try {
                if (!gradingThreadPool.awaitTermination(30, TimeUnit.SECONDS)) {
                    log.warn("线程池在30秒内未能优雅关闭，强制关闭");
                    gradingThreadPool.shutdownNow();
                }
            } catch (InterruptedException e) {
                log.error("等待线程池关闭时被中断", e);
                gradingThreadPool.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    // ==================== 异步批量判卷功能 ====================

    @Override
    @Async("gradingTaskExecutor")
    public CompletableFuture<Void> executeBatchGradingAsync(String batchTaskId, List<String> answerIds, String configId) {
        log.info("开始异步批量判卷，任务ID：{}，答案数量：{}", batchTaskId, answerIds.size());
        
        return CompletableFuture.runAsync(() -> {
            try {
                // 注册运行中的任务
                CompletableFuture<Void> currentTask = CompletableFuture.completedFuture(null);
                runningTasks.put(batchTaskId, currentTask);
                taskCounter.incrementAndGet();

                // 获取批量处理配置
                int batchSize = gradingConfigService.getMaxBatchSize();
                int timeout = gradingConfigService.getGradingTimeout();
                
                // 更新任务状态为运行中
                updateTaskStatusSafely(batchTaskId, "RUNNING", "异步批量判卷开始");

                executeBatchGradingWithChunks(batchTaskId, answerIds, configId, batchSize, timeout);

                // 任务完成
                updateTaskStatusSafely(batchTaskId, "COMPLETED", "异步批量判卷完成");
                log.info("异步批量判卷完成，任务ID：{}", batchTaskId);

            } catch (Exception e) {
                log.error("异步批量判卷失败，任务ID：{}", batchTaskId, e);
                updateTaskStatusSafely(batchTaskId, "FAILED", "异步批量判卷失败：" + e.getMessage());
                throw new RuntimeException("异步批量判卷失败", e);
            } finally {
                // 清理运行中的任务记录
                runningTasks.remove(batchTaskId);
                taskCounter.decrementAndGet();
            }
        }, gradingThreadPool);
    }

    @Override
    @Async("gradingTaskExecutor")
    public CompletableFuture<Double> executeSingleGradingAsync(String answerId, String configId) {
        log.info("开始异步单个判卷，答案ID：{}", answerId);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                ScoringEngineResult result = scoringEngine.calculateSingleScore(answerId, configId);
                
                if (result.isSuccess()) {
                    log.info("异步单个判卷成功，答案ID：{}，得分：{}", answerId, result.getScore());
                    return result.getScore();
                } else {
                    log.warn("异步单个判卷失败，答案ID：{}，错误：{}", answerId, result.getMessage());
                    throw new RuntimeException("判卷失败：" + result.getMessage());
                }
                
            } catch (Exception e) {
                log.error("异步单个判卷异常，答案ID：{}", answerId, e);
                throw new RuntimeException("判卷异常", e);
            }
        }, gradingThreadPool);
    }

    // ==================== 任务管理功能 ====================

    @Override
    public boolean stopAsyncBatchTask(String batchTaskId) {
        try {
            CompletableFuture<Void> task = runningTasks.get(batchTaskId);
            if (task != null) {
                boolean cancelled = task.cancel(true);
                if (cancelled) {
                    runningTasks.remove(batchTaskId);
                    updateTaskStatusSafely(batchTaskId, "CANCELLED", "用户主动取消任务");
                    log.info("成功停止异步批量任务，任务ID：{}", batchTaskId);
                    return true;
                }
            }
            
            log.warn("未找到正在运行的异步任务，任务ID：{}", batchTaskId);
            return false;
            
        } catch (Exception e) {
            log.error("停止异步批量任务失败，任务ID：{}", batchTaskId, e);
            return false;
        }
    }

    @Override
    public boolean isAsyncTaskRunning(String batchTaskId) {
        CompletableFuture<Void> task = runningTasks.get(batchTaskId);
        return task != null && !task.isDone();
    }

    @Override
    public int getRunningAsyncTaskCount() {
        return taskCounter.get();
    }

    @Override
    public String getThreadPoolStatus() {
        if (gradingThreadPool == null) {
            return "线程池未初始化";
        }

        return String.format("线程池状态 - 活跃线程：%d，核心线程：%d，最大线程：%d，队列大小：%d，完成任务：%d",
                gradingThreadPool.getActiveCount(),
                gradingThreadPool.getCorePoolSize(),
                gradingThreadPool.getMaximumPoolSize(),
                gradingThreadPool.getQueue().size(),
                gradingThreadPool.getCompletedTaskCount());
    }

    // ==================== 异步维护功能 ====================

    @Override
    @Async("gradingTaskExecutor")
    public CompletableFuture<Integer> cleanupExpiredResultsAsync(int retentionDays) {
        log.info("开始异步清理过期判卷结果，保留天数：{}", retentionDays);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // TODO: 实现清理逻辑
                // 这里应该调用相关服务清理过期数据
                int cleanedCount = 0; // 实际清理的数量
                
                log.info("异步清理过期判卷结果完成，清理数量：{}", cleanedCount);
                return cleanedCount;
                
            } catch (Exception e) {
                log.error("异步清理过期判卷结果失败", e);
                throw new RuntimeException("清理失败", e);
            }
        }, gradingThreadPool);
    }

    @Override
    @Async("gradingTaskExecutor")
    public CompletableFuture<Boolean> rebuildStatisticsAsync(String examId) {
        log.info("开始异步重建判卷统计数据，考试ID：{}", examId);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // TODO: 实现统计重建逻辑
                // 这里应该调用相关服务重建统计数据
                
                log.info("异步重建判卷统计数据完成，考试ID：{}", examId);
                return true;
                
            } catch (Exception e) {
                log.error("异步重建判卷统计数据失败，考试ID：{}", examId, e);
                throw new RuntimeException("重建统计失败", e);
            }
        }, gradingThreadPool);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 分块执行批量判卷，提高性能和稳定性
     */
    private void executeBatchGradingWithChunks(String batchTaskId, List<String> answerIds, 
                                             String configId, int chunkSize, int timeoutSeconds) {
        int totalAnswers = answerIds.size();
        int processedCount = 0;
        int successCount = 0;
        int failCount = 0;

        log.info("开始分块执行批量判卷，总数：{}，块大小：{}", totalAnswers, chunkSize);

        // 将答案列表分块处理
        for (int i = 0; i < totalAnswers; i += chunkSize) {
            int endIndex = Math.min(i + chunkSize, totalAnswers);
            List<String> chunk = answerIds.subList(i, endIndex);
            
            log.info("处理第{}块，答案索引：{}-{}", (i / chunkSize + 1), i, endIndex - 1);

            // 处理当前块
            for (String answerId : chunk) {
                try {
                    // 检查任务是否被取消
                    if (Thread.currentThread().isInterrupted()) {
                        log.warn("检测到任务中断信号，停止批量判卷，任务ID：{}", batchTaskId);
                        return;
                    }

                    // 执行单个答案的判卷，带超时控制
                    CompletableFuture<ScoringEngineResult> future = CompletableFuture.supplyAsync(() -> 
                        scoringEngine.calculateSingleScore(answerId, configId), gradingThreadPool);

                    ScoringEngineResult result = future.get(timeoutSeconds, TimeUnit.SECONDS);
                    
                    processedCount++;
                    if (result.isSuccess()) {
                        successCount++;
                    } else {
                        failCount++;
                        log.warn("判卷失败，答案ID：{}，错误：{}", answerId, result.getMessage());
                    }

                } catch (TimeoutException e) {
                    log.error("判卷超时，答案ID：{}，超时时间：{}秒", answerId, timeoutSeconds);
                    processedCount++;
                    failCount++;
                } catch (InterruptedException e) {
                    log.warn("判卷被中断，答案ID：{}", answerId);
                    Thread.currentThread().interrupt();
                    return;
                } catch (Exception e) {
                    log.error("判卷异常，答案ID：{}", answerId, e);
                    processedCount++;
                    failCount++;
                }
            }

            // 更新批量任务进度
            updateTaskProgressSafely(batchTaskId, processedCount, successCount, failCount);

            // 短暂休息，避免资源过度消耗
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return;
            }
        }

        log.info("分块批量判卷完成，任务ID：{}，总计：{}，成功：{}，失败：{}", 
                batchTaskId, processedCount, successCount, failCount);
    }

    /**
     * 安全地更新任务状态
     */
    private void updateTaskStatusSafely(String batchTaskId, String status, String message) {
        try {
            // TODO: 调用batchTaskService的更新状态方法
            // batchTaskService.updateTaskStatus(batchTaskId, status, message);
            log.info("更新任务状态，任务ID：{}，状态：{}，消息：{}", batchTaskId, status, message);
        } catch (Exception e) {
            log.error("更新任务状态失败，任务ID：{}，状态：{}", batchTaskId, status, e);
        }
    }

    /**
     * 安全地更新任务进度
     */
    private void updateTaskProgressSafely(String batchTaskId, int processedCount, int successCount, int failCount) {
        try {
            // TODO: 调用batchTaskService的更新进度方法
            // batchTaskService.updateTaskProgress(batchTaskId, processedCount, successCount, failCount);
            log.info("更新任务进度，任务ID：{}，已处理：{}，成功：{}，失败：{}", 
                    batchTaskId, processedCount, successCount, failCount);
        } catch (Exception e) {
            log.error("更新任务进度失败，任务ID：{}", batchTaskId, e);
        }
    }
} 