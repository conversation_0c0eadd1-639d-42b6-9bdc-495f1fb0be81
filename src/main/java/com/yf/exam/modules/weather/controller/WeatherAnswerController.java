package com.yf.exam.modules.weather.controller;

import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.modules.weather.dto.WeatherAnswerDTO;
import com.yf.exam.modules.weather.service.WeatherAnswerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <p>
 * 天气预报答案控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Api(tags = {"天气预报答案"})
@RestController
@RequestMapping("/exam/api/weather/answer")
public class WeatherAnswerController extends BaseController {

    @Autowired
    private WeatherAnswerService weatherAnswerService;

    /**
     * 保存答题数据
     */
    @ApiOperation(value = "保存答题数据")
    @PostMapping("/save")
    public ApiRest<Boolean> saveAnswer(@RequestBody WeatherAnswerDTO answerDTO) {
        boolean result = weatherAnswerService.saveAnswer(answerDTO);
        return super.success(result);
    }

    /**
     * 根据试卷题目ID获取答题数据
     */
    @ApiOperation(value = "根据试卷题目ID获取答题数据")
    @GetMapping("/{paperQuId}")
    public ApiRest<WeatherAnswerDTO> getAnswer(@PathVariable String paperQuId) {
        WeatherAnswerDTO answer = weatherAnswerService.getAnswerByPaperQuId(paperQuId);
        return super.success(answer);
    }

    /**
     * 根据用户ID和试卷题目ID获取答题数据
     */
    @ApiOperation(value = "根据用户ID和试卷题目ID获取答题数据")
    @GetMapping("/user/{userId}/paper-qu/{paperQuId}")
    public ApiRest<WeatherAnswerDTO> getAnswerByUser(@PathVariable String userId, @PathVariable String paperQuId) {
        WeatherAnswerDTO answer = weatherAnswerService.getAnswerByUserAndPaperQu(userId, paperQuId);
        return super.success(answer);
    }

    /**
     * 验证答案数据
     */
    @ApiOperation(value = "验证答案数据")
    @PostMapping("/validate")
    public ApiRest<Map<String, Object>> validateAnswer(@RequestBody WeatherAnswerDTO answerDTO) {
        Map<String, Object> result = weatherAnswerService.validateAnswer(answerDTO);
        return super.success(result);
    }

    /**
     * 计算答案得分
     */
    @ApiOperation(value = "计算答案得分")
    @PostMapping("/score/calculate")
    public ApiRest<Map<String, Object>> calculateScore(@RequestBody Map<String, WeatherAnswerDTO> request) {
        WeatherAnswerDTO answerDTO = request.get("answer");
        WeatherAnswerDTO standardAnswer = request.get("standardAnswer");
        
        Map<String, Object> result = weatherAnswerService.calculateScore(answerDTO, standardAnswer);
        return super.success(result);
    }

    /**
     * 更新答题状态
     */
    @ApiOperation(value = "更新答题状态")
    @PostMapping("/status/update")
    public ApiRest<Boolean> updateAnswerStatus(@RequestBody Map<String, Object> request) {
        String paperQuId = (String) request.get("paperQuId");
        Boolean answerStatus = (Boolean) request.get("answerStatus");
        
        boolean result = weatherAnswerService.updateAnswerStatus(paperQuId, answerStatus);
        return super.success(result);
    }

    /**
     * 删除答题数据
     */
    @ApiOperation(value = "删除答题数据")
    @DeleteMapping("/{paperQuId}")
    public ApiRest<Boolean> deleteAnswer(@PathVariable String paperQuId) {
        boolean result = weatherAnswerService.deleteAnswer(paperQuId);
        return super.success(result);
    }
}
