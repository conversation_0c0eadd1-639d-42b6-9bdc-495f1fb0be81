package com.yf.exam.modules.weather.controller;

import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.modules.weather.scoring.engine.ScoringEngineResult;
import com.yf.exam.modules.weather.service.WeatherAsyncGradingService;
import com.yf.exam.modules.weather.service.WeatherGradingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 天气预报判卷管理控制器
 *
 * 提供判卷系统的REST API接口，包括：
 * - 单个答案判卷
 * - 批量答案判卷
 * - 判卷任务管理
 * - 判卷结果查询
 * - 系统配置管理
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Api(tags = "天气预报判卷管理")
@Slf4j
@RestController
@RequestMapping("/exam/api/weather/grading")
public class WeatherGradingController extends BaseController {

    @Autowired
    private WeatherGradingService gradingService;

    @Autowired
    private WeatherAsyncGradingService asyncGradingService;

    // ==================== 单个判卷API ====================

    @ApiOperation("对单个答案进行判卷")
    @PostMapping("/single")
    public ApiRest<Map<String, Object>> gradeSingleAnswer(
            @ApiParam(value = "答案ID", required = true) @RequestParam String answerId,
            @ApiParam(value = "评分配置ID，为空时使用默认配置") @RequestParam(required = false) String configId,
            @ApiParam(value = "是否强制重新判卷") @RequestParam(defaultValue = "false") boolean forceRegrade) {

        try {
            log.info("接收单个判卷请求，答案ID：{}，配置ID：{}，强制重判：{}", answerId, configId, forceRegrade);

            if (!StringUtils.hasText(answerId)) {
                return super.failure("答案ID不能为空");
            }

            ScoringEngineResult result;
            if (forceRegrade) {
                result = gradingService.regradeAnswer(answerId, configId);
            } else {
                result = gradingService.gradeAnswer(answerId, configId);
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            response.put("score", result.getScore());
            response.put("scoringResultId", result.getScoringResultId());
            response.put("message", result.getMessage());
            response.put("elapsedTime", result.getElapsedTime());

            if (result.isSuccess()) {
                return super.success(response);
            } else {
                return super.failure("判卷失败：" + result.getMessage());
            }

        } catch (Exception e) {
            log.error("单个判卷API异常，答案ID：{}", answerId, e);
            return super.failure("判卷异常：" + e.getMessage());
        }
    }

    @ApiOperation("检查答案是否已被判卷")
    @GetMapping("/check/{answerId}")
    public ApiRest<Map<String, Object>> checkGradingStatus(
            @ApiParam(value = "答案ID", required = true) @PathVariable String answerId) {

        try {
            boolean isGraded = gradingService.isAnswerGraded(answerId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("answerId", answerId);
            response.put("isGraded", isGraded);

            return super.success(isGraded ? "答案已被判卷" : "答案未被判卷", response);

        } catch (Exception e) {
            log.error("检查判卷状态API异常，答案ID：{}", answerId, e);
            return super.failure("检查状态异常：" + e.getMessage());
        }
    }

    // ==================== 批量判卷API ====================

    @ApiOperation("按考试ID批量判卷")
    @PostMapping("/batch/exam")
    public ApiRest<Map<String, Object>> batchGradeByExam(
            @ApiParam(value = "考试ID", required = true) @RequestParam String examId,
            @ApiParam(value = "评分配置ID，为空时使用默认配置") @RequestParam(required = false) String configId,
            @ApiParam(value = "是否仅处理未判卷的答案") @RequestParam(defaultValue = "true") boolean onlyUngraded,
            @ApiParam(value = "是否使用异步处理") @RequestParam(defaultValue = "false") boolean useAsync) {

        try {
            log.info("接收按考试批量判卷请求，考试ID：{}，配置ID：{}，仅未判卷：{}，异步：{}", 
                    examId, configId, onlyUngraded, useAsync);

            if (!StringUtils.hasText(examId)) {
                return super.failure("考试ID不能为空");
            }

            String taskId = gradingService.batchGradeByExam(examId, configId, onlyUngraded);

            Map<String, Object> response = new HashMap<>();
            response.put("taskId", taskId);
            response.put("examId", examId);
            response.put("async", useAsync);

            if (taskId != null) {
                return super.success("批量判卷任务创建成功", response);
            } else {
                return super.failure("创建批量判卷任务失败");
            }

        } catch (Exception e) {
            log.error("按考试批量判卷API异常，考试ID：{}", examId, e);
            return super.failure("批量判卷异常：" + e.getMessage());
        }
    }

    @ApiOperation("按题目ID批量判卷")
    @PostMapping("/batch/question")
    public ApiRest<Map<String, Object>> batchGradeByQuestion(
            @ApiParam(value = "题目ID", required = true) @RequestParam String questionId,
            @ApiParam(value = "评分配置ID，为空时使用默认配置") @RequestParam(required = false) String configId,
            @ApiParam(value = "是否仅处理未判卷的答案") @RequestParam(defaultValue = "true") boolean onlyUngraded) {

        try {
            log.info("接收按题目批量判卷请求，题目ID：{}，配置ID：{}，仅未判卷：{}", 
                    questionId, configId, onlyUngraded);

            if (!StringUtils.hasText(questionId)) {
                return super.failure("题目ID不能为空");
            }

            String taskId = gradingService.batchGradeByQuestion(questionId, configId, onlyUngraded);

            Map<String, Object> response = new HashMap<>();
            response.put("taskId", taskId);
            response.put("questionId", questionId);

            if (taskId != null) {
                return super.success("批量判卷任务创建成功", response);
            } else {
                return super.failure("创建批量判卷任务失败");
            }

        } catch (Exception e) {
            log.error("按题目批量判卷API异常，题目ID：{}", questionId, e);
            return super.failure("批量判卷异常：" + e.getMessage());
        }
    }

    @ApiOperation("按用户列表批量判卷")
    @PostMapping("/batch/users")
    public ApiRest<Map<String, Object>> batchGradeByUsers(
            @ApiParam(value = "用户ID列表（逗号分隔）", required = true) @RequestParam String userIds,
            @ApiParam(value = "考试ID，可选") @RequestParam(required = false) String examId,
            @ApiParam(value = "评分配置ID，为空时使用默认配置") @RequestParam(required = false) String configId,
            @ApiParam(value = "是否仅处理未判卷的答案") @RequestParam(defaultValue = "true") boolean onlyUngraded) {

        try {
            log.info("接收按用户批量判卷请求，用户ID：{}，考试ID：{}，配置ID：{}，仅未判卷：{}", 
                    userIds, examId, configId, onlyUngraded);

            if (!StringUtils.hasText(userIds)) {
                return super.failure("用户ID列表不能为空");
            }

            List<String> userIdList = Arrays.asList(userIds.split(","));
            String taskId = gradingService.batchGradeByUsers(userIdList, examId, configId, onlyUngraded);

            Map<String, Object> response = new HashMap<>();
            response.put("taskId", taskId);
            response.put("userCount", userIdList.size());
            response.put("examId", examId);

            if (taskId != null) {
                return super.success("批量判卷任务创建成功", response);
            } else {
                return super.failure("创建批量判卷任务失败");
            }

        } catch (Exception e) {
            log.error("按用户批量判卷API异常，用户ID：{}", userIds, e);
            return super.failure("批量判卷异常：" + e.getMessage());
        }
    }

    @ApiOperation("按答案ID列表批量判卷")
    @PostMapping("/batch/answers")
    public ApiRest<Map<String, Object>> batchGradeByAnswers(
            @ApiParam(value = "答案ID列表（逗号分隔）", required = true) @RequestParam String answerIds,
            @ApiParam(value = "评分配置ID，为空时使用默认配置") @RequestParam(required = false) String configId,
            @ApiParam(value = "是否强制重新判卷") @RequestParam(defaultValue = "false") boolean forceRegrade) {

        try {
            log.info("接收按答案列表批量判卷请求，答案ID：{}，配置ID：{}，强制重判：{}", 
                    answerIds, configId, forceRegrade);

            if (!StringUtils.hasText(answerIds)) {
                return super.failure("答案ID列表不能为空");
            }

            List<String> answerIdList = Arrays.asList(answerIds.split(","));
            String taskId = gradingService.batchGradeByAnswers(answerIdList, configId, forceRegrade);

            Map<String, Object> response = new HashMap<>();
            response.put("taskId", taskId);
            response.put("answerCount", answerIdList.size());
            response.put("forceRegrade", forceRegrade);

            if (taskId != null) {
                return super.success("批量判卷任务创建成功", response);
            } else {
                return super.failure("创建批量判卷任务失败");
            }

        } catch (Exception e) {
            log.error("按答案列表批量判卷API异常，答案ID：{}", answerIds, e);
            return super.failure("批量判卷异常：" + e.getMessage());
        }
    }

    @ApiOperation("按时间范围批量判卷")
    @PostMapping("/batch/timerange")
    public ApiRest<Map<String, Object>> batchGradeByTimeRange(
            @ApiParam(value = "开始时间（格式：yyyy-MM-ddTHH:mm:ss）", required = true) @RequestParam String startTime,
            @ApiParam(value = "结束时间（格式：yyyy-MM-ddTHH:mm:ss）", required = true) @RequestParam String endTime,
            @ApiParam(value = "考试ID，可选") @RequestParam(required = false) String examId,
            @ApiParam(value = "评分配置ID，为空时使用默认配置") @RequestParam(required = false) String configId,
            @ApiParam(value = "是否仅处理未判卷的答案") @RequestParam(defaultValue = "true") boolean onlyUngraded) {

        try {
            log.info("接收按时间范围批量判卷请求，时间：{} - {}，考试ID：{}，配置ID：{}，仅未判卷：{}", 
                    startTime, endTime, examId, configId, onlyUngraded);

            if (!StringUtils.hasText(startTime) || !StringUtils.hasText(endTime)) {
                return super.failure("开始时间和结束时间不能为空");
            }

            LocalDateTime start = LocalDateTime.parse(startTime);
            LocalDateTime end = LocalDateTime.parse(endTime);

            String taskId = gradingService.batchGradeByTimeRange(start, end, examId, configId, onlyUngraded);

            Map<String, Object> response = new HashMap<>();
            response.put("taskId", taskId);
            response.put("startTime", startTime);
            response.put("endTime", endTime);
            response.put("examId", examId);

            if (taskId != null) {
                return super.success("批量判卷任务创建成功", response);
            } else {
                return super.failure("创建批量判卷任务失败");
            }

        } catch (Exception e) {
            log.error("按时间范围批量判卷API异常，时间：{} - {}", startTime, endTime, e);
            return super.failure("批量判卷异常：" + e.getMessage());
        }
    }

    // ==================== 任务管理API ====================

    @ApiOperation("获取批量任务状态")
    @GetMapping("/task/status/{taskId}")
    public ApiRest<Map<String, Object>> getBatchTaskStatus(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {

        try {
            Map<String, Object> status = gradingService.getBatchTaskStatus(taskId);
            
            if (status != null && !status.isEmpty()) {
                return super.success("获取任务状态成功", status);
            } else {
                return super.failure("任务不存在或获取状态失败");
            }

        } catch (Exception e) {
            log.error("获取批量任务状态API异常，任务ID：{}", taskId, e);
            return super.failure("获取状态异常：" + e.getMessage());
        }
    }

    @ApiOperation("获取批量任务详细进度")
    @GetMapping("/task/progress/{taskId}")
    public ApiRest<Map<String, Object>> getBatchTaskProgress(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {

        try {
            Map<String, Object> progress = gradingService.getBatchTaskProgress(taskId);
            
            if (progress != null && !progress.isEmpty()) {
                return super.success("获取任务进度成功", progress);
            } else {
                return super.failure("任务不存在或获取进度失败");
            }

        } catch (Exception e) {
            log.error("获取批量任务进度API异常，任务ID：{}", taskId, e);
            return super.failure("获取进度异常：" + e.getMessage());
        }
    }

    @ApiOperation("停止批量任务")
    @PostMapping("/task/stop/{taskId}")
    public ApiRest<Map<String, Object>> stopBatchTask(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {

        try {
            boolean stopped = gradingService.stopBatchTask(taskId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("taskId", taskId);
            response.put("stopped", stopped);

            if (stopped) {
                return super.success("任务停止成功", response);
            } else {
                return super.failure("任务停止失败或任务不存在");
            }

        } catch (Exception e) {
            log.error("停止批量任务API异常，任务ID：{}", taskId, e);
            return super.failure("停止任务异常：" + e.getMessage());
        }
    }

    @ApiOperation("重启批量任务")
    @PostMapping("/task/restart/{taskId}")
    public ApiRest<Map<String, Object>> restartBatchTask(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {

        try {
            boolean restarted = gradingService.restartBatchTask(taskId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("taskId", taskId);
            response.put("restarted", restarted);

            if (restarted) {
                return super.success("任务重启成功", response);
            } else {
                return super.failure("任务重启失败或任务不存在");
            }

        } catch (Exception e) {
            log.error("重启批量任务API异常，任务ID：{}", taskId, e);
            return super.failure("重启任务异常：" + e.getMessage());
        }
    }

    @ApiOperation("获取正在运行的批量任务列表")
    @GetMapping("/task/running")
    public ApiRest<List<Map<String, Object>>> getRunningBatchTasks() {
        try {
            List<Map<String, Object>> runningTasks = gradingService.getRunningBatchTasks();
            return super.success("获取运行中任务列表成功", runningTasks);

        } catch (Exception e) {
            log.error("获取运行中任务列表API异常", e);
            return super.failure("获取任务列表异常：" + e.getMessage());
        }
    }

    @ApiOperation("获取用户的任务历史")
    @GetMapping("/task/history")
    public ApiRest<List<Map<String, Object>>> getUserTaskHistory(
            @ApiParam(value = "用户ID，为空时获取当前用户") @RequestParam(required = false) String userId,
            @ApiParam(value = "返回数量限制") @RequestParam(defaultValue = "10") int limit) {

        try {
            // 如果没有指定用户ID，获取当前用户
            if (!StringUtils.hasText(userId)) {
                userId = getCurrentUserId();
            }

            List<Map<String, Object>> taskHistory = gradingService.getUserTaskHistory(userId, limit);
            return super.success("获取用户任务历史成功", taskHistory);

        } catch (Exception e) {
            log.error("获取用户任务历史API异常，用户ID：{}", userId, e);
            return super.failure("获取任务历史异常：" + e.getMessage());
        }
    }

    // ==================== 结果查询API ====================

    @ApiOperation("获取判卷结果详情")
    @GetMapping("/result/{answerId}")
    public ApiRest<Map<String, Object>> getGradingResult(
            @ApiParam(value = "答案ID", required = true) @PathVariable String answerId) {

        try {
            Map<String, Object> result = gradingService.getGradingResult(answerId);
            
            if (result != null && !result.isEmpty()) {
                return super.success("获取判卷结果成功", result);
            } else {
                return super.failure("判卷结果不存在");
            }

        } catch (Exception e) {
            log.error("获取判卷结果API异常，答案ID：{}", answerId, e);
            return super.failure("获取结果异常：" + e.getMessage());
        }
    }

    @ApiOperation("获取用户的判卷结果列表")
    @GetMapping("/result/user")
    public ApiRest<List<Map<String, Object>>> getUserGradingResults(
            @ApiParam(value = "用户ID，为空时获取当前用户") @RequestParam(required = false) String userId,
            @ApiParam(value = "考试ID，可选") @RequestParam(required = false) String examId) {

        try {
            // 如果没有指定用户ID，获取当前用户
            if (!StringUtils.hasText(userId)) {
                userId = getCurrentUserId();
            }

            List<Map<String, Object>> results = gradingService.getUserGradingResults(userId, examId);
            return super.success("获取用户判卷结果成功", results);

        } catch (Exception e) {
            log.error("获取用户判卷结果API异常，用户ID：{}，考试ID：{}", userId, examId, e);
            return super.failure("获取结果异常：" + e.getMessage());
        }
    }

    @ApiOperation("获取考试的判卷结果统计")
    @GetMapping("/statistics/exam/{examId}")
    public ApiRest<Map<String, Object>> getExamGradingStatistics(
            @ApiParam(value = "考试ID", required = true) @PathVariable String examId) {

        try {
            Map<String, Object> statistics = gradingService.getExamGradingStatistics(examId);
            return super.success("获取考试判卷统计成功", statistics);

        } catch (Exception e) {
            log.error("获取考试判卷统计API异常，考试ID：{}", examId, e);
            return super.failure("获取统计异常：" + e.getMessage());
        }
    }

    @ApiOperation("获取题目的判卷结果统计")
    @GetMapping("/statistics/question/{questionId}")
    public ApiRest<Map<String, Object>> getQuestionGradingStatistics(
            @ApiParam(value = "题目ID", required = true) @PathVariable String questionId) {

        try {
            Map<String, Object> statistics = gradingService.getQuestionGradingStatistics(questionId);
            return super.success("获取题目判卷统计成功", statistics);

        } catch (Exception e) {
            log.error("获取题目判卷统计API异常，题目ID：{}", questionId, e);
            return super.failure("获取统计异常：" + e.getMessage());
        }
    }

    @ApiOperation("获取系统判卷总体统计")
    @GetMapping("/statistics/system")
    public ApiRest<Map<String, Object>> getSystemGradingStatistics() {
        try {
            Map<String, Object> statistics = gradingService.getSystemGradingStatistics();
            return super.success("获取系统判卷统计成功", statistics);

        } catch (Exception e) {
            log.error("获取系统判卷统计API异常", e);
            return super.failure("获取统计异常：" + e.getMessage());
        }
    }

    // ==================== 配置管理API ====================

    @ApiOperation("获取判卷配置摘要")
    @GetMapping("/config")
    public ApiRest<Map<String, Object>> getGradingConfigSummary() {
        try {
            Map<String, Object> config = gradingService.getGradingConfigSummary();
            return super.success("获取判卷配置成功", config);

        } catch (Exception e) {
            log.error("获取判卷配置API异常", e);
            return super.failure("获取配置异常：" + e.getMessage());
        }
    }

    @ApiOperation("更新判卷配置")
    @PostMapping("/config")
    public ApiRest<Map<String, Object>> updateGradingConfig(
            @ApiParam(value = "配置键", required = true) @RequestParam String configKey,
            @ApiParam(value = "配置值", required = true) @RequestParam String configValue) {

        try {
            boolean updated = gradingService.updateGradingConfig(configKey, configValue);
            
            Map<String, Object> response = new HashMap<>();
            response.put("configKey", configKey);
            response.put("configValue", configValue);
            response.put("updated", updated);

            if (updated) {
                return super.success("更新判卷配置成功", response);
            } else {
                return super.failure("更新判卷配置失败");
            }

        } catch (Exception e) {
            log.error("更新判卷配置API异常，配置键：{}，值：{}", configKey, configValue, e);
            return super.failure("更新配置异常：" + e.getMessage());
        }
    }

    // ==================== 系统监控API ====================

    @ApiOperation("获取判卷性能监控数据")
    @GetMapping("/metrics")
    public ApiRest<Map<String, Object>> getGradingPerformanceMetrics() {
        try {
            Map<String, Object> metrics = gradingService.getGradingPerformanceMetrics();
            
            // 添加异步服务监控数据
            metrics.put("runningAsyncTasks", asyncGradingService.getRunningAsyncTaskCount());
            metrics.put("threadPoolStatus", asyncGradingService.getThreadPoolStatus());

            return super.success("获取性能监控数据成功", metrics);

        } catch (Exception e) {
            log.error("获取性能监控数据API异常", e);
            return super.failure("获取监控数据异常：" + e.getMessage());
        }
    }

    @ApiOperation("检查判卷系统健康状态")
    @GetMapping("/health")
    public ApiRest<Map<String, Object>> checkGradingSystemHealth() {
        try {
            Map<String, Object> health = gradingService.checkGradingSystemHealth();
            return super.success("系统健康检查完成", health);

        } catch (Exception e) {
            log.error("系统健康检查API异常", e);
            return super.failure("健康检查异常：" + e.getMessage());
        }
    }

    // ==================== 异步处理API ====================

    @ApiOperation("异步执行单个答案判卷")
    @PostMapping("/async/single")
    public ApiRest<Map<String, Object>> asyncGradeSingleAnswer(
            @ApiParam(value = "答案ID", required = true) @RequestParam String answerId,
            @ApiParam(value = "评分配置ID，为空时使用默认配置") @RequestParam(required = false) String configId) {

        try {
            CompletableFuture<Double> future = asyncGradingService.executeSingleGradingAsync(answerId, configId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("answerId", answerId);
            response.put("async", true);
            response.put("submitted", true);

            return super.success("异步判卷任务已提交", response);

        } catch (Exception e) {
            log.error("异步单个判卷API异常，答案ID：{}", answerId, e);
            return super.failure("异步判卷异常：" + e.getMessage());
        }
    }

    @ApiOperation("查询未判卷的答案")
    @GetMapping("/ungraded")
    public ApiRest<List<Map<String, Object>>> getUngradedAnswers(
            @ApiParam(value = "考试ID，可选") @RequestParam(required = false) String examId,
            @ApiParam(value = "题目ID，可选") @RequestParam(required = false) String questionId,
            @ApiParam(value = "返回数量限制") @RequestParam(defaultValue = "20") int limit) {

        try {
            List<Map<String, Object>> ungradedAnswers = gradingService.getUngradedAnswers(examId, questionId, limit);
            return super.success("获取未判卷答案成功", ungradedAnswers);

        } catch (Exception e) {
            log.error("获取未判卷答案API异常，考试ID：{}，题目ID：{}", examId, questionId, e);
            return super.failure("获取未判卷答案异常：" + e.getMessage());
        }
    }

    @ApiOperation("查询判卷失败的答案")
    @GetMapping("/failed")
    public ApiRest<List<Map<String, Object>>> getFailedGradingAnswers(
            @ApiParam(value = "考试ID，可选") @RequestParam(required = false) String examId,
            @ApiParam(value = "返回数量限制") @RequestParam(defaultValue = "20") int limit) {

        try {
            List<Map<String, Object>> failedAnswers = gradingService.getFailedGradingAnswers(examId, limit);
            return super.success("获取判卷失败答案成功", failedAnswers);

        } catch (Exception e) {
            log.error("获取判卷失败答案API异常，考试ID：{}", examId, e);
            return super.failure("获取判卷失败答案异常：" + e.getMessage());
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 获取当前用户ID
     * 这里需要根据实际的用户认证方式实现
     */
    private String getCurrentUserId() {
        // TODO: 实现获取当前用户ID的逻辑
        // 可能从SecurityContext、Session或JWT Token中获取
        return "current_user_id";
    }
} 