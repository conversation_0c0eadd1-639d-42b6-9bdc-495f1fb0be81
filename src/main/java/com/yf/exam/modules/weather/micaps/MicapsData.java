package com.yf.exam.modules.weather.micaps;

/**
 * MICAPS数据基类
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */
public abstract class MicapsData {
    
    /** 数据类型 */
    private int dataType;
    
    /** 数据说明 */
    private String description;
    
    /** 年 */
    private int year;
    
    /** 月 */
    private int month;
    
    /** 日 */
    private int day;
    
    /** 时次 */
    private int hour;
    
    public int getDataType() {
        return dataType;
    }
    
    public void setDataType(int dataType) {
        this.dataType = dataType;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public int getYear() {
        return year;
    }
    
    public void setYear(int year) {
        this.year = year;
    }
    
    public int getMonth() {
        return month;
    }
    
    public void setMonth(int month) {
        this.month = month;
    }
    
    public int getDay() {
        return day;
    }
    
    public void setDay(int day) {
        this.day = day;
    }
    
    public int getHour() {
        return hour;
    }
    
    public void setHour(int hour) {
        this.hour = hour;
    }
    
    @Override
    public String toString() {
        return "MicapsData{" +
                "dataType=" + dataType +
                ", description='" + description + '\'' +
                ", year=" + year +
                ", month=" + month +
                ", day=" + day +
                ", hour=" + hour +
                '}';
    }
}
