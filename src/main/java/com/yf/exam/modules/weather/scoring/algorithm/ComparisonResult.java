package com.yf.exam.modules.weather.scoring.algorithm;

import lombok.Data;

/**
 * 天气要素比较结果
 * 
 * <AUTHOR>
 * @since 2024-12-21
 */
@Data
public class ComparisonResult {

    /**
     * 是否匹配（在容差范围内）
     */
    private boolean match;

    /**
     * 得分（0.0 - 1.0）
     */
    private double score;

    /**
     * 比较原因/说明
     */
    private String reason;

    /**
     * 差异值
     */
    private double difference;

    /**
     * 预测值
     */
    private Object predictedValue;

    /**
     * 实际值
     */
    private Object actualValue;

    /**
     * 构造函数
     */
    public ComparisonResult() {
        this.match = false;
        this.score = 0.0;
        this.reason = "";
        this.difference = 0.0;
    }

    /**
     * 构造函数
     * 
     * @param match 是否匹配
     * @param score 得分
     * @param reason 原因
     */
    public ComparisonResult(boolean match, double score, String reason) {
        this.match = match;
        this.score = score;
        this.reason = reason;
        this.difference = 0.0;
    }

    /**
     * 构造函数
     * 
     * @param match 是否匹配
     * @param score 得分
     * @param reason 原因
     * @param difference 差异值
     */
    public ComparisonResult(boolean match, double score, String reason, double difference) {
        this.match = match;
        this.score = score;
        this.reason = reason;
        this.difference = difference;
    }
} 