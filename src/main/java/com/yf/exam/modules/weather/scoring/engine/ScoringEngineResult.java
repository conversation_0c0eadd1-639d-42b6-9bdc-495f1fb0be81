package com.yf.exam.modules.weather.scoring.engine;

import com.yf.exam.modules.weather.scoring.algorithm.WeatherComparisonResult;
import lombok.Data;

/**
 * 评分引擎计算结果
 * 
 * <AUTHOR>
 * @since 2024-12-21
 */
@Data
public class ScoringEngineResult {

    /**
     * 是否计算成功
     */
    private boolean success;

    /**
     * 最终得分
     */
    private double score;

    /**
     * 评分结果记录ID
     */
    private String scoringResultId;

    /**
     * 详细比较结果
     */
    private WeatherComparisonResult comparisonResult;

    /**
     * 结果消息
     */
    private String message;

    /**
     * 异常信息（如果有）
     */
    private Exception exception;

    /**
     * 计算耗时（毫秒）
     */
    private long elapsedTime;

    /**
     * 构造函数
     */
    public ScoringEngineResult() {
        this.success = false;
        this.score = 0.0;
        this.elapsedTime = 0;
    }

    /**
     * 构造函数
     * 
     * @param success 是否成功
     * @param score 得分
     * @param message 消息
     */
    public ScoringEngineResult(boolean success, double score, String message) {
        this.success = success;
        this.score = score;
        this.message = message;
        this.elapsedTime = 0;
    }

    /**
     * 创建成功结果
     * 
     * @param score 得分
     * @param scoringResultId 评分结果ID
     * @param comparisonResult 比较结果
     * @return 成功结果
     */
    public static ScoringEngineResult success(double score, String scoringResultId, WeatherComparisonResult comparisonResult) {
        ScoringEngineResult result = new ScoringEngineResult();
        result.setSuccess(true);
        result.setScore(score);
        result.setScoringResultId(scoringResultId);
        result.setComparisonResult(comparisonResult);
        result.setMessage("评分计算成功");
        return result;
    }

    /**
     * 创建失败结果
     * 
     * @param message 错误消息
     * @return 失败结果
     */
    public static ScoringEngineResult failure(String message) {
        ScoringEngineResult result = new ScoringEngineResult();
        result.setSuccess(false);
        result.setMessage(message);
        return result;
    }

    /**
     * 创建失败结果
     * 
     * @param message 错误消息
     * @param exception 异常
     * @return 失败结果
     */
    public static ScoringEngineResult failure(String message, Exception exception) {
        ScoringEngineResult result = new ScoringEngineResult();
        result.setSuccess(false);
        result.setMessage(message);
        result.setException(exception);
        return result;
    }

    /**
     * 设置计算耗时
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    public void setElapsedTime(long startTime, long endTime) {
        this.elapsedTime = endTime - startTime;
    }

    /**
     * 检查是否有异常
     * 
     * @return 是否有异常
     */
    public boolean hasException() {
        return exception != null;
    }

    /**
     * 获取格式化的得分
     * 
     * @return 格式化的得分字符串
     */
    public String getFormattedScore() {
        if (!success) {
            return "N/A";
        }
        return String.format("%.2f", score);
    }

    /**
     * 获取简化的结果摘要
     * 
     * @return 结果摘要
     */
    public String getSummary() {
        if (success) {
            return String.format("评分成功：%.2f分", score);
        } else {
            return "评分失败：" + message;
        }
    }
} 