package com.yf.exam.modules.weather.scoring.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.exam.modules.weather.entity.WeatherScoringConfig;

import java.util.List;
import java.util.Map;

/**
 * 天气预报评分配置Service接口
 * 
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface ScoringConfigService extends IService<WeatherScoringConfig> {

    /**
     * 获取当前活跃的评分配置
     * 
     * @return 当前活跃的配置，如果没有则返回null
     */
    WeatherScoringConfig getActiveConfig();

    /**
     * 根据版本号获取评分配置
     * 
     * @param version 配置版本号
     * @return 指定版本的配置，如果不存在则返回null
     */
    WeatherScoringConfig getConfigByVersion(String version);

    /**
     * 保存评分配置
     * 
     * @param config 评分配置对象
     * @return 保存是否成功
     */
    boolean saveConfig(WeatherScoringConfig config);

    /**
     * 激活指定的评分配置
     * 
     * @param configId 要激活的配置ID
     * @return 激活是否成功
     */
    boolean activateConfig(String configId);

    /**
     * 获取配置历史记录
     * 
     * @param configName 配置名称，可为空
     * @return 配置历史记录列表
     */
    List<WeatherScoringConfig> getConfigHistory(String configName);

    /**
     * 创建配置的新版本
     * 
     * @param sourceConfigId 源配置ID
     * @param newVersion 新版本号
     * @param newConfigName 新配置名称（可选，为空则使用源配置名称）
     * @param createBy 创建人
     * @return 创建的新配置对象
     */
    WeatherScoringConfig createNewVersion(String sourceConfigId, String newVersion, String newConfigName, String createBy);

    /**
     * 验证配置的有效性
     * 
     * @param config 要验证的配置
     * @return 验证结果，包含是否有效和错误信息
     */
    Map<String, Object> validateConfig(WeatherScoringConfig config);

    /**
     * 获取配置使用统计
     * 
     * @param configId 配置ID
     * @return 使用统计信息
     */
    Map<String, Object> getConfigUsageStats(String configId);

    /**
     * 批量更新配置状态
     * 
     * @param configIds 配置ID列表
     * @param isActive 是否活跃
     * @return 更新成功的记录数
     */
    int batchUpdateStatus(List<String> configIds, Boolean isActive);

    /**
     * 获取所有可用的配置版本
     * 
     * @return 版本号列表
     */
    List<String> getAllVersions();

    /**
     * 检查配置是否存在
     * 
     * @param configName 配置名称
     * @param configVersion 配置版本
     * @return 是否存在
     */
    boolean configExists(String configName, String configVersion);

    /**
     * 获取配置差异对比
     * 
     * @param configId1 配置1的ID
     * @param configId2 配置2的ID
     * @return 差异对比结果
     */
    Map<String, Object> compareConfigs(String configId1, String configId2);

    /**
     * 清理无效配置
     * 
     * @param retentionDays 保留天数
     * @return 清理的记录数
     */
    int cleanupUnusedConfigs(int retentionDays);

    /**
     * 导出配置为JSON格式
     * 
     * @param configId 配置ID
     * @return JSON格式的配置字符串
     */
    String exportConfigAsJson(String configId);

    /**
     * 从JSON导入配置
     * 
     * @param configJson JSON格式的配置字符串
     * @param createBy 创建人
     * @return 导入的配置对象
     */
    WeatherScoringConfig importConfigFromJson(String configJson, String createBy);
} 