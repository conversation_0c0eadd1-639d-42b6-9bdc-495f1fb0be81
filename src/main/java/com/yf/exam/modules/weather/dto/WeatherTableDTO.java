package com.yf.exam.modules.weather.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 天气预报表格数据DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WeatherTableDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置ID
     */
    private String configId;

    /**
     * 表格标题
     */
    private String title;

    /**
     * 表格描述
     */
    private String description;

    /**
     * 表格列配置
     */
    private List<Map<String, Object>> columns;

    /**
     * 表格行数据
     */
    private List<Map<String, Object>> rows;

    /**
     * 验证规则
     */
    private Map<String, Object> validationRules;

    /**
     * 评分配置
     */
    private Map<String, Object> scoringConfig;

    /**
     * 模板类型
     */
    private String templateType;
}
