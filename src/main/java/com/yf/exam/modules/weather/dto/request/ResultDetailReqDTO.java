package com.yf.exam.modules.weather.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 结果查看题目详情请求DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@ApiModel(value = "结果查看题目详情请求", description = "结果查看题目详情请求")
public class ResultDetailReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "考试ID", required = true)
    private String examId;

    @ApiModelProperty(value = "题目ID", required = true)
    private String questionId;
}
