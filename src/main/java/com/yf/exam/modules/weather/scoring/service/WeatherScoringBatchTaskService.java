package com.yf.exam.modules.weather.scoring.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.exam.modules.weather.entity.WeatherScoringBatchTask;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 天气预报批量评分任务Service接口
 * 
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface WeatherScoringBatchTaskService extends IService<WeatherScoringBatchTask> {

    /**
     * 创建批量评分任务
     * 
     * @param batchName 任务名称
     * @param configId 配置ID
     * @param totalCount 总数量
     * @return 任务对象
     */
    WeatherScoringBatchTask createBatchTask(String batchName, String configId, int totalCount);

    /**
     * 更新任务进度
     * 
     * @param taskId 任务ID
     * @param completedCount 已完成数量
     * @param successCount 成功数量
     * @param failureCount 失败数量
     * @return 更新是否成功
     */
    boolean updateProgress(String taskId, int completedCount, int successCount, int failureCount);

    /**
     * 完成批量任务
     * 
     * @param taskId 任务ID
     * @param status 最终状态
     * @param endTime 结束时间
     * @return 更新是否成功
     */
    boolean finalize(String taskId, String status, LocalDateTime endTime);

    /**
     * 获取正在运行的任务列表
     * 
     * @return 任务列表
     */
    List<WeatherScoringBatchTask> getRunningTasks();

    /**
     * 获取用户的任务历史
     * 
     * @param userId 用户ID
     * @return 任务列表
     */
    List<WeatherScoringBatchTask> getUserTaskHistory(String userId);

    /**
     * 获取任务统计信息
     * 
     * @param taskId 任务ID
     * @return 统计信息
     */
    Map<String, Object> getTaskStatistics(String taskId);

    /**
     * 停止批量任务
     * 
     * @param taskId 任务ID
     * @param reason 停止原因
     * @return 停止是否成功
     */
    boolean stopTask(String taskId, String reason);

    /**
     * 重启失败的批量任务
     * 
     * @param taskId 任务ID
     * @return 重启是否成功
     */
    boolean restartTask(String taskId);

    /**
     * 删除过期的任务记录
     * 
     * @param retentionDays 保留天数
     * @return 删除的任务数量
     */
    int cleanupExpiredTasks(int retentionDays);

    /**
     * 获取任务的详细进度信息
     * 
     * @param taskId 任务ID
     * @return 进度信息
     */
    Map<String, Object> getTaskProgress(String taskId);

    /**
     * 批量更新任务状态
     * 
     * @param taskIds 任务ID列表
     * @param status 状态
     * @return 更新的任务数量
     */
    int batchUpdateStatus(List<String> taskIds, String status);

    /**
     * 检查任务是否可以停止
     * 
     * @param taskId 任务ID
     * @return 是否可以停止
     */
    boolean canStopTask(String taskId);

    /**
     * 获取任务执行日志
     * 
     * @param taskId 任务ID
     * @return 日志信息
     */
    List<String> getTaskLogs(String taskId);
} 