package com.yf.exam.modules.weather.scoring.controller;

import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.modules.weather.scoring.dto.PrecipitationScoringResult;
import com.yf.exam.modules.weather.scoring.service.PrecipitationAreaScoringService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.Arrays;

/**
 * 降水落区评分控制器
 *
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
@Slf4j
@RestController
@RequestMapping("/weather/scoring/precipitation")
@Api(tags = "降水落区评分")
public class PrecipitationScoringController extends BaseController {

    @Autowired
    private PrecipitationAreaScoringService precipitationAreaScoringService;

    /**
     * 计算降水落区评分
     */
    @PostMapping("/calculate")
    @ApiOperation("计算降水落区评分")
    public ApiRest<PrecipitationScoringResult> calculatePrecipitationScore(
            @ApiParam("实况降水文件路径") @RequestParam String actualFilePath,
            @ApiParam("CMA-MESO文件路径") @RequestParam String cmaMesoFilePath,
            @ApiParam("考生答案") @RequestBody Map<String, Object> studentAnswer) {
        
        try {
            log.info("开始计算降水落区评分，实况文件：{}，CMA文件：{}", actualFilePath, cmaMesoFilePath);
            
            PrecipitationScoringResult result = precipitationAreaScoringService.calculatePrecipitationScore(
                actualFilePath, cmaMesoFilePath, studentAnswer);
            
            if (result.isSuccess()) {
                log.info("降水落区评分计算成功，得分：{}", result.getFinalScore());
                return super.success("降水落区评分计算成功", result);
            } else {
                log.warn("降水落区评分计算失败：{}", result.getMessage());
                return super.failure(result.getMessage());
            }

        } catch (Exception e) {
            log.error("降水落区评分计算异常", e);
            return super.failure("降水落区评分计算异常：" + e.getMessage());
        }
    }

    /**
     * 测试降水落区评分（使用示例数据）
     */
    @GetMapping("/test")
    @ApiOperation("测试降水落区评分")
    public ApiRest<PrecipitationScoringResult> testPrecipitationScoring() {
        try {
            // 创建测试用的考生答案
            Map<String, Object> testAnswer = createTestStudentAnswer();
            
            // 使用示例文件进行测试
            String actualFilePath = "sample_actual.000";  // 实况文件
            String cmaMesoFilePath = "sample_cma_meso.004"; // CMA-MESO文件
            
            PrecipitationScoringResult result = precipitationAreaScoringService.calculatePrecipitationScore(
                actualFilePath, cmaMesoFilePath, testAnswer);
            
            return super.success("测试完成", result);

        } catch (Exception e) {
            log.error("测试降水落区评分异常", e);
            return super.failure("测试异常：" + e.getMessage());
        }
    }

    /**
     * 创建测试用的考生答案
     */
    private Map<String, Object> createTestStudentAnswer() {
        // 这里创建一个示例的降水落区答案
        // 实际使用时，这个数据来自前端用户绘制的降水落区
        Map<String, Object> answer = new HashMap<>();
        Map<String, Object> content = new HashMap<>();

        // 小雨区域
        Map<String, Object> smallRainArea = new HashMap<>();
        Map<String, Object> smallRainGeometry = new HashMap<>();
        smallRainGeometry.put("type", "Polygon");
        smallRainGeometry.put("coordinates", Arrays.asList(
            Arrays.asList(
                Arrays.asList(116.0, 39.5),
                Arrays.asList(117.0, 39.5),
                Arrays.asList(117.0, 40.5),
                Arrays.asList(116.0, 40.5),
                Arrays.asList(116.0, 39.5)
            )
        ));
        smallRainArea.put("geometry", smallRainGeometry);
        content.put("小雨", Arrays.asList(smallRainArea));

        // 中雨区域
        Map<String, Object> mediumRainArea = new HashMap<>();
        Map<String, Object> mediumRainGeometry = new HashMap<>();
        mediumRainGeometry.put("type", "Polygon");
        mediumRainGeometry.put("coordinates", Arrays.asList(
            Arrays.asList(
                Arrays.asList(116.5, 40.0),
                Arrays.asList(117.5, 40.0),
                Arrays.asList(117.5, 41.0),
                Arrays.asList(116.5, 41.0),
                Arrays.asList(116.5, 40.0)
            )
        ));
        mediumRainArea.put("geometry", mediumRainGeometry);
        content.put("中雨", Arrays.asList(mediumRainArea));

        answer.put("content", content);
        return answer;
    }
}
