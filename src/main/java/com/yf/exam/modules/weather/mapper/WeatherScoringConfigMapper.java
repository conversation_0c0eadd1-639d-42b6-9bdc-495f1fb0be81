package com.yf.exam.modules.weather.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yf.exam.modules.weather.entity.WeatherScoringConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 天气预报评分配置Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-12-21
 */
@Mapper
public interface WeatherScoringConfigMapper extends BaseMapper<WeatherScoringConfig> {

    /**
     * 获取当前活跃的配置
     * 
     * @return 活跃的评分配置
     */
    @Select("SELECT * FROM el_weather_scoring_config WHERE is_active = 1 ORDER BY update_time DESC LIMIT 1")
    WeatherScoringConfig selectActiveConfig();

    /**
     * 根据版本获取配置
     * 
     * @param configVersion 配置版本
     * @return 评分配置
     */
    @Select("SELECT * FROM el_weather_scoring_config WHERE config_version = #{configVersion} ORDER BY update_time DESC LIMIT 1")
    WeatherScoringConfig selectByVersion(@Param("configVersion") String configVersion);

    /**
     * 获取配置历史记录
     * 
     * @param configName 配置名称
     * @return 配置历史记录列表
     */
    @Select("SELECT * FROM el_weather_scoring_config WHERE config_name = #{configName} ORDER BY update_time DESC")
    List<WeatherScoringConfig> selectConfigHistory(@Param("configName") String configName);

    /**
     * 激活指定配置，同时将其他配置设为非活跃
     * 
     * @param configId 要激活的配置ID
     * @return 影响的行数
     */
    @Update("UPDATE el_weather_scoring_config SET is_active = CASE WHEN id = #{configId} THEN 1 ELSE 0 END")
    int activateConfig(@Param("configId") String configId);

    /**
     * 获取所有可用的配置版本
     * 
     * @return 配置版本列表
     */
    @Select("SELECT DISTINCT config_version FROM el_weather_scoring_config ORDER BY config_version DESC")
    List<String> selectAllVersions();

    /**
     * 根据配置名称和版本检查是否存在
     * 
     * @param configName 配置名称
     * @param configVersion 配置版本
     * @return 存在的记录数
     */
    @Select("SELECT COUNT(1) FROM el_weather_scoring_config WHERE config_name = #{configName} AND config_version = #{configVersion}")
    int checkConfigExists(@Param("configName") String configName, @Param("configVersion") String configVersion);

    /**
     * 批量更新配置状态
     * 
     * @param configIds 配置ID列表
     * @param isActive 是否活跃
     * @return 影响的行数
     */
    @Update("<script>" +
            "UPDATE el_weather_scoring_config SET is_active = #{isActive} WHERE id IN " +
            "<foreach collection='configIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int batchUpdateStatus(@Param("configIds") List<String> configIds, @Param("isActive") Boolean isActive);
} 