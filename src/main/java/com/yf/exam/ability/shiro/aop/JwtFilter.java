package com.yf.exam.ability.shiro.aop;

import com.alibaba.fastjson.JSON;
import com.yf.exam.ability.shiro.jwt.JwtToken;
import com.yf.exam.ability.shiro.jwt.JwtUtils;
import com.yf.exam.core.api.ApiRest;
import com.yf.exam.modules.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.web.filter.authc.BasicHttpAuthenticationFilter;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import com.yf.exam.modules.sys.user.entity.SysUser;
import com.yf.exam.modules.sys.user.service.SysUserService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.shiro.authc.AuthenticationException;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMethod;
import org.apache.shiro.web.util.WebUtils;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * 鉴权登录拦截器
 * <AUTHOR>
 */
@Slf4j
@Component
public class JwtFilter extends BasicHttpAuthenticationFilter {

	private static final String USER_TOKEN_PREFIX = "user:token:";
	
	private final RedisTemplate<String, Object> redisTemplate;
	
	private final SysUserService sysUserService;

	public JwtFilter(RedisTemplate<String, Object> redisTemplate, SysUserService sysUserService) {
		this.redisTemplate = redisTemplate;
		this.sysUserService = sysUserService;
	}

	@Override
	protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws Exception {
		HttpServletRequest httpRequest = WebUtils.toHttp(request);
		String requestURI = httpRequest.getRequestURI();
		
		// 如果是匿名访问路径，直接放行
		if (isAnonymousUrl(requestURI)) {
			return true;
		}
		
		return executeLogin(request, response);
	}

	@Override
	protected boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue) {
		HttpServletRequest httpRequest = WebUtils.toHttp(request);
		String requestURI = httpRequest.getRequestURI();
		
		log.debug("处理请求: {}", requestURI);
		
		// 如果是OPTIONS请求，直接放行
		if (httpRequest.getMethod().equals(RequestMethod.OPTIONS.name())) {
			log.debug("OPTIONS请求，直接放行");
			return true;
		}
		
		// 如果是匿名访问路径，直接放行
		if (isAnonymousUrl(requestURI)) {
			log.debug("匿名访问路径，直接放行: {}", requestURI);
			return true;
		}

		// 如果不是匿名访问，则验证token
		String token = httpRequest.getHeader(Constant.TOKEN);
		if (token == null || token.isEmpty()) {
			log.debug("非匿名访问，token为空: {}", requestURI);
			responseError(response, "Token不能为空");
			return false;
		}

		try {
			return executeLogin(request, response);
		} catch (Exception e) {
			log.error("登录验证失败: {}, 错误: {}", requestURI, e.getMessage());
			responseError(response, e.getMessage());
			return false;
		}
	}

	/**
	 * 检查是否是匿名访问的URL
	 */
	private boolean isAnonymousUrl(String requestURI) {
		// 匿名访问的路径列表
		String[] anonymousUrls = {
			"/exam/api/sys/user/login",
			"/error",
			"/exam/api/sys/user/appLogin",
			"/exam/api/sys/user/reg",
			"/exam/api/sys/user/quick-reg",
			"/exam/api/sys/user/token",
			"/exam/api/sys/config/detail",
			"/exam/api/sys/config/appVersion",
			"/error"
		};

		for (String url : anonymousUrls) {
			if (requestURI.equals(url)) {
				return true;
			}
		}

		// 静态资源
		if (requestURI.matches(".+\\.(js|css|html|svg|pdf|jpg|png|ico|ttf|woff|woff2)$") ||
			requestURI.startsWith("/upload/file/") ||
			requestURI.startsWith("/v2/") ||
			requestURI.equals("/") ||
			requestURI.equals("/doc.html") ||
			requestURI.startsWith("/swagger") ||
			requestURI.startsWith("/webjars/") ||
			requestURI.startsWith("/druid/")) {
			return true;
		}

		return false;
	}

	@Override
	protected boolean isLoginAttempt(ServletRequest request, ServletResponse response) {
		HttpServletRequest req = (HttpServletRequest) request;
		String token = req.getHeader(Constant.TOKEN);
		return token != null && !token.isEmpty();
	}

	@Override
	protected boolean executeLogin(ServletRequest request, ServletResponse response) throws Exception {
		HttpServletRequest httpServletRequest = (HttpServletRequest) request;
		String requestURI = httpServletRequest.getRequestURI();
		
		// 如果是匿名访问路径，直接返回true
		if (isAnonymousUrl(requestURI)) {
			return true;
		}
		
		String token = httpServletRequest.getHeader(Constant.TOKEN);
		JwtToken jwtToken = new JwtToken(token);
		
		// 获取用户信息
		String username = JwtUtils.getUsername(token);
		if (username == null) {
			throw new AuthenticationException("Token无效");
		}

		QueryWrapper<SysUser> wrapper = new QueryWrapper<>();
		wrapper.lambda().eq(SysUser::getUserName, username);
		SysUser user = sysUserService.getOne(wrapper, false);
		
		if (user == null) {
			log.info("用户不存在，用户名: {}", username);
			throw new AuthenticationException("用户不存在");
		}

		// 检查Redis中是否存在该token
		String key = USER_TOKEN_PREFIX + user.getId();
		String storedToken = (String) redisTemplate.opsForValue().get(key);
		
		// 如果token不存在或与存储的token不匹配，则认证失败
		if (storedToken == null || !storedToken.equals(token)) {
			log.info("用户token已失效，用户名: {}，用户id: {}", username, user.getId());
			return false;
		}
		
		// 提交给realm进行登入，如果错误他会抛出异常并被捕获
		getSubject(request, response).login(jwtToken);
		return true;
	}

	/**
	 * 对跨域提供支持
	 */
	@Override
	protected boolean preHandle(ServletRequest request, ServletResponse response) throws Exception {
		HttpServletRequest httpServletRequest = (HttpServletRequest) request;
		HttpServletResponse httpServletResponse = (HttpServletResponse) response;
		httpServletResponse.setHeader("Access-control-Allow-Origin", httpServletRequest.getHeader("Origin"));
		httpServletResponse.setHeader("Access-Control-Allow-Methods", "GET,POST,OPTIONS,PUT,DELETE");
		httpServletResponse.setHeader("Access-Control-Allow-Headers", httpServletRequest.getHeader("Access-Control-Request-Headers"));
		// 跨域时会首先发送一个OPTIONS请求，这里我们给OPTIONS请求直接返回正常状态
		if (httpServletRequest.getMethod().equals(RequestMethod.OPTIONS.name())) {
			httpServletResponse.setStatus(HttpStatus.OK.value());
			return false;
		}
		return super.preHandle(request, response);
	}

	/**
	 * 将非法请求跳转到 /unauthorized
	 */
	private void responseError(ServletResponse response, String message) {
		HttpServletResponse httpServletResponse = (HttpServletResponse) response;
		httpServletResponse.setStatus(HttpStatus.OK.value());
		httpServletResponse.setCharacterEncoding("UTF-8");
		httpServletResponse.setContentType("application/json; charset=utf-8");
		try (PrintWriter out = httpServletResponse.getWriter()) {
			ApiRest<String> rest = new ApiRest<>();
			rest.setCode(1);
			rest.setMsg(message);
			out.append(JSON.toJSONString(rest));
		} catch (IOException e) {
			log.error("返回错误信息失败", e);
		}
	}
}
