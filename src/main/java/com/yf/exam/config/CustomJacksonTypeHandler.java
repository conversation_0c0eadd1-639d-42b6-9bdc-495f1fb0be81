package com.yf.exam.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

/**
 * 自定义Jackson类型处理器
 * 用于处理Map<String, Object>类型的JSON字段
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@MappedTypes({Map.class})
public class CustomJacksonTypeHandler extends BaseTypeHandler<Map<String, Object>> {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Map<String, Object> parameter, JdbcType jdbcType) throws SQLException {
        try {
            String json = objectMapper.writeValueAsString(parameter);
            log.debug("=== CustomJacksonTypeHandler 保存数据 ===");
            log.debug("原始数据: {}", parameter);
            log.debug("序列化后的JSON: {}", json);
            ps.setString(i, json);
        } catch (JsonProcessingException e) {
            log.error("JSON序列化失败", e);
            throw new SQLException("JSON序列化失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return parseJson(json, columnName);
    }

    @Override
    public Map<String, Object> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return parseJson(json, "columnIndex:" + columnIndex);
    }

    @Override
    public Map<String, Object> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return parseJson(json, "callableStatement:" + columnIndex);
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> parseJson(String json, String source) {
        if (json == null || json.trim().isEmpty()) {
            log.debug("=== CustomJacksonTypeHandler 读取数据 ({}) ===", source);
            log.debug("JSON为空，返回null");
            return null;
        }

        try {
            Map<String, Object> result = objectMapper.readValue(json, Map.class);
            log.debug("=== CustomJacksonTypeHandler 读取数据 ({}) ===", source);
            log.debug("原始JSON: {}", json);
            log.debug("反序列化后的数据: {}", result);
            return result;
        } catch (JsonProcessingException e) {
            log.error("JSON反序列化失败，source: {}, json: {}", source, json, e);
            throw new RuntimeException("JSON反序列化失败: " + e.getMessage(), e);
        }
    }
}
