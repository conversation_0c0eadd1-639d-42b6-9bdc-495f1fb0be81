<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800">
  <defs>
    <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#87CEEB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4682B4;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="cloudGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#f0f0f0;stop-opacity:0.8" />
    </linearGradient>
    
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Sky background -->
  <rect width="1200" height="800" fill="url(#skyGradient)"/>
  
  <!-- Weather patterns -->
  <g opacity="0.1">
    <circle cx="100" cy="100" r="50" fill="#ffffff"/>
    <circle cx="300" cy="150" r="30" fill="#ffffff"/>
    <circle cx="500" cy="80" r="40" fill="#ffffff"/>
    <circle cx="700" cy="120" r="35" fill="#ffffff"/>
    <circle cx="900" cy="90" r="45" fill="#ffffff"/>
    <circle cx="1100" cy="140" r="25" fill="#ffffff"/>
  </g>
  
  <!-- Clouds -->
  <g opacity="0.7">
    <!-- Cloud 1 -->
    <ellipse cx="200" cy="200" rx="60" ry="30" fill="url(#cloudGradient)"/>
    <ellipse cx="180" cy="185" rx="40" ry="25" fill="url(#cloudGradient)"/>
    <ellipse cx="220" cy="185" rx="45" ry="28" fill="url(#cloudGradient)"/>
    
    <!-- Cloud 2 -->
    <ellipse cx="600" cy="250" rx="80" ry="40" fill="url(#cloudGradient)"/>
    <ellipse cx="570" cy="230" rx="50" ry="30" fill="url(#cloudGradient)"/>
    <ellipse cx="630" cy="230" rx="55" ry="35" fill="url(#cloudGradient)"/>
    
    <!-- Cloud 3 -->
    <ellipse cx="1000" cy="180" rx="70" ry="35" fill="url(#cloudGradient)"/>
    <ellipse cx="980" cy="165" rx="45" ry="25" fill="url(#cloudGradient)"/>
    <ellipse cx="1020" cy="165" rx="50" ry="30" fill="url(#cloudGradient)"/>
  </g>
  
  <!-- Weather symbols -->
  <g opacity="0.3" fill="#ffffff">
    <!-- Sun rays -->
    <g transform="translate(1000,600)">
      <circle r="30" fill="#FFD700" opacity="0.6"/>
      <g stroke="#FFD700" stroke-width="3" opacity="0.4">
        <line x1="0" y1="-50" x2="0" y2="-40"/>
        <line x1="35" y1="-35" x2="28" y2="-28"/>
        <line x1="50" y1="0" x2="40" y2="0"/>
        <line x1="35" y1="35" x2="28" y2="28"/>
        <line x1="0" y1="50" x2="0" y2="40"/>
        <line x1="-35" y1="35" x2="-28" y2="28"/>
        <line x1="-50" y1="0" x2="-40" y2="0"/>
        <line x1="-35" y1="-35" x2="-28" y2="-28"/>
      </g>
    </g>
    
    <!-- Lightning -->
    <g transform="translate(400,400)" opacity="0.2">
      <path d="M0,0 L10,20 L-5,20 L5,40 L-10,15 L5,15 Z" fill="#FFD700"/>
    </g>
    
    <!-- Rain drops -->
    <g opacity="0.2">
      <ellipse cx="150" cy="350" rx="2" ry="8" fill="#4682B4"/>
      <ellipse cx="170" cy="380" rx="2" ry="8" fill="#4682B4"/>
      <ellipse cx="190" cy="360" rx="2" ry="8" fill="#4682B4"/>
      <ellipse cx="210" cy="390" rx="2" ry="8" fill="#4682B4"/>
      <ellipse cx="230" cy="370" rx="2" ry="8" fill="#4682B4"/>
    </g>
  </g>
  
  <!-- Weather data visualization -->
  <g opacity="0.1" stroke="#ffffff" stroke-width="1" fill="none">
    <!-- Temperature curve -->
    <path d="M50,600 Q200,550 350,580 T650,560 T950,590 L1150,570"/>
    
    <!-- Pressure lines -->
    <line x1="0" y1="500" x2="1200" y2="520"/>
    <line x1="0" y1="520" x2="1200" y2="540"/>
    <line x1="0" y1="540" x2="1200" y2="560"/>
    
    <!-- Wind direction arrows -->
    <g transform="translate(800,300)">
      <path d="M0,0 L20,0 M15,-5 L20,0 L15,5"/>
    </g>
    <g transform="translate(900,320)">
      <path d="M0,0 L20,0 M15,-5 L20,0 L15,5"/>
    </g>
  </g>
</svg>
