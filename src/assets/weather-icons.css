/* 气象图标样式 */
.weather-icon {
  font-family: 'Element UI Icons', sans-serif;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  vertical-align: baseline;
  display: inline-block;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 自定义气象图标动画 */
.weather-icon-animated {
  animation: weatherPulse 2s ease-in-out infinite;
}

@keyframes weatherPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* 温度计图标 */
.weather-icon-thermometer::before {
  content: "🌡️";
  font-size: 1.2em;
}

/* 湿度图标 */
.weather-icon-humidity::before {
  content: "💧";
  font-size: 1.2em;
}

/* 气压图标 */
.weather-icon-pressure::before {
  content: "📊";
  font-size: 1.2em;
}

/* 风向图标 */
.weather-icon-wind::before {
  content: "🌪️";
  font-size: 1.2em;
}

/* 雨量图标 */
.weather-icon-rain::before {
  content: "🌧️";
  font-size: 1.2em;
}

/* 雪花图标 */
.weather-icon-snow::before {
  content: "❄️";
  font-size: 1.2em;
}

/* 雷电图标 */
.weather-icon-thunder::before {
  content: "⚡";
  font-size: 1.2em;
}

/* 彩虹图标 */
.weather-icon-rainbow::before {
  content: "🌈";
  font-size: 1.2em;
}

/* 气象站图标 */
.weather-icon-station::before {
  content: "🏢";
  font-size: 1.2em;
}

/* 卫星图标 */
.weather-icon-satellite::before {
  content: "🛰️";
  font-size: 1.2em;
}

/* 雷达图标 */
.weather-icon-radar::before {
  content: "📡";
  font-size: 1.2em;
}

/* 预警图标 */
.weather-icon-warning::before {
  content: "⚠️";
  font-size: 1.2em;
}

/* 图表图标 */
.weather-icon-chart::before {
  content: "📈";
  font-size: 1.2em;
}

/* 数据库图标 */
.weather-icon-database::before {
  content: "🗄️";
  font-size: 1.2em;
}

/* 分析图标 */
.weather-icon-analysis::before {
  content: "🔍";
  font-size: 1.2em;
}

/* 旋转动画 */
.weather-icon-rotating {
  animation: weatherRotate 3s linear infinite;
}

@keyframes weatherRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 浮动动画 */
.weather-icon-floating {
  animation: weatherFloat 3s ease-in-out infinite;
}

@keyframes weatherFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 闪烁动画 */
.weather-icon-blinking {
  animation: weatherBlink 1.5s ease-in-out infinite;
}

@keyframes weatherBlink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

/* 摇摆动画 */
.weather-icon-swaying {
  animation: weatherSway 2s ease-in-out infinite;
}

@keyframes weatherSway {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(5deg);
  }
  75% {
    transform: rotate(-5deg);
  }
}

/* 颜色主题 */
.weather-icon-blue {
  color: #3498db;
}

.weather-icon-green {
  color: #2ecc71;
}

.weather-icon-orange {
  color: #f39c12;
}

.weather-icon-red {
  color: #e74c3c;
}

.weather-icon-purple {
  color: #9b59b6;
}

.weather-icon-gray {
  color: #95a5a6;
}

/* 大小变体 */
.weather-icon-xs {
  font-size: 0.8em;
}

.weather-icon-sm {
  font-size: 1em;
}

.weather-icon-md {
  font-size: 1.5em;
}

.weather-icon-lg {
  font-size: 2em;
}

.weather-icon-xl {
  font-size: 3em;
}

/* 阴影效果 */
.weather-icon-shadow {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.weather-icon-glow {
  text-shadow: 0 0 10px currentColor;
}
