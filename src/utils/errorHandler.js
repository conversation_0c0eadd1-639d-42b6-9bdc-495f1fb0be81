import { Message, MessageBox } from 'element-ui'
import store from '@/store'
import router from '@/router'

/**
 * 强对流模块专用错误处理器
 */
export class ConvectionErrorHandler {
  /**
   * 处理API调用错误
   * @param {Error} error 错误对象
   * @param {String} context 错误上下文
   * @param {Object} options 处理选项
   */
  static handleApiError(error, context = '操作', options = {}) {
    const {
      showMessage = true,
      showDialog = false,
      logError = true,
      autoRetry = false,
      retryTimes = 0
    } = options

    // 记录错误日志
    if (logError) {
      console.error(`[ConvectionError] ${context}失败:`, error)
      this.reportError(error, context)
    }

    // 解析错误类型和消息
    const errorInfo = this.parseError(error)

    // 根据错误类型采取不同处理策略
    switch (errorInfo.type) {
      case 'network':
        this.handleNetworkError(errorInfo, context, options)
        break
      case 'auth':
        this.handleAuthError(errorInfo, context)
        break
      case 'business':
        this.handleBusinessError(errorInfo, context, showMessage, showDialog)
        break
      case 'validation':
        this.handleValidationError(errorInfo, context, showMessage)
        break
      default:
        this.handleUnknownError(errorInfo, context, showMessage)
    }

    // 自动重试逻辑
    if (autoRetry && retryTimes > 0 && errorInfo.type === 'network') {
      return this.scheduleRetry(error, context, { ...options, retryTimes: retryTimes - 1 })
    }

    return Promise.reject(errorInfo)
  }

  /**
   * 解析错误信息
   * @param {Error} error 错误对象
   * @returns {Object} 解析后的错误信息
   */
  static parseError(error) {
    let type = 'unknown'
    let message = '未知错误'
    let code = null
    let details = null

    if (error.response) {
      // HTTP响应错误
      const { status, data } = error.response
      code = status

      if (status === 401) {
        type = 'auth'
        message = '登录已过期，请重新登录'
      } else if (status === 403) {
        type = 'auth'
        message = '没有权限执行此操作'
      } else if (status === 404) {
        type = 'business'
        message = '请求的资源不存在'
      } else if (status >= 500) {
        type = 'network'
        message = '服务器内部错误，请稍后重试'
      } else if (data && data.msg) {
        type = data.code === 0 ? 'business' : 'validation'
        message = data.msg
        details = data.data
      }
    } else if (error.request) {
      // 网络请求错误
      type = 'network'
      message = '网络连接失败，请检查网络状态'
    } else if (error.message) {
      // 其他错误
      message = error.message

      // 判断是否为业务逻辑错误
      if (error.message.includes('验证') || error.message.includes('格式')) {
        type = 'validation'
      } else if (error.message.includes('权限') || error.message.includes('登录')) {
        type = 'auth'
      } else {
        type = 'business'
      }
    }

    return { type, message, code, details, original: error }
  }

  /**
   * 处理网络错误
   */
  static handleNetworkError(errorInfo, context, options) {
    const message = `${context}失败：${errorInfo.message}`

    if (options.showMessage) {
      Message.error({
        message,
        duration: 5000,
        showClose: true
      })
    }

    // 网络错误时更新UI状态
    store.commit('convection/SET_UI_LOADING', { type: 'networkError', value: true })

    // 3秒后清除错误状态
    setTimeout(() => {
      store.commit('convection/SET_UI_LOADING', { type: 'networkError', value: false })
    }, 3000)
  }

  /**
   * 处理权限认证错误
   */
  static handleAuthError(errorInfo, context) {
    MessageBox.alert(errorInfo.message, '权限提示', {
      confirmButtonText: '重新登录',
      type: 'warning',
      callback: () => {
        // 清除用户信息并跳转到登录页
        store.dispatch('user/logout')
        router.push('/login')
      }
    })
  }

  /**
   * 处理业务逻辑错误
   */
  static handleBusinessError(errorInfo, context, showMessage, showDialog) {
    const message = `${context}失败：${errorInfo.message}`

    if (showDialog) {
      MessageBox.alert(errorInfo.message, '操作提示', {
        confirmButtonText: '确定',
        type: 'warning'
      })
    } else if (showMessage) {
      Message.error({
        message,
        duration: 4000,
        showClose: true
      })
    }
  }

  /**
   * 处理数据验证错误
   */
  static handleValidationError(errorInfo, context, showMessage) {
    if (showMessage) {
      Message.warning({
        message: errorInfo.message,
        duration: 3000,
        showClose: true
      })
    }
  }

  /**
   * 处理未知错误
   */
  static handleUnknownError(errorInfo, context, showMessage) {
    const message = `${context}遇到未知错误，请联系管理员`

    if (showMessage) {
      Message.error({
        message,
        duration: 5000,
        showClose: true
      })
    }
  }

  /**
   * 安排重试
   */
  static scheduleRetry(error, context, options) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        Message.info(`正在重试${context}...`)
        // 这里需要由调用方实现重试逻辑
        reject(error)
      }, 2000)
    })
  }

  /**
   * 上报错误到监控系统
   */
  static reportError(error, context) {
    // 实际项目中可以集成第三方错误监控服务
    const errorReport = {
      timestamp: new Date().toISOString(),
      context,
      message: error.message,
      stack: error.stack,
      userAgent: window.navigator.userAgent,
      url: window.location.href,
      userId: store.getters.userId
    }

    console.log('[ErrorReport]', errorReport)

    // 示例：发送到监控服务
    // this.sendToMonitoringService(errorReport)
  }
}

/**
 * 强对流模块专用消息提示工具
 */
export class ConvectionMessageHelper {
  /**
   * 显示成功消息
   */
  static success(message, options = {}) {
    Message.success({
      message,
      duration: 3000,
      showClose: true,
      ...options
    })
  }

  /**
   * 显示警告消息
   */
  static warning(message, options = {}) {
    Message.warning({
      message,
      duration: 4000,
      showClose: true,
      ...options
    })
  }

  /**
   * 显示信息消息
   */
  static info(message, options = {}) {
    Message.info({
      message,
      duration: 3000,
      showClose: true,
      ...options
    })
  }

  /**
   * 显示加载提示
   */
  static loading(message = '加载中...') {
    return Message({
      message,
      type: 'info',
      duration: 0,
      showClose: false,
      customClass: 'convection-loading-message'
    })
  }

  /**
   * 显示操作确认对话框
   */
  static confirm(message, title = '确认操作', options = {}) {
    return MessageBox.confirm(message, title, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      ...options
    })
  }

  /**
   * 显示答题进度提示
   */
  static progressTip(progress, type = 'station') {
    const typeMap = {
      station: '站点预报',
      area: '落区绘制',
      overall: '整体'
    }

    const typeName = typeMap[type] || '答题'
    let message = ''
    let messageType = 'info'

    if (progress < 20) {
      message = `${typeName}进度较低（${progress}%），请继续完善答案`
      messageType = 'warning'
    } else if (progress < 50) {
      message = `${typeName}进度为${progress}%，继续加油！`
      messageType = 'info'
    } else if (progress < 80) {
      message = `${typeName}进度良好（${progress}%），即将完成`
      messageType = 'success'
    } else {
      message = `${typeName}进度优秀（${progress}%）！`
      messageType = 'success'
    }

    Message({
      message,
      type: messageType,
      duration: 2000,
      showClose: false
    })
  }

  /**
   * 显示自动保存状态
   */
  static autoSaveStatus(success = true, error = null) {
    if (success) {
      // 使用较淡的提示，避免干扰用户
      const notification = document.createElement('div')
      notification.className = 'auto-save-notification success'
      notification.innerHTML = '<i class="el-icon-check"></i> 已自动保存'
      document.body.appendChild(notification)

      setTimeout(() => {
        notification.remove()
      }, 1500)
    } else {
      Message.error({
        message: `自动保存失败：${error || '未知错误'}`,
        duration: 3000,
        showClose: true
      })
    }
  }

  /**
   * 显示考试提交确认
   */
  static submitConfirm(progress, wordCount) {
    let warningText = ''

    if (progress < 50) {
      warningText += `\n• 当前完成进度为${progress}%，建议完成至少50%后再提交`
    }

    if (wordCount < 100) {
      warningText += `\n• 预报依据仅有${wordCount}字，建议至少输入100字`
    }

    const message = `确定要提交考试吗？提交后将无法修改。${warningText}`

    return MessageBox.confirm(message, '确认提交考试', {
      confirmButtonText: '确定提交',
      cancelButtonText: '继续编辑',
      type: warningText ? 'warning' : 'info',
      dangerouslyUseHTMLString: false
    })
  }
}

// 为Vue实例添加全局方法
export function installConvectionErrorHandler(Vue) {
  Vue.prototype.$convectionError = ConvectionErrorHandler
  Vue.prototype.$convectionMessage = ConvectionMessageHelper
}

export default {
  ConvectionErrorHandler,
  ConvectionMessageHelper,
  install: installConvectionErrorHandler
}
