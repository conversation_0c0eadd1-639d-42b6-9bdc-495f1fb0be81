import store from '@/store'
import { getToken } from '@/utils/auth'

/**
 * Token辅助工具类
 */
export default {
  /**
   * 检查Token是否存在且有效
   */
  checkToken() {
    const token = getToken()
    if (!token) {
      return false
    }

    // 可以在这里添加Token过期检查逻辑
    // 目前简单检查是否存在
    return true
  },

  /**
   * 处理认证错误
   * @param {Error} error 错误对象
   * @param {Object} router Vue Router实例
   * @param {Function} message 消息提示函数
   */
  handleAuthError(error, router, message) {
    console.error('认证错误:', error)

    // 检查是否是401未授权错误
    if (error.response && error.response.status === 401) {
      message.error('登录已过期，请重新登录')
      this.redirectToLogin(router)
      return true
    }

    // 检查是否是Token相关错误
    if (error.message && (
      error.message.includes('Token') ||
      error.message.includes('token') ||
      error.message.includes('认证') ||
      error.message.includes('登录')
    )) {
      message.error('认证失败，请重新登录')
      this.redirectToLogin(router)
      return true
    }

    // 检查响应数据中的错误码
    if (error.response && error.response.data) {
      const { code, msg } = error.response.data
      if (code === 10010002 || msg.includes('Token')) {
        message.error('登录已过期，请重新登录')
        this.redirectToLogin(router)
        return true
      }
    }

    return false
  },

  /**
   * 重定向到登录页面
   * @param {Object} router Vue Router实例
   */
  redirectToLogin(router) {
    // 清除Token
    store.dispatch('user/resetToken')

    // 重定向到登录页面，并保存当前页面路径
    const currentPath = router.currentRoute.fullPath
    router.push(`/login?redirect=${encodeURIComponent(currentPath)}`)
  },

  /**
   * 在API调用前检查Token
   * @param {Object} router Vue Router实例
   * @param {Function} message 消息提示函数
   * @returns {boolean} 是否可以继续API调用
   */
  checkBeforeApiCall(router, message) {
    if (!this.checkToken()) {
      message.error('登录已过期，请重新登录')
      this.redirectToLogin(router)
      return false
    }
    return true
  }
}
