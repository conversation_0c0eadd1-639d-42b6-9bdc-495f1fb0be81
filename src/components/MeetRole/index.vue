<template>

  <el-select
    v-model="values"
    :remote-method="fetchList"
    style="width: 100%"
    multiple
    filterable
    remote
    reserve-keyword
    clearable
    automatic-dropdown
    placeholder="请选择角色"
    @change="handlerChange"
  >
    <el-option
      v-for="item in list"
      :key="item.id"
      :label="getRoleLabel(item.id)"
      :value="item.id"
    />
  </el-select>

</template>

<script>

import { fetchList } from '@/api/sys/role/role'

export default {
  name: 'Meet<PERSON><PERSON>',
  props: {
    value: Array,
    default: Array
  },
  data() {
    return {
      // 下拉选项值
      list: [],
      values: [],
      // 角色映射
      roleMap: {
        'sa': '管理员',
        'student': '学员',
        'teacher': '教练'
      }
    }
  },

  watch: {
    // 检测查询变化
    value: {
      handler() {
        this.values = this.value
      },
      deep: true
    }
  },
  created() {
    this.values = this.value
    this.fetchList()
  },
  methods: {
    // 获取角色显示名称
    getRoleLabel(roleId) {
      return this.roleMap[roleId] || roleId
    },

    fetchList() {
      fetchList().then(response => {
        this.list = response.data
      })
    },
    handlerChange(e) {
      this.$emit('change', e)
      this.$emit('input', e)
    }
  }
}
</script>
