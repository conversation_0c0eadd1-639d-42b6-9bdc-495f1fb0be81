<template>
  <div class="grading-panel">
    <!-- 面板头部 -->
    <div class="panel-header">
      <div class="header-left">
        <h4>预报依据评分</h4>
        <span class="score-info">(人工批卷部分 20分)</span>
      </div>
      <div class="header-right">
        <el-tag :type="getStatusType(gradingStatus)" size="small">
          {{ getStatusText(gradingStatus) }}
        </el-tag>
      </div>
    </div>

    <!-- 评分表单 -->
    <div class="grading-form">
      <el-form
        ref="gradingForm"
        :model="formData"
        :rules="formRules"
        label-width="140px"
        size="medium"
      >
        <!-- 分级依据阐述评分 -->
        <el-form-item label="分级依据阐述" prop="reasoningGradingBasisScore" class="score-item">
          <div class="score-input-container">
            <div class="score-input-group">
              <el-input-number
                v-model="formData.reasoningGradingBasisScore"
                :min="0"
                :max="10"
                :precision="1"
                :step="0.5"
                :disabled="readonly"
                size="large"
                class="score-input"
                @change="handleScoreChange"
              />
              <span class="score-suffix">/ 10分</span>
            </div>
            <div class="score-slider">
              <el-slider
                v-model="formData.reasoningGradingBasisScore"
                :min="0"
                :max="10"
                :step="0.5"
                :disabled="readonly"
                @change="handleScoreChange"
              />
            </div>
          </div>
          <div class="score-description">
            <div class="description-title">评分标准：</div>
            <ul class="description-list">
              <li><strong>9-10分</strong>：分级标准理解准确，阐述完整清晰，专业术语使用恰当</li>
              <li><strong>7-8分</strong>：分级标准基本正确，阐述较为完整，术语使用基本恰当</li>
              <li><strong>5-6分</strong>：分级标准大致正确，阐述不够完整，术语使用一般</li>
              <li><strong>3-4分</strong>：分级标准部分正确，阐述简单，术语使用不当</li>
              <li><strong>1-2分</strong>：分级标准理解错误，阐述不清，术语使用错误</li>
              <li><strong>0分</strong>：未作答或完全错误</li>
            </ul>
          </div>
        </el-form-item>

        <!-- 极端天气预报理由评分 -->
        <el-form-item label="极端天气预报理由" prop="reasoningExtremeScore" class="score-item">
          <div class="score-input-container">
            <div class="score-input-group">
              <el-input-number
                v-model="formData.reasoningExtremeScore"
                :min="0"
                :max="10"
                :precision="1"
                :step="0.5"
                :disabled="readonly"
                size="large"
                class="score-input"
                @change="handleScoreChange"
              />
              <span class="score-suffix">/ 10分</span>
            </div>
            <div class="score-slider">
              <el-slider
                v-model="formData.reasoningExtremeScore"
                :min="0"
                :max="10"
                :step="0.5"
                :disabled="readonly"
                @change="handleScoreChange"
              />
            </div>
          </div>
          <div class="score-description">
            <div class="description-title">评分标准：</div>
            <ul class="description-list">
              <li><strong>9-10分</strong>：预报依据科学合理，逻辑清晰，关键指标识别准确</li>
              <li><strong>7-8分</strong>：预报依据较为合理，逻辑基本清晰，指标识别基本准确</li>
              <li><strong>5-6分</strong>：预报依据基本合理，逻辑一般，指标识别部分准确</li>
              <li><strong>3-4分</strong>：预报依据部分合理，逻辑不够清晰，指标识别有误</li>
              <li><strong>1-2分</strong>：预报依据不够合理，逻辑混乱，指标识别错误</li>
              <li><strong>0分</strong>：未作答或完全错误</li>
            </ul>
          </div>
        </el-form-item>

        <!-- 总分显示 -->
        <el-form-item label="预报依据总分" class="total-score-item">
          <div class="total-score-display">
            <div class="score-number">
              <span class="score-value">{{ totalReasoningScore }}</span>
              <span class="score-max">/ 20分</span>
            </div>
            <div class="score-progress">
              <el-progress
                :percentage="getScorePercentage(totalReasoningScore, 20)"
                :stroke-width="8"
                :color="getScoreColor(totalReasoningScore, 20)"
                :show-text="false"
              />
            </div>
            <div class="score-level">
              <el-tag :type="getScoreLevelType(totalReasoningScore, 20)" size="small">
                {{ getScoreLevel(totalReasoningScore, 20) }}
              </el-tag>
            </div>
          </div>
        </el-form-item>

        <!-- 批卷评语 -->
        <el-form-item label="批卷评语" prop="gradingComments">
          <el-input
            v-model="formData.gradingComments"
            :rows="4"
            :disabled="readonly"
            type="textarea"
            placeholder="请输入批卷评语，指出答案的优点和不足..."
            maxlength="500"
            show-word-limit
            class="comment-textarea"
          />
          <div class="comment-tips">
            <div class="tip-item">
              <i class="el-icon-info" />
              <span>建议从预报依据的科学性、逻辑性、完整性等方面进行评价</span>
            </div>
          </div>
        </el-form-item>

        <!-- 改进建议 -->
        <el-form-item label="改进建议" prop="improvementSuggestions">
          <el-input
            v-model="formData.improvementSuggestions"
            :rows="3"
            :disabled="readonly"
            type="textarea"
            placeholder="请提供具体的改进建议..."
            maxlength="300"
            show-word-limit
            class="suggestion-textarea"
          />
          <div class="comment-tips">
            <div class="tip-item">
              <i class="el-icon-lightbulb" />
              <span>可以提供学习资料建议、预报方法改进等具体指导</span>
            </div>
          </div>
        </el-form-item>

        <!-- 批卷标记 -->
        <el-form-item v-if="showAdvanced" label="批卷标记">
          <div class="marking-options">
            <el-checkbox
              v-model="formData.needsReview"
              :disabled="readonly"
            >
              需要复审
            </el-checkbox>
            <el-checkbox
              v-model="formData.isExcellent"
              :disabled="readonly"
            >
              优秀答案
            </el-checkbox>
            <el-checkbox
              v-model="formData.hasTypicalError"
              :disabled="readonly"
            >
              典型错误
            </el-checkbox>
          </div>
        </el-form-item>

        <!-- 批卷时间信息 -->
        <el-form-item v-if="gradingStartTime || gradingEndTime" label="批卷信息">
          <div class="grading-info">
            <div v-if="gradingStartTime" class="info-item">
              <span class="info-label">开始时间：</span>
              <span class="info-value">{{ formatTime(gradingStartTime) }}</span>
            </div>
            <div v-if="gradingEndTime" class="info-item">
              <span class="info-label">完成时间：</span>
              <span class="info-value">{{ formatTime(gradingEndTime) }}</span>
            </div>
            <div v-if="gradingDuration" class="info-item">
              <span class="info-label">用时：</span>
              <span class="info-value">{{ gradingDuration }}分钟</span>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮 -->
    <div v-if="!readonly" class="panel-actions">
      <el-button
        :loading="savingDraft"
        size="large"
        icon="el-icon-document"
        @click="handleSaveDraft"
      >
        保存草稿
      </el-button>
      <el-button
        :loading="submitting"
        :disabled="!canSubmit"
        type="primary"
        size="large"
        icon="el-icon-check"
        @click="handleSubmitGrading"
      >
        提交批卷结果
      </el-button>
      <el-button
        v-if="showAdvanced"
        size="large"
        icon="el-icon-refresh-left"
        @click="handleReset"
      >
        重置评分
      </el-button>
    </div>

    <!-- 快速评分工具 -->
    <div v-if="showQuickGrading && !readonly" class="quick-grading">
      <div class="quick-title">
        <i class="el-icon-magic-stick" />
        <span>快速评分</span>
      </div>
      <div class="quick-buttons">
        <el-button
          size="small"
          type="success"
          @click="applyQuickScore('excellent')"
        >
          优秀 (18-20分)
        </el-button>
        <el-button
          size="small"
          type="primary"
          @click="applyQuickScore('good')"
        >
          良好 (14-17分)
        </el-button>
        <el-button
          size="small"
          type="warning"
          @click="applyQuickScore('average')"
        >
          一般 (10-13分)
        </el-button>
        <el-button
          size="small"
          type="danger"
          @click="applyQuickScore('poor')"
        >
          较差 (6-9分)
        </el-button>
        <el-button
          size="small"
          type="info"
          @click="applyQuickScore('fail')"
        >
          不及格 (0-5分)
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GradingPanel',
  props: {
    // 初始表单数据
    initialData: {
      type: Object,
      default: () => ({})
    },
    // 是否只读
    readonly: {
      type: Boolean,
      default: false
    },
    // 批卷状态
    gradingStatus: {
      type: Number,
      default: 0 // 0-未批卷，1-已批卷，2-已复审
    },
    // 是否显示高级选项
    showAdvanced: {
      type: Boolean,
      default: false
    },
    // 是否显示快速评分
    showQuickGrading: {
      type: Boolean,
      default: true
    },
    // 批卷时间信息
    gradingStartTime: {
      type: String,
      default: ''
    },
    gradingEndTime: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formData: {
        reasoningGradingBasisScore: 0,
        reasoningExtremeScore: 0,
        gradingComments: '',
        improvementSuggestions: '',
        needsReview: false,
        isExcellent: false,
        hasTypicalError: false
      },
      formRules: {
        reasoningGradingBasisScore: [
          { required: true, message: '请输入分级依据阐述得分', trigger: 'blur' },
          { type: 'number', min: 0, max: 10, message: '分数范围为0-10分', trigger: 'blur' }
        ],
        reasoningExtremeScore: [
          { required: true, message: '请输入极端天气预报理由得分', trigger: 'blur' },
          { type: 'number', min: 0, max: 10, message: '分数范围为0-10分', trigger: 'blur' }
        ],
        gradingComments: [
          { required: true, message: '请输入批卷评语', trigger: 'blur' },
          { min: 10, message: '评语至少10个字符', trigger: 'blur' }
        ]
      },
      savingDraft: false,
      submitting: false
    }
  },
  computed: {
    totalReasoningScore() {
      return (this.formData.reasoningGradingBasisScore || 0) + (this.formData.reasoningExtremeScore || 0)
    },

    canSubmit() {
      return this.totalReasoningScore > 0 && this.formData.gradingComments.trim().length >= 10
    },

    gradingDuration() {
      if (!this.gradingStartTime || !this.gradingEndTime) return null
      const start = new Date(this.gradingStartTime)
      const end = new Date(this.gradingEndTime)
      return Math.round((end - start) / (1000 * 60))
    }
  },
  watch: {
    initialData: {
      handler(newData) {
        if (newData && typeof newData === 'object') {
          this.formData = { ...this.formData, ...newData }
        }
      },
      immediate: true
    }
  },
  methods: {
    // 处理分数变化
    handleScoreChange() {
      this.$emit('score-change', {
        reasoningGradingBasisScore: this.formData.reasoningGradingBasisScore,
        reasoningExtremeScore: this.formData.reasoningExtremeScore,
        totalScore: this.totalReasoningScore
      })
    },

    // 保存草稿
    async handleSaveDraft() {
      this.savingDraft = true
      try {
        await this.$emit('save-draft', this.formData)
        this.$message.success('草稿已保存')
      } catch (error) {
        this.$message.error('保存草稿失败：' + error.message)
      } finally {
        this.savingDraft = false
      }
    },

    // 提交批卷结果
    async handleSubmitGrading() {
      // 表单验证
      const valid = await this.$refs.gradingForm.validate().catch(() => false)
      if (!valid) return

      this.$confirm('确定要提交批卷结果吗？提交后将无法修改。', '确认提交', {
        confirmButtonText: '确定提交',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        this.submitting = true
        try {
          await this.$emit('submit-grading', {
            ...this.formData,
            totalScore: this.totalReasoningScore,
            gradingEndTime: new Date().toISOString()
          })
          this.$message.success('批卷结果提交成功')
        } catch (error) {
          this.$message.error('提交失败：' + error.message)
        } finally {
          this.submitting = false
        }
      }).catch(() => {
        // 用户取消
      })
    },

    // 重置评分
    handleReset() {
      this.$confirm('确定要重置所有评分吗？', '确认重置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.formData = {
          reasoningGradingBasisScore: 0,
          reasoningExtremeScore: 0,
          gradingComments: '',
          improvementSuggestions: '',
          needsReview: false,
          isExcellent: false,
          hasTypicalError: false
        }
        this.handleScoreChange()
        this.$message.success('评分已重置')
      }).catch(() => {
        // 用户取消
      })
    },

    // 应用快速评分
    applyQuickScore(level) {
      const scoreMap = {
        excellent: { basis: 9.5, extreme: 9.5, comment: '答案优秀，预报依据科学合理，分级标准理解准确，逻辑清晰，表述专业。' },
        good: { basis: 8, extreme: 8, comment: '答案良好，预报依据较为合理，分级标准基本正确，逻辑基本清晰。' },
        average: { basis: 6.5, extreme: 6, comment: '答案一般，预报依据基本合理，分级标准大致正确，但阐述不够完整。' },
        poor: { basis: 4.5, extreme: 4, comment: '答案较差，预报依据部分合理，分级标准理解有误，阐述不够清晰。' },
        fail: { basis: 2.5, extreme: 2, comment: '答案不及格，预报依据不够合理，分级标准理解错误，需要重新学习相关知识。' }
      }

      const config = scoreMap[level]
      if (config) {
        this.formData.reasoningGradingBasisScore = config.basis
        this.formData.reasoningExtremeScore = config.extreme
        this.formData.gradingComments = config.comment
        this.handleScoreChange()
        this.$message.success(`已应用${level}等级评分模板`)
      }
    },

    // 获取状态类型
    getStatusType(status) {
      const typeMap = { 0: 'info', 1: 'success', 2: 'warning' }
      return typeMap[status] || 'info'
    },

    // 获取状态文字
    getStatusText(status) {
      const textMap = { 0: '待批卷', 1: '已批卷', 2: '已复审' }
      return textMap[status] || '未知'
    },

    // 获取分数百分比
    getScorePercentage(score, total) {
      return total > 0 ? Math.round(score / total * 100) : 0
    },

    // 获取分数颜色
    getScoreColor(score, total) {
      const percentage = this.getScorePercentage(score, total)
      if (percentage >= 80) return '#67c23a'
      if (percentage >= 60) return '#e6a23c'
      return '#f56c6c'
    },

    // 获取分数等级
    getScoreLevel(score, total) {
      const percentage = this.getScorePercentage(score, total)
      if (percentage >= 90) return '优秀'
      if (percentage >= 80) return '良好'
      if (percentage >= 70) return '中等'
      if (percentage >= 60) return '及格'
      return '不及格'
    },

    // 获取分数等级类型
    getScoreLevelType(score, total) {
      const percentage = this.getScorePercentage(score, total)
      if (percentage >= 90) return 'success'
      if (percentage >= 80) return 'primary'
      if (percentage >= 70) return 'warning'
      if (percentage >= 60) return 'info'
      return 'danger'
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return '--'
      return new Date(time).toLocaleString()
    }
  }
}
</script>

<style scoped>
.grading-panel {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.panel-header h4 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.score-info {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 14px;
}

.grading-form {
  padding: 25px;
}

.score-item {
  margin-bottom: 30px;
}

.score-input-container {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 15px;
}

.score-input-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.score-input {
  width: 120px;
}

.score-suffix {
  color: #606266;
  font-size: 14px;
  font-weight: 600;
}

.score-slider {
  flex: 1;
  max-width: 200px;
}

.score-description {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #e9ecef;
}

.description-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 10px;
}

.description-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.description-list li {
  padding: 4px 0;
  font-size: 13px;
  color: #6c757d;
  line-height: 1.4;
}

.description-list strong {
  color: #495057;
}

.total-score-item {
  margin-bottom: 25px;
}

.total-score-display {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 15px 20px;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-radius: 10px;
  color: #ffffff;
}

.score-number {
  display: flex;
  align-items: baseline;
  gap: 5px;
}

.score-value {
  font-size: 24px;
  font-weight: 700;
}

.score-max {
  font-size: 16px;
  opacity: 0.8;
}

.score-progress {
  flex: 1;
  max-width: 150px;
}

.score-level {
  min-width: 60px;
}

.comment-textarea,
.suggestion-textarea {
  margin-bottom: 10px;
}

.comment-tips {
  margin-top: 8px;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #8c8c8c;
  font-size: 12px;
}

.marking-options {
  display: flex;
  gap: 20px;
}

.grading-info {
  background: #f6f8fa;
  border-radius: 6px;
  padding: 12px 15px;
  display: flex;
  gap: 20px;
}

.info-item {
  display: flex;
  gap: 8px;
  font-size: 13px;
}

.info-label {
  color: #8c8c8c;
}

.info-value {
  color: #303133;
  font-weight: 600;
}

.panel-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  padding: 20px 25px;
  background: #fafafa;
  border-top: 1px solid #e8e8e8;
}

.quick-grading {
  padding: 20px 25px;
  background: #f0f9ff;
  border-top: 1px solid #bae7ff;
}

.quick-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
}

.quick-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .score-input-container {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .total-score-display {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .grading-info {
    flex-direction: column;
    gap: 10px;
  }

  .panel-actions {
    flex-direction: column;
  }

  .quick-buttons {
    flex-direction: column;
  }
}
</style>
