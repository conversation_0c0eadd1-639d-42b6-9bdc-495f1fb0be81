<template>
  <div class="forecast-reasoning-textarea">
    <!-- 组件标题 -->
    <div class="textarea-header">
      <div class="title-section">
        <h4>预报依据阐述</h4>
        <div class="subtitle">请详细阐述您的预报依据，包括分级判断标准和极端天气预报理由</div>
      </div>
      
      <div class="word-count-section">
        <div class="word-count" :class="getWordCountClass()">
          <span class="current-count">{{ wordCount }}</span>
          <span class="separator">/</span>
          <span class="max-count">{{ maxWords }}</span>
          <span class="unit">字</span>
        </div>
        <div class="count-status">
          <el-tag 
            :type="getWordCountTagType()" 
            size="mini"
            v-if="showCountStatus"
          >
            {{ getWordCountStatus() }}
          </el-tag>
        </div>
      </div>
    </div>
    
    <!-- 文本输入区域 -->
    <div class="textarea-content">
      <el-input
        v-model="reasoningText"
        type="textarea"
        :rows="textareaRows"
        :maxlength="maxWords"
        :placeholder="placeholder"
        :readonly="readonly"
        :disabled="disabled"
        show-word-limit
        resize="vertical"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
        @change="handleChange"
        class="reasoning-input"
      />
      
      <!-- 快速插入模板 -->
      <div class="template-section" v-if="!readonly && showTemplates">
        <div class="template-title">
          <i class="el-icon-document"></i>
          <span>快速插入模板</span>
          <el-button 
            type="text" 
            size="mini" 
            @click="toggleTemplates"
            class="toggle-btn"
          >
            {{ templateExpanded ? '收起' : '展开' }}
          </el-button>
        </div>
        
        <div class="template-content" v-show="templateExpanded">
          <div class="template-buttons">
            <el-button
              v-for="template in reasoningTemplates"
              :key="template.key"
              type="primary"
              plain
              size="mini"
              @click="insertTemplate(template)"
              class="template-btn"
            >
              {{ template.name }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部信息区域 -->
    <div class="textarea-footer">
      <!-- 阐述要点提示 -->
      <div class="tips-section">
        <div class="tips-header">
          <i class="el-icon-info"></i>
          <span class="tip-title">阐述要点提示</span>
        </div>
        <div class="tips-content">
          <div class="tip-categories">
            <div class="tip-category">
              <div class="category-title">分级依据（10分）</div>
              <ul class="tip-list">
                <li>短时强降水分级标准理解和应用</li>
                <li>雷暴大风分级标准掌握程度</li>
                <li>冰雹识别要点和判断依据</li>
              </ul>
            </div>
            <div class="tip-category">
              <div class="category-title">极端天气理由（10分）</div>
              <ul class="tip-list">
                <li>极端天气形成机制分析</li>
                <li>预报关键指标识别</li>
                <li>预警发布依据说明</li>
              </ul>
            </div>
            <div class="tip-category">
              <div class="category-title">气象分析</div>
              <ul class="tip-list">
                <li>基于MICAPS资料的专业分析</li>
                <li>预报思路和判断过程</li>
                <li>关键气象要素变化趋势</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 自动保存状态 -->
      <div class="auto-save-section">
        <div class="save-status" v-if="autoSave">
          <transition name="fade">
            <span v-if="autoSaveStatus === 'saving'" class="saving">
              <i class="el-icon-loading"></i>
              <span>保存中...</span>
            </span>
            <span v-else-if="autoSaveStatus === 'saved'" class="saved">
              <i class="el-icon-check"></i>
              <span>已自动保存</span>
            </span>
            <span v-else-if="autoSaveStatus === 'error'" class="error">
              <i class="el-icon-warning"></i>
              <span>保存失败</span>
            </span>
          </transition>
        </div>
        
        <div class="last-save-time" v-if="lastSaveTime">
          <span>最后保存：{{ formatSaveTime(lastSaveTime) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ForecastReasoningTextarea',
  
  props: {
    // 文本内容
    value: {
      type: String,
      default: ''
    },
    // 最大字数
    maxWords: {
      type: Number,
      default: 1500
    },
    // 最小建议字数
    minWords: {
      type: Number,
      default: 300
    },
    // 只读模式
    readonly: {
      type: Boolean,
      default: false
    },
    // 禁用状态
    disabled: {
      type: Boolean,
      default: false
    },
    // 自动保存
    autoSave: {
      type: Boolean,
      default: true
    },
    // 占位符文本
    placeholder: {
      type: String,
      default: '请详细阐述您的预报依据，包括分级判断标准、极端天气预报理由、气象资料分析等...\n\n建议从以下方面进行阐述：\n1. 各类强对流天气的分级判断依据\n2. 极端天气预报的科学理由和关键指标\n3. 基于MICAPS资料的专业分析\n4. 预报思路和判断过程'
    },
    // 文本框行数
    textareaRows: {
      type: Number,
      default: 12
    },
    // 显示模板
    showTemplates: {
      type: Boolean,
      default: true
    }
  },
  
  data() {
    return {
      reasoningText: this.value,
      autoSaveTimer: null,
      autoSaveStatus: null, // null, 'saving', 'saved', 'error'
      lastSaveTime: null,
      focused: false,
      templateExpanded: false,
      
      // 预报依据模板
      reasoningTemplates: [
        {
          key: 'rainfall_classification',
          name: '短时强降水分级',
          content: `短时强降水分级依据：
根据1小时降水量进行分级：
- 20≤R1＜40mm/h：轻度短时强降水
- 40≤R1＜80mm/h：中度短时强降水  
- 80≤R1mm/h以上：重度短时强降水

判断依据：结合雷达回波强度、降水率、水汽条件等要素进行综合分析。`
        },
        {
          key: 'wind_classification',
          name: '雷暴大风分级',
          content: `雷暴大风分级依据：
根据最大风速等级进行分级：
- 8级≤Wg＜10级：中等雷暴大风
- 10级≤Wg＜12级：强雷暴大风
- 12级≤Wg或龙卷：极强雷暴大风

判断依据：分析风切变强度、对流云发展高度、环境风场条件等。`
        },
        {
          key: 'hail_analysis',
          name: '冰雹预报分析',
          content: `冰雹预报分析：
2cm以上大冰雹形成条件：
- 强烈的垂直风切变
- 充足的不稳定能量（CAPE值）
- 适宜的0℃层高度
- 超级单体风暴结构

关键指标：雷达回波顶高、VIL值、中气旋特征等。`
        },
        {
          key: 'extreme_weather',
          name: '极端天气机制',
          content: `极端天气形成机制：
1. 热力条件：强烈的不稳定层结，高CAPE值
2. 动力条件：低层辐合、高层辐散，垂直风切变
3. 水汽条件：充沛的水汽输送和聚集
4. 触发机制：地形抬升、冷空气入侵、辐合线等

预报关键：综合分析各层大气要素的配置和演变趋势。`
        }
      ]
    }
  },
  
  computed: {
    // 当前字数
    wordCount() {
      return this.reasoningText ? this.reasoningText.length : 0
    },
    
    // 是否显示字数状态
    showCountStatus() {
      return this.wordCount > 0
    },
    
    // 文本质量评估
    textQuality() {
      return {
        wordCount: this.wordCount,
        isAdequate: this.wordCount >= this.minWords,
        isExcessive: this.wordCount > this.maxWords,
        completeness: Math.min(this.wordCount / this.minWords, 1),
        progress: Math.min((this.wordCount / this.minWords) * 100, 100)
      }
    }
  },
  
  watch: {
    value(newVal) {
      if (newVal !== this.reasoningText) {
        this.reasoningText = newVal
      }
    },
    
    wordCount(newCount) {
      this.$emit('word-count-change', newCount)
    }
  },
  
  beforeDestroy() {
    if (this.autoSaveTimer) {
      clearTimeout(this.autoSaveTimer)
    }
  },
  
  methods: {
    // 处理输入事件
    handleInput(value) {
      this.reasoningText = value
      this.$emit('input', value)
      
      // 自动保存
      if (this.autoSave && !this.readonly) {
        this.scheduleAutoSave()
      }
    },
    
    // 处理失焦事件
    handleBlur() {
      this.focused = false
      this.$emit('blur', this.reasoningText)
      
      // 失焦时立即保存
      if (this.autoSave && !this.readonly) {
        this.performAutoSave()
      }
    },
    
    // 处理聚焦事件
    handleFocus() {
      this.focused = true
      this.$emit('focus', this.reasoningText)
    },
    
    // 处理变更事件
    handleChange(value) {
      this.$emit('change', value)
    },
    
    // 安排自动保存
    scheduleAutoSave() {
      if (this.autoSaveTimer) {
        clearTimeout(this.autoSaveTimer)
      }
      
      this.autoSaveTimer = setTimeout(() => {
        this.performAutoSave()
      }, 2000) // 2秒后自动保存
    },
    
    // 执行自动保存
    async performAutoSave() {
      if (this.autoSaveStatus === 'saving') return
      
      this.autoSaveStatus = 'saving'
      
      try {
        await this.$emit('auto-save', this.reasoningText)
        this.autoSaveStatus = 'saved'
        this.lastSaveTime = new Date()
        
        // 3秒后清除保存状态
        setTimeout(() => {
          this.autoSaveStatus = null
        }, 3000)
      } catch (error) {
        this.autoSaveStatus = 'error'
        console.error('自动保存失败:', error)
        
        // 5秒后清除错误状态
        setTimeout(() => {
          this.autoSaveStatus = null
        }, 5000)
      }
    },
    
    // 插入模板
    insertTemplate(template) {
      const cursorPosition = this.$el.querySelector('.reasoning-input textarea').selectionStart || 0
      const currentText = this.reasoningText || ''
      const beforeCursor = currentText.substring(0, cursorPosition)
      const afterCursor = currentText.substring(cursorPosition)
      
      // 在光标位置插入模板内容
      const newText = beforeCursor + '\n\n' + template.content + '\n\n' + afterCursor
      
      this.reasoningText = newText
      this.$emit('input', newText)
      
      this.$message.success(`已插入"${template.name}"模板`)
      
      // 触发自动保存
      if (this.autoSave) {
        this.scheduleAutoSave()
      }
    },
    
    // 切换模板显示
    toggleTemplates() {
      this.templateExpanded = !this.templateExpanded
    },
    
    // 获取字数样式类
    getWordCountClass() {
      if (this.wordCount < this.minWords) {
        return 'warning'
      } else if (this.wordCount > this.maxWords) {
        return 'error'
      } else {
        return 'normal'
      }
    },
    
    // 获取字数标签类型
    getWordCountTagType() {
      if (this.wordCount < this.minWords) {
        return 'warning'
      } else if (this.wordCount > this.maxWords) {
        return 'danger'
      } else {
        return 'success'
      }
    },
    
    // 获取字数状态文本
    getWordCountStatus() {
      if (this.wordCount < this.minWords) {
        return `建议至少${this.minWords}字`
      } else if (this.wordCount > this.maxWords) {
        return '超出字数限制'
      } else {
        return '字数适中'
      }
    },
    
    // 格式化保存时间
    formatSaveTime(time) {
      const now = new Date()
      const diff = now - time
      
      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return `${Math.floor(diff / 60000)}分钟前`
      } else {
        return time.toLocaleTimeString('zh-CN', { 
          hour: '2-digit', 
          minute: '2-digit' 
        })
      }
    },
    
    // 获取文本质量评估
    getTextQuality() {
      return this.textQuality
    },
    
    // 清空内容
    clearContent() {
      this.$confirm('确定要清空所有内容吗？', '确认清空', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.reasoningText = ''
        this.$emit('input', '')
        this.$message.success('已清空内容')
      }).catch(() => {
        // 用户取消
      })
    },
    
    // 复制内容到剪贴板
    async copyToClipboard() {
      try {
        await navigator.clipboard.writeText(this.reasoningText)
        this.$message.success('已复制到剪贴板')
      } catch (error) {
        this.$message.error('复制失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.forecast-reasoning-textarea {
  .textarea-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    
    .title-section {
      flex: 1;
      
      h4 {
        margin: 0 0 5px 0;
        color: #303133;
        font-size: 16px;
        font-weight: bold;
      }
      
      .subtitle {
        color: #909399;
        font-size: 13px;
        line-height: 1.4;
      }
    }
    
    .word-count-section {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 5px;
      
      .word-count {
        font-size: 16px;
        font-weight: bold;
        
        &.normal {
          color: #303133;
        }
        
        &.warning {
          color: #E6A23C;
        }
        
        &.error {
          color: #F56C6C;
        }
        
        .current-count {
          font-size: 18px;
        }
        
        .separator {
          margin: 0 4px;
          color: #C0C4CC;
        }
        
        .max-count {
          color: #909399;
        }
        
        .unit {
          margin-left: 4px;
          color: #909399;
          font-size: 14px;
        }
      }
      
      .count-status {
        height: 20px; // 固定高度避免布局跳动
      }
    }
  }
  
  .textarea-content {
    position: relative;
    margin-bottom: 20px;
    
    ::v-deep .reasoning-input {
      .el-textarea__inner {
        font-family: 'Microsoft YaHei', Arial, sans-serif;
        font-size: 14px;
        line-height: 1.6;
        border-radius: 8px;
        border: 2px solid #E4E7ED;
        transition: border-color 0.3s ease;
        
        &:focus {
          border-color: #409EFF;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
        }
        
        &:hover:not(:focus) {
          border-color: #C0C4CC;
        }
      }
      
      .el-input__count {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 4px;
        padding: 2px 6px;
        font-size: 12px;
      }
    }
    
    .template-section {
      margin-top: 15px;
      border: 1px solid #E4E7ED;
      border-radius: 8px;
      background: #FAFBFC;
      
      .template-title {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        border-bottom: 1px solid #E4E7ED;
        background: #F5F7FA;
        border-radius: 8px 8px 0 0;
        
        i {
          color: #409EFF;
          margin-right: 8px;
        }
        
        span {
          flex: 1;
          color: #303133;
          font-weight: bold;
          font-size: 14px;
        }
        
        .toggle-btn {
          padding: 0;
          font-size: 12px;
        }
      }
      
      .template-content {
        padding: 15px;
        
        .template-buttons {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          
          .template-btn {
            flex: 0 0 auto;
            font-size: 12px;
            padding: 6px 12px;
            border-radius: 4px;
            
            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
            }
          }
        }
      }
    }
  }
  
  .textarea-footer {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 30px;
    
    .tips-section {
      flex: 1;
      
      .tips-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        
        i {
          color: #409EFF;
          margin-right: 8px;
          font-size: 16px;
        }
        
        .tip-title {
          font-size: 15px;
          font-weight: bold;
          color: #303133;
        }
      }
      
      .tips-content {
        .tip-categories {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          gap: 20px;
          
          .tip-category {
            .category-title {
              font-size: 14px;
              font-weight: bold;
              color: #409EFF;
              margin-bottom: 8px;
              padding-bottom: 4px;
              border-bottom: 2px solid #E1F0FF;
            }
            
            .tip-list {
              margin: 0;
              padding-left: 16px;
              
              li {
                font-size: 13px;
                color: #606266;
                line-height: 1.6;
                margin-bottom: 6px;
                
                &:last-child {
                  margin-bottom: 0;
                }
                
                &::marker {
                  color: #C0C4CC;
                }
              }
            }
          }
        }
      }
    }
    
    .auto-save-section {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 8px;
      min-width: 120px;
      
      .save-status {
        height: 20px; // 固定高度避免布局跳动
        
        span {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          
          &.saving {
            color: #409EFF;
            
            i {
              animation: rotate 1s linear infinite;
            }
          }
          
          &.saved {
            color: #67C23A;
          }
          
          &.error {
            color: #F56C6C;
          }
        }
      }
      
      .last-save-time {
        span {
          font-size: 11px;
          color: #C0C4CC;
        }
      }
    }
  }
}

// 动画效果
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .forecast-reasoning-textarea {
    .textarea-footer {
      flex-direction: column;
      gap: 20px;
      
      .tips-section .tips-content .tip-categories {
        grid-template-columns: 1fr;
      }
      
      .auto-save-section {
        align-items: flex-start;
      }
    }
  }
}

@media (max-width: 768px) {
  .forecast-reasoning-textarea {
    .textarea-header {
      flex-direction: column;
      gap: 10px;
      align-items: flex-start;
      
      .word-count-section {
        align-items: flex-start;
      }
    }
    
    .textarea-content .template-section .template-content .template-buttons {
      flex-direction: column;
      
      .template-btn {
        flex: 1;
      }
    }
  }
}
</style> 