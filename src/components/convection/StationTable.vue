<template>
  <div class="convection-station-table">
    <!-- 表格头部 -->
    <div class="table-header">
      <div class="header-left">
        <h4>第一部分：站点预报表格</h4>
        <span class="score-info">(68分)</span>
      </div>
      <div class="header-right">
        <div class="progress-container">
          <span class="progress-label">完成进度：</span>
          <el-progress
            :percentage="stationProgress"
            :stroke-width="8"
            :color="progressColor"
            class="progress-bar"
          />
          <span class="progress-text">{{ stationProgress }}%</span>
        </div>
      </div>
    </div>

    <!-- 说明文字 -->
    <div class="instruction-text">
      <p>请根据MICAPS资料，对各站点的强对流天气进行预报。每个站点可能出现多种天气现象，请仔细分析后选择：</p>
      <ul>
        <li><strong>短时强降水</strong>：level1(20≤R1＜40mm/h)、level2(40≤R1＜80mm/h)、level3(80≤R1mm/h以上)</li>
        <li><strong>雷暴大风</strong>：moderate(8-10级)、severe(10-12级)、extreme(12级以上或龙卷)</li>
        <li><strong>冰雹</strong>：large(2cm以上大冰雹)</li>
      </ul>
    </div>

    <!-- 站点预报表格 -->
    <el-table
      :data="tableData"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontWeight: 'bold' }"
      border
      stripe
      size="medium"
      class="station-prediction-table"
    >
      <!-- 天气要素列 -->
      <el-table-column
        prop="element"
        label="天气要素"
        width="180"
        fixed="left"
        align="center"
      >
        <template slot-scope="scope">
          <div class="element-cell">
            <i :class="scope.row.icon" class="element-icon" />
            <strong>{{ scope.row.elementLabel }}</strong>
          </div>
        </template>
      </el-table-column>

      <!-- 动态站点列 -->
      <el-table-column
        v-for="station in stations"
        :key="station.code"
        :prop="station.code"
        :label="station.name"
        width="200"
        align="center"
      >
        <template slot="header" slot-scope="scope">
          <div class="station-header">
            <div class="station-name">{{ station.name }}</div>
            <div class="station-code">{{ station.code }}</div>
            <div class="station-progress">
              <el-progress
                :percentage="getStationProgress(station.code)"
                :stroke-width="4"
                :show-text="false"
                :color="getStationProgressColor(station.code)"
              />
            </div>
          </div>
        </template>

        <template slot-scope="scope">
          <div class="station-cell">
            <!-- 短时强降水选择 -->
            <div v-if="scope.row.element === 'rainfall'" class="weather-options">
              <div
                v-for="option in rainfallOptions"
                :key="option.value"
                :class="getOptionClass(station.code, 'rainfall', option.value)"
                class="weather-option"
                @click="selectWeatherOption(station.code, 'rainfall', option.value)"
              >
                <div class="option-indicator">
                  <i :class="getOptionIcon(station.code, 'rainfall', option.value)" />
                </div>
                <div class="option-content">
                  <div class="option-label">{{ option.label }}</div>
                  <div class="option-desc">{{ option.description }}</div>
                </div>
              </div>
            </div>

            <!-- 雷暴大风选择 -->
            <div v-else-if="scope.row.element === 'wind'" class="weather-options">
              <div
                v-for="option in windOptions"
                :key="option.value"
                :class="getOptionClass(station.code, 'wind', option.value)"
                class="weather-option"
                @click="selectWeatherOption(station.code, 'wind', option.value)"
              >
                <div class="option-indicator">
                  <i :class="getOptionIcon(station.code, 'wind', option.value)" />
                </div>
                <div class="option-content">
                  <div class="option-label">{{ option.label }}</div>
                  <div class="option-desc">{{ option.description }}</div>
                </div>
              </div>
            </div>

            <!-- 冰雹选择 -->
            <div v-else-if="scope.row.element === 'hail'" class="weather-options">
              <div
                :class="getOptionClass(station.code, 'hail', 'large')"
                class="weather-option"
                @click="selectWeatherOption(station.code, 'hail', 'large')"
              >
                <div class="option-indicator">
                  <i :class="getOptionIcon(station.code, 'hail', 'large')" />
                </div>
                <div class="option-content">
                  <div class="option-label">2cm以上大冰雹</div>
                  <div class="option-desc">直径≥20mm</div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 预报依据阐述区域 -->
    <div class="reasoning-section">
      <reasoning-input
        v-model="forecastReasoning"
        :max-words="1500"
        :min-words="100"
        :readonly="readonly"
        placeholder="请详细阐述预报依据，包括：&#10;1. 各类强对流天气的分级判断依据（如短时强降水的时次降水量标准）&#10;2. 极端天气预报的理由和关键指标（如大冰雹的识别要点）&#10;3. 基于MICAPS气象资料的分析结论和预报逻辑"
        @input="handleReasoningChange"
        @word-count-change="handleWordCountChange"
      />
    </div>

    <!-- 自动保存提示 -->
    <div v-if="autoSaving" class="auto-save-indicator">
      <i class="el-icon-loading" />
      <span>正在自动保存...</span>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from 'vuex'
import ReasoningInput from './ReasoningInput'

export default {
  name: 'ConvectionStationTable',
  components: {
    ReasoningInput
  },
  props: {
    stations: {
      type: Array,
      default: () => []
    },
    examId: {
      type: String,
      required: true
    },
    questionId: {
      type: String,
      required: true
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      autoSaveTimer: null,

      // 表格行数据
      tableData: [
        {
          element: 'rainfall',
          elementLabel: '短时强降水',
          icon: 'el-icon-heavy-rain'
        },
        {
          element: 'wind',
          elementLabel: '雷暴大风',
          icon: 'el-icon-wind-power'
        },
        {
          element: 'hail',
          elementLabel: '冰雹',
          icon: 'el-icon-ice-cream-round'
        }
      ],

      // 选项配置
      rainfallOptions: [
        {
          value: 'level1',
          label: 'Level 1',
          description: '20≤R1＜40mm/h'
        },
        {
          value: 'level2',
          label: 'Level 2',
          description: '40≤R1＜80mm/h'
        },
        {
          value: 'level3',
          label: 'Level 3',
          description: '80≤R1mm/h以上'
        }
      ],
      windOptions: [
        {
          value: 'moderate',
          label: 'Moderate',
          description: '8级≤Wg＜10级'
        },
        {
          value: 'severe',
          label: 'Severe',
          description: '10级≤Wg＜12级'
        },
        {
          value: 'extreme',
          label: 'Extreme',
          description: '12级≤Wg或龙卷'
        }
      ]
    }
  },
  computed: {
    ...mapState('convection', {
      examAnswer: state => state.examAnswer,
      autoSaving: state => state.ui.autoSaving
    }),

    stationAnswers() {
      return this.examAnswer.stationAnswer || {}
    },

    forecastReasoning: {
      get() {
        return this.examAnswer.forecastReasoning || ''
      },
      set(value) {
        this.UPDATE_FORECAST_REASONING(value)
      }
    },

    stationProgress() {
      return this.examAnswer.stationProgress || 0
    },

    progressColor() {
      const progress = this.stationProgress
      if (progress < 30) return '#f56c6c'
      if (progress < 70) return '#e6a23c'
      return '#67c23a'
    }
  },
  methods: {
    ...mapMutations('convection', [
      'UPDATE_STATION_ANSWER',
      'UPDATE_FORECAST_REASONING',
      'UPDATE_PROGRESS'
    ]),

    ...mapActions('convection', [
      'autoSave',
      'calculateProgress'
    ]),

    getOptionClass(stationCode, weatherType, optionValue) {
      const isSelected = this.isSelected(stationCode, weatherType, optionValue)
      return {
        'selected': isSelected,
        'unselected': !isSelected,
        'readonly': this.readonly
      }
    },

    getOptionIcon(stationCode, weatherType, optionValue) {
      const isSelected = this.isSelected(stationCode, weatherType, optionValue)
      return isSelected ? 'el-icon-check' : 'el-icon-close'
    },

    isSelected(stationCode, weatherType, optionValue) {
      return this.stationAnswers[stationCode] &&
             this.stationAnswers[stationCode][weatherType] === optionValue
    },

    selectWeatherOption(stationCode, weatherType, optionValue) {
      if (this.readonly) return

      const currentValue = this.stationAnswers[stationCode] &&
                          this.stationAnswers[stationCode][weatherType]

      // 如果已选中相同选项，则取消选择；否则选择新选项
      const newValue = currentValue === optionValue ? null : optionValue

      this.UPDATE_STATION_ANSWER({
        stationCode,
        weatherType,
        value: newValue
      })

      this.calculateStationProgress()
      this.scheduleAutoSave()
    },

    handleReasoningChange(reasoning) {
      this.UPDATE_FORECAST_REASONING(reasoning)
      this.calculateStationProgress()
      this.scheduleAutoSave()
    },

    handleWordCountChange(count) {
      this.$emit('word-count-change', count)
    },

    calculateStationProgress() {
      this.calculateProgress({
        type: 'station',
        stations: this.stations
      })
    },

    getStationProgress(stationCode) {
      const stationAnswer = this.stationAnswers[stationCode]
      if (!stationAnswer) return 0

      let completed = 0
      if (stationAnswer.rainfall) completed++
      if (stationAnswer.wind) completed++
      if (stationAnswer.hail) completed++

      return Math.round(completed / 3 * 100)
    },

    getStationProgressColor(stationCode) {
      const progress = this.getStationProgress(stationCode)
      if (progress < 50) return '#f56c6c'
      if (progress < 100) return '#e6a23c'
      return '#67c23a'
    },

    scheduleAutoSave() {
      if (this.autoSaveTimer) {
        clearTimeout(this.autoSaveTimer)
      }

      this.autoSaveTimer = setTimeout(() => {
        this.autoSave()
      }, 2000) // 2秒后自动保存
    }
  },

  mounted() {
    this.calculateStationProgress()
  },

  destroyed() {
    if (this.autoSaveTimer) {
      clearTimeout(this.autoSaveTimer)
    }
  }
}
</script>

<style scoped>
.convection-station-table {
  padding: 20px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f0f2f5;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-left h4 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.score-info {
  color: #667eea;
  font-size: 16px;
  font-weight: 600;
  background: rgba(102, 126, 234, 0.1);
  padding: 4px 12px;
  border-radius: 12px;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-label {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

.progress-bar {
  width: 120px;
}

.progress-text {
  color: #303133;
  font-size: 14px;
  font-weight: 600;
  min-width: 35px;
}

.instruction-text {
  background: #f0f9ff;
  border: 1px solid #bae7ff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.instruction-text p {
  margin: 0 0 10px 0;
  color: #1890ff;
  font-weight: 500;
}

.instruction-text ul {
  margin: 0;
  padding-left: 20px;
  color: #595959;
}

.instruction-text li {
  margin-bottom: 5px;
  line-height: 1.6;
}

.station-prediction-table {
  margin-bottom: 25px;
  border-radius: 8px;
  overflow: hidden;
}

.element-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #303133;
}

.element-icon {
  font-size: 18px;
  color: #409eff;
}

.station-header {
  text-align: center;
}

.station-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.station-code {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.station-progress {
  padding: 0 10px;
}

.station-cell {
  padding: 10px 5px;
}

.weather-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.weather-option {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid #e0e6ed;
  background: #ffffff;
  min-height: 45px;
}

.weather-option:hover:not(.readonly) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.weather-option.selected {
  background: linear-gradient(135deg, #f0f9ff, #e6f7ff);
  border-color: #1890ff;
  color: #1890ff;
}

.weather-option.unselected {
  background: #ffffff;
  border-color: #e0e6ed;
  color: #606266;
}

.weather-option.readonly {
  cursor: not-allowed;
  opacity: 0.6;
}

.option-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: currentColor;
  color: #ffffff;
  font-size: 12px;
}

.weather-option.selected .option-indicator {
  background: #52c41a;
}

.weather-option.unselected .option-indicator {
  background: #ff4d4f;
}

.option-content {
  flex: 1;
}

.option-label {
  font-weight: 600;
  font-size: 13px;
  margin-bottom: 2px;
}

.option-desc {
  font-size: 11px;
  color: #8c8c8c;
  line-height: 1.2;
}

.reasoning-section {
  background: #fafbfc;
  border: 2px dashed #d9d9d9;
  border-radius: 12px;
  padding: 20px;
}

.auto-save-indicator {
  position: fixed;
  top: 80px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  z-index: 9999;
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
