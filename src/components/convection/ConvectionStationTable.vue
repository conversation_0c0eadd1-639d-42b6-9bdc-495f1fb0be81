<template>
  <div class="convection-station-table">
    <!-- 表格标题 -->
    <div class="table-title">
      <h3>站点预报表格</h3>
      <div class="progress-info">
        <span>完成进度：{{ progress }}%</span>
        <el-progress :percentage="progress" :show-text="false" size="small" />
      </div>
    </div>

    <!-- 表格容器 -->
    <div class="table-container">
      <!-- 表格头部 -->
      <div class="table-header">
        <div class="corner-cell">
          <div class="cell-content">
            <span class="main-text">天气要素</span>
            <span class="sub-text">站点</span>
          </div>
        </div>
        <div 
          v-for="station in stations" 
          :key="station.id"
          class="station-header"
        >
          <div class="station-info">
            <div class="station-name">{{ station.name }}</div>
            <div class="station-code">{{ station.code }}</div>
          </div>
        </div>
      </div>
      
      <!-- 表格主体 -->
      <div class="table-body">
        <div 
          v-for="weather in weatherTypes" 
          :key="weather.type"
          class="weather-row"
        >
          <!-- 天气要素标签 -->
          <div class="weather-label">
            <div class="weather-name">{{ weather.label }}</div>
            <div class="weather-desc">{{ weather.description }}</div>
          </div>
          
          <!-- 站点选项单元格 -->
          <div 
            v-for="station in stations"
            :key="`${weather.type}_${station.id}`"
            class="option-cell"
          >
            <div class="option-group">
              <div 
                v-for="option in weather.options"
                :key="option.value"
                class="option-item"
                :class="{ 
                  'selected': isSelected(station.id, weather.type, option.value),
                  'disabled': readonly || isDisabled(station.id, weather.type, option.value)
                }"
                @click="handleOptionClick(station.id, weather.type, option.value)"
                :title="option.label"
              >
                <div class="option-indicator">
                  <i 
                    :class="getIndicatorIcon(station.id, weather.type, option.value)"
                    :style="{ color: getIndicatorColor(station.id, weather.type, option.value) }"
                  ></i>
                </div>
                <div class="option-text">
                  <div class="option-label">{{ option.shortLabel || option.label }}</div>
                  <div class="option-score">{{ option.score }}分</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 选择统计 -->
    <div class="selection-summary">
      <div class="summary-item">
        <span class="label">已选择:</span>
        <span class="value">{{ selectedCount }} / {{ totalCount }}</span>
      </div>
      <div class="summary-item">
        <span class="label">预计得分:</span>
        <span class="value score">{{ estimatedScore }}分</span>
      </div>
    </div>

    <!-- 操作提示 -->
    <div class="operation-tips" v-if="!readonly">
      <el-alert
        title="操作提示"
        type="info"
        :closable="false"
        show-icon
      >
        <div slot="description">
          <ul>
            <li>每个站点的每类天气现象最多只能选择一个选项</li>
            <li>点击已选中的选项可以取消选择</li>
            <li>绿色圆圈(○)表示已选中，红色叉号(×)表示未选中</li>
            <li>建议根据气象资料分析结果进行选择</li>
          </ul>
        </div>
      </el-alert>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ConvectionStationTable',
  
  props: {
    // 站点列表
    stations: {
      type: Array,
      required: true,
      default: () => []
    },
    // 答案数据
    value: {
      type: Object,
      default: () => ({})
    },
    // 只读模式
    readonly: {
      type: Boolean,
      default: false
    },
    // 显示分数
    showScore: {
      type: Boolean,
      default: true
    }
  },
  
  data() {
    return {
      // 天气类型配置
      weatherTypes: [
        {
          type: 'shortTimeRainfall',
          label: '短时强降水',
          description: '1小时降水量',
          options: [
            { 
              value: 'level1', 
              label: '20≤R1＜40mm/h', 
              shortLabel: '20-40mm/h',
              score: 3 
            },
            { 
              value: 'level2', 
              label: '40≤R1＜80mm/h', 
              shortLabel: '40-80mm/h',
              score: 3 
            },
            { 
              value: 'level3', 
              label: '80≤R1mm/h以上', 
              shortLabel: '≥80mm/h',
              score: 3 
            }
          ]
        },
        {
          type: 'thunderstormWind',
          label: '雷暴大风',
          description: '最大风速等级',
          options: [
            { 
              value: 'moderate', 
              label: '8级≤Wg＜10级或6级≤W2＜8级', 
              shortLabel: '8-10级',
              score: 3 
            },
            { 
              value: 'severe', 
              label: '10级≤Wg＜12级或8级≤W2＜10级', 
              shortLabel: '10-12级',
              score: 3 
            },
            { 
              value: 'extreme', 
              label: '12级≤Wg或龙卷或10级≤W2', 
              shortLabel: '≥12级/龙卷',
              score: 3 
            }
          ]
        },
        {
          type: 'hail',
          label: '冰雹',
          description: '冰雹直径',
          options: [
            { 
              value: 'large', 
              label: '2cm以上大冰雹', 
              shortLabel: '≥2cm',
              score: 3 
            }
          ]
        }
      ],
      
      // 内部状态
      progress: 0
    }
  },
  
  computed: {
    // 总选项数
    totalCount() {
      return this.stations.length * this.weatherTypes.length
    },
    
    // 已选择数量
    selectedCount() {
      let count = 0
      this.stations.forEach(station => {
        this.weatherTypes.forEach(weather => {
          if (this.value[station.id] && this.value[station.id][weather.type]) {
            count++
          }
        })
      })
      return count
    },
    
    // 预计得分
    estimatedScore() {
      let score = 0
      this.stations.forEach(station => {
        this.weatherTypes.forEach(weather => {
          const selectedValue = this.value[station.id] && this.value[station.id][weather.type]
          if (selectedValue) {
            const option = weather.options.find(opt => opt.value === selectedValue)
            if (option) {
              score += option.score
            }
          }
        })
      })
      return score
    }
  },
  
  watch: {
    selectedCount: {
      handler(newCount) {
        this.updateProgress()
      },
      immediate: true
    }
  },
  
  methods: {
    // 检查选项是否被选中
    isSelected(stationId, weatherType, optionValue) {
      return this.value[stationId] && 
             this.value[stationId][weatherType] === optionValue
    },
    
    // 检查选项是否被禁用
    isDisabled(stationId, weatherType, optionValue) {
      // 目前没有禁用逻辑，可以根据需要扩展
      return false
    },
    
    // 获取指示器图标
    getIndicatorIcon(stationId, weatherType, optionValue) {
      if (this.isSelected(stationId, weatherType, optionValue)) {
        return 'el-icon-success'
      } else {
        return 'el-icon-close'
      }
    },
    
    // 获取指示器颜色
    getIndicatorColor(stationId, weatherType, optionValue) {
      if (this.isSelected(stationId, weatherType, optionValue)) {
        return '#67C23A' // 绿色
      } else {
        return '#F56C6C' // 红色
      }
    },
    
    // 处理选项点击事件
    handleOptionClick(stationId, weatherType, optionValue) {
      if (this.readonly) return
      
      const newValue = JSON.parse(JSON.stringify(this.value))
      
      // 初始化站点数据
      if (!newValue[stationId]) {
        newValue[stationId] = {}
      }
      
      // 单选约束：如果已选中则取消，否则选中（取消同类其他选项）
      if (newValue[stationId][weatherType] === optionValue) {
        // 取消选择
        newValue[stationId][weatherType] = null
      } else {
        // 选中当前选项
        newValue[stationId][weatherType] = optionValue
      }
      
      // 清理空值
      Object.keys(newValue).forEach(stId => {
        Object.keys(newValue[stId]).forEach(wType => {
          if (!newValue[stId][wType]) {
            delete newValue[stId][wType]
          }
        })
        if (Object.keys(newValue[stId]).length === 0) {
          delete newValue[stId]
        }
      })
      
      this.$emit('input', newValue)
      this.$emit('change', newValue)
      
      // 更新进度
      this.$nextTick(() => {
        this.updateProgress()
      })
    },
    
    // 更新答题进度
    updateProgress() {
      const progress = this.totalCount > 0 ? 
        Math.round((this.selectedCount / this.totalCount) * 100) : 0
      
      this.progress = progress
      this.$emit('progress-change', progress)
    },
    
    // 获取站点答案摘要
    getStationSummary(stationId) {
      const stationAnswer = this.value[stationId] || {}
      const summary = []
      
      this.weatherTypes.forEach(weather => {
        const selectedValue = stationAnswer[weather.type]
        if (selectedValue) {
          const option = weather.options.find(opt => opt.value === selectedValue)
          if (option) {
            summary.push(`${weather.label}: ${option.shortLabel || option.label}`)
          }
        }
      })
      
      return summary.join('; ')
    },
    
    // 验证答案完整性
    validateAnswers() {
      const errors = []
      
      this.stations.forEach(station => {
        const stationAnswer = this.value[station.id] || {}
        const selectedCount = Object.keys(stationAnswer).length
        
        if (selectedCount === 0) {
          errors.push(`${station.name}未选择任何天气现象`)
        }
      })
      
      return {
        isValid: errors.length === 0,
        errors
      }
    },
    
    // 重置所有选择
    resetAll() {
      this.$confirm('确定要重置所有选择吗？', '确认重置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('input', {})
        this.$emit('change', {})
        this.$message.success('已重置所有选择')
      }).catch(() => {
        // 用户取消
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.convection-station-table {
  .table-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
      color: #303133;
      font-size: 18px;
    }
    
    .progress-info {
      display: flex;
      align-items: center;
      gap: 10px;
      
      span {
        font-size: 14px;
        color: #606266;
        white-space: nowrap;
      }
      
      .el-progress {
        width: 120px;
      }
    }
  }
  
  .table-container {
    border: 1px solid #EBEEF5;
    border-radius: 8px;
    overflow: hidden;
    background: #fff;
    
    .table-header {
      display: flex;
      background: #F5F7FA;
      border-bottom: 2px solid #E4E7ED;
      
      .corner-cell {
        width: 200px;
        min-height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-right: 1px solid #E4E7ED;
        background: #FAFAFA;
        
        .cell-content {
          text-align: center;
          
          .main-text {
            display: block;
            font-weight: bold;
            color: #303133;
            font-size: 14px;
          }
          
          .sub-text {
            display: block;
            color: #909399;
            font-size: 12px;
            margin-top: 4px;
          }
        }
      }
      
      .station-header {
        flex: 1;
        min-height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-right: 1px solid #E4E7ED;
        
        &:last-child {
          border-right: none;
        }
        
        .station-info {
          text-align: center;
          
          .station-name {
            font-weight: bold;
            color: #303133;
            font-size: 16px;
            margin-bottom: 4px;
          }
          
          .station-code {
            color: #909399;
            font-size: 12px;
          }
        }
      }
    }
    
    .table-body {
      .weather-row {
        display: flex;
        border-bottom: 1px solid #EBEEF5;
        
        &:last-child {
          border-bottom: none;
        }
        
        .weather-label {
          width: 200px;
          min-height: 120px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          border-right: 1px solid #E4E7ED;
          background: #FAFBFC;
          padding: 15px 10px;
          
          .weather-name {
            font-weight: bold;
            color: #303133;
            font-size: 15px;
            margin-bottom: 4px;
            text-align: center;
          }
          
          .weather-desc {
            color: #909399;
            font-size: 12px;
            text-align: center;
          }
        }
        
        .option-cell {
          flex: 1;
          min-height: 120px;
          border-right: 1px solid #E4E7ED;
          padding: 10px;
          
          &:last-child {
            border-right: none;
          }
          
          .option-group {
            height: 100%;
            display: flex;
            flex-direction: column;
            gap: 8px;
            
            .option-item {
              flex: 1;
              display: flex;
              align-items: center;
              padding: 8px 12px;
              border: 1px solid #E4E7ED;
              border-radius: 6px;
              cursor: pointer;
              transition: all 0.3s ease;
              background: #fff;
              
              &:hover:not(.disabled) {
                border-color: #409EFF;
                background: #F0F9FF;
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
              }
              
              &.selected {
                border-color: #67C23A;
                background: #F0F9FF;
                box-shadow: 0 2px 8px rgba(103, 194, 58, 0.1);
              }
              
              &.disabled {
                cursor: not-allowed;
                opacity: 0.6;
                background: #F5F7FA;
              }
              
              .option-indicator {
                margin-right: 8px;
                font-size: 16px;
                width: 20px;
                text-align: center;
              }
              
              .option-text {
                flex: 1;
                
                .option-label {
                  font-size: 13px;
                  color: #303133;
                  line-height: 1.4;
                  margin-bottom: 2px;
                }
                
                .option-score {
                  font-size: 11px;
                  color: #409EFF;
                  font-weight: bold;
                }
              }
            }
          }
        }
      }
    }
  }
  
  .selection-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
    padding: 15px 20px;
    background: #F8F9FA;
    border-radius: 8px;
    border: 1px solid #E9ECEF;
    
    .summary-item {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .label {
        color: #606266;
        font-size: 14px;
      }
      
      .value {
        color: #303133;
        font-size: 16px;
        font-weight: bold;
        
        &.score {
          color: #E6A23C;
        }
      }
    }
  }
  
  .operation-tips {
    margin-top: 20px;
    
    ::v-deep .el-alert__description {
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          margin-bottom: 5px;
          color: #606266;
          font-size: 13px;
          line-height: 1.5;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .convection-station-table {
    .table-container {
      .table-header .corner-cell {
        width: 160px;
      }
      
      .table-body .weather-label {
        width: 160px;
      }
    }
  }
}

@media (max-width: 768px) {
  .convection-station-table {
    .table-title {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }
    
    .selection-summary {
      flex-direction: column;
      gap: 10px;
      text-align: center;
    }
  }
}
</style> 