<template>
  <div ref="container" class="virtual-table-container">
    <!-- 表格头部 -->
    <div ref="header" class="virtual-table-header">
      <table class="virtual-table">
        <thead>
          <tr>
            <th
              v-for="column in columns"
              :key="column.prop"
              :style="{ width: column.width || 'auto' }"
              :class="column.className"
            >
              {{ column.label }}
            </th>
          </tr>
        </thead>
      </table>
    </div>

    <!-- 可滚动区域 -->
    <div
      ref="scrollContainer"
      :style="{ height: containerHeight + 'px' }"
      class="virtual-table-body"
      @scroll="handleScroll"
    >
      <!-- 占位元素，用于撑开滚动条 -->
      <div
        :style="{ height: totalHeight + 'px' }"
        class="virtual-table-spacer"
      />

      <!-- 实际渲染的表格内容 -->
      <div
        :style="{ transform: `translateY(${offsetY}px)` }"
        class="virtual-table-content"
      >
        <table class="virtual-table">
          <tbody>
            <tr
              v-for="(item, index) in visibleData"
              :key="getRowKey(item, startIndex + index)"
              :class="getRowClass(item, startIndex + index)"
              @click="handleRowClick(item, startIndex + index)"
            >
              <td
                v-for="column in columns"
                :key="column.prop"
                :style="{ width: column.width || 'auto' }"
                :class="column.className"
              >
                <!-- 自定义插槽 -->
                <slot
                  v-if="column.slot"
                  :name="column.slot"
                  :row="item"
                  :index="startIndex + index"
                  :column="column"
                />
                <!-- 默认显示 -->
                <span v-else>{{ getColumnValue(item, column) }}</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="virtual-table-loading">
      <i class="el-icon-loading" />
      <span>加载中...</span>
    </div>

    <!-- 空数据状态 -->
    <div v-if="!loading && data.length === 0" class="virtual-table-empty">
      <i class="el-icon-document" />
      <span>暂无数据</span>
    </div>
  </div>
</template>

<script>
/**
 * 虚拟滚动表格组件
 * 适用于大数据量场景，只渲染可见区域的数据
 * 支持固定行高和动态行高
 */
export default {
  name: 'VirtualTable',
  props: {
    // 表格数据
    data: {
      type: Array,
      default: () => []
    },

    // 表格列配置
    columns: {
      type: Array,
      required: true
    },

    // 容器高度
    containerHeight: {
      type: Number,
      default: 400
    },

    // 行高（固定）
    itemHeight: {
      type: Number,
      default: 60
    },

    // 缓冲区大小（额外渲染的行数）
    buffer: {
      type: Number,
      default: 10
    },

    // 行键值字段
    rowKey: {
      type: String,
      default: 'id'
    },

    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },

    // 行点击事件
    rowClick: {
      type: Function,
      default: null
    },

    // 自定义行类名
    rowClassName: {
      type: [String, Function],
      default: ''
    }
  },

  data() {
    return {
      // 滚动位置
      scrollTop: 0,

      // 可见区域的开始索引
      startIndex: 0,

      // 可见区域的结束索引
      endIndex: 0,

      // 缓存已计算的行高
      itemHeights: {},

      // 防抖定时器
      scrollTimer: null
    }
  },

  computed: {
    // 总高度
    totalHeight() {
      return this.data.length * this.itemHeight
    },

    // 可见区域渲染的数据量
    visibleCount() {
      return Math.ceil(this.containerHeight / this.itemHeight) + this.buffer * 2
    },

    // 实际渲染的数据
    visibleData() {
      return this.data.slice(this.startIndex, this.endIndex + 1)
    },

    // Y轴偏移量
    offsetY() {
      return this.startIndex * this.itemHeight
    }
  },

  watch: {
    data: {
      handler() {
        this.$nextTick(() => {
          this.calculateVisibleRange()
        })
      },
      immediate: true
    },

    containerHeight() {
      this.calculateVisibleRange()
    }
  },

  mounted() {
    this.calculateVisibleRange()
    this.addResizeListener()
  },

  beforeDestroy() {
    this.removeResizeListener()
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer)
    }
  },

  methods: {
    /**
     * 滚动事件处理
     */
    handleScroll(event) {
      this.scrollTop = event.target.scrollTop

      // 防抖处理，避免频繁计算
      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer)
      }

      this.scrollTimer = setTimeout(() => {
        this.calculateVisibleRange()
      }, 16) // 约60fps
    },

    /**
     * 计算可见区域范围
     */
    calculateVisibleRange() {
      const scrollTop = this.scrollTop
      const containerHeight = this.containerHeight
      const itemHeight = this.itemHeight
      const buffer = this.buffer

      // 计算开始索引
      let startIndex = Math.floor(scrollTop / itemHeight)
      startIndex = Math.max(0, startIndex - buffer)

      // 计算结束索引
      let endIndex = Math.ceil((scrollTop + containerHeight) / itemHeight)
      endIndex = Math.min(this.data.length - 1, endIndex + buffer)

      // 更新索引
      this.startIndex = startIndex
      this.endIndex = endIndex

      // 触发可见区域变化事件
      this.$emit('visible-change', {
        startIndex,
        endIndex,
        visibleData: this.visibleData
      })
    },

    /**
     * 获取行键值
     */
    getRowKey(row, index) {
      if (typeof this.rowKey === 'function') {
        return this.rowKey(row, index)
      }
      return row[this.rowKey] || index
    },

    /**
     * 获取行样式类
     */
    getRowClass(row, index) {
      const className = []

      if (typeof this.rowClassName === 'function') {
        className.push(this.rowClassName(row, index))
      } else if (this.rowClassName) {
        className.push(this.rowClassName)
      }

      // 奇偶行样式
      if (index % 2 === 1) {
        className.push('virtual-table-row-odd')
      }

      return className.join(' ')
    },

    /**
     * 获取列值
     */
    getColumnValue(row, column) {
      if (column.formatter && typeof column.formatter === 'function') {
        return column.formatter(row, column)
      }

      const prop = column.prop
      if (prop.includes('.')) {
        // 支持嵌套属性，如 'user.name'
        return prop.split('.').reduce((obj, key) => obj && obj[key], row)
      }

      return row[prop]
    },

    /**
     * 行点击事件
     */
    handleRowClick(row, index) {
      if (this.rowClick && typeof this.rowClick === 'function') {
        this.rowClick(row, index)
      }

      this.$emit('row-click', row, index)
    },

    /**
     * 滚动到指定索引
     */
    scrollToIndex(index) {
      const scrollTop = index * this.itemHeight
      this.$refs.scrollContainer.scrollTop = scrollTop
    },

    /**
     * 滚动到顶部
     */
    scrollToTop() {
      this.scrollToIndex(0)
    },

    /**
     * 滚动到底部
     */
    scrollToBottom() {
      this.scrollToIndex(this.data.length - 1)
    },

    /**
     * 添加窗口大小变化监听
     */
    addResizeListener() {
      window.addEventListener('resize', this.handleResize)
    },

    /**
     * 移除窗口大小变化监听
     */
    removeResizeListener() {
      window.removeEventListener('resize', this.handleResize)
    },

    /**
     * 窗口大小变化处理
     */
    handleResize() {
      this.$nextTick(() => {
        this.calculateVisibleRange()
      })
    }
  }
}
</script>

<style scoped>
.virtual-table-container {
  position: relative;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  background: #ffffff;
}

.virtual-table-header {
  position: relative;
  z-index: 10;
  background: #fafafa;
  border-bottom: 1px solid #ebeef5;
}

.virtual-table-body {
  position: relative;
  overflow: auto;
}

.virtual-table-spacer {
  position: absolute;
  top: 0;
  left: 0;
  width: 1px;
  z-index: -1;
}

.virtual-table-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1;
}

.virtual-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

.virtual-table th,
.virtual-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #ebeef5;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.virtual-table th {
  background: #fafafa;
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.virtual-table td {
  color: #606266;
  font-size: 14px;
}

.virtual-table tbody tr {
  transition: background-color 0.2s;
  cursor: pointer;
}

.virtual-table tbody tr:hover {
  background: #f5f7fa;
}

.virtual-table-row-odd {
  background: #fafafa;
}

.virtual-table-row-odd:hover {
  background: #f0f2f5;
}

.virtual-table-loading,
.virtual-table-empty {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: #909399;
  font-size: 14px;
}

.virtual-table-loading i,
.virtual-table-empty i {
  font-size: 24px;
}

/* 滚动条样式 */
.virtual-table-body::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.virtual-table-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.virtual-table-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.virtual-table-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
