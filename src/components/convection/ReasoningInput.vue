<template>
  <div class="reasoning-input">
    <div class="input-header">
      <h4>预报依据阐述</h4>
      <div class="word-count">
        <span :class="{ 'over-limit': wordCount > maxWords, 'sufficient': wordCount >= minWords }">
          {{ wordCount }}/{{ maxWords }}
        </span>
        <span v-if="minWords > 0" class="min-requirement">
          (建议不少于{{ minWords }}字)
        </span>
      </div>
    </div>

    <el-input
      v-model="reasoning"
      :rows="rows"
      :placeholder="placeholder"
      :maxlength="maxWords"
      :disabled="readonly"
      type="textarea"
      show-word-limit
      resize="vertical"
      class="reasoning-textarea"
      @input="handleInput"
    />

    <div v-if="!readonly" class="input-tips">
      <div class="tip-item">
        <i class="el-icon-info" />
        <span>请详细阐述各类强对流天气的分级依据和预报理由</span>
      </div>
      <div class="tip-item">
        <i class="el-icon-warning" />
        <span>建议包含：分级标准、关键指标、分析结论</span>
      </div>
    </div>

    <div v-if="showProgress" class="progress-container">
      <div class="progress-label">完成度：</div>
      <el-progress
        :percentage="progressPercentage"
        :stroke-width="6"
        :color="progressColor"
        :show-text="false"
      />
      <span class="progress-text">{{ progressPercentage }}%</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ReasoningInput',
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请详细阐述预报依据，包括：\n1. 各类强对流天气的分级判断依据（如短时强降水的时次降水量标准）\n2. 极端天气预报的理由和关键指标（如大冰雹的识别要点）\n3. 基于MICAPS气象资料的分析结论和预报逻辑'
    },
    maxWords: {
      type: Number,
      default: 2000
    },
    minWords: {
      type: Number,
      default: 100
    },
    rows: {
      type: Number,
      default: 12
    },
    readonly: {
      type: Boolean,
      default: false
    },
    showProgress: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      reasoning: this.value
    }
  },
  computed: {
    wordCount() {
      return this.reasoning ? this.reasoning.length : 0
    },
    progressPercentage() {
      if (this.wordCount < this.minWords) {
        return Math.min(50, Math.round(this.wordCount / this.minWords * 50))
      }
      return Math.min(100, Math.round(this.wordCount / (this.minWords * 2) * 100))
    },
    progressColor() {
      const progress = this.progressPercentage
      if (progress < 30) return '#f56c6c'
      if (progress < 70) return '#e6a23c'
      return '#67c23a'
    }
  },
  watch: {
    value(newVal) {
      this.reasoning = newVal
    }
  },
  methods: {
    handleInput() {
      this.$emit('input', this.reasoning)
      this.$emit('word-count-change', this.wordCount)
      this.$emit('progress-change', this.progressPercentage)
    }
  }
}
</script>

<style scoped>
.reasoning-input {
  margin-bottom: 20px;
}

.input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.input-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.word-count {
  color: #909399;
  font-size: 14px;
}

.word-count .over-limit {
  color: #f56c6c;
  font-weight: bold;
}

.word-count .sufficient {
  color: #67c23a;
  font-weight: bold;
}

.min-requirement {
  color: #8c8c8c;
  margin-left: 8px;
  font-size: 12px;
}

.reasoning-textarea {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.reasoning-textarea ::v-deep .el-textarea__inner {
  line-height: 1.8;
  font-size: 14px;
  border-radius: 8px;
  border: 2px solid #e0e6ed;
  transition: all 0.3s ease;
  resize: vertical;
}

.reasoning-textarea ::v-deep .el-textarea__inner:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.input-tips {
  margin-top: 10px;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 5px;
  color: #606266;
  font-size: 13px;
}

.tip-item i {
  color: #909399;
  font-size: 14px;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e8e8e8;
}

.progress-label {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  min-width: 60px;
}

.progress-container .el-progress {
  flex: 1;
}

.progress-text {
  color: #303133;
  font-size: 14px;
  font-weight: 600;
  min-width: 35px;
}
</style>
