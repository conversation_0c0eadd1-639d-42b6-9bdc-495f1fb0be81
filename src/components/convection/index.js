// 强对流模块组件统一导出

// 核心组件
import ReasoningInput from './ReasoningInput.vue'
import StationTable from './StationTable.vue'
import AreaDrawing from './AreaDrawing.vue'
import AnswerComparison from './AnswerComparison.vue'
import GradingPanel from './GradingPanel.vue'

// 统一导出
export {
  ReasoningInput,
  StationTable,
  AreaDrawing,
  AnswerComparison,
  GradingPanel
}

// 默认导出
export default {
  ReasoningInput,
  StationTable,
  AreaDrawing,
  AnswerComparison,
  GradingPanel
}

// 组件安装函数（可选）
export const install = function(Vue) {
  Vue.component('ConvectionReasoningInput', ReasoningInput)
  Vue.component('ConvectionStationTable', StationTable)
  Vue.component('ConvectionAreaDrawing', AreaDrawing)
  Vue.component('ConvectionAnswerComparison', AnswerComparison)
  Vue.component('ConvectionGradingPanel', GradingPanel)
}
