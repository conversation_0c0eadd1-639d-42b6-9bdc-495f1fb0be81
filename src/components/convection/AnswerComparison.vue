<template>
  <div class="answer-comparison">
    <!-- 对比头部 -->
    <div class="comparison-header">
      <h4>答案对比</h4>
      <div class="comparison-actions">
        <el-button
          :icon="viewMode === 'side' ? 'el-icon-s-grid' : 'el-icon-position'"
          size="small"
          @click="toggleView"
        >
          {{ viewMode === 'side' ? '上下对比' : '左右对比' }}
        </el-button>
        <el-button
          size="small"
          icon="el-icon-download"
          @click="exportComparison"
        >
          导出对比
        </el-button>
      </div>
    </div>

    <!-- 对比内容 -->
    <div :class="{ 'vertical-mode': viewMode === 'vertical' }" class="comparison-content">
      <!-- 学生答案部分 -->
      <div class="answer-section student-section">
        <div class="section-header student-header">
          <div class="header-left">
            <i class="el-icon-user" />
            <span class="section-title">学生答案</span>
            <span class="student-name">{{ studentName }}</span>
          </div>
          <div class="header-right">
            <el-tag :type="getSubmitStatusType(submitStatus)" size="small">
              {{ getSubmitStatusText(submitStatus) }}
            </el-tag>
            <span class="submit-time">{{ formatTime(submitTime) }}</span>
          </div>
        </div>
        <div class="section-content">
          <!-- 站点预报对比 -->
          <div class="station-comparison">
            <h5 class="sub-title">站点预报 (68分)</h5>
            <div class="station-table-wrapper">
              <el-table
                :data="stationComparisonData"
                border
                size="small"
                class="comparison-table"
              >
                <el-table-column label="站点" width="80" align="center">
                  <template slot-scope="scope">
                    <strong>{{ scope.row.stationName }}</strong>
                  </template>
                </el-table-column>
                <el-table-column label="短时强降水" width="120" align="center">
                  <template slot-scope="scope">
                    <div class="weather-cell">
                      <el-tag
                        v-if="scope.row.student.rainfall"
                        :type="getWeatherTagType('rainfall', scope.row.student.rainfall, scope.row.standard.rainfall)"
                        size="mini"
                      >
                        {{ getRainfallLabel(scope.row.student.rainfall) }}
                      </el-tag>
                      <span v-else class="no-answer">未答</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="雷暴大风" width="120" align="center">
                  <template slot-scope="scope">
                    <div class="weather-cell">
                      <el-tag
                        v-if="scope.row.student.wind"
                        :type="getWeatherTagType('wind', scope.row.student.wind, scope.row.standard.wind)"
                        size="mini"
                      >
                        {{ getWindLabel(scope.row.student.wind) }}
                      </el-tag>
                      <span v-else class="no-answer">未答</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="冰雹" width="100" align="center">
                  <template slot-scope="scope">
                    <div class="weather-cell">
                      <el-tag
                        v-if="scope.row.student.hail"
                        :type="getWeatherTagType('hail', scope.row.student.hail, scope.row.standard.hail)"
                        size="mini"
                      >
                        大冰雹
                      </el-tag>
                      <span v-else class="no-answer">未答</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="准确性" width="80" align="center">
                  <template slot-scope="scope">
                    <div class="accuracy-indicator">
                      <i
                        :class="getAccuracyIcon(scope.row.accuracy)"
                        :style="{ color: getAccuracyColor(scope.row.accuracy) }"
                      />
                      <span :style="{ color: getAccuracyColor(scope.row.accuracy) }">
                        {{ scope.row.accuracy }}%
                      </span>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>

          <!-- 落区绘制对比 -->
          <div class="area-comparison">
            <h5 class="sub-title">落区绘制 (32分)</h5>
            <div class="area-summary">
              <div class="area-stats">
                <div v-for="type in convectionTypes" :key="type.value" class="stat-item">
                  <div class="stat-label">
                    <div :style="{ backgroundColor: type.color }" class="color-dot" />
                    <span>{{ type.shortLabel }}：</span>
                  </div>
                  <span class="stat-value">
                    {{ getAreaCount(studentAreaAnswer, type.value) }}个落区
                  </span>
                </div>
              </div>
              <div class="area-score">
                <span class="score-label">落区绘制得分：</span>
                <span class="score-value">{{ areaScore || '--' }} / 32分</span>
              </div>
            </div>
          </div>

          <!-- 预报依据对比 -->
          <div class="reasoning-comparison">
            <h5 class="sub-title">预报依据 (20分)</h5>
            <div class="reasoning-content student-reasoning">
              <div class="reasoning-meta">
                <span class="word-count">字数：{{ studentReasoningWordCount }}字</span>
                <span class="reasoning-score">得分：{{ reasoningScore || '--' }} / 20分</span>
              </div>
              <div class="reasoning-text">
                {{ studentReasoning || '未填写预报依据' }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 标准答案部分 -->
      <div class="answer-section standard-section">
        <div class="section-header standard-header">
          <div class="header-left">
            <i class="el-icon-star-on" />
            <span class="section-title">标准答案</span>
          </div>
          <div class="header-right">
            <el-tag size="small" type="success">参考答案</el-tag>
          </div>
        </div>
        <div class="section-content">
          <!-- 标准站点预报 -->
          <div class="station-comparison">
            <h5 class="sub-title">标准站点预报</h5>
            <div class="station-table-wrapper">
              <el-table
                :data="stationComparisonData"
                border
                size="small"
                class="comparison-table standard-table"
              >
                <el-table-column label="站点" width="80" align="center">
                  <template slot-scope="scope">
                    <strong>{{ scope.row.stationName }}</strong>
                  </template>
                </el-table-column>
                <el-table-column label="短时强降水" width="120" align="center">
                  <template slot-scope="scope">
                    <div class="weather-cell">
                      <el-tag
                        v-if="scope.row.standard.rainfall"
                        size="mini"
                        type="success"
                      >
                        {{ getRainfallLabel(scope.row.standard.rainfall) }}
                      </el-tag>
                      <span v-else class="no-answer">无</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="雷暴大风" width="120" align="center">
                  <template slot-scope="scope">
                    <div class="weather-cell">
                      <el-tag
                        v-if="scope.row.standard.wind"
                        size="mini"
                        type="success"
                      >
                        {{ getWindLabel(scope.row.standard.wind) }}
                      </el-tag>
                      <span v-else class="no-answer">无</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="冰雹" width="100" align="center">
                  <template slot-scope="scope">
                    <div class="weather-cell">
                      <el-tag
                        v-if="scope.row.standard.hail"
                        size="mini"
                        type="success"
                      >
                        大冰雹
                      </el-tag>
                      <span v-else class="no-answer">无</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="说明" width="120" align="center">
                  <template slot-scope="scope">
                    <span class="standard-note">{{ scope.row.note || '标准答案' }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>

          <!-- 标准落区绘制 -->
          <div class="area-comparison">
            <h5 class="sub-title">标准落区绘制</h5>
            <div class="area-summary">
              <div class="area-stats">
                <div v-for="type in convectionTypes" :key="type.value" class="stat-item">
                  <div class="stat-label">
                    <div :style="{ backgroundColor: type.color }" class="color-dot" />
                    <span>{{ type.shortLabel }}：</span>
                  </div>
                  <span class="stat-value">
                    {{ getAreaCount(standardAreaAnswer, type.value) }}个落区
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 标准预报依据 -->
          <div class="reasoning-comparison">
            <h5 class="sub-title">标准预报依据</h5>
            <div class="reasoning-content standard-reasoning">
              <div class="reasoning-meta">
                <span class="word-count">字数：{{ standardReasoningWordCount }}字</span>
                <el-tag size="mini" type="success">标准答案</el-tag>
              </div>
              <div class="reasoning-text">
                {{ standardReasoning || '暂无标准预报依据' }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 总体评分摘要 -->
    <div class="score-summary">
      <h5>评分摘要</h5>
      <div class="score-items">
        <div class="score-item">
          <span class="score-label">站点预报：</span>
          <span class="score-value">{{ stationScore || '--' }} / 68分</span>
          <div class="score-bar">
            <el-progress
              :percentage="getScorePercentage(stationScore, 68)"
              :stroke-width="6"
              :show-text="false"
              :color="getScoreColor(stationScore, 68)"
            />
          </div>
        </div>
        <div class="score-item">
          <span class="score-label">落区绘制：</span>
          <span class="score-value">{{ areaScore || '--' }} / 32分</span>
          <div class="score-bar">
            <el-progress
              :percentage="getScorePercentage(areaScore, 32)"
              :stroke-width="6"
              :show-text="false"
              :color="getScoreColor(areaScore, 32)"
            />
          </div>
        </div>
        <div class="score-item">
          <span class="score-label">预报依据：</span>
          <span class="score-value">{{ reasoningScore || '--' }} / 20分</span>
          <div class="score-bar">
            <el-progress
              :percentage="getScorePercentage(reasoningScore, 20)"
              :stroke-width="6"
              :show-text="false"
              :color="getScoreColor(reasoningScore, 20)"
            />
          </div>
        </div>
        <div class="score-item total-score">
          <span class="score-label">总分：</span>
          <span class="score-value">{{ totalScore || '--' }} / 120分</span>
          <div class="score-bar">
            <el-progress
              :percentage="getScorePercentage(totalScore, 120)"
              :stroke-width="8"
              :show-text="false"
              :color="getScoreColor(totalScore, 120)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AnswerComparison',
  props: {
    // 学生信息
    studentName: {
      type: String,
      default: ''
    },
    submitTime: {
      type: String,
      default: ''
    },
    submitStatus: {
      type: Number,
      default: 1
    },

    // 学生答案
    studentStationAnswer: {
      type: Object,
      default: () => ({})
    },
    studentAreaAnswer: {
      type: Object,
      default: () => ({})
    },
    studentReasoning: {
      type: String,
      default: ''
    },

    // 标准答案
    standardStationAnswer: {
      type: Object,
      default: () => ({})
    },
    standardAreaAnswer: {
      type: Object,
      default: () => ({})
    },
    standardReasoning: {
      type: String,
      default: ''
    },

    // 评分结果
    stationScore: {
      type: Number,
      default: null
    },
    areaScore: {
      type: Number,
      default: null
    },
    reasoningScore: {
      type: Number,
      default: null
    },
    totalScore: {
      type: Number,
      default: null
    },

    // 站点配置
    stations: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      viewMode: 'side', // side | vertical

      // 强对流类型配置
      convectionTypes: [
        { label: '短时强降水', shortLabel: '强降水', value: 'heavy_rainfall', color: '#1890ff' },
        { label: '雷暴大风', shortLabel: '雷暴风', value: 'thunderstorm_wind', color: '#52c41a' },
        { label: '冰雹', shortLabel: '冰雹', value: 'hail', color: '#722ed1' },
        { label: '龙卷', shortLabel: '龙卷', value: 'tornado', color: '#f5222d' }
      ],

      // 降水标签映射
      rainfallLabels: {
        'level1': 'Level 1',
        'level2': 'Level 2',
        'level3': 'Level 3'
      },

      // 风力标签映射
      windLabels: {
        'moderate': 'Moderate',
        'severe': 'Severe',
        'extreme': 'Extreme'
      }
    }
  },
  computed: {
    studentReasoningWordCount() {
      return this.studentReasoning ? this.studentReasoning.length : 0
    },

    standardReasoningWordCount() {
      return this.standardReasoning ? this.standardReasoning.length : 0
    },

    stationComparisonData() {
      return this.stations.map(station => {
        const studentAnswer = this.studentStationAnswer[station.code] || {}
        const standardAnswer = this.standardStationAnswer[station.code] || {}

        // 计算准确性
        let correct = 0
        let total = 0

        if (standardAnswer.rainfall !== undefined) {
          total++
          if (studentAnswer.rainfall === standardAnswer.rainfall) correct++
        }
        if (standardAnswer.wind !== undefined) {
          total++
          if (studentAnswer.wind === standardAnswer.wind) correct++
        }
        if (standardAnswer.hail !== undefined) {
          total++
          if (studentAnswer.hail === standardAnswer.hail) correct++
        }

        const accuracy = total > 0 ? Math.round(correct / total * 100) : 0

        return {
          stationName: station.name,
          stationCode: station.code,
          student: studentAnswer,
          standard: standardAnswer,
          accuracy,
          note: '根据MICAPS资料分析'
        }
      })
    }
  },
  methods: {
    toggleView() {
      this.viewMode = this.viewMode === 'side' ? 'vertical' : 'side'
    },

    exportComparison() {
      // 导出对比数据逻辑
      this.$message.info('导出功能开发中...')
    },

    getSubmitStatusType(status) {
      return status === 1 ? 'success' : 'warning'
    },

    getSubmitStatusText(status) {
      return status === 1 ? '已提交' : '答题中'
    },

    formatTime(time) {
      if (!time) return '--'
      return new Date(time).toLocaleString()
    },

    getRainfallLabel(value) {
      return this.rainfallLabels[value] || value
    },

    getWindLabel(value) {
      return this.windLabels[value] || value
    },

    getWeatherTagType(weatherType, studentValue, standardValue) {
      if (!studentValue && !standardValue) return 'info'
      if (studentValue === standardValue) return 'success'
      if (!studentValue) return 'warning'
      return 'danger'
    },

    getAccuracyIcon(accuracy) {
      if (accuracy >= 80) return 'el-icon-success'
      if (accuracy >= 60) return 'el-icon-warning'
      return 'el-icon-error'
    },

    getAccuracyColor(accuracy) {
      if (accuracy >= 80) return '#67c23a'
      if (accuracy >= 60) return '#e6a23c'
      return '#f56c6c'
    },

    getAreaCount(areaAnswer, type) {
      return areaAnswer && areaAnswer[type] ? areaAnswer[type].length : 0
    },

    getScorePercentage(score, total) {
      if (!score || !total) return 0
      return Math.round(score / total * 100)
    },

    getScoreColor(score, total) {
      const percentage = this.getScorePercentage(score, total)
      if (percentage >= 80) return '#67c23a'
      if (percentage >= 60) return '#e6a23c'
      return '#f56c6c'
    }
  }
}
</script>

<style scoped>
.answer-comparison {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
}

.comparison-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.comparison-header h4 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.comparison-content {
  display: flex;
  min-height: 600px;
}

.comparison-content.vertical-mode {
  flex-direction: column;
}

.answer-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.student-section {
  border-right: 1px solid #e8e8e8;
}

.comparison-content.vertical-mode .student-section {
  border-right: none;
  border-bottom: 1px solid #e8e8e8;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.student-header {
  background: #f0f9ff;
}

.standard-header {
  background: #f6ffed;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.student-name {
  color: #1890ff;
  font-size: 14px;
  background: rgba(24, 144, 255, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.submit-time {
  font-size: 12px;
  color: #8c8c8c;
}

.section-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.sub-title {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.sub-title::before {
  content: '';
  width: 3px;
  height: 16px;
  background: #1890ff;
  border-radius: 2px;
}

.station-table-wrapper {
  margin-bottom: 25px;
}

.comparison-table {
  width: 100%;
  font-size: 12px;
}

.comparison-table ::v-deep .el-table__header {
  background: #fafafa;
}

.weather-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

.no-answer {
  color: #bfbfbf;
  font-size: 12px;
}

.accuracy-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
}

.area-comparison {
  margin-bottom: 25px;
}

.area-summary {
  background: #fafafa;
  border-radius: 6px;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.area-stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.stat-label {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #606266;
}

.color-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.stat-value {
  color: #303133;
  font-weight: 600;
}

.area-score {
  font-size: 13px;
}

.score-label {
  color: #606266;
}

.score-value {
  color: #303133;
  font-weight: 600;
}

.reasoning-comparison {
  margin-bottom: 20px;
}

.reasoning-content {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: hidden;
}

.reasoning-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  font-size: 12px;
}

.word-count {
  color: #8c8c8c;
}

.reasoning-score {
  color: #1890ff;
  font-weight: 600;
}

.reasoning-text {
  padding: 15px;
  line-height: 1.6;
  font-size: 14px;
  color: #303133;
  min-height: 100px;
  max-height: 200px;
  overflow-y: auto;
}

.student-reasoning .reasoning-text {
  background: #f0f9ff;
}

.standard-reasoning .reasoning-text {
  background: #f6ffed;
}

.standard-note {
  font-size: 11px;
  color: #52c41a;
}

.score-summary {
  background: #fafafa;
  border-top: 1px solid #e8e8e8;
  padding: 20px;
}

.score-summary h5 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.score-items {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.score-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px 15px;
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.score-item.total-score {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  font-weight: 600;
}

.score-item .score-label {
  min-width: 80px;
  font-size: 14px;
}

.score-item .score-value {
  min-width: 80px;
  font-size: 14px;
  font-weight: 600;
}

.score-bar {
  flex: 1;
  max-width: 200px;
}

.total-score .score-label,
.total-score .score-value {
  color: #ffffff;
}
</style>
