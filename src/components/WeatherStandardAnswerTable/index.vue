<template>
  <div class="weather-standard-answer-table">
    <!-- 题目信息 -->
    <div class="question-header">
      <h3>{{ question.title || '历史个例天气预报标准答案' }}</h3>
      <p class="question-content">{{ question.content }}</p>
      <div class="forecast-info">
        <span>预报起报日期及时间：{{ question.forecastDate }} {{ question.forecastTime }}</span>
      </div>
    </div>

    <!-- 天气预报标准答案表格 -->
    <el-table
      :data="tableData"
      :span-method="spanMethod"
      :max-height="600"
      :row-style="{ height: '60px' }"
      :cell-style="{ padding: '12px 8px' }"
      border
      class="forecast-table"
      style="width: 100%;"
    >
      <!-- 预报起报日期及时间列 -->
      <el-table-column
        prop="dateTime"
        label="预报起报日期及时间"
        min-width="150"
        align="center"
      />

      <!-- 站名站号列 -->
      <el-table-column
        prop="stationName"
        label="站名站号"
        min-width="120"
        align="center"
      />

      <!-- 08-08最大风力(级) -->
      <el-table-column
        label="08-08最大风力(级)"
        min-width="140"
        align="center"
      >
        <template v-slot="scope">
          <div class="input-cell">
            <el-select
              v-model="scope.row.standardAnswers.windForce"
              size="small"
              placeholder="请选择"
              @change="onStandardAnswerChange(scope.row.stationName, 'windForce', $event)"
            >
              <el-option label="静风" value="静风" />
              <el-option label="一级" value="一级" />
              <el-option label="二级" value="二级" />
              <el-option label="三级" value="三级" />
              <el-option label="四级" value="四级" />
              <el-option label="五级" value="五级" />
              <el-option label="六级" value="六级" />
              <el-option label="七级" value="七级" />
              <el-option label="八级" value="八级" />
              <el-option label="九级" value="九级" />
              <el-option label="十级" value="十级" />
              <el-option label="十一级" value="十一级" />
              <el-option label="十二级" value="十二级" />
              <el-option label="大于十二级" value="大于十二级" />
            </el-select>
          </div>
        </template>
      </el-table-column>

      <!-- 08-08最大风力时的风向(角度制) -->
      <el-table-column
        label="08-08最大风力时的风向"
        min-width="160"
        align="center"
      >
        <template v-slot="scope">
          <div class="input-cell">
            <el-select
              v-model="scope.row.standardAnswers.windDirection"
              size="small"
              placeholder="请选择角度范围"
              @change="onStandardAnswerChange(scope.row.stationName, 'windDirection', $event)"
            >
              <el-option label="0.0～22.5°" value="0.0～22.5°" />
              <el-option label="22.5～45.0°" value="22.5～45.0°" />
              <el-option label="45.0～67.5°" value="45.0～67.5°" />
              <el-option label="67.5～90.0°" value="67.5～90.0°" />
              <el-option label="90.0～112.5°" value="90.0～112.5°" />
              <el-option label="112.5～135.0°" value="112.5～135.0°" />
              <el-option label="135.0～157.5°" value="135.0～157.5°" />
              <el-option label="157.5～180.0°" value="157.5～180.0°" />
              <el-option label="180.0～202.5°" value="180.0～202.5°" />
              <el-option label="202.5～225.0°" value="202.5～225.0°" />
              <el-option label="225.0～247.5°" value="225.0～247.5°" />
              <el-option label="247.5～270.0°" value="247.5～270.0°" />
              <el-option label="270.0～292.5°" value="270.0～292.5°" />
              <el-option label="292.5～315.0°" value="292.5～315.0°" />
              <el-option label="315.0～337.5°" value="315.0～337.5°" />
              <el-option label="337.5～360.0°" value="337.5～360.0°" />
            </el-select>
          </div>
        </template>
      </el-table-column>

      <!-- 最低气温℃ -->
      <el-table-column
        label="最低气温℃"
        min-width="120"
        align="center"
      >
        <template v-slot="scope">
          <div class="input-cell">
            <el-input-number
              v-model="scope.row.standardAnswers.minTemperature"
              :min="-50"
              :max="50"
              :precision="0"
              size="small"
              @change="onStandardAnswerChange(scope.row.stationName, 'minTemperature', $event)"
            />
          </div>
        </template>
      </el-table-column>

      <!-- 最高气温℃ -->
      <el-table-column
        label="最高气温℃"
        min-width="120"
        align="center"
      >
        <template v-slot="scope">
          <div class="input-cell">
            <el-input-number
              v-model="scope.row.standardAnswers.maxTemperature"
              :min="-50"
              :max="50"
              :precision="0"
              size="small"
              @change="onStandardAnswerChange(scope.row.stationName, 'maxTemperature', $event)"
            />
          </div>
        </template>
      </el-table-column>

      <!-- 08-08降水(雨、雪)量级 -->
      <el-table-column
        label="08-08降水(雨、雪)量级"
        min-width="170"
        align="center"
      >
        <template v-slot="scope">
          <div class="input-cell">
            <el-select
              v-model="scope.row.standardAnswers.precipitation"
              size="small"
              placeholder="请选择"
              @change="onStandardAnswerChange(scope.row.stationName, 'precipitation', $event)"
            >
              <el-option label="无雨雪" value="无雨雪" />
              <el-option label="雨夹雪" value="雨夹雪" />
              <el-option label="小雨" value="小雨" />
              <el-option label="小雪" value="小雪" />
              <el-option label="中雨" value="中雨" />
              <el-option label="中雪" value="中雪" />
              <el-option label="大雨" value="大雨" />
              <el-option label="暴雨" value="暴雨" />
              <el-option label="大雪" value="大雪" />
              <el-option label="大暴雨以上" value="大暴雨以上" />
              <el-option label="暴雪" value="暴雪" />
            </el-select>
          </div>
        </template>
      </el-table-column>

      <!-- 灾害性天气类型 -->
      <el-table-column
        label="灾害性天气类型"
        min-width="140"
        align="center"
      >
        <template v-slot="scope">
          <div class="input-cell">
            <el-select
              v-model="scope.row.standardAnswers.disasterWeather"
              size="small"
              multiple
              collapse-tags
              placeholder="请选择"
              style="width: 100%;"
              @change="onStandardAnswerChange(scope.row.stationName, 'disasterWeather', $event)"
            >
              <el-option label="无" value="无" />
              <el-option label="暴雨" value="暴雨" />
              <el-option label="暴雪" value="暴雪" />
              <el-option label="高温" value="高温" />
              <el-option label="大风" value="大风" />
              <el-option label="寒潮" value="寒潮" />
              <el-option label="大雾" value="大雾" />
              <el-option label="沙尘暴" value="沙尘暴" />
              <el-option label="雷暴" value="雷暴" />
              <el-option label="冰雹" value="冰雹" />
            </el-select>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button
        :loading="saving"
        size="large"
        @click="saveStandardAnswers"
      >
        <i class="el-icon-document" />
        保存标准答案
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WeatherStandardAnswerTable',
  props: {
    // 题目信息
    question: {
      type: Object,
      required: true
    },
    // 初始标准答案数据
    initialStandardAnswers: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tableData: [],
      standardAnswers: {},
      stations: [],
      saving: false
    }
  },
  watch: {
    question: {
      handler() {
        this.initTable()
      },
      immediate: true
    },
    initialStandardAnswers: {
      handler() {
        this.loadStandardAnswers()
      },
      immediate: true
    }
  },
  methods: {
    initTable() {
      if (!this.question) return

      // 解析站点列表
      try {
        this.stations = JSON.parse(this.question.stations || '[]')
      } catch (e) {
        console.error('解析站点列表失败:', e)
        this.stations = []
      }

      this.tableData = this.stations.map(station => {
        const stationStandardAnswers = this.standardAnswers[station] || {}

        // 确保灾害性天气类型为数组格式
        if (stationStandardAnswers.disasterWeather && !Array.isArray(stationStandardAnswers.disasterWeather)) {
          stationStandardAnswers.disasterWeather = [stationStandardAnswers.disasterWeather]
        } else if (!stationStandardAnswers.disasterWeather) {
          stationStandardAnswers.disasterWeather = []
        }

        const tableRow = {
          dateTime: this.question.forecastDate + ' ' + this.question.forecastTime,
          stationName: station,
          standardAnswers: stationStandardAnswers
        }

        return tableRow
      })
    },

    loadStandardAnswers() {
      // 将初始标准答案数据加载到组件的内部状态
      if (this.initialStandardAnswers && Object.keys(this.initialStandardAnswers).length > 0) {
        // 深拷贝 initialStandardAnswers 到 this.standardAnswers
        this.standardAnswers = JSON.parse(JSON.stringify(this.initialStandardAnswers))

        // 确保每个站点的灾害性天气类型为数组格式
        Object.keys(this.standardAnswers).forEach(stationName => {
          if (this.standardAnswers[stationName] && this.standardAnswers[stationName].disasterWeather) {
            if (!Array.isArray(this.standardAnswers[stationName].disasterWeather)) {
              this.standardAnswers[stationName].disasterWeather = [this.standardAnswers[stationName].disasterWeather]
            }
          }
        })
      }

      this.initTable()
    },

    spanMethod({ row, column, rowIndex, columnIndex }) {
      // 合并第一列的日期时间单元格
      if (columnIndex === 0) {
        if (rowIndex === 0) {
          return {
            rowspan: this.tableData.length,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },

    onStandardAnswerChange(stationName, elementKey, value) {
      if (!this.standardAnswers[stationName]) {
        this.$set(this.standardAnswers, stationName, {})
      }

      // 特殊处理灾害性天气类型的多选逻辑
      if (elementKey === 'disasterWeather') {
        // 如果选择了"无"，清空其他选项
        if (Array.isArray(value) && value.includes('无')) {
          if (value.length > 1) {
            // 如果同时选择了"无"和其他选项，只保留"无"
            value = ['无']
          }
        }
        // 如果选择了其他选项，移除"无"
        else if (Array.isArray(value) && value.length > 0 && !value.includes('无')) {
          value = value.filter(item => item !== '无')
        }
      }

      this.$set(this.standardAnswers[stationName], elementKey, value)

      // 触发标准答案变更事件
      this.$emit('standard-answer-change', {
        stationName,
        elementKey,
        value,
        allStandardAnswers: this.standardAnswers
      })
    },

    async saveStandardAnswers() {
      this.saving = true
      try {
        // 触发保存事件
        this.$emit('standard-answer-save', this.standardAnswers)
        this.$message.success('标准答案保存成功')
      } catch (error) {
        this.$message.error('保存标准答案失败：' + (error.message || '未知错误'))
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style scoped>
/* 复用 WeatherForecastTable 的样式 */
.weather-standard-answer-table {
  padding: 0;
  width: 100%;
}

.question-header {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #67c23a;
}

.question-header h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 18px;
}

.question-content {
  margin: 0 0 15px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.6;
}

.forecast-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.forecast-table {
  margin-bottom: 20px;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.input-cell .el-select,
.input-cell .el-input-number,
.input-cell .el-input {
  width: 100%;
  min-height: 36px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}

.action-buttons .el-button {
  min-width: 120px;
}
</style>
