<template>
  <div class="weather-forecast-table">
    <!-- 题目信息 -->
    <div class="question-header">
      <h3>{{ question.title }}</h3>
      <p class="question-content">{{ question.content }}</p>
      <div class="forecast-info">
        <span>预报起报日期及时间：{{ question.forecastDate }} {{ question.forecastTime }}</span>
      </div>
    </div>

    <!-- 天气预报表格 -->
    <el-table
      :data="tableData"
      :span-method="spanMethod"
      :max-height="600"
      :row-style="{ height: '50px' }"
      :cell-style="{ padding: '6px 8px' }"
      border
      class="forecast-table"
      style="width: 100%;"
    >
      <!-- 预报起报日期及时间列 -->
      <el-table-column
        prop="dateTime"
        label="预报起报日期及时间"
        min-width="150"
        align="center"
      />

      <!-- 站名站号列 -->
      <el-table-column
        prop="stationName"
        label="站名站号"
        min-width="120"
        align="center"
      />

      <!-- 08-08最大风力(级) -->
      <el-table-column
        label="08-08最大风力(级)"
        min-width="140"
        align="center"
      >
        <template v-slot="scope">
          <div v-if="!readonly" class="input-cell">
            <el-select
              v-model="scope.row.answers.windForce"
              size="small"
              placeholder="请选择"
              @change="onAnswerChange(scope.row.stationName, 'windForce', $event)"
            >
              <el-option label="静风" value="静风" />
              <el-option label="一级" value="一级" />
              <el-option label="二级" value="二级" />
              <el-option label="三级" value="三级" />
              <el-option label="四级" value="四级" />
              <el-option label="五级" value="五级" />
              <el-option label="六级" value="六级" />
              <el-option label="七级" value="七级" />
              <el-option label="八级" value="八级" />
              <el-option label="九级" value="九级" />
              <el-option label="十级" value="十级" />
              <el-option label="十一级" value="十一级" />
              <el-option label="十二级" value="十二级" />
            </el-select>
          </div>
          <div v-else class="display-cell">
            {{ scope.row.answers.windForce || '-' }}
          </div>
        </template>
      </el-table-column>

      <!-- 08-08最大风力时的风向 -->
      <el-table-column
        label="08-08最大风力时的风向"
        min-width="160"
        align="center"
      >
        <template v-slot="scope">
          <div v-if="!readonly" class="input-cell">
            <el-select
              v-model="scope.row.answers.windDirection"
              size="small"
              placeholder="请选择"
              @change="onAnswerChange(scope.row.stationName, 'windDirection', $event)"
            >
              <el-option label="北" value="北" />
              <el-option label="东北" value="东北" />
              <el-option label="东" value="东" />
              <el-option label="东南" value="东南" />
              <el-option label="南" value="南" />
              <el-option label="西南" value="西南" />
              <el-option label="西" value="西" />
              <el-option label="西北" value="西北" />
            </el-select>
          </div>
          <div v-else class="display-cell">
            {{ scope.row.answers.windDirection || '-' }}
          </div>
        </template>
      </el-table-column>

      <!-- 最低气温℃ -->
      <el-table-column
        label="最低气温℃"
        min-width="120"
        align="center"
      >
        <template v-slot="scope">
          <div v-if="!readonly" class="input-cell">
            <el-input-number
              v-model="scope.row.answers.minTemperature"
              :min="-50"
              :max="50"
              :precision="0"
              size="small"
              @change="onAnswerChange(scope.row.stationName, 'minTemperature', $event)"
            />
          </div>
          <div v-else class="display-cell">
            {{ scope.row.answers.minTemperature || '-' }}
          </div>
        </template>
      </el-table-column>

      <!-- 最高气温℃ -->
      <el-table-column
        label="最高气温℃"
        min-width="120"
        align="center"
      >
        <template v-slot="scope">
          <div v-if="!readonly" class="input-cell">
            <el-input-number
              v-model="scope.row.answers.maxTemperature"
              :min="-50"
              :max="50"
              :precision="0"
              size="small"
              @change="onAnswerChange(scope.row.stationName, 'maxTemperature', $event)"
            />
          </div>
          <div v-else class="display-cell">
            {{ scope.row.answers.maxTemperature || '-' }}
          </div>
        </template>
      </el-table-column>

      <!-- 08-08降水(雨、雪)量级 -->
      <el-table-column
        label="08-08降水(雨、雪)量级"
        min-width="170"
        align="center"
      >
        <template v-slot="scope">
          <div v-if="!readonly" class="input-cell">
            <el-select
              v-model="scope.row.answers.precipitation"
              size="small"
              placeholder="请选择"
              @change="onAnswerChange(scope.row.stationName, 'precipitation', $event)"
            >
              <el-option label="无雨雪" value="无雨雪" />
              <el-option label="雨夹雪" value="雨夹雪" />
              <el-option label="小雨" value="小雨" />
              <el-option label="小雪" value="小雪" />
              <el-option label="中雨雪" value="中雨雪" />
              <el-option label="中雨" value="中雨" />
              <el-option label="中雪" value="中雪" />
              <el-option label="大雨雪" value="大雨雪" />
              <el-option label="大雨" value="大雨" />
              <el-option label="大雪" value="大雪" />
              <el-option label="暴雨" value="暴雨" />
              <el-option label="大暴雨以上" value="大暴雨以上" />
              <el-option label="暴雪" value="暴雪" />
            </el-select>
          </div>
          <div v-else class="display-cell">
            {{ scope.row.answers.precipitation || '-' }}
          </div>
        </template>
      </el-table-column>

      <!-- 灾害性天气类型 -->
      <el-table-column
        label="灾害性天气类型"
        min-width="140"
        align="center"
      >
        <template v-slot="scope">
          <div v-if="!readonly" class="input-cell">
            <el-select
              v-model="scope.row.answers.disasterWeather"
              size="small"
              multiple
              collapse-tags
              placeholder="请选择"
              style="width: 100%;"
              @change="onAnswerChange(scope.row.stationName, 'disasterWeather', $event)"
            >
              <el-option label="无" value="无" />
              <el-option label="暴雨" value="暴雨" />
              <el-option label="暴雪" value="暴雪" />
              <el-option label="高温" value="高温" />
              <el-option label="大风" value="大风" />
              <el-option label="寒潮" value="寒潮" />
              <el-option label="大雾" value="大雾" />
              <el-option label="沙尘暴" value="沙尘暴" />
              <el-option label="雷暴" value="雷暴" />
              <el-option label="冰雹" value="冰雹" />
            </el-select>
          </div>
          <div v-else class="display-cell">
            <span v-if="Array.isArray(scope.row.answers.disasterWeather) && scope.row.answers.disasterWeather.length > 0">
              {{ scope.row.answers.disasterWeather.join(', ') }}
            </span>
            <span v-else-if="scope.row.answers.disasterWeather && !Array.isArray(scope.row.answers.disasterWeather)">
              {{ scope.row.answers.disasterWeather }}
            </span>
            <span v-else>-</span>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 阅卷结果 -->
    <div v-if="readonly && showScore" class="score-result">
      <h4>评分结果</h4>
      <el-table :data="scoreData" border size="small">
        <el-table-column prop="stationName" label="站点" width="100" />
        <el-table-column prop="score" label="得分" width="80" />
        <el-table-column prop="maxScore" label="满分" width="80" />
        <el-table-column prop="details" label="详情" />
      </el-table>
      <p class="total-score">总分：{{ totalScore }}/{{ maxTotalScore }}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WeatherForecastTable',
  props: {
    // 题目信息
    question: {
      type: Object,
      required: true
    },
    // 初始答案数据
    initialAnswers: {
      type: Object,
      default: () => ({})
    },
    // 是否只读模式（阅卷时使用）
    readonly: {
      type: Boolean,
      default: false
    },
    // 是否显示分数
    showScore: {
      type: Boolean,
      default: false
    },
    // 评分数据
    scoreData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tableData: [],
      answers: {},
      stations: []
    }
  },
  computed: {
    answeredCount() {
      let count = 0
      const stations = this.stations
      const elements = ['windForce0808', 'windDirection0808', 'minTemperature', 'maxTemperature', 'precipitation0808', 'disasterWeather']

      stations.forEach(station => {
        elements.forEach(element => {
          const answer = this.answers[station] && this.answers[station][element]
          // 特殊处理灾害性天气类型的数组格式
          if (element === 'disasterWeather') {
            if (Array.isArray(answer) && answer.length > 0) {
              count++
            } else if (answer && !Array.isArray(answer)) {
              count++
            }
          } else {
            if (answer) {
              count++
            }
          }
        })
      })
      return count
    },
    totalCount() {
      const stations = this.stations
      const elements = ['windForce0808', 'windDirection0808', 'minTemperature', 'maxTemperature', 'precipitation0808', 'disasterWeather']
      return stations.length * elements.length
    },
    progressPercentage() {
      if (this.totalCount === 0) return 0
      const percentage = Math.round((this.answeredCount / this.totalCount) * 100)
      return Math.max(0, Math.min(100, percentage)) // 确保在0-100范围内
    },
    totalScore() {
      return this.scoreData.reduce((sum, item) => sum + (item.score || 0), 0)
    },
    maxTotalScore() {
      return this.scoreData.reduce((sum, item) => sum + (item.maxScore || 0), 0)
    }
  },
  watch: {
    question: {
      handler() {
        this.initTable()
      },
      immediate: true
    },
    initialAnswers: {
      handler() {
        this.loadAnswers()
      },
      immediate: true
    }
  },
  methods: {
    initTable() {
      if (!this.question) return

      // 解析站点列表
      try {
        this.stations = JSON.parse(this.question.stations || '[]')
      } catch (e) {
        console.error('解析站点列表失败:', e)
        this.stations = []
      }

      this.tableData = this.stations.map(station => {
        const stationAnswers = this.answers[station] || {}

        // 确保灾害性天气类型为数组格式
        if (stationAnswers.disasterWeather && !Array.isArray(stationAnswers.disasterWeather)) {
          stationAnswers.disasterWeather = [stationAnswers.disasterWeather]
        } else if (!stationAnswers.disasterWeather) {
          stationAnswers.disasterWeather = []
        }

        const tableRow = {
          dateTime: this.question.forecastDate + ' ' + this.question.forecastTime,
          stationName: station,
          isHeader: false,
          answers: stationAnswers
        }

        return tableRow
      })
    },

    loadAnswers() {
      console.log('WeatherForecastTable loadAnswers - initialAnswers:', this.initialAnswers)

      // 将 initialAnswers 的数据加载到组件的内部状态
      // 处理 Vue 响应式对象，需要检查实际的数据内容
      const hasData = this.initialAnswers &&
                     typeof this.initialAnswers === 'object' &&
                     Object.keys(this.initialAnswers).length > 0 &&
                     // 排除只有 __ob__ 属性的空响应式对象
                     Object.keys(this.initialAnswers).some(key => key !== '__ob__')

      if (hasData) {
        // 深拷贝 initialAnswers 到 this.answers
        this.answers = JSON.parse(JSON.stringify(this.initialAnswers))

        // 确保每个站点的灾害性天气类型为数组格式
        Object.keys(this.answers).forEach(stationName => {
          if (this.answers[stationName] && this.answers[stationName].disasterWeather) {
            if (!Array.isArray(this.answers[stationName].disasterWeather)) {
              this.answers[stationName].disasterWeather = [this.answers[stationName].disasterWeather]
            }
          }
        })
        console.log('WeatherForecastTable 答案加载完成，站点数:', Object.keys(this.answers).length)
        console.log('加载完成后的 this.answers:', this.answers)
      } else {
        console.log('WeatherForecastTable initialAnswers 为空或无有效数据')
      }

      console.log('loadAnswers 即将调用 initTable，当前 this.answers:', this.answers)
      this.initTable()
    },

    spanMethod({ row, column, rowIndex, columnIndex }) {
      // 合并第一列的日期时间单元格
      if (columnIndex === 0) {
        if (rowIndex === 0) {
          return {
            rowspan: this.tableData.length,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },

    onAnswerChange(stationName, elementKey, value) {
      if (!this.answers[stationName]) {
        this.$set(this.answers, stationName, {})
      }

      // 特殊处理灾害性天气类型的多选逻辑
      if (elementKey === 'disasterWeather') {
        // 如果选择了"无"，清空其他选项
        if (Array.isArray(value) && value.includes('无')) {
          if (value.length > 1) {
            // 如果同时选择了"无"和其他选项，只保留"无"
            value = ['无']
          }
        }
        // 如果选择了其他选项，移除"无"
        else if (Array.isArray(value) && value.length > 0 && !value.includes('无')) {
          value = value.filter(item => item !== '无')
        }
      }

      this.$set(this.answers[stationName], elementKey, value)

      // 触发答案变更事件
      this.$emit('answer-change', {
        stationName,
        elementKey,
        value,
        allAnswers: this.answers
      })
    },

    submitAnswers() {
      // 先保存答案
      this.$emit('answer-save', this.answers)

      // 然后触发考试提交（直接使用原始答案）
      this.$emit('exam-submit', this.answers)
    },

    // 移除风向转换方法，考生答案直接存储八方位

    // 风力转换：将考生的风力等级转换为标准格式
    convertWindForceToStandard(force) {
      const forceMap = {
        '静风': '静风',
        '一级': '一级',
        '二级': '二级',
        '三级': '三级',
        '四级': '四级',
        '五级': '五级',
        '六级': '六级',
        '七级': '七级',
        '八级': '八级',
        '九级': '九级',
        '十级': '十级',
        '十一级': '十一级',
        '十二级': '十二级'
      }
      return forceMap[force] || force
    },

    // 获取答案数据（直接返回原始答案，不进行转换）
    getAnswersForSubmit() {
      // 直接返回考生的原始答案，包含八方位风向和风力等级
      return this.answers
    }

  }
}
</script>

<style scoped>
.weather-forecast-table {
  padding: 0;
  width: 100%;
}

.question-header {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.question-header h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 18px;
}

.question-content {
  margin: 0 0 15px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.6;
}

.forecast-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.forecast-table {
  margin-bottom: 20px;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.forecast-table .el-table__body-wrapper {
  overflow-x: auto;
}

.forecast-table .el-table__header-wrapper {
  overflow-x: auto;
}

/* 确保表格占满容器宽度 */
.forecast-table .el-table {
  width: 100% !important;
  min-width: 1000px; /* 增加最小宽度 */
  table-layout: auto; /* 允许列宽自动调整 */
}

/* 调整表格行高为更合适的尺寸 */
.forecast-table .el-table__row {
  height: 50px !important;
}

/* 优化表格单元格样式 */
.forecast-table .el-table td,
.forecast-table .el-table th {
  padding: 6px 5px !important;
  vertical-align: middle;
  height: 50px !important;
}

.forecast-table .el-table td {
  padding: 0 !important;
}

/* 优化表头样式 */
.forecast-table .el-table__header-wrapper .el-table__header th {
  background-color: #f8f9fa;
  color: #333;
  font-weight: 600;
  font-size: 14px;
}

.header-cell {
  font-weight: bold;
  background-color: #f5f7fa;
}

.input-cell {
  padding: 0 !important;
  margin: 0 !important;
  height: 50px !important;
  width: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-sizing: border-box !important;
}

.input-cell .el-select,
.input-cell .el-input-number,
.input-cell .el-input {
  width: 100% !important;
  height: 38px !important;
  margin: 0 !important;
  padding: 0 !important;
}

.input-cell .el-select .el-input,
.input-cell .el-input-number .el-input {
  height: 38px !important;
  margin: 0 !important;
}

.input-cell .el-select .el-input__inner,
.input-cell .el-input-number .el-input__inner,
.input-cell .el-input .el-input__inner {
  height: 38px !important;
  line-height: 38px !important;
  margin: 0 !important;
  padding: 0 30px 0 15px !important;
  text-align: center !important;
  font-size: 14px !important;
}

/* 修复small尺寸组件的空白问题 */
.input-cell .el-input-number--small,
.input-cell .el-select--small,
.input-cell .el-input--small {
  height: 42px !important;
  margin: 0 !important;
  padding: 0 !important;
}

.input-cell .el-input-number--small .el-input,
.input-cell .el-select--small .el-input {
  height: 42px !important;
  margin: 0 !important;
}

.input-cell .el-input-number--small .el-input__inner,
.input-cell .el-select--small .el-input__inner,
.input-cell .el-input--small .el-input__inner {
  height: 42px !important;
  line-height: 42px !important;
  font-size: 14px !important;
  margin: 0 !important;
}

  .input-cell .el-input-number--small .el-input-number__increase,
  .input-cell .el-input-number--small .el-input-number__decrease {
    height: 19px !important;
    line-height: 17px !important;
    width: 26px !important;
  }

/* 强制输入框填充整个单元格，消除所有空白 */
.input-cell > * {
  flex: 1 !important;
  width: 100% !important;
  height: 38px !important;
  margin: 0 !important;
  padding: 0 5px !important;
  box-sizing: border-box !important;
}

/* 确保数字输入框内部没有额外边距 */
.input-cell .el-input-number {
  width: 100% !important;
  height: 38px !important;
}

.input-cell .el-input-number .el-input {
  width: 100% !important;
  height: 38px !important;
  margin: 0 !important;
}

/* 多选标签样式优化 */
.input-cell .el-select.el-select--multiple .el-input__inner {
  height: auto;
  min-height: 36px;
  padding: 2px 25px 2px 8px;
}

.input-cell .el-select.el-select--multiple .el-tag {
  margin: 2px 4px 2px 0;
  max-width: calc(100% - 30px);
}

.input-cell .el-select.el-select--multiple .el-tag .el-tag__close {
  color: #409eff;
}

/* 多选下拉框样式 */
.input-cell .el-select-dropdown__item {
  padding: 8px 12px;
  font-size: 13px;
}

.display-cell {
  padding: 5px;
  text-align: center;
}

.answer-status {
  text-align: center;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-top: 20px;
}

.answer-status .progress-info {
  margin-bottom: 15px;
}

.answer-status .progress-text {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  font-size: 14px;
  color: #606266;
}

.answer-status .progress-percentage {
  color: #409eff;
  font-weight: bold;
}

.answer-status .status-indicators {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
}

.score-result {
  margin-top: 20px;
  padding: 20px;
  background-color: #f0f9ff;
  border-radius: 4px;
}

.total-score {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
  margin-top: 10px;
}
</style>
