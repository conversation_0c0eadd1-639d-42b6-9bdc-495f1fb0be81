<template>
  <div class="weather-table-editor">
    <div class="table-header">
      <h3>{{ tableConfig.title || '天气预报表格' }}</h3>
      <p v-if="tableConfig.description" class="table-description">{{ tableConfig.description }}</p>
    </div>

    <div class="table-container">
      <el-table
        :data="tableData"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        border
        stripe
        class="weather-table"
      >
        <!-- 预报起报日期及时间列 -->
        <el-table-column
          prop="forecastTime"
          label="预报起报日期及时间"
          width="180"
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.forecastTime }}</span>
          </template>
        </el-table-column>

        <!-- 站名站号列 -->
        <el-table-column
          prop="stationInfo"
          label="站名站号"
          width="120"
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.stationInfo }}</span>
          </template>
        </el-table-column>

        <!-- 最大风力列 -->
        <el-table-column
          label="08—08最大风力(级)"
          width="150"
          align="center"
        >
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.maxWindForce"
              placeholder="请选择"
              size="small"
              @change="onCellChange(scope.$index, 'maxWindForce', scope.row.maxWindForce)"
            >
              <el-option label="0" value="0" />
              <el-option label="1" value="1" />
              <el-option label="2" value="2" />
              <el-option label="3-4" value="3-4" />
              <el-option label="5-6" value="5-6" />
              <el-option label="7-8" value="7-8" />
              <el-option label="9-10" value="9-10" />
              <el-option label="11-12" value="11-12" />
            </el-select>
          </template>
        </el-table-column>

        <!-- 风向列 -->
        <el-table-column
          label="08—08最大风力时的风向"
          width="180"
          align="center"
        >
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.windDirection"
              placeholder="请选择"
              size="small"
              @change="onCellChange(scope.$index, 'windDirection', scope.row.windDirection)"
            >
              <el-option label="北" value="北" />
              <el-option label="东北" value="东北" />
              <el-option label="东" value="东" />
              <el-option label="东南" value="东南" />
              <el-option label="南" value="南" />
              <el-option label="西南" value="西南" />
              <el-option label="西" value="西" />
              <el-option label="西北" value="西北" />
              <el-option label="无风" value="无风" />
            </el-select>
          </template>
        </el-table-column>

        <!-- 最低气温列 -->
        <el-table-column
          label="最低气温℃"
          width="120"
          align="center"
        >
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.minTemp"
              :min="-50"
              :max="50"
              size="small"
              @change="onCellChange(scope.$index, 'minTemp', scope.row.minTemp)"
            />
          </template>
        </el-table-column>

        <!-- 最高气温列 -->
        <el-table-column
          label="最高气温℃"
          width="120"
          align="center"
        >
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.maxTemp"
              :min="-50"
              :max="50"
              size="small"
              @change="onCellChange(scope.$index, 'maxTemp', scope.row.maxTemp)"
            />
          </template>
        </el-table-column>

        <!-- 降水量级列 -->
        <el-table-column
          label="08—08降水(雨、雪)量级"
          width="180"
          align="center"
        >
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.precipitation"
              placeholder="请选择"
              size="small"
              @change="onCellChange(scope.$index, 'precipitation', scope.row.precipitation)"
            >
              <el-option label="无" value="无" />
              <el-option label="小雨" value="小雨" />
              <el-option label="中雨" value="中雨" />
              <el-option label="大雨" value="大雨" />
              <el-option label="暴雨" value="暴雨" />
              <el-option label="大暴雨" value="大暴雨" />
              <el-option label="特大暴雨" value="特大暴雨" />
              <el-option label="小雪" value="小雪" />
              <el-option label="中雪" value="中雪" />
              <el-option label="大雪" value="大雪" />
              <el-option label="暴雪" value="暴雪" />
            </el-select>
          </template>
        </el-table-column>

        <!-- 灾害性天气类型列 -->
        <el-table-column
          label="灾害性天气类型"
          width="200"
          align="center"
        >
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.disasterWeather"
              multiple
              placeholder="请选择"
              size="small"
              @change="onCellChange(scope.$index, 'disasterWeather', scope.row.disasterWeather)"
            >
              <el-option label="无" value="无" />
              <el-option label="雷暴" value="雷暴" />
              <el-option label="冰雹" value="冰雹" />
              <el-option label="大风" value="大风" />
              <el-option label="龙卷风" value="龙卷风" />
              <el-option label="暴雨" value="暴雨" />
              <el-option label="暴雪" value="暴雪" />
              <el-option label="沙尘暴" value="沙尘暴" />
              <el-option label="雾" value="雾" />
              <el-option label="霾" value="霾" />
            </el-select>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="table-footer">
      <el-button type="primary" size="small" @click="validateData">验证数据</el-button>
      <el-button type="success" size="small" @click="saveData">保存答案</el-button>
      <span v-if="validationMessage" :class="validationClass">{{ validationMessage }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WeatherTableEditor',
  props: {
    // 表格配置
    tableConfig: {
      type: Object,
      default: () => ({
        title: '天气预报表格',
        description: '',
        rows: 6,
        timeInterval: 4
      })
    },
    // 初始数据
    initialData: {
      type: Object,
      default: () => ({})
    },
    // 是否只读
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableData: [],
      validationMessage: '',
      validationClass: ''
    }
  },
  watch: {
    initialData: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.loadInitialData(newVal)
        }
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.initTableData()
  },
  methods: {
    // 初始化表格数据
    initTableData() {
      const rows = this.tableConfig.rows || 6
      const baseTime = new Date('2035-09-09 08:00:00')

      this.tableData = []
      for (let i = 0; i < rows; i++) {
        this.tableData.push({
          forecastTime: this.formatDateTime(baseTime),
          stationInfo: '站点',
          maxWindForce: '',
          windDirection: '',
          minTemp: null,
          maxTemp: null,
          precipitation: '',
          disasterWeather: []
        })
      }
    },

    // 加载初始数据
    loadInitialData(data) {
      if (data.cellData) {
        Object.keys(data.cellData).forEach((rowKey, index) => {
          if (this.tableData[index]) {
            const rowData = data.cellData[rowKey]
            Object.assign(this.tableData[index], rowData)
          }
        })
      }
    },

    // 格式化日期时间
    formatDateTime(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hour = String(date.getHours()).padStart(2, '0')
      return `${year}年${month}月${day}日${hour}时`
    },

    // 单元格数据变化处理
    onCellChange(rowIndex, field, value) {
      this.$emit('cell-change', {
        rowIndex,
        field,
        value,
        rowData: this.tableData[rowIndex]
      })

      // 实时验证
      this.validateCell(rowIndex, field, value)
    },

    // 验证单个单元格
    validateCell(rowIndex, field, value) {
      const row = this.tableData[rowIndex]

      // 温度逻辑验证
      if (field === 'minTemp' || field === 'maxTemp') {
        if (row.minTemp !== null && row.maxTemp !== null) {
          if (row.minTemp > row.maxTemp) {
            this.showValidationMessage('警告：最低气温不应高于最高气温', 'warning')
          }
        }
      }

      // 降水与灾害天气一致性验证
      if (field === 'precipitation' || field === 'disasterWeather') {
        if (row.precipitation && row.precipitation !== '无') {
          if (!row.disasterWeather.includes('暴雨') &&
              (row.precipitation.includes('暴雨') || row.precipitation.includes('大雨'))) {
            this.showValidationMessage('提示：强降水可能伴随暴雨灾害', 'info')
          }
        }
      }
    },

    // 验证整个表格数据
    validateData() {
      let isValid = true
      const messages = []

      this.tableData.forEach((row, index) => {
        // 检查必填项
        if (!row.maxWindForce || !row.windDirection || !row.precipitation) {
          messages.push(`第${index + 1}行：请填写完整的预报要素`)
          isValid = false
        }

        // 检查温度合理性
        if (row.minTemp !== null && row.maxTemp !== null) {
          if (row.minTemp > row.maxTemp) {
            messages.push(`第${index + 1}行：最低气温不应高于最高气温`)
            isValid = false
          }
        }
      })

      if (isValid) {
        this.showValidationMessage('数据验证通过', 'success')
      } else {
        this.showValidationMessage(messages.join('; '), 'error')
      }

      return isValid
    },

    // 显示验证消息
    showValidationMessage(message, type) {
      this.validationMessage = message
      this.validationClass = `validation-${type}`

      setTimeout(() => {
        this.validationMessage = ''
        this.validationClass = ''
      }, 5000)
    },

    // 保存数据
    saveData() {
      if (this.validateData()) {
        const cellData = {}
        this.tableData.forEach((row, index) => {
          cellData[`row_${index}`] = { ...row }
        })

        this.$emit('save', {
          cellData,
          answerStatus: true,
          validationResult: {
            isValid: true,
            errors: [],
            warnings: []
          }
        })
      }
    },

    // 获取当前数据
    getCurrentData() {
      const cellData = {}
      this.tableData.forEach((row, index) => {
        cellData[`row_${index}`] = { ...row }
      })
      return cellData
    }
  }
}
</script>

<style scoped lang="scss">
.weather-table-editor {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);

  .table-header {
    margin-bottom: 20px;
    text-align: center;

    h3 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 18px;
      font-weight: 600;
    }

    .table-description {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .table-container {
    margin-bottom: 20px;

    .weather-table {
      width: 100%;

      ::v-deep .el-table__header-wrapper {
        .el-table__header {
          th {
            background-color: #f5f7fa !important;
            color: #606266 !important;
            font-weight: 600;
          }
        }
      }

      ::v-deep .el-table__body-wrapper {
        .el-table__row {
          &:hover {
            background-color: #f5f7fa;
          }
        }
      }

      .el-select,
      .el-input-number {
        width: 100%;
      }
    }
  }

  .table-footer {
    display: flex;
    align-items: center;
    gap: 15px;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;

    .validation-success {
      color: #67c23a;
      font-size: 14px;
    }

    .validation-warning {
      color: #e6a23c;
      font-size: 14px;
    }

    .validation-error {
      color: #f56c6c;
      font-size: 14px;
    }

    .validation-info {
      color: #409eff;
      font-size: 14px;
    }
  }
}
</style>
