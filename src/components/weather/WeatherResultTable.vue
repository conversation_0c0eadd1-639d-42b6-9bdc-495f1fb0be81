<template>
  <div class="weather-result-table">
    <!-- 表格头部信息 -->
    <div class="table-header">
      <h3>天气预报结果对比</h3>
      <div class="header-info">
        <span>预报起报日期及时间：{{ question.forecastDate }} {{ question.forecastTime }}</span>
      </div>
    </div>

    <!-- 结果对比表格 -->
    <el-table
      :data="tableData"
      :span-method="spanMethod"
      :max-height="600"
      :row-style="{ height: '60px' }"
      :cell-style="cellStyle"
      border
      class="result-table"
      style="width: 100%;"
    >
      <!-- 预报起报日期及时间列 -->
      <el-table-column
        prop="dateTime"
        label="预报起报日期及时间"
        min-width="150"
        align="center"
      />

      <!-- 站名站号列 -->
      <el-table-column
        prop="stationName"
        label="站名站号"
        min-width="120"
        align="center"
      />

      <!-- 08-08最大风力(级) -->
      <el-table-column
        label="08-08最大风力(级)"
        min-width="160"
        align="center"
      >
        <template v-slot="scope">
          <div class="result-cell">
            <div class="answer-row user-answer">
              <span class="answer-label">您的答案：</span>
              <span :class="getAnswerClass('windForce', scope.row.stationName)">
                {{ getUserAnswer(scope.row.stationName, 'windForce') || '-' }}
              </span>
            </div>
            <div class="answer-row standard-answer">
              <span class="answer-label">标准答案：</span>
              <span class="standard-value">
                {{ getStandardAnswer(scope.row.stationName, 'windForce') || '-' }}
              </span>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 08-08最大风力时的风向 -->
      <el-table-column
        label="08-08最大风力时的风向"
        min-width="180"
        align="center"
      >
        <template v-slot="scope">
          <div class="result-cell">
            <div class="answer-row user-answer">
              <span class="answer-label">您的答案：</span>
              <span :class="getAnswerClass('windDirection', scope.row.stationName)">
                {{ getUserAnswer(scope.row.stationName, 'windDirection') || '-' }}
              </span>
            </div>
            <div class="answer-row standard-answer">
              <span class="answer-label">标准答案：</span>
              <span class="standard-value">
                {{ getStandardAnswer(scope.row.stationName, 'windDirection') || '-' }}
              </span>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 最低气温℃ -->
      <el-table-column
        label="最低气温℃"
        min-width="140"
        align="center"
      >
        <template v-slot="scope">
          <div class="result-cell">
            <div class="answer-row user-answer">
              <span class="answer-label">您的答案：</span>
              <span :class="getAnswerClass('minTemperature', scope.row.stationName)">
                {{ getUserAnswer(scope.row.stationName, 'minTemperature') || '-' }}
                <span v-if="getUserAnswer(scope.row.stationName, 'minTemperature')">℃</span>
              </span>
            </div>
            <div class="answer-row standard-answer">
              <span class="answer-label">标准答案：</span>
              <span class="standard-value">
                {{ getStandardAnswer(scope.row.stationName, 'minTemperature') || '-' }}
                <span v-if="getStandardAnswer(scope.row.stationName, 'minTemperature')">℃</span>
              </span>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 最高气温℃ -->
      <el-table-column
        label="最高气温℃"
        min-width="140"
        align="center"
      >
        <template v-slot="scope">
          <div class="result-cell">
            <div class="answer-row user-answer">
              <span class="answer-label">您的答案：</span>
              <span :class="getAnswerClass('maxTemperature', scope.row.stationName)">
                {{ getUserAnswer(scope.row.stationName, 'maxTemperature') || '-' }}
                <span v-if="getUserAnswer(scope.row.stationName, 'maxTemperature')">℃</span>
              </span>
            </div>
            <div class="answer-row standard-answer">
              <span class="answer-label">标准答案：</span>
              <span class="standard-value">
                {{ getStandardAnswer(scope.row.stationName, 'maxTemperature') || '-' }}
                <span v-if="getStandardAnswer(scope.row.stationName, 'maxTemperature')">℃</span>
              </span>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 08-08降水(雨、雪)量级 -->
      <el-table-column
        label="08-08降水(雨、雪)量级"
        min-width="180"
        align="center"
      >
        <template v-slot="scope">
          <div class="result-cell">
            <div class="answer-row user-answer">
              <span class="answer-label">您的答案：</span>
              <span :class="getAnswerClass('precipitation', scope.row.stationName)">
                {{ getUserAnswer(scope.row.stationName, 'precipitation') || '-' }}
              </span>
            </div>
            <div class="answer-row standard-answer">
              <span class="answer-label">标准答案：</span>
              <span class="standard-value">
                {{ getStandardAnswer(scope.row.stationName, 'precipitation') || '-' }}
              </span>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 08-08灾害性天气现象 -->
      <el-table-column
        label="08-08灾害性天气现象"
        min-width="200"
        align="center"
      >
        <template v-slot="scope">
          <div class="result-cell">
            <div class="answer-row user-answer">
              <span class="answer-label">您的答案：</span>
              <div class="disaster-weather-list">
                <el-tag
                  v-for="weather in getUserDisasterWeather(scope.row.stationName)"
                  :key="weather"
                  :type="isCorrectDisasterWeather(scope.row.stationName, weather) ? 'success' : 'danger'"
                  size="mini"
                  class="weather-tag"
                >
                  {{ weather }}
                </el-tag>
                <span v-if="!getUserDisasterWeather(scope.row.stationName).length" class="no-answer">-</span>
              </div>
            </div>
            <div class="answer-row standard-answer">
              <span class="answer-label">标准答案：</span>
              <div class="disaster-weather-list">
                <el-tag
                  v-for="weather in getStandardDisasterWeather(scope.row.stationName)"
                  :key="weather"
                  size="mini"
                  type="info"
                  class="weather-tag"
                >
                  {{ weather }}
                </el-tag>
                <span v-if="!getStandardDisasterWeather(scope.row.stationName).length" class="no-answer">无</span>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>

  </div>
</template>

<script>
export default {
  name: 'WeatherResultTable',
  props: {
    question: {
      type: Object,
      default: () => ({})
    },
    userAnswers: {
      type: Object,
      default: () => ({})
    },
    standardAnswers: {
      type: Object,
      default: () => ({})
    },
    scoringResult: {
      type: Object,
      default: () => ({})
    },
    readonly: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    // 表格数据
    tableData() {
      const stations = this.getStations()
      const dateTime = `${this.question.forecastDate} ${this.question.forecastTime}`

      return stations.map(station => ({
        dateTime: dateTime,
        stationName: station
      }))
    }
  },
  methods: {
    // 获取站点列表
    getStations() {
      try {
        if (this.question.stations) {
          const stations = JSON.parse(this.question.stations)
          return Array.isArray(stations) ? stations : []
        }
        return []
      } catch (error) {
        console.warn('解析站点数据失败:', error)
        return []
      }
    },

    // 获取用户答案
    getUserAnswer(stationName, element) {
      if (this.userAnswers[stationName] && this.userAnswers[stationName][element] !== undefined) {
        return this.userAnswers[stationName][element]
      }
      return null
    },

    // 获取标准答案
    getStandardAnswer(stationName, element) {
      if (this.standardAnswers[stationName] && this.standardAnswers[stationName][element] !== undefined) {
        return this.standardAnswers[stationName][element]
      }
      return null
    },

    // 获取用户灾害天气答案
    getUserDisasterWeather(stationName) {
      const answer = this.getUserAnswer(stationName, 'disasterWeather')
      if (Array.isArray(answer)) {
        return answer.filter(item => item && item !== '无')
      }
      if (typeof answer === 'string' && answer && answer !== '无') {
        return [answer]
      }
      return []
    },

    // 获取标准灾害天气答案
    getStandardDisasterWeather(stationName) {
      const answer = this.getStandardAnswer(stationName, 'disasterWeather')
      if (Array.isArray(answer)) {
        return answer.filter(item => item && item !== '无')
      }
      if (typeof answer === 'string' && answer && answer !== '无') {
        return [answer]
      }
      return []
    },

    // 判断灾害天气是否正确
    isCorrectDisasterWeather(stationName, weather) {
      const standardWeathers = this.getStandardDisasterWeather(stationName)
      return standardWeathers.includes(weather)
    },

    // 获取答案样式类
    getAnswerClass(element, stationName) {
      const userAnswer = this.getUserAnswer(stationName, element)
      const standardAnswer = this.getStandardAnswer(stationName, element)

      if (!userAnswer && userAnswer !== 0) {
        return 'no-answer'
      }

      if (userAnswer === standardAnswer) {
        return 'correct-answer'
      }

      // 对于温度，检查是否在容差范围内
      if ((element === 'minTemperature' || element === 'maxTemperature') &&
          typeof userAnswer === 'number' && typeof standardAnswer === 'number') {
        const diff = Math.abs(userAnswer - standardAnswer)
        if (diff <= 2) {
          return 'correct-answer'
        }
      }

      return 'incorrect-answer'
    },

    // 表格合并方法
    spanMethod({ row, column, rowIndex, columnIndex }) {
      // 第一列（预报起报日期及时间）合并所有行
      if (columnIndex === 0) {
        if (rowIndex === 0) {
          return {
            rowspan: this.tableData.length,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },

    // 单元格样式
    cellStyle({ row, column, rowIndex, columnIndex }) {
      const baseStyle = {
        padding: '8px 5px',
        'vertical-align': 'middle'
      }

      // 第一列特殊样式
      if (columnIndex === 0) {
        return {
          ...baseStyle,
          'background-color': '#f8f9fa',
          'font-weight': '600',
          'text-align': 'center'
        }
      }

      return baseStyle
    }
  }
}
</script>

<style scoped>
.weather-result-table {
  width: 100%;
}

.table-header {
  margin-bottom: 20px;
  padding: 15px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 8px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.table-header h3 {
  margin: 0 0 10px 0;
  color: #1976d2;
  font-size: 18px;
  font-weight: 600;
}

.header-info {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

.result-table {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

.result-table ::v-deep .el-table__header-wrapper .el-table__header th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  font-size: 13px;
  text-align: center;
  padding: 12px 8px;
}

.result-table ::v-deep .el-table__row {
  height: auto;
  min-height: 80px;
}

.result-table ::v-deep .el-table td {
  padding: 0;
  vertical-align: top;
}

.result-cell {
  padding: 12px 8px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  min-height: 70px;
  justify-content: center;
}

.answer-row {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;
  line-height: 1.5;
}

.answer-label {
  font-weight: 600;
  color: #909399;
  font-size: 13px;
  min-width: 65px;
  white-space: nowrap;
}

.user-answer .answer-label {
  color: #409eff;
}

.standard-answer .answer-label {
  color: #67c23a;
}

.correct-answer {
  color: #67c23a;
  font-weight: 600;
  font-size: 15px;
}

.incorrect-answer {
  color: #f56c6c;
  font-weight: 600;
  font-size: 15px;
}

.no-answer {
  color: #c0c4cc;
  font-style: italic;
  font-size: 15px;
}

.standard-value {
  color: #67c23a;
  font-weight: 600;
  font-size: 15px;
}

.disaster-weather-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
  flex: 1;
}

.weather-tag {
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 10px;
}

.scoring-legend {
  margin-top: 20px;
  padding: 15px;
  background: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.scoring-legend h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.legend-items {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .result-table {
    font-size: 12px;
  }

  .result-cell {
    padding: 8px 6px;
    min-height: 60px;
  }

  .answer-row {
    font-size: 14px;
  }

  .answer-label {
    font-size: 12px;
    min-width: 55px;
  }

  .legend-items {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
