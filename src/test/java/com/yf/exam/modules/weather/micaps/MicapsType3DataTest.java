package com.yf.exam.modules.weather.micaps;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 第三类MICAPS数据解析测试
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-30
 */
@SpringBootTest
public class MicapsType3DataTest {

    private MicapsDataService micapsDataService;
    private File testFile;

    @BeforeEach
    void setUp() throws IOException {
        micapsDataService = new MicapsDataService();
        
        // 创建测试用的第三类数据文件
        testFile = File.createTempFile("test_type3_", ".mdfs");
        createTestType3File();
    }

    /**
     * 创建测试用的第三类数据文件
     */
    private void createTestType3File() throws IOException {
        try (FileWriter writer = new FileWriter(testFile)) {
            // 写入第三类数据的示例内容
            writer.write("diamond 3 98年08月21日08时地面温度\n");
            writer.write("\n");
            writer.write("98 08 21 08 -3\n");
            writer.write("\n");
            writer.write("      0\n");
            writer.write("\n");
            writer.write("      1     25      0\n");
            writer.write("\n");
            writer.write("    1   1930\n");
            writer.write("\n");
            writer.write("52533   98.48   39.77 1478   16.6\n");
            writer.write("52652  100.43   38.93 1483   16.9\n");
            writer.write("52866  101.77   36.62 2262   10.1\n");
            writer.write("52889  103.88   36.05 1518   17.4\n");
            writer.write("53588  113.53   39.03 2898   12.2\n");
            writer.write("53772  112.55   37.78  779   19.8\n");
            writer.write("53915  106.67   35.55 1348   18.9\n");
        }
    }

    @Test
    void testParseType3Data() throws IOException {
        // 解析第三类数据
        MicapsData result = micapsDataService.parseMicapsData(testFile);
        
        // 验证基本信息
        assertNotNull(result);
        assertTrue(result instanceof MicapsType3Data);
        
        MicapsType3Data type3Data = (MicapsType3Data) result;
        
        // 验证数据类型
        assertEquals(3, type3Data.getDataType());
        
        // 验证时间信息
        assertEquals(98, type3Data.getYear());
        assertEquals(8, type3Data.getMonth());
        assertEquals(21, type3Data.getDay());
        assertEquals(8, type3Data.getHour());
        assertEquals(-3, type3Data.getLevel()); // 温度格式
        
        // 验证描述信息
        assertNotNull(type3Data.getDescription());
        assertTrue(type3Data.getDescription().contains("温度"));
        
        // 验证站点数据
        assertNotNull(type3Data.getStations());
        assertEquals(7, type3Data.getStations().size());
        
        // 验证第一个站点
        MicapsType3Data.MicapsType3Station firstStation = type3Data.getStations().get(0);
        assertEquals(52533L, firstStation.getStationId());
        assertEquals(98.48, firstStation.getLongitude(), 0.01);
        assertEquals(39.77, firstStation.getLatitude(), 0.01);
        assertEquals(1478.0, firstStation.getElevation(), 0.1);
        
        // 验证站点值（温度格式应该是整数）
        assertNotNull(firstStation.getValue1());
        assertEquals("17", firstStation.getValue1()); // 16.6 -> 17 (整数格式)
    }

    @Test
    void testType3DataFormatting() {
        MicapsType3Data data = new MicapsType3Data();
        
        // 测试不同层次的格式描述
        data.setLevel(-1);
        assertEquals("6小时降水量格式", data.getFormatDescription());
        
        data.setLevel(-2);
        assertEquals("24小时降水量格式", data.getFormatDescription());
        
        data.setLevel(-3);
        assertEquals("温度格式", data.getFormatDescription());
        
        data.setLevel(850);
        assertEquals("层次: 850", data.getFormatDescription());
    }

    @Test
    void testType3StationValidation() {
        MicapsType3Data.MicapsType3Station station = new MicapsType3Data.MicapsType3Station();
        
        // 无效站点
        assertFalse(station.isValid());
        
        // 设置基本信息
        station.setStationId(52533L);
        station.setLongitude(98.48);
        station.setLatitude(39.77);
        station.setValue1("16.6");
        
        // 现在应该有效
        assertTrue(station.isValid());
        
        // 测试数值转换
        assertEquals(16.6, station.getNumericValue1(), 0.01);
        assertNull(station.getNumericValue2());
    }

    @Test
    void testType3DataFeatures() {
        MicapsType3Data data = new MicapsType3Data();
        
        // 测试等值线支持
        data.setContourCount(0);
        assertFalse(data.supportsContourDrawing());
        
        data.setContourCount(5);
        assertTrue(data.supportsContourDrawing());
        
        // 测试剪切区域
        data.setClipBoundaryPointCount(0);
        assertFalse(data.hasClipRegion());
        
        data.setClipBoundaryPointCount(4);
        data.setClipBoundaryPoints(List.of(
            new MicapsType3Data.ClipBoundaryPoint(100.0, 30.0),
            new MicapsType3Data.ClipBoundaryPoint(120.0, 30.0),
            new MicapsType3Data.ClipBoundaryPoint(120.0, 40.0),
            new MicapsType3Data.ClipBoundaryPoint(100.0, 40.0)
        ));
        assertTrue(data.hasClipRegion());
    }

    @Test
    void testStationFiltering() {
        MicapsType3Data data = new MicapsType3Data();
        
        // 创建测试站点
        List<MicapsType3Data.MicapsType3Station> stations = List.of(
            createTestStation(52533L, 98.48, 39.77, "16.6"),
            createTestStation(52652L, 100.43, 38.93, "16.9"),
            createTestStation(53588L, 113.53, 39.03, "12.2")
        );
        data.setStations(stations);
        
        // 测试区域过滤
        List<MicapsType3Data.MicapsType3Station> regionStations = 
            data.getStationsInRegion(98.0, 101.0, 38.0, 40.0);
        assertEquals(2, regionStations.size());
        
        // 测试有效值过滤
        List<MicapsType3Data.MicapsType3Station> validStations = 
            data.getStationsWithValidValues();
        assertEquals(3, validStations.size());
    }

    private MicapsType3Data.MicapsType3Station createTestStation(long id, double lon, double lat, String value) {
        MicapsType3Data.MicapsType3Station station = new MicapsType3Data.MicapsType3Station();
        station.setStationId(id);
        station.setLongitude(lon);
        station.setLatitude(lat);
        station.setElevation(1000.0);
        station.setValue1(value);
        return station;
    }

    @Test
    void testClipBoundaryPoint() {
        MicapsType3Data.ClipBoundaryPoint point = new MicapsType3Data.ClipBoundaryPoint(120.5, 35.8);
        
        assertEquals(120.5, point.getLongitude(), 0.01);
        assertEquals(35.8, point.getLatitude(), 0.01);
    }
}
