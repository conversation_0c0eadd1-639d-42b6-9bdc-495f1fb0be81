package com.yf.exam.modules.weather.micaps;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.io.TempDir;
import static org.junit.jupiter.api.Assertions.*;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Path;
import java.util.List;

/**
 * MICAPS解析器测试类
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */
public class MicapsParserTest {
    
    @TempDir
    Path tempDir;
    
    private MicapsDataService micapsDataService;
    
    @BeforeEach
    void setUp() {
        micapsDataService = new MicapsDataService();
    }
    
    @Test
    void testParseType1Data() throws IOException {
        // 创建测试用的第一类数据文件
        File testFile = createTestType1File();
        
        // 解析文件
        MicapsData data = micapsDataService.parseMicapsFile(testFile.getAbsolutePath());
        
        // 验证解析结果
        assertNotNull(data);
        assertTrue(data instanceof MicapsType1Data);
        
        MicapsType1Data type1Data = (MicapsType1Data) data;
        assertEquals(1, type1Data.getDataType());
        assertEquals("测试地面填图数据", type1Data.getDescription());
        assertEquals(2025, type1Data.getYear());
        assertEquals(1, type1Data.getMonth());
        assertEquals(22, type1Data.getDay());
        assertEquals(8, type1Data.getHour());
        assertEquals(3, type1Data.getTotalStations());
        
        List<MicapsStation> stations = type1Data.getStations();
        assertNotNull(stations);
        assertEquals(3, stations.size());
        
        // 验证第一个站点数据
        MicapsStation station1 = stations.get(0);
        assertEquals(50468L, station1.getStationId());
        assertEquals(127.45, station1.getLongitude(), 0.01);
        assertEquals(50.25, station1.getLatitude(), 0.01);
        assertEquals(166.0, station1.getElevation(), 0.01);
        assertEquals(16, station1.getLevel());
        assertEquals(7, station1.getTotalCloudCover());
        assertEquals(340, station1.getWindDirection());
        assertEquals(6, station1.getWindSpeed());
        assertEquals(975.0, station1.getPressure(), 0.01);
    }
    
    @Test
    void testParseType4Data() throws IOException {
        // 创建测试用的第四类数据文件
        File testFile = createTestType4File();
        
        // 解析文件
        MicapsData data = micapsDataService.parseMicapsFile(testFile.getAbsolutePath());
        
        // 验证解析结果
        assertNotNull(data);
        assertTrue(data instanceof MicapsType4Data);
        
        MicapsType4Data type4Data = (MicapsType4Data) data;
        assertEquals(4, type4Data.getDataType());
        assertEquals("测试格点数据", type4Data.getDescription());
        assertEquals(2025, type4Data.getYear());
        assertEquals(1, type4Data.getMonth());
        assertEquals(22, type4Data.getDay());
        assertEquals(8, type4Data.getHour());
        assertEquals(0, type4Data.getForecastHour());
        assertEquals(500, type4Data.getLevel());
        
        assertEquals(2.5, type4Data.getLonInterval(), 0.01);
        assertEquals(2.5, type4Data.getLatInterval(), 0.01);
        assertEquals(70.0, type4Data.getStartLon(), 0.01);
        assertEquals(140.0, type4Data.getEndLon(), 0.01);
        assertEquals(15.0, type4Data.getStartLat(), 0.01);
        assertEquals(55.0, type4Data.getEndLat(), 0.01);
        assertEquals(17, type4Data.getLatGridNum());
        assertEquals(29, type4Data.getLonGridNum());
        
        List<Double> gridValues = type4Data.getGridValues();
        assertNotNull(gridValues);
        assertTrue(gridValues.size() > 0);
    }
    
    @Test
    void testGetStationsInRegion() throws IOException {
        File testFile = createTestType1File();
        MicapsData data = micapsDataService.parseMicapsFile(testFile.getAbsolutePath());
        
        // 查询指定区域内的站点
        List<MicapsStation> stations = micapsDataService.getStationsInRegion(
            data, 90.0, 130.0, 35.0, 55.0);
        
        assertNotNull(stations);
        assertTrue(stations.size() > 0);
        
        // 验证返回的站点都在指定区域内
        for (MicapsStation station : stations) {
            assertTrue(station.getLongitude() >= 90.0 && station.getLongitude() <= 130.0);
            assertTrue(station.getLatitude() >= 35.0 && station.getLatitude() <= 55.0);
        }
    }
    
    @Test
    void testGetStationsWithTemperature() throws IOException {
        File testFile = createTestType1File();
        MicapsData data = micapsDataService.parseMicapsFile(testFile.getAbsolutePath());
        
        List<MicapsStation> stations = micapsDataService.getStationsWithTemperature(data);
        
        assertNotNull(stations);
        assertTrue(stations.size() > 0);
        
        // 验证返回的站点都有温度数据
        for (MicapsStation station : stations) {
            assertNotNull(station.getTemperature());
        }
    }
    
    @Test
    void testGetGridValueAtPosition() throws IOException {
        File testFile = createTestType4File();
        MicapsData data = micapsDataService.parseMicapsFile(testFile.getAbsolutePath());
        
        // 查询指定位置的格点值
        Double value = micapsDataService.getGridValueAtPosition(data, 105.0, 35.0);
        
        assertNotNull(value);
        assertTrue(value instanceof Double);
    }
    
    @Test
    void testValidateMicapsData() throws IOException {
        File testFile = createTestType1File();
        MicapsData data = micapsDataService.parseMicapsFile(testFile.getAbsolutePath());
        
        MicapsDataService.ValidationResult result = micapsDataService.validateMicapsData(data);
        
        assertNotNull(result);
        assertTrue(result.isValid());
        assertTrue(result.getErrors().isEmpty());
    }
    
    @Test
    void testWindForceLevel() throws IOException {
        File testFile = createTestType1File();
        MicapsData data = micapsDataService.parseMicapsFile(testFile.getAbsolutePath());
        
        MicapsType1Data type1Data = (MicapsType1Data) data;
        MicapsStation station = type1Data.getStations().get(0);
        
        Integer windForce = station.getWindForceLevel();
        assertNotNull(windForce);
        assertTrue(windForce >= 0 && windForce <= 12);
    }
    
    @Test
    void testWindDirectionDescription() throws IOException {
        File testFile = createTestType1File();
        MicapsData data = micapsDataService.parseMicapsFile(testFile.getAbsolutePath());
        
        MicapsType1Data type1Data = (MicapsType1Data) data;
        MicapsStation station = type1Data.getStations().get(0);
        
        String windDirection = station.getWindDirectionDescription();
        assertNotNull(windDirection);
        assertTrue(windDirection.contains("风"));
    }
    
    /**
     * 创建测试用的第一类数据文件
     */
    private File createTestType1File() throws IOException {
        File testFile = tempDir.resolve("test_type1.000").toFile();
        
        try (FileWriter writer = new FileWriter(testFile)) {
            writer.write("diamond 1 测试地面填图数据 2025 1 22 8\n");
            writer.write("3\n");
            writer.write("50468 127.45 50.25 166 16 7 340 6 975 4 8 0.1 38 7 600 9.1 25.0 0 14.7 9999 9999 1 2 1 -3\n");
            writer.write("52533 98.48 39.77 1478 1 8 0 0 98 7 8 0.01 30 8 2500 10.7 30.0 60 16.8 27 9999 1 2 2 3\n");
            writer.write("52652 100.43 38.93 1483 4 8 270 3 115 11 6 0.5 30 4 2500 12.6 15.0 61 16.0 24 17 1 2 1 2\n");
        }
        
        return testFile;
    }
    
    /**
     * 创建测试用的第四类数据文件
     */
    private File createTestType4File() throws IOException {
        File testFile = tempDir.resolve("test_type4.024").toFile();
        
        try (FileWriter writer = new FileWriter(testFile)) {
            writer.write("diamond 4 测试格点数据 2025 1 22 8 0 500\n");
            writer.write("2.5 2.5 70.0 140.0 15.0 55.0 17 29 10 -100 100 1 0\n");
            
            // 写入格点数据（17*29=493个点）
            for (int i = 0; i < 493; i++) {
                writer.write(String.valueOf(i % 100));
                if ((i + 1) % 10 == 0) {
                    writer.write("\n");
                } else {
                    writer.write(" ");
                }
            }
        }
        
        return testFile;
    }
}
