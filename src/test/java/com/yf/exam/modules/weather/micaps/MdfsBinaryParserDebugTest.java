package com.yf.exam.modules.weather.micaps;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.io.File;

@SpringBootTest
@ActiveProfiles("test")
public class MdfsBinaryParserDebugTest {

    @Autowired
    private MdfsBinaryParser parser;

    @Test
    public void testDebugStationId() throws Exception {
        // 使用您的测试文件路径
        File testFile = new File("D:/java/exam/exam-api/test-data/24010108.000");
        
        if (!testFile.exists()) {
            System.out.println("测试文件不存在: " + testFile.getAbsolutePath());
            return;
        }
        
        System.out.println("开始调试站点ID读取...");
        
        // 创建一个简单的文件头对象
        MdfsFileHeader header = new MdfsFileHeader();
        header.setDescription("测试文件");
        
        try {
            MicapsType1Data result = parser.parseStationBinaryData(testFile, header);
            System.out.println("解析完成，站点数量: " + result.getTotalStations());
            
            if (!result.getStations().isEmpty()) {
                MicapsStation firstStation = result.getStations().get(0);
                System.out.println("第一个站点: ID=" + firstStation.getStationId() + 
                                 ", 经度=" + firstStation.getLongitude() + 
                                 ", 纬度=" + firstStation.getLatitude());
            }
        } catch (Exception e) {
            System.out.println("解析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
