package com.yf.exam.modules.weather.scoring.algorithm;

import java.util.*;

/**
 * WeatherDataComparator测试类
 * 验证修复后的数据结构处理是否正确
 */
public class WeatherDataComparatorTest {

    private WeatherDataComparator comparator;

    public WeatherDataComparatorTest() {
        comparator = new WeatherDataComparator();
    }

    public void testCompareWeatherDataWithNestedStructure() {
        // 构造嵌套结构的预测数据
        Map<String, Object> predictedData = new HashMap<>();
        List<Map<String, Object>> predictedStations = new ArrayList<>();
        
        Map<String, Object> predictedStation = new HashMap<>();
        predictedStation.put("stationId", "station001");
        predictedStation.put("stationName", "测试站点");
        predictedStation.put("windForce", "3级");
        predictedStation.put("windDirection", "东北");
        predictedStation.put("maxTemperature", 25);
        predictedStation.put("minTemperature", 15);
        predictedStation.put("precipitation", "小雨");
        predictedStation.put("disasterWeather", "无");
        
        predictedStations.add(predictedStation);
        predictedData.put("stations", predictedStations);

        // 构造嵌套结构的实际数据
        Map<String, Object> actualData = new HashMap<>();
        List<Map<String, Object>> actualStations = new ArrayList<>();
        
        Map<String, Object> actualStation = new HashMap<>();
        actualStation.put("stationId", "station001");
        actualStation.put("stationName", "测试站点");
        actualStation.put("windForce", "3级");
        actualStation.put("windDirection", "东北");
        actualStation.put("maxTemperature", 26);
        actualStation.put("minTemperature", 14);
        actualStation.put("precipitation", "小雨");
        actualStation.put("disasterWeather", "无");
        
        actualStations.add(actualStation);
        actualData.put("stations", actualStations);

        // 执行比较（现在使用配置文件中的权重和容差配置）
        WeatherComparisonResult result = comparator.compareWeatherData(
            predictedData, actualData
        );

        // 验证结果
        if (result == null) {
            throw new RuntimeException("比较结果为null");
        }
        if (result.getOverallScore() <= 0.0) {
            throw new RuntimeException("总体得分应该大于0，实际得分：" + result.getOverallScore());
        }
        if (result.getDetailResults() == null) {
            throw new RuntimeException("详细结果为null");
        }

        // 验证各要素都有比较结果
        if (!result.getDetailResults().containsKey("windForce")) {
            throw new RuntimeException("缺少风力比较结果");
        }
        if (!result.getDetailResults().containsKey("windDirection")) {
            throw new RuntimeException("缺少风向比较结果");
        }
        if (!result.getDetailResults().containsKey("maxTemperature")) {
            throw new RuntimeException("缺少最高温度比较结果");
        }
        if (!result.getDetailResults().containsKey("minTemperature")) {
            throw new RuntimeException("缺少最低温度比较结果");
        }
        if (!result.getDetailResults().containsKey("precipitation")) {
            throw new RuntimeException("缺少降水比较结果");
        }
        if (!result.getDetailResults().containsKey("disasterWeather")) {
            throw new RuntimeException("缺少灾害天气比较结果");
        }
        
        System.out.println("测试通过：嵌套结构数据比较正常");
        System.out.println("总体得分：" + result.getOverallScore());
        System.out.println("详细结果数量：" + result.getDetailResults().size());
    }

    public void testCompareWeatherDataWithFlatStructure() {
        // 构造平铺结构的数据（向后兼容）
        Map<String, Object> predictedData = new HashMap<>();
        predictedData.put("windForce", "4级");
        predictedData.put("windDirection", "东");
        predictedData.put("maxTemperature", 28);
        predictedData.put("minTemperature", 18);
        predictedData.put("precipitation", "中雨");
        predictedData.put("disasterWeather", "雷暴");

        Map<String, Object> actualData = new HashMap<>();
        actualData.put("windForce", "4级");
        actualData.put("windDirection", "东");
        actualData.put("maxTemperature", 27);
        actualData.put("minTemperature", 17);
        actualData.put("precipitation", "中雨");
        actualData.put("disasterWeather", "雷暴");

        // 执行比较（现在使用配置文件中的权重和容差配置）
        WeatherComparisonResult result = comparator.compareWeatherData(
            predictedData, actualData
        );

        // 验证结果
        if (result == null) {
            throw new RuntimeException("比较结果为null");
        }
        if (result.getOverallScore() <= 0.0) {
            throw new RuntimeException("总体得分应该大于0，实际得分：" + result.getOverallScore());
        }
        if (result.getDetailResults() == null) {
            throw new RuntimeException("详细结果为null");
        }
        
        System.out.println("测试通过：平铺结构数据比较正常（向后兼容）");
        System.out.println("总体得分：" + result.getOverallScore());
    }

    public void testCompareWeatherDataWithEmptyStations() {
        // 测试空站点数据的处理
        Map<String, Object> predictedData = new HashMap<>();
        predictedData.put("stations", new ArrayList<>());

        Map<String, Object> actualData = new HashMap<>();
        actualData.put("stations", new ArrayList<>());

        WeatherComparisonResult result = comparator.compareWeatherData(
            predictedData, actualData
        );

        if (result == null) {
            throw new RuntimeException("比较结果为null");
        }
        if (result.getOverallScore() != 0.0) {
            throw new RuntimeException("空站点数据的得分应该为0，实际得分：" + result.getOverallScore());
        }
        if (result.isOverallMatch()) {
            throw new RuntimeException("空站点数据不应该匹配");
        }

        System.out.println("测试通过：空站点数据处理正常");
    }

    public static void main(String[] args) {
        WeatherDataComparatorTest test = new WeatherDataComparatorTest();

        try {
            System.out.println("开始测试WeatherDataComparator修复...");

            test.testCompareWeatherDataWithNestedStructure();
            System.out.println("✓ 嵌套结构数据测试通过");

            test.testCompareWeatherDataWithFlatStructure();
            System.out.println("✓ 平铺结构数据测试通过");

            test.testCompareWeatherDataWithEmptyStations();
            System.out.println("✓ 空站点数据测试通过");

            System.out.println("\n所有测试通过！修复成功。");

        } catch (Exception e) {
            System.err.println("测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
}
