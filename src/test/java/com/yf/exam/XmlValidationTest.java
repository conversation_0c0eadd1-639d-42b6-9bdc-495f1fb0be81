package com.yf.exam;

import org.apache.ibatis.parsing.XPathParser;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;

/**
 * XML验证测试
 */
public class XmlValidationTest {

    public static void main(String[] args) {
        String mapperDir = "src/main/resources/mapper";
        new XmlValidationTest().validateXmlFiles(new File(mapperDir));
    }

    private void validateXmlFiles(File dir) {
        if (dir.isDirectory()) {
            File[] files = dir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        validateXmlFiles(file);
                    } else if (file.getName().endsWith(".xml")) {
                        validateXmlFile(file);
                    }
                }
            }
        }
    }

    private void validateXmlFile(File xmlFile) {
        try (InputStream inputStream = new FileInputStream(xmlFile)) {
            System.out.println("验证文件: " + xmlFile.getPath());
            
            // 使用MyBatis的XPathParser来验证XML
            XPathParser parser = new XPathParser(inputStream, true, null, null);
            parser.evalNode("/mapper");
            
            System.out.println("✓ " + xmlFile.getName() + " 验证通过");
            
        } catch (Exception e) {
            System.err.println("✗ " + xmlFile.getName() + " 验证失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
