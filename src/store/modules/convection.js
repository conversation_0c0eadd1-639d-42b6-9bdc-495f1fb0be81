import { convectionApi } from '@/api/convection/convection'
import { gradingApi } from '@/api/convection/grading'

const state = {
  // ========== 考试状态 ==========
  currentExam: null,           // 当前考试信息
  examStatus: 'not_started',   // 考试状态: not_started, in_progress, submitted
  examTimer: null,             // 考试计时器
  remainingTime: 0,            // 剩余时间（秒）
  
  // ========== 题目信息 ==========
  currentQuestion: null,       // 当前题目信息
  questionData: null,          // 题目详细数据
  
  // ========== 答题数据 ==========
  answerData: {
    stationAnswer: {},         // 站点预报答案
    areaAnswer: {},            // 落区绘制答案
    forecastReasoning: '',     // 预报依据阐述
    reasoningWordCount: 0      // 预报依据字数
  },
  
  // ========== 答题进度 ==========
  stationProgress: 0,          // 站点预报进度
  areaProgress: 0,             // 落区绘制进度
  overallProgress: 0,          // 整体进度
  
  // ========== 批卷数据 ==========
  gradingTaskList: [],         // 批卷任务列表
  currentGradingTask: null,    // 当前批卷任务
  answerComparison: null,      // 答案对比数据
  gradingScores: {             // 批卷评分
    gradingBasisScore: 0,      // 分级依据得分
    extremeReasoningScore: 0   // 极端天气理由得分
  },
  aiSuggestions: null,         // AI评分建议
  
  // ========== UI状态 ==========
  loading: false,              // 加载状态
  saving: false,               // 保存状态
  submitting: false,           // 提交状态
  dialogVisible: false,        // 对话框显示状态
  
  // ========== 缓存数据 ==========
  examList: [],                // 考试列表缓存
  questionList: [],            // 试题列表缓存
  lastSaveTime: null           // 最后保存时间
}

const mutations = {
  // ========== 考试状态变更 ==========
  SET_CURRENT_EXAM(state, exam) {
    state.currentExam = exam
  },
  
  SET_EXAM_STATUS(state, status) {
    state.examStatus = status
  },
  
  SET_EXAM_TIMER(state, timer) {
    state.examTimer = timer
  },
  
  SET_REMAINING_TIME(state, time) {
    state.remainingTime = time
  },
  
  DECREASE_REMAINING_TIME(state) {
    if (state.remainingTime > 0) {
      state.remainingTime--
    }
  },
  
  // ========== 题目信息变更 ==========
  SET_CURRENT_QUESTION(state, question) {
    state.currentQuestion = question
  },
  
  SET_QUESTION_DATA(state, data) {
    state.questionData = data
  },
  
  // ========== 答题数据变更 ==========
  SET_ANSWER_DATA(state, data) {
    state.answerData = { ...state.answerData, ...data }
  },
  
  SET_STATION_ANSWER(state, answer) {
    state.answerData.stationAnswer = answer
  },
  
  SET_AREA_ANSWER(state, answer) {
    state.answerData.areaAnswer = answer
  },
  
  SET_FORECAST_REASONING(state, reasoning) {
    state.answerData.forecastReasoning = reasoning
    state.answerData.reasoningWordCount = reasoning ? reasoning.length : 0
  },
  
  // ========== 进度变更 ==========
  SET_STATION_PROGRESS(state, progress) {
    state.stationProgress = progress
  },
  
  SET_AREA_PROGRESS(state, progress) {
    state.areaProgress = progress
  },
  
  SET_OVERALL_PROGRESS(state, progress) {
    state.overallProgress = progress
  },
  
  UPDATE_OVERALL_PROGRESS(state) {
    // 站点预报占50%，落区绘制占30%，预报依据占20%
    const stationWeight = 0.5
    const areaWeight = 0.3
    const reasoningWeight = 0.2
    
    const reasoningProgress = Math.min(state.answerData.reasoningWordCount / 300, 1) * 100
    
    state.overallProgress = Math.round(
      state.stationProgress * stationWeight +
      state.areaProgress * areaWeight +
      reasoningProgress * reasoningWeight
    )
  },
  
  // ========== 批卷数据变更 ==========
  SET_GRADING_TASK_LIST(state, list) {
    state.gradingTaskList = list
  },
  
  SET_CURRENT_GRADING_TASK(state, task) {
    state.currentGradingTask = task
  },
  
  SET_ANSWER_COMPARISON(state, comparison) {
    state.answerComparison = comparison
  },
  
  SET_GRADING_SCORES(state, scores) {
    state.gradingScores = { ...state.gradingScores, ...scores }
  },
  
  SET_AI_SUGGESTIONS(state, suggestions) {
    state.aiSuggestions = suggestions
  },
  
  // ========== UI状态变更 ==========
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  
  SET_SAVING(state, saving) {
    state.saving = saving
  },
  
  SET_SUBMITTING(state, submitting) {
    state.submitting = submitting
  },
  
  SET_DIALOG_VISIBLE(state, visible) {
    state.dialogVisible = visible
  },
  
  // ========== 缓存数据变更 ==========
  SET_EXAM_LIST(state, list) {
    state.examList = list
  },
  
  SET_QUESTION_LIST(state, list) {
    state.questionList = list
  },
  
  SET_LAST_SAVE_TIME(state, time) {
    state.lastSaveTime = time
  },
  
  // ========== 重置状态 ==========
  RESET_EXAM_STATE(state) {
    state.currentExam = null
    state.examStatus = 'not_started'
    state.examTimer = null
    state.remainingTime = 0
    state.currentQuestion = null
    state.questionData = null
    state.answerData = {
      stationAnswer: {},
      areaAnswer: {},
      forecastReasoning: '',
      reasoningWordCount: 0
    }
    state.stationProgress = 0
    state.areaProgress = 0
    state.overallProgress = 0
  },
  
  RESET_GRADING_STATE(state) {
    state.currentGradingTask = null
    state.answerComparison = null
    state.gradingScores = {
      gradingBasisScore: 0,
      extremeReasoningScore: 0
    }
    state.aiSuggestions = null
  }
}

const actions = {
  // ========== 考试相关操作 ==========
  
  /**
   * 加载考试列表
   */
  async loadExamList({ commit }, params) {
    commit('SET_LOADING', true)
    try {
      const response = await convectionApi.getExamList(params)
      commit('SET_EXAM_LIST', response.data.records || response.data)
      return response.data
    } catch (error) {
      console.error('加载考试列表失败:', error)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  /**
   * 开始考试
   */
  async startExam({ commit }, examId) {
    commit('SET_LOADING', true)
    try {
      // 加载考试信息
      const examResponse = await convectionApi.getExamDetail(examId)
      const examData = examResponse.data
      
      commit('SET_CURRENT_EXAM', examData)
      commit('SET_REMAINING_TIME', examData.remainingTime * 60) // 转换为秒
      commit('SET_EXAM_STATUS', 'in_progress')
      
      // 开始计时器
      const timer = setInterval(() => {
        commit('DECREASE_REMAINING_TIME')
      }, 1000)
      commit('SET_EXAM_TIMER', timer)
      
      return examData
    } catch (error) {
      console.error('开始考试失败:', error)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  /**
   * 加载题目数据
   */
  async loadQuestionData({ commit }, questionId) {
    try {
      const response = await convectionApi.getQuestionDetail(questionId)
      const questionData = response.data
      
      commit('SET_CURRENT_QUESTION', questionData)
      commit('SET_QUESTION_DATA', questionData)
      
      return questionData
    } catch (error) {
      console.error('加载题目数据失败:', error)
      throw error
    }
  },
  
  /**
   * 加载已有答案
   */
  async loadExistingAnswer({ commit }, { examId, questionId }) {
    try {
      const response = await convectionApi.getExistingAnswer(examId, questionId)
      if (response.data) {
        commit('SET_ANSWER_DATA', response.data)
        commit('UPDATE_OVERALL_PROGRESS')
      }
      return response.data
    } catch (error) {
      console.log('没有找到已有答案，开始新的答题')
      return null
    }
  },
  
  /**
   * 保存答案草稿
   */
  async saveAnswerDraft({ commit, state }, { examId, questionId }) {
    commit('SET_SAVING', true)
    try {
      const data = {
        examId,
        questionId,
        answerData: state.answerData,
        overallProgress: state.overallProgress
      }
      
      await convectionApi.saveAnswerDraft(data)
      commit('SET_LAST_SAVE_TIME', new Date())
      
      return true
    } catch (error) {
      console.error('保存答案草稿失败:', error)
      throw error
    } finally {
      commit('SET_SAVING', false)
    }
  },
  
  /**
   * 提交考试
   */
  async submitExam({ commit, state }, { examId, questionId }) {
    commit('SET_SUBMITTING', true)
    try {
      const data = {
        examId,
        questionId,
        answerData: state.answerData,
        submitTime: new Date().toISOString()
      }
      
      await convectionApi.submitExam(data)
      commit('SET_EXAM_STATUS', 'submitted')
      
      // 停止计时器
      if (state.examTimer) {
        clearInterval(state.examTimer)
        commit('SET_EXAM_TIMER', null)
      }
      
      return true
    } catch (error) {
      console.error('提交考试失败:', error)
      throw error
    } finally {
      commit('SET_SUBMITTING', false)
    }
  },
  
  /**
   * 停止考试计时器
   */
  stopExamTimer({ commit, state }) {
    if (state.examTimer) {
      clearInterval(state.examTimer)
      commit('SET_EXAM_TIMER', null)
    }
  },
  
  // ========== 答题数据操作 ==========
  
  /**
   * 更新站点答案
   */
  updateStationAnswer({ commit }, answer) {
    commit('SET_STATION_ANSWER', answer)
    commit('UPDATE_OVERALL_PROGRESS')
  },
  
  /**
   * 更新落区答案
   */
  updateAreaAnswer({ commit }, answer) {
    commit('SET_AREA_ANSWER', answer)
    commit('UPDATE_OVERALL_PROGRESS')
  },
  
  /**
   * 更新预报依据
   */
  updateForecastReasoning({ commit }, reasoning) {
    commit('SET_FORECAST_REASONING', reasoning)
    commit('UPDATE_OVERALL_PROGRESS')
  },
  
  /**
   * 更新站点进度
   */
  updateStationProgress({ commit }, progress) {
    commit('SET_STATION_PROGRESS', progress)
    commit('UPDATE_OVERALL_PROGRESS')
  },
  
  /**
   * 更新落区进度
   */
  updateAreaProgress({ commit }, progress) {
    commit('SET_AREA_PROGRESS', progress)
    commit('UPDATE_OVERALL_PROGRESS')
  },
  
  // ========== 批卷相关操作 ==========
  
  /**
   * 加载批卷任务列表
   */
  async loadGradingTaskList({ commit }, params) {
    commit('SET_LOADING', true)
    try {
      const response = await gradingApi.getGradingTaskList(params)
      commit('SET_GRADING_TASK_LIST', response.data.records || response.data)
      return response.data
    } catch (error) {
      console.error('加载批卷任务列表失败:', error)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  /**
   * 开始批卷任务
   */
  async startGradingTask({ commit }, taskId) {
    commit('SET_LOADING', true)
    try {
      // 加载批卷任务
      const taskResponse = await gradingApi.getGradingTask(taskId)
      commit('SET_CURRENT_GRADING_TASK', taskResponse.data)
      
      // 加载答案对比数据
      const comparisonResponse = await gradingApi.getAnswerComparison(taskId)
      commit('SET_ANSWER_COMPARISON', comparisonResponse.data)
      
      // 加载AI建议
      try {
        const aiResponse = await gradingApi.getAISuggestions(taskId)
        commit('SET_AI_SUGGESTIONS', aiResponse.data)
      } catch (error) {
        console.log('AI评分建议加载失败，将使用人工评分')
      }
      
      return taskResponse.data
    } catch (error) {
      console.error('开始批卷任务失败:', error)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  /**
   * 保存批卷草稿
   */
  async saveGradingDraft({ commit, state }, { taskId, gradingComments }) {
    commit('SET_SAVING', true)
    try {
      const data = {
        taskId,
        gradingScores: state.gradingScores,
        gradingComments
      }
      
      await gradingApi.saveGradingDraft(data)
      return true
    } catch (error) {
      console.error('保存批卷草稿失败:', error)
      throw error
    } finally {
      commit('SET_SAVING', false)
    }
  },
  
  /**
   * 提交批卷结果
   */
  async submitGradingResult({ commit, state }, { taskId, gradingComments }) {
    commit('SET_SUBMITTING', true)
    try {
      const data = {
        taskId,
        gradingScores: state.gradingScores,
        totalScore: state.gradingScores.gradingBasisScore + state.gradingScores.extremeReasoningScore,
        gradingComments,
        submitTime: new Date().toISOString()
      }
      
      await gradingApi.submitGradingResult(data)
      commit('RESET_GRADING_STATE')
      
      return true
    } catch (error) {
      console.error('提交批卷结果失败:', error)
      throw error
    } finally {
      commit('SET_SUBMITTING', false)
    }
  },
  
  /**
   * 更新批卷评分
   */
  updateGradingScores({ commit }, scores) {
    commit('SET_GRADING_SCORES', scores)
  }
}

const getters = {
  // ========== 考试状态 ==========
  isExamInProgress: state => state.examStatus === 'in_progress',
  isExamSubmitted: state => state.examStatus === 'submitted',
  canSubmitExam: state => state.overallProgress >= 80 && !state.submitting,
  
  // ========== 时间格式化 ==========
  formattedRemainingTime: state => {
    const seconds = state.remainingTime
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    } else {
      return `${minutes}:${secs.toString().padStart(2, '0')}`
    }
  },
  
  // ========== 批卷状态 ==========
  totalReasoningScore: state => {
    return state.gradingScores.gradingBasisScore + state.gradingScores.extremeReasoningScore
  },
  
  canSubmitGrading: (state, getters) => {
    return getters.totalReasoningScore > 0 && !state.submitting
  },
  
  // ========== 答题完成度 ==========
  isStationAnswerComplete: state => {
    return state.stationProgress >= 100
  },
  
  isAreaAnswerComplete: state => {
    return state.areaProgress >= 100
  },
  
  isReasoningComplete: state => {
    return state.answerData.reasoningWordCount >= 300
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
