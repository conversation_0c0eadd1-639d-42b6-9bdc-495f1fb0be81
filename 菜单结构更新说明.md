# 菜单结构更新说明

## 更新内容

### 1. 菜单名称修改
- **原名称**：考试管理
- **新名称**：理论考试管理

### 2. 新增历史个例考试菜单
在理论考试管理同级添加了"历史个例考试"菜单，包含以下子菜单：

#### 历史个例考试子菜单：
1. **试题管理** (`/weather/qu`)
   - 专门管理天气预报表格题（题目类型=6）
   - 支持导入导出、题库操作等功能
   - 显示天气配置状态

2. **考试管理** (`/weather/exam`)
   - 管理历史个例考试
   - 显示考试类型为"历史个例"
   - 支持考试人员和考试记录管理

3. **阅卷管理** (`/weather/exam/paper/list`)
   - 专门针对历史个例考试的阅卷
   - 显示天气预报得分情况
   - 支持进度条显示得分比例

## 文件结构

### 新增文件：
```
exam-vue/src/views/weather/
├── qu/
│   └── index.vue          # 历史个例试题管理
├── exam/
│   └── index.vue          # 历史个例考试管理
└── mark/
    └── index.vue          # 历史个例阅卷管理
```

### 新增图标：
```
exam-vue/src/icons/svg/weather.svg  # 天气图标
```

## 路由配置

### 理论考试管理 (`/exam`)
- 题库管理
- 试题管理  
- 考试管理
- 阅卷管理

### 历史个例考试 (`/weather`)
- 试题管理（仅天气预报表格题）
- 考试管理（历史个例类型）
- 阅卷管理（天气预报专用）

## 功能特点

### 历史个例考试特点：
1. **题目过滤**：自动过滤只显示天气预报表格题（quType=6）
2. **考试标识**：考试类型显示为"历史个例"
3. **专业阅卷**：显示天气预报得分和进度条
4. **配置状态**：显示天气配置是否完成

### 权限控制：
- 系统管理员（sa）：完全访问权限
- 教师（teacher）：基本管理权限

## 使用说明

1. **创建历史个例题目**：
   - 进入"历史个例考试" → "试题管理"
   - 添加天气预报表格题
   - 配置天气预报表格参数

2. **组织历史个例考试**：
   - 进入"历史个例考试" → "考试管理"
   - 创建考试，系统自动标识为历史个例类型
   - 添加考试人员

3. **阅卷管理**：
   - 进入"历史个例考试" → "阅卷管理"
   - 查看天气预报得分情况
   - 进行专业阅卷
