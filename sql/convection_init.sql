-- =====================================================
-- 强对流天气临近预报考试模块 - 数据库初始化脚本
-- 创建时间：2024-12-22
-- 版本：V20241222_1
-- 描述：创建强对流考试答案表、人工批卷记录表及相关索引
-- =====================================================

-- 1. 创建强对流考试答案表
CREATE TABLE IF NOT EXISTS `el_convection_exam_answer` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `exam_id` varchar(64) NOT NULL COMMENT '考试ID',
  `question_id` varchar(64) NOT NULL COMMENT '题目ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  
  -- 第一部分：站点预报答案 (JSON格式)
  `station_answer` json COMMENT '站点预报答案JSON',
  `station_progress` int DEFAULT 0 COMMENT '站点预报进度百分比',
  
  -- 第二部分：落区绘制答案 (JSON格式)
  `area_answer` json COMMENT '落区绘制答案JSON',
  `area_progress` int DEFAULT 0 COMMENT '落区绘制进度百分比',
  
  -- 预报依据阐述（考生输入）
  `forecast_reasoning` text COMMENT '考生预报依据阐述',
  `reasoning_word_count` int DEFAULT 0 COMMENT '预报依据字数',
  
  -- 整体状态
  `overall_progress` int DEFAULT 0 COMMENT '整体进度百分比',
  `answer_status` tinyint DEFAULT 0 COMMENT '答题状态：0-答题中，1-已提交',
  `submit_time` datetime COMMENT '提交时间',
  
  -- 评分相关字段
  `station_score` decimal(5,2) DEFAULT 0 COMMENT '站点预报得分',
  `area_score` decimal(5,2) DEFAULT 0 COMMENT '落区绘制得分',
  `total_score` decimal(5,2) DEFAULT 0 COMMENT '总得分',
  `scoring_status` tinyint DEFAULT 0 COMMENT '评分状态：0-未评分，1-自动评分完成，2-人工批卷完成',
  
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_exam_user` (`exam_id`, `user_id`),
  KEY `idx_exam_user` (`exam_id`, `user_id`),
  KEY `idx_question` (`question_id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_status` (`answer_status`),
  KEY `idx_submit_time` (`submit_time`),
  KEY `idx_scoring_status` (`scoring_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='强对流考试答案表';

-- 2. 创建强对流人工批卷记录表
CREATE TABLE IF NOT EXISTS `el_convection_grading_record` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `answer_id` varchar(64) NOT NULL COMMENT '答案ID',
  `exam_id` varchar(64) NOT NULL COMMENT '考试ID',
  `student_user_id` varchar(64) NOT NULL COMMENT '学生用户ID',
  `grader_user_id` varchar(64) NOT NULL COMMENT '批卷教师用户ID',
  
  -- 预报依据评分详情
  `reasoning_grading_basis_score` decimal(5,2) DEFAULT 0 COMMENT '分级依据阐述得分(10分)',
  `reasoning_extreme_score` decimal(5,2) DEFAULT 0 COMMENT '极端天气预报理由得分(10分)',
  `reasoning_total_score` decimal(5,2) DEFAULT 0 COMMENT '预报依据总得分(20分)',
  
  -- 批卷评语和建议
  `grading_comments` text COMMENT '批卷评语',
  `improvement_suggestions` text COMMENT '改进建议',
  
  -- 批卷状态和时间
  `grading_status` tinyint DEFAULT 0 COMMENT '批卷状态：0-未批卷，1-已批卷',
  `grading_time` datetime COMMENT '批卷完成时间',
  `grading_duration` int DEFAULT 0 COMMENT '批卷耗时(秒)',
  
  -- 质量控制字段
  `reviewer_user_id` varchar(64) COMMENT '复核教师用户ID',
  `review_status` tinyint DEFAULT 0 COMMENT '复核状态：0-未复核，1-已复核',
  `review_time` datetime COMMENT '复核时间',
  `review_comments` text COMMENT '复核意见',
  
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_answer_grader` (`answer_id`, `grader_user_id`),
  KEY `idx_answer` (`answer_id`),
  KEY `idx_exam` (`exam_id`),
  KEY `idx_grader` (`grader_user_id`),
  KEY `idx_student` (`student_user_id`),
  KEY `idx_status` (`grading_status`),
  KEY `idx_grading_time` (`grading_time`),
  KEY `idx_reviewer` (`reviewer_user_id`),
  CONSTRAINT `fk_grading_answer` FOREIGN KEY (`answer_id`) REFERENCES `el_convection_exam_answer` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='强对流人工批卷记录表';

-- 3. 扩展现有题目表，添加强对流专用字段
-- 检查字段是否已存在，避免重复添加
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'el_qu' AND COLUMN_NAME = 'convection_standard_reasoning');

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `el_qu` ADD COLUMN `convection_standard_reasoning` text COMMENT "强对流标准预报依据（管理员录入）" AFTER `scenario_data`',
    'SELECT "Column convection_standard_reasoning already exists" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加强对流题型标识字段
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'el_qu' AND COLUMN_NAME = 'convection_config');

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE `el_qu` ADD COLUMN `convection_config` json COMMENT "强对流考试配置JSON（站点信息、评分权重等）" AFTER `convection_standard_reasoning`',
    'SELECT "Column convection_config already exists" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 创建性能优化索引
-- 高频查询复合索引
CREATE INDEX IF NOT EXISTS `idx_exam_user_status` ON `el_convection_exam_answer` (`exam_id`, `user_id`, `answer_status`);
CREATE INDEX IF NOT EXISTS `idx_grading_status_time` ON `el_convection_grading_record` (`grading_status`, `grading_time`);

-- JSON字段虚拟列索引（MySQL 8.0+）
SET @mysql_version = (SELECT SUBSTRING_INDEX(VERSION(), '.', 2));
SET @is_mysql8 = IF(@mysql_version >= '8.0', 1, 0);

SET @sql = IF(@is_mysql8 = 1, '
    ALTER TABLE `el_convection_exam_answer` 
    ADD COLUMN `station_completion` tinyint GENERATED ALWAYS AS (
      CASE WHEN JSON_LENGTH(`station_answer`) > 0 THEN 1 ELSE 0 END
    ) VIRTUAL;
    CREATE INDEX `idx_station_completion` ON `el_convection_exam_answer` (`station_completion`);
', 'SELECT "MySQL 8.0+ required for JSON virtual columns" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 创建监控和统计视图
-- 考试答题统计视图
CREATE OR REPLACE VIEW `v_convection_exam_statistics` AS
SELECT 
    DATE(cea.create_time) as exam_date,
    cea.exam_id,
    e.title as exam_title,
    COUNT(*) as total_answers,
    SUM(CASE WHEN cea.answer_status = 1 THEN 1 ELSE 0 END) as submitted_count,
    ROUND(AVG(cea.overall_progress), 2) as avg_progress,
    ROUND(AVG(cea.reasoning_word_count), 0) as avg_reasoning_words,
    ROUND(AVG(cea.total_score), 2) as avg_total_score,
    SUM(CASE WHEN cea.scoring_status = 2 THEN 1 ELSE 0 END) as graded_count
FROM `el_convection_exam_answer` cea
LEFT JOIN `el_exam` e ON cea.exam_id = e.id
GROUP BY DATE(cea.create_time), cea.exam_id, e.title
ORDER BY exam_date DESC;

-- 批卷进度统计视图
CREATE OR REPLACE VIEW `v_convection_grading_statistics` AS
SELECT 
    DATE(cgr.create_time) as grading_date,
    cgr.exam_id,
    e.title as exam_title,
    COUNT(*) as total_grading_tasks,
    SUM(CASE WHEN cgr.grading_status = 1 THEN 1 ELSE 0 END) as completed_grading,
    ROUND(AVG(cgr.reasoning_total_score), 2) as avg_reasoning_score,
    COUNT(DISTINCT cgr.grader_user_id) as active_graders,
    ROUND(AVG(cgr.grading_duration), 0) as avg_grading_duration,
    SUM(CASE WHEN cgr.review_status = 1 THEN 1 ELSE 0 END) as reviewed_count
FROM `el_convection_grading_record` cgr
LEFT JOIN `el_exam` e ON cgr.exam_id = e.id
GROUP BY DATE(cgr.create_time), cgr.exam_id, e.title
ORDER BY grading_date DESC;

-- 6. 创建数据清理存储过程
DELIMITER //
DROP PROCEDURE IF EXISTS `CleanupOldConvectionData`//
CREATE PROCEDURE `CleanupOldConvectionData`()
BEGIN
    DECLARE cleanup_count INT DEFAULT 0;
    
    -- 清理1年前的草稿答案（未提交的答案）
    DELETE FROM `el_convection_exam_answer` 
    WHERE answer_status = 0 
    AND create_time < DATE_SUB(NOW(), INTERVAL 1 YEAR);
    
    SET cleanup_count = ROW_COUNT();
    SELECT CONCAT('Cleaned up ', cleanup_count, ' draft answers') as cleanup_result;
    
    -- 清理2年前的批卷记录（已完成的）
    DELETE FROM `el_convection_grading_record`
    WHERE grading_status = 1 
    AND create_time < DATE_SUB(NOW(), INTERVAL 2 YEAR);
    
    SET cleanup_count = ROW_COUNT();
    SELECT CONCAT('Cleaned up ', cleanup_count, ' old grading records') as cleanup_result;
    
END //
DELIMITER ;

-- 7. 插入强对流考试类型到字典表（如果存在）
INSERT IGNORE INTO `el_sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `create_time`) 
VALUES ('convection_exam_type', '强对流考试类型', 'convection', '强对流天气临近预报考试模块', NOW());

INSERT IGNORE INTO `el_sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort`, `create_time`)
VALUES 
('convection_qu_type', 'convection_exam_type', '强对流预报题', '7', '强对流天气预报考试题型', 7, NOW()),
('convection_exam_type', 'convection_exam_type', '强对流考试', 'convection', '强对流天气临近预报考试', 1, NOW());

-- 8. 创建强对流评分配置表
CREATE TABLE IF NOT EXISTS `el_convection_scoring_config` (
  `id` varchar(64) NOT NULL COMMENT '配置ID',
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `config_version` varchar(20) DEFAULT '1.0' COMMENT '配置版本',
  `total_score` int DEFAULT 100 COMMENT '总分',
  `station_forecast_score` int DEFAULT 68 COMMENT '站点预报分数',
  `area_forecast_score` int DEFAULT 32 COMMENT '落区绘制分数',
  `scoring_rules` json NOT NULL COMMENT '评分规则JSON配置',
  `tolerance_config` json COMMENT '容差配置',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_by` varchar(64) COMMENT '创建人',
  `update_by` varchar(64) COMMENT '更新人',
  
  PRIMARY KEY (`id`),
  KEY `idx_config_name` (`config_name`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_config_version` (`config_version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='强对流评分配置表';

-- 插入默认评分配置
INSERT IGNORE INTO `el_convection_scoring_config` (`id`, `config_name`, `config_version`, `scoring_rules`, `create_by`)
VALUES ('default_convection_scoring', '默认强对流评分配置', '1.0', 
'{
  "station_forecast": {
    "basic_station_score": 44,
    "extreme_weather_score": 4,
    "reasoning_score": 20,
    "per_station": {
      "total_score": 11,
      "rainfall": {"type_correct": 3, "intensity_correct": 1},
      "wind": {"type_correct": 3, "intensity_correct": 1},
      "hail": {"correct": 3}
    }
  },
  "area_forecast": {
    "heavy_rainfall": 8,
    "thunderstorm_wind": 8,
    "hail": 8,
    "tornado": 8
  }
}', 'system');

-- =====================================================
-- 数据库初始化完成
-- =====================================================

SELECT 'Convection exam module database initialization completed!' as result; 