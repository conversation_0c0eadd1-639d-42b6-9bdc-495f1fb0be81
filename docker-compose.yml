version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
      target: production-stage
    container_name: yf-exam-frontend
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - VUE_APP_API_BASE_URL=http://backend:8080
      - VUE_APP_CONVECTION_ENABLED=true
    volumes:
      - ./logs/nginx:/var/log/nginx
    networks:
      - exam-network
    depends_on:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 后端服务（假设存在）
  backend:
    image: yf-exam-backend:latest
    container_name: yf-exam-backend
    ports:
      - "8081:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_DATABASE=yf_exam
      - MYSQL_USERNAME=yf_user
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
    volumes:
      - ./logs/backend:/app/logs
      - ./uploads:/app/uploads
    networks:
      - exam-network
    depends_on:
      - mysql
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # 数据库服务
  mysql:
    image: mysql:8.0
    container_name: yf-exam-mysql
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=yf_exam
      - MYSQL_USER=yf_user
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
      - ./mysql.cnf:/etc/mysql/conf.d/custom.cnf
    networks:
      - exam-network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Redis缓存服务  
  redis:
    image: redis:6-alpine
    container_name: yf-exam-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - exam-network
    restart: unless-stopped
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx负载均衡（生产环境）
  nginx-lb:
    image: nginx:1.21-alpine
    container_name: yf-exam-nginx-lb
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx-lb.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - ./logs/nginx-lb:/var/log/nginx
    networks:
      - exam-network
    depends_on:
      - frontend
    restart: unless-stopped
    profiles: ["production"]

  # 监控服务
  prometheus:
    image: prom/prometheus:latest
    container_name: yf-exam-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - exam-network
    restart: unless-stopped
    profiles: ["monitoring"]

  grafana:
    image: grafana/grafana:latest
    container_name: yf-exam-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    networks:
      - exam-network
    depends_on:
      - prometheus
    restart: unless-stopped
    profiles: ["monitoring"]

# 网络配置
networks:
  exam-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local 