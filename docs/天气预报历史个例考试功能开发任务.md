# 上下文
文件名：天气预报历史个例考试功能开发任务.md
创建于：2024-12-19
创建者：AI助手
关联协议：RIPER-5 + Multidimensional + Agent Protocol (Conditional Interactive Step Review Enhanced)

# 任务描述
在现有考试系统基础上，新增天气预报历史个例考试功能。具体要求：
1. 制作未来24小时内指定站点的灾害性天气和气象要素预报
2. 制作表格形式的预报内容，参照用户提供的截图样式
3. 集成到现有的考试流程中

# 项目概述
**系统类型**: 前后端分离的在线考试系统
- **后端**: Spring Boot 2.1.4 + MyBatis Plus 3.4.1 + MySQL
- **前端**: Vue.js 2.6.10 + Element UI 2.15.7
- **架构特点**: 模块化设计，支持多种题型考试

---
*以下部分由 AI 在协议执行过程中维护*
---

# 分析 (由 RESEARCH 模式填充)

## 现有系统架构分析

### 后端模块结构
```
src/main/java/com/yf/exam/modules/
├── exam/          # 考试管理模块
├── paper/         # 试卷管理模块  
├── qu/            # 题目管理模块
├── repo/          # 题库管理模块
├── user/          # 用户管理模块
└── sys/           # 系统管理模块
```

### 核心数据库表
- `el_exam` - 考试配置表
- `el_paper` - 试卷实例表
- `el_qu` - 题目内容表
- `el_repo` - 题库分类表
- `el_paper_qu` - 试卷题目关联表
- `el_paper_qu_answer` - 试卷答案选项表

### 前端页面结构
```
exam-vue/src/views/
├── exam/          # 考试管理页面
├── paper/         # 试卷相关页面
│   └── exam/      # 考试答题页面
├── qu/            # 题目管理页面
└── user/          # 用户相关页面
```

### 现有题型支持
1. 单选题 (quType=1)
2. 多选题 (quType=2) 
3. 判断题 (quType=3)
4. 填空题 (quType=4)
5. 简答题 (quType=5)

### 考试流程分析
1. **考试创建**: 通过`ExamController`创建考试，配置题库和规则
2. **试卷生成**: `PaperService.createPaper()`从题库中随机抽取题目生成试卷
3. **答题过程**: 前端`exam.vue`页面处理答题交互，调用`fillAnswer`API保存答案
4. **评分机制**: 客观题自动评分，主观题支持人工批阅

### 技术栈特点
- **后端框架**: Spring Boot + Shiro安全框架 + MyBatis Plus ORM
- **前端技术**: Vue.js + Element UI + Vuex状态管理
- **数据库**: MySQL 8.0.11
- **构建工具**: Maven (后端) + Vue CLI (前端)

### 系统优势
- 模块化设计，易于扩展新功能
- 支持多种题型和灵活的组卷规则
- 前后端分离架构，便于独立开发部署
- 完善的权限管理和考试监控功能

### 发现的技术约束
- 现有题型结构相对固定，需要扩展支持表格填写类型
- 前端表格组件需要增强以支持复杂的天气预报表格
- 需要新增天气数据相关的实体类和业务逻辑
- 评分规则需要针对表格类答题进行定制

## 用户需求分析

根据用户提供的截图和要求，天气预报历史个例考试需要实现：

### 表格结构要求
- **时间维度**: 未来24小时内的时间段划分
- **站点信息**: 指定观测站点的基本信息
- **预报要素**: 包括风力、风向、气温、降水、灾害性天气等
- **表格样式**: 参照截图中的专业气象预报表格格式

### 功能需求
1. **题目类型扩展**: 新增"天气预报表格填写"题型
2. **表格编辑器**: 前端实现可交互的表格填写界面
3. **数据验证**: 对填写的气象数据进行合理性校验
4. **评分标准**: 制定针对表格填写的评分规则
5. **历史案例**: 支持基于历史天气事件的考试场景

### 业务场景
- 气象专业人员技能考核
- 天气预报能力培训
- 历史天气案例学习和复盘
- 专业技能认证考试

## 技术挑战识别

### 数据模型挑战
- 现有`Qu`实体主要支持文本内容，需要扩展支持结构化的表格数据
- 答案存储需要支持复杂的JSON格式数据
- 评分算法需要支持多维度的表格内容比较

### 前端实现挑战  
- Element UI表格组件需要定制以支持考试答题场景
- 表格数据的双向绑定和状态管理
- 响应式设计以适应不同屏幕尺寸

### 业务逻辑挑战
- 天气预报专业知识的系统化建模
- 灵活的表格模板配置机制
- 智能化的答案评判逻辑

## 集成点分析

### 与现有系统的集成
1. **题目管理**: 在`qu`模块中扩展支持表格类题目
2. **考试流程**: 复用现有的考试创建、答题、评分流程
3. **用户权限**: 继承现有的角色权限体系
4. **数据存储**: 利用现有的MySQL数据库和MyBatis映射

### 需要修改的关键组件
- `Qu`实体类：扩展字段支持表格结构
- `QuController`：新增表格题目的CRUD接口
- `exam.vue`：新增表格答题组件
- `PaperService`：扩展评分逻辑支持表格类题目

# 提议的解决方案 (由 INNOVATE 模式填充)

## 方案一：基于现有架构的渐进式扩展

### 核心思路
在不破坏现有系统结构的前提下，通过新增字段和组件的方式实现天气预报表格功能。

### 优势
- 风险最小，不影响现有功能
- 开发周期短，可快速上线
- 维护成本低，技术栈统一

### 劣势  
- 功能相对简单，扩展性有限
- 表格复杂度受现有架构约束

### 实现策略
1. 新增题型`quType=6`代表"天气预报表格题"
2. 在`Qu`表中新增`tableConfig`字段存储表格结构JSON
3. 前端新增`WeatherTableEditor`组件处理表格交互
4. 扩展评分算法支持表格答案对比

## 方案二：独立模块化设计

### 核心思路
创建独立的天气预报模块，与现有考试系统通过接口集成。

### 优势
- 功能完整，专业性强
- 可独立迭代，不受现有架构束缚
- 便于后续功能扩展

### 劣势
- 开发复杂度高，周期长
- 需要额外的维护成本
- 系统架构复杂度增加

### 实现策略
1. 新增`weather`模块专门处理气象相关逻辑
2. 设计专门的天气预报题目实体和表结构
3. 前端创建独立的天气预报答题页面
4. 通过适配器模式与现有考试流程集成

## 方案三：混合式架构

### 核心思路
结合方案一和方案二的优势，在现有架构基础上新增专门的天气预报功能模块。

### 优势
- 平衡了功能完整性和开发复杂度
- 既保证了专业性，又控制了风险
- 为后续扩展留出了空间

### 劣势
- 架构设计需要更仔细的规划
- 需要在多个层面进行集成测试

### 实现策略
1. 扩展现有题目系统支持表格类型
2. 新增weather子模块处理专业气象逻辑
3. 前端采用组件化设计，可复用可扩展
4. 设计灵活的配置机制支持不同表格模板

## 推荐方案：方案三（混合式架构）

综合考虑开发效率、功能完整性和系统可维护性，推荐采用混合式架构方案。这种方案能够：

1. **最大化复用现有系统**：考试流程、用户管理、权限控制等核心功能完全复用
2. **保证专业功能完整性**：通过专门的天气模块确保气象专业功能的完整性
3. **控制开发风险**：渐进式开发，可分阶段验证和上线
4. **预留扩展空间**：为后续可能的其他专业领域考试功能扩展奠定基础

# 实施计划 (由 PLAN 模式生成)

## 📋 技术架构详细规划

### 数据库设计规范

**新增表结构**：

```sql
-- 天气预报表格配置表
CREATE TABLE `el_weather_table_config` (
  `id` varchar(64) NOT NULL COMMENT 'ID',
  `name` varchar(100) NOT NULL COMMENT '配置名称',
  `table_schema` json NOT NULL COMMENT '表格结构JSON',
  `validation_rules` json COMMENT '验证规则JSON',
  `scoring_config` json COMMENT '评分配置JSON',
  `create_time` datetime NOT NULL,
  `update_time` datetime NOT NULL,
  PRIMARY KEY (`id`)
) COMMENT='天气预报表格配置';

-- 扩展题目表支持表格类型
ALTER TABLE `el_qu` ADD COLUMN `table_config_id` varchar(64) COMMENT '表格配置ID';
ALTER TABLE `el_qu` ADD COLUMN `weather_scenario` text COMMENT '天气情景描述';

-- 天气预报答案表
CREATE TABLE `el_weather_answer` (
  `id` varchar(64) NOT NULL,
  `paper_qu_id` varchar(64) NOT NULL COMMENT '试卷题目ID',
  `cell_data` json NOT NULL COMMENT '表格单元格数据',
  `create_time` datetime NOT NULL,
  `update_time` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_paper_qu` (`paper_qu_id`)
) COMMENT='天气预报答案数据';
```

### 后端模块架构规范

**新增Weather模块结构**：
```
src/main/java/com/yf/exam/modules/weather/
├── entity/
│   ├── WeatherTableConfig.java     # 表格配置实体
│   ├── WeatherAnswer.java          # 天气答案实体
│   └── WeatherScenario.java        # 天气情景实体
├── dto/
│   ├── WeatherTableDTO.java        # 表格数据传输对象
│   ├── WeatherCellDTO.java         # 单元格数据对象
│   └── WeatherScoreDTO.java        # 评分结果对象
├── controller/
│   └── WeatherController.java      # 天气相关接口
├── service/
│   ├── WeatherTableService.java    # 表格业务服务
│   ├── WeatherScoringService.java  # 评分业务服务
│   └── impl/                       # 实现类
├── mapper/
│   ├── WeatherTableConfigMapper.java
│   └── WeatherAnswerMapper.java
└── enums/
    ├── WeatherElementType.java     # 气象要素类型枚举
    └── DisasterType.java           # 灾害类型枚举
```

### 前端组件设计规范

**核心组件结构**：
```
exam-vue/src/components/Weather/
├── WeatherTableEditor/
│   ├── index.vue                   # 主表格编辑器
│   ├── WeatherCell.vue            # 单元格组件
│   ├── ValidationPanel.vue        # 验证面板
│   └── ScorePreview.vue           # 评分预览
├── WeatherStationPicker/
│   └── index.vue                  # 站点选择器
├── TimeRangePicker/
│   └── index.vue                  # 时间范围选择
└── WeatherElementInput/
    ├── WindDirectionSelector.vue   # 风向选择器
    ├── WindForceSelector.vue       # 风力选择器
    ├── TemperatureInput.vue        # 温度输入
    ├── PrecipitationSelector.vue   # 降水选择器
    └── DisasterTypeSelector.vue    # 灾害类型选择
```

### API接口设计规范

**RESTful接口规划**：
```java
@RestController
@RequestMapping("/exam/api/weather")
public class WeatherController {
    
    // 获取表格配置
    @GetMapping("/table-config/{id}")
    public ApiRest<WeatherTableDTO> getTableConfig(@PathVariable String id);
    
    // 保存答题数据
    @PostMapping("/answer/save")
    public ApiRest saveAnswer(@RequestBody WeatherAnswerDTO answerDTO);
    
    // 获取答题数据
    @GetMapping("/answer/{paperQuId}")
    public ApiRest<WeatherAnswerDTO> getAnswer(@PathVariable String paperQuId);
    
    // 评分接口
    @PostMapping("/score/calculate")
    public ApiRest<WeatherScoreDTO> calculateScore(@RequestBody WeatherScoreReqDTO reqDTO);
    
    // 验证答案合理性
    @PostMapping("/validate")
    public ApiRest<ValidationResultDTO> validateAnswer(@RequestBody WeatherAnswerDTO answerDTO);
}
```

## 🎯 实施检查清单

### 阶段一：基础架构搭建

1. [创建数据库表结构, review:true]
   - 执行SQL创建`el_weather_table_config`表
   - 修改`el_qu`表添加扩展字段
   - 创建`el_weather_answer`表
   - 验证表结构和索引正确性

2. [创建后端基础实体类, review:true]
   - 新建WeatherTableConfig.java实体
   - 新建WeatherAnswer.java实体
   - 添加JSON类型处理器配置
   - 编写实体类单元测试

3. [新增Weather模块基础结构, review:true]
   - 创建weather模块目录结构
   - 新建Controller、Service、Mapper接口
   - 配置MyBatis映射文件
   - 添加基础的CRUD操作

4. [扩展现有Qu模块支持表格类型, review:true]
   - 修改Qu实体类添加表格相关字段
   - 更新QuService支持表格题目创建
   - 修改QuController添加表格相关接口
   - 更新数据库映射文件

### 阶段二：前端组件开发

5. [创建基础Weather组件库, review:true]
   - 新建WeatherTableEditor主组件
   - 实现WeatherCell通用单元格组件
   - 创建各种气象要素输入组件
   - 编写组件文档和示例

6. [集成表格编辑器到考试页面, review:true]
   - 修改exam.vue支持天气预报题型
   - 添加题型判断和组件切换逻辑
   - 实现表格数据的双向绑定
   - 添加答题状态管理

7. [实现答题数据保存功能, review:true]
   - 设计表格数据序列化格式
   - 实现与后端API的数据交互
   - 添加自动保存和手动保存功能
   - 处理网络异常和数据恢复

### 阶段三：业务逻辑完善

8. [开发天气预报评分算法, review:true]
   - 实现基础的精确匹配评分
   - 添加数据合理性验证逻辑
   - 开发趋势分析评分算法
   - 编写评分算法单元测试

9. [实现表格配置管理功能, review:true]
   - 创建表格配置的管理界面
   - 实现配置的增删改查功能
   - 添加配置模板和预设方案
   - 设计配置的导入导出功能

10. [添加数据验证和提示功能, review:false]
    - 实现实时数据验证
    - 添加智能提示和警告
    - 优化用户体验和交互流程

### 阶段四：系统集成测试

11. [进行模块集成测试, review:true]
    - 测试前后端数据交互
    - 验证考试流程完整性
    - 测试评分算法准确性
    - 检查异常处理和边界情况

12. [用户体验测试和优化, review:true]
    - 进行可用性测试
    - 优化界面响应速度
    - 完善移动端适配
    - 收集用户反馈并改进

13. [编写技术文档, review:false]
    - 撰写API接口文档
    - 编写用户使用手册
    - 完善代码注释和开发文档

14. [部署和上线准备, review:true]
    - 准备生产环境配置
    - 进行压力测试和性能优化
    - 制定上线方案和回滚计划
    - 培训管理员使用新功能

## 📅 开发时间规划

- **阶段一** (5-7天): 基础架构搭建
- **阶段二** (8-10天): 前端组件开发  
- **阶段三** (5-7天): 业务逻辑完善
- **阶段四** (3-5天): 系统集成测试

**预计总开发周期**: 21-29天

## 🎯 技术风险评估

### 高风险项目
- 天气预报评分算法的准确性验证
- 复杂表格的前端性能优化
- 与现有考试流程的深度集成

### 中风险项目  
- JSON数据结构的向后兼容性
- 移动端表格交互体验
- 大数据量的表格渲染性能

### 低风险项目
- 基础CRUD功能实现
- 简单的数据验证逻辑
- 静态页面和组件开发

# 当前执行步骤 (由 EXECUTE 模式在开始执行某步骤时更新)

> 正在执行: "5. 创建Controller控制层" (审查需求: review:true, 状态: 初步实施中)

# 任务进度 (由 EXECUTE 模式在每步完成后，以及在交互式审查迭代中追加)

*   2024-12-19 15:30
    *   步骤：1. 创建数据库表结构 (初步完成, 审查需求: review:true)
    *   修改：[创建了数据库迁移文件 src/main/resources/db/migration/V20241219_1__Add_Weather_Tables.sql，包含4张核心表的完整结构]
    *   更改摘要：[完成天气预报功能所需的全部数据库表设计，包括表格配置表、扩展题目表、答案数据表、评分记录表，以及完整的索引和基础数据]
    *   原因：[执行计划步骤 1 的初步实施]
    *   阻碍：[无]
    *   用户确认状态：[成功]
    *   交互式审查脚本退出信息: [用户通过"继续"关键字正常结束审查]

*   2024-12-19 15:35
    *   步骤：2. 创建后端基础实体类 (初步完成, 审查需求: review:true)
    *   修改：[创建了完整的天气预报模块实体类结构，包括WeatherTableConfig主配置实体、WeatherAnswer答案实体、TableSchema/ValidationRules/ScoringConfig配置类，以及WeatherMybatisConfig配置类，并配置了JSON类型处理器]
    *   更改摘要：[建立了完整的天气预报模块实体层，支持复杂JSON数据存储和处理，包含表格配置、答案存储、验证规则、评分配置等核心功能]
    *   原因：[执行计划步骤 2 的初步实施]
    *   阻碍：[无]
    *   用户确认状态：[成功]
    *   交互式审查脚本退出信息: [用户通过"继续"关键字正常结束审查]

*   2024-12-19 15:45
    *   步骤：3. 创建Mapper接口和XML文件 (初步完成, 审查需求: review:true)
    *   修改：[创建了完整的Mapper层架构，包括WeatherTableConfigMapper和WeatherAnswerMapper接口，以及对应的XML映射文件，实现了基础CRUD、条件查询、批量操作、统计分析等核心数据访问功能]
    *   更改摘要：[建立了完整的数据访问层，支持表格配置管理、答案数据操作、复杂查询统计，配置了JSON类型处理器确保复杂数据正确映射]
    *   原因：[执行计划步骤 3 的初步实施]
    *   阻碍：[无]
    *   用户确认状态：[成功]
    *   交互式审查脚本退出信息: [用户通过"继续"关键字正常结束审查]

*   2024-12-19 15:55
    *   步骤：4. 创建Service业务逻辑层 (初步完成, 审查需求: review:true)
    *   修改：[创建了完整的Service业务逻辑层，包括WeatherTableConfigService和WeatherAnswerService接口及其实现类，实现了配置管理、答题数据处理、数据验证、自动评分、人工评分、统计分析等核心业务功能]
    *   更改摘要：[建立了完整的业务逻辑层，支持表格配置验证和管理、答题流程控制、复杂的数据验证规则、智能评分算法框架、统计分析和数据导出等功能，为上层Controller提供了丰富的业务服务]
    *   原因：[执行计划步骤 4 的初步实施]
    *   阻碍：[无]
    *   用户确认状态：[成功]
    *   交互式审查脚本退出信息: [用户通过"继续"关键字正常结束审查]

*   2024-12-19 16:05
    *   步骤：5. 创建Controller控制层 (初步完成, 审查需求: review:true)
    *   修改：[创建了完整的Controller控制层，包括WeatherTableConfigController和WeatherAnswerController，实现了表格配置管理API、答题数据管理API、验证评分API、统计分析API等REST接口，遵循现有系统的ApiRest规范]
    *   更改摘要：[建立了完整的API接口层，提供了配置CRUD、答题流程控制、数据验证、自动评分、人工评分、统计导出等功能的HTTP接口，使用Swagger注解提供API文档]
    *   原因：[执行计划步骤 5 的初步实施]
    *   阻碍：[无]
    *   状态：[等待后续处理（审查）]

# 最终审查 (由 REVIEW 模式填充)

[待REVIEW模式填充最终的符合性评估] 