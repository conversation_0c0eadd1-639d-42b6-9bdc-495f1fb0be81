# 错题集功能使用说明

## 功能介绍

错题集功能允许系统在用户回答错误的题目后自动将其添加到个人错题集中。用户可以通过错题集专项练习这些错题，并标记是否已掌握，以提高考试成绩。

## 数据库设计

系统创建了`el_user_wrong_book`表用于存储错题数据：

| 字段 | 类型 | 说明 |
| --- | --- | --- |
| id | varchar(64) | 主键ID |
| user_id | varchar(64) | 用户ID |
| qu_id | varchar(64) | 题目ID |
| qu_type | int | 题目类型 |
| answer | varchar(5000) | 用户的错误答案 |
| wrong_count | int | 错误次数 |
| is_mastered | tinyint(1) | 是否已掌握 |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |

## 主要功能

1. **错题自动收集**：用户在APP上答题错误时，系统自动将错题添加到个人错题集
2. **错题集查询**：用户可以分页查询自己的错题列表，支持按题型和是否掌握筛选
3. **错题详情查看**：用户可以查看错题的详细信息，包括题目内容、选项和正确答案
4. **掌握状态标记**：用户可以标记错题为"已掌握"或"未掌握"，帮助学习进度跟踪

## 接口说明

### 1. 分页查询错题集

- 请求路径：`/exam/api/user/wrong-book-v2/paging`
- 请求方法：POST
- 请求参数：
  ```json
  {
    "current": 1,
    "size": 10,
    "params": {
      "quType": null,
      "isMastered": false
    }
  }
  ```
- 返回结果：分页的错题集列表

### 2. 查询错题详情

- 请求路径：`/exam/api/user/wrong-book-v2/detail`
- 请求方法：POST
- 请求参数：
  ```json
  {
    "id": "题目ID"
  }
  ```
- 返回结果：错题的详细信息

### 3. 标记为已掌握

- 请求路径：`/exam/api/user/wrong-book-v2/set-mastered`
- 请求方法：POST
- 请求参数：
  ```json
  {
    "id": "错题记录ID"
  }
  ```
- 返回结果：操作结果

### 4. 标记为未掌握

- 请求路径：`/exam/api/user/wrong-book-v2/set-unmastered`
- 请求方法：POST
- 请求参数：
  ```json
  {
    "id": "错题记录ID"
  }
  ```
- 返回结果：操作结果

## 使用步骤

1. 运行`docs/el_user_wrong_book.sql`创建错题集数据表
2. 用户在APP上答题后，系统会自动保存错误的题目到错题集
3. 用户可以通过前端接口查看和管理自己的错题集

## 未来扩展方向

1. 错题集智能推荐功能
2. 错题分析和统计功能
3. 错题专项训练功能
4. 错题导出功能 