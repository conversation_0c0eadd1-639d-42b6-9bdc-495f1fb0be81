CREATE TABLE `el_user_wrong_book` (
  `id` varchar(64) NOT NULL COMMENT 'ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `qu_id` varchar(64) NOT NULL COMMENT '题目ID',
  `qu_type` int(11) NOT NULL COMMENT '题目类型',
  `answer` varchar(5000) DEFAULT NULL COMMENT '用户的答案',
  `wrong_count` int(11) NOT NULL DEFAULT '1' COMMENT '错误次数',
  `is_mastered` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已掌握',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_qu_index` (`user_id`,`qu_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户错题本'; 