module.exports = {
  preset: '@vue/cli-plugin-unit-jest',
  
  // 测试环境配置
  testEnvironment: 'jsdom',
  
  // 测试文件匹配模式
  testMatch: [
    '**/tests/unit/**/*.spec.(js|jsx|ts|tsx)|**/__tests__/*.(js|jsx|ts|tsx)'
  ],
  
  // 模块路径映射
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@convection/(.*)$': '<rootDir>/src/components/convection/$1'
  },
  
  // 转换配置
  transform: {
    '^.+\\.vue$': 'vue-jest',
    '.+\\.(css|styl|less|sass|scss|svg|png|jpg|ttf|woff|woff2)$': 'jest-transform-stub',
    '^.+\\.(js|jsx)$': 'babel-jest'
  },
  
  // 模块文件扩展名
  moduleFileExtensions: [
    'js',
    'jsx',
    'json',
    'vue'
  ],
  
  // 覆盖率配置
  collectCoverage: true,
  collectCoverageFrom: [
    // 强对流模块组件
    'src/components/convection/**/*.{js,vue}',
    'src/views/convection/**/*.{js,vue}',
    'src/api/convection/**/*.js',
    'src/store/modules/convection.js',
    'src/utils/errorHandler.js',
    // 排除文件
    '!**/node_modules/**',
    '!**/dist/**',
    '!**/coverage/**',
    '!src/api/convection/index.js', // 导出文件
    '!src/components/convection/index.js' // 导出文件
  ],
  
  // 覆盖率报告
  coverageReporters: [
    'html',
    'text',
    'text-summary',
    'lcov'
  ],
  
  // 覆盖率阈值
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 75,
      lines: 75,
      statements: 75
    },
    // 强对流模块特定阈值
    'src/components/convection/**/*.{js,vue}': {
      branches: 80,
      functions: 85,
      lines: 80,
      statements: 80
    },
    'src/store/modules/convection.js': {
      branches: 85,
      functions: 90,
      lines: 85,
      statements: 85
    }
  },
  
  // 忽略的文件模式
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/coverage/'
  ],
  
  // 测试设置文件
  setupFilesAfterEnv: [
    '<rootDir>/tests/unit/setup.js'
  ],
  
  // 模拟的模块
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  
  // 快照序列化器
  snapshotSerializers: [
    'jest-serializer-vue'
  ],
  
  // 全局变量
  globals: {
    'vue-jest': {
      babelConfig: {
        presets: [
          [
            '@babel/preset-env',
            {
              targets: {
                node: 'current'
              }
            }
          ]
        ],
        plugins: [
          '@babel/plugin-syntax-dynamic-import'
        ]
      }
    }
  },
  
  // 测试超时时间
  testTimeout: 10000,
  
  // 详细输出
  verbose: true,
  
  // 测试结果处理器
  testResultsProcessor: 'jest-sonar-reporter',
  
  // 清除模拟
  clearMocks: true,
  
  // 恢复模拟
  restoreMocks: true
} 