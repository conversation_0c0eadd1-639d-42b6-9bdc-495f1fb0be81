# 历史个例考试保存问题修复总结

## 🔍 问题分析

### 根本原因
历史个例考试保存成功但在分页接口中查询不到数据的问题，根本原因是：

1. **前端问题**：历史个例考试保存时没有设置`openType`和`departIds`字段
2. **后端逻辑**：ExamServiceImpl.save方法只有在`openType == DEPT_OPEN`时才保存部门信息到`el_exam_depart`表
3. **查询限制**：ExamMapper.xml的paging查询要求所有考试都必须在`el_exam_depart`表中有记录

### 查询SQL限制
```sql
AND ex.id IN (SELECT exam_id FROM el_exam_depart WHERE depart_id = (SELECT depart_id FROM sys_user WHERE id = #{userId}))
```

这个条件导致没有部门记录的考试无法被查询到。

## 🛠️ 解决方案

### 1. 前端Store修复
**文件**: `exam-vue/src/store/modules/user.js`

- 添加`departId`字段到state中
- 添加`SET_DEPART_ID` mutation
- 在`getInfo`方法中存储用户的`departId`

### 2. 前端Getters修复
**文件**: `exam-vue/src/store/getters.js`

- 添加`departId: state => state.user.departId`

### 3. 历史个例考试保存修复
**文件**: `exam-vue/src/views/weather/exam/index.vue`

修改`submitForm`方法，为历史个例考试设置默认值：
```javascript
const examData = {
  ...this.examForm,
  openType: 2, // 部门开放
  departIds: [this.$store.getters.departId], // 当前用户所在部门
  totalTime: this.examForm.duration // 将duration映射为totalTime
}
```

## 📋 修改的文件列表

1. `exam-vue/src/store/modules/user.js` - 添加departId存储
2. `exam-vue/src/store/getters.js` - 添加departId getter
3. `exam-vue/src/views/weather/exam/index.vue` - 修复考试保存逻辑

## ✅ 预期效果

### 保存流程
1. 前端保存历史个例考试时自动设置`openType=2`和`departIds=[当前用户部门ID]`
2. 后端ExamServiceImpl.save方法检测到`openType == DEPT_OPEN`，调用`examDepartService.saveAll`
3. 考试记录和部门关联记录都正确保存到数据库

### 查询流程
1. 分页查询时，SQL能够在`el_exam_depart`表中找到对应的记录
2. 历史个例考试能够正常显示在列表中

## 🧪 测试步骤

1. **重启前端应用**
   ```bash
   cd exam-vue
   npm run dev
   ```

2. **创建历史个例考试**
   - 访问"历史个例考试管理"页面
   - 点击"新增"按钮
   - 填写考试信息并保存

3. **验证保存结果**
   - 检查考试是否出现在列表中
   - 检查数据库`el_exam`和`el_exam_depart`表中的数据

4. **验证查询功能**
   - 刷新页面，确认考试仍然显示
   - 尝试搜索和筛选功能

## 🔧 数据库验证

可以通过以下SQL验证修复效果：

```sql
-- 查看考试记录
SELECT id, title, open_type, question_id FROM el_exam WHERE question_id IS NOT NULL;

-- 查看部门关联记录
SELECT ed.exam_id, ed.depart_id, e.title 
FROM el_exam_depart ed 
JOIN el_exam e ON ed.exam_id = e.id 
WHERE e.question_id IS NOT NULL;
```

## 📝 注意事项

1. 此修复确保历史个例考试遵循系统的部门权限控制机制
2. 考试只对创建者所在部门的用户可见
3. 如需要更改权限范围，可以在前端界面中添加部门选择功能
