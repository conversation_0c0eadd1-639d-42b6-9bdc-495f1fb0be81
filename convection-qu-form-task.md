# 上下文
文件名：convection-qu-form-task.md
创建于：2024-12-28
创建者：用户/AI
关联协议：RIPER-5 + Multidimensional + Agent Protocol (Conditional Interactive Step Review Enhanced)

# 任务描述
历史个例试题管理中，我需要修改为原来的降水落区文件上传，而不是现在的雷暴大风和短时强降水文件上传

# 项目概述
这是一个Vue.js前端项目中的天气考试系统，需要将历史个例试题管理页面中的文件上传功能从雷暴大风和短时强降水文件上传修改回降水落区文件上传。

---
*以下部分由 AI 在协议执行过程中维护*
---

# 分析 (由 RESEARCH 模式填充)
- 发现当前系统在 `exam-vue/src/views/weather/qu/index.vue` 中实现了雷暴大风和短时强降水文件上传
- 表格列显示：第74-90行有"雷暴大风实况"和"短时强降水实况"列
- 上传表单：第501-631行有对应的文件上传组件
- 数据字段：thunderstormWindFilePath、thunderstormWindFileName、shortPrecipitationFilePath、shortPrecipitationFileName
- API接口：/exam/api/weather/case/upload/thunderstorm-wind 和 /exam/api/weather/case/upload/short-precipitation
- 原有降水落区上传接口：/exam/api/weather/case/upload/precipitation-area（在PrecipitationDrawing组件中使用）

# 提议的解决方案 (由 INNOVATE 模式生成)
推荐采用方案一的优化版本：
1. 将两个文件上传位置合并为一个"降水落区文件上传"
2. 复用现有的文件上传框架，但更改为降水落区专用的API接口
3. 更新表格显示列，将两列合并为一个"降水落区文件"列
4. 保持现有的文件上传UI组件结构，但调整标签和验证逻辑

这个方案平衡了实现复杂度和功能需求，既满足了用户将文件上传改回降水落区的需求，又保持了代码的可维护性。

# 实施计划 (由 PLAN 模式生成)
实施检查清单：
1. [修改表格列显示，将雷暴大风实况和短时强降水实况列合并为降水落区文件列, review:true]
2. [移除雷暴大风和短时强降水文件上传组件，添加新的降水落区文件上传组件, review:true]
3. [更新data部分的字段定义，移除旧字段添加新字段, review:true]
4. [更新API接口配置，使用降水落区上传接口, review:true]
5. [实现降水落区文件上传相关的JavaScript处理方法, review:true]
6. [更新resetForm方法中的字段重置逻辑, review:true]
7. [更新handleEdit方法中的数据解析和文件列表重建逻辑, review:true]
8. [更新表单提交方法中的数据保存逻辑, review:true]
9. [检查和更新任何其他引用到旧字段的地方, review:true]
10. [验证修改后的功能是否正常工作, review:false]

# 当前执行步骤 (由 EXECUTE 模式在开始执行某步骤时更新)
> 正在执行: "检查清单第2项：移除雷暴大风和短时强降水文件上传组件，添加新的降水落区文件上传组件" (审查需求: review:true, 状态: 初步完成，等待交互式审查)

# 任务进度 (由 EXECUTE 模式在每步完成后，以及在交互式审查迭代中追加)
*   2024-12-28 16:35:10
    *   步骤：检查清单第1项：修改表格列显示 (审查需求: review:true, 状态：交互式审查结束)
    *   修改：将第74-90行的两个表格列（雷暴大风实况、短时强降水实况）合并为一个新列（降水落区文件），使用新的方法名getPrecipitationAreaFileName；实现了getPrecipitationAreaFileName方法修复运行时错误
    *   更改摘要：成功将表格显示从分离的雷暴大风和短时强降水实况列改为统一的降水落区文件列，简化了用户界面；修复了方法缺失的运行时错误
    *   原因：完成对步骤1的交互式审查，并立即修复发现的错误
    *   阻碍：用户反馈发现getPrecipitationAreaFileName方法未实现，已修复
    *   用户确认状态：成功
    *   交互式审查脚本退出信息: 用户通过'完成'结束了对【本步骤】的审查

*   2024-12-28 16:42:15
    *   步骤：检查清单第2项：移除雷暴大风和短时强降水文件上传组件，添加新的降水落区文件上传组件 (审查需求: review:true, 状态：初步完成)
    *   修改：将第492-622行的两个文件上传组件（雷暴大风实况文件和短时强降水实况文件）合并为一个降水落区文件上传组件，更新了所有相关的属性和事件处理器引用
    *   更改摘要：成功简化了文件上传界面，从两个分离的上传组件合并为一个统一的降水落区文件上传组件，保持了相同的UI结构但使用了新的命名约定
    *   原因：执行计划步骤2的初步实施
    *   阻碍：无
    *   用户确认状态：等待交互式审查确认
    *   (若适用)交互式审查脚本退出信息: 待启动

# 最终审查 (由 REVIEW 模式填充)
[待填充]