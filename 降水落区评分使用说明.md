# 历史个例降水落区评分业务实现

## 概述

本文档介绍了历史个例批卷业务中降水落区评分的具体实现，包括MICAPS文件解析、考生答案处理、TS评分计算和最终评分生成。

## 系统架构

### 核心组件

1. **PrecipitationAreaScoringService** - 降水落区评分核心服务
2. **MicapsDataService** - MICAPS文件解析服务
3. **WeatherScoringEngine** - 天气评分引擎（已集成降水落区评分）
4. **PrecipitationScoringController** - 降水落区评分控制器

### 数据流程

```
实况降水文件(MICAPS第一类) → 解析站点降水数据
CMA-MESO文件(MICAPS第四类) → 格点数据插值到站点
考生答案(JSON降水落区) → 判断站点在哪个降水等级区域内
↓
计算各量级TS评分 → 计算基础分 → 计算技巧评分 → 加权求和得到最终评分
```

## 文件格式说明

### 1. 实况降水文件（MICAPS第一类）

**文件格式**：
```
diamond 1 actual_precipitation 2025 1 29 8
10
54511 116.28 39.93 55.0 -2.5 1013.2 270 8 5.2 10.0
54527 117.12 40.48 32.0 -1.8 1012.8 280 6 12.5 15.0
...
```

**字段说明**：
- 第1行：文件头（diamond 数据类型 描述 年 月 日 时）
- 第2行：站点总数
- 后续行：站号 经度 纬度 海拔 温度 气压 风向 风速 **降水量** 能见度

### 2. CMA-MESO文件（MICAPS第四类）

**文件格式**：
```
diamond 4 24h_precipitation 2025 1 29 8 24 0
0.5 0.5 115.0 119.0 38.0 42.0 8 8
0.0 0.0 0.0 0.0 0.0 0.0 0.0 0.0
0.0 2.5 5.0 7.5 10.0 12.5 15.0 0.0
...
```

**字段说明**：
- 第1行：文件头（diamond 数据类型 描述 年 月 日 时 时效 层次）
- 第2行：经度间隔 纬度间隔 起始经度 终止经度 起始纬度 终止纬度 经向格点数 纬向格点数
- 后续行：格点数据值（按先纬向后经向排列）

### 3. 考生答案（JSON格式）

**数据结构**：
```json
{
  "content": {
    "小雨": [
      {
        "geometry": {
          "type": "Polygon",
          "coordinates": [
            [
              [116.0, 39.5],
              [117.0, 39.5],
              [117.0, 40.5],
              [116.0, 40.5],
              [116.0, 39.5]
            ]
          ]
        }
      }
    ],
    "中雨": [...],
    "大雨": [...],
    "暴雨": [...],
    "大暴雨": [...]
  }
}
```

## 评分算法详解

### 1. 降水等级分类

```java
private String classifyPrecipitationLevel(double precipitation) {
    if (precipitation < 0.1) return "无雨";
    else if (precipitation < 10.0) return "小雨";
    else if (precipitation < 25.0) return "中雨";
    else if (precipitation < 50.0) return "大雨";
    else if (precipitation < 100.0) return "暴雨";
    else return "大暴雨";
}
```

### 2. 晴雨TS评分

**公式**：`晴雨TS = (A + D) / (A + B + C + D)`

**混淆矩阵**：
- A：正确预报有降水的站点数
- B：空报降水的站点数
- C：漏报降水的站点数
- D：正确预报无降水的站点数

### 3. 降水分级TS评分

**公式**：`量级TS = A / (A + B + C)`

**评分规则**：
- A：实况该量级且预报该量级的站点数
- B：实况该量级但预报为其他量级的站点数
- C：实况该量级但预报无雨的站点数

### 4. 技巧评分计算

**基础分规则**：
- 学生TS ≥ CMA-MESO TS → 基础分 = 0.3
- 学生TS < CMA-MESO TS → 基础分 = 0.0

**技巧评分公式**：
```
技巧评分 = 基础分 + 学生TS × 0.7
```

### 5. 最终评分计算

**权重分配**：
- 晴雨：0.1
- 小雨：0.2
- 中雨：0.2
- 大雨：0.2
- 暴雨：0.2
- 大暴雨：0.1

**最终评分公式**：
```
最终评分 = (各量级技巧评分 × 对应权重) × 40分
```

## API接口说明

### 1. 计算降水落区评分

**接口**：`POST /weather/scoring/precipitation/calculate`

**参数**：
- `actualFilePath`: 实况降水文件路径
- `cmaMesoFilePath`: CMA-MESO文件路径
- `studentAnswer`: 考生答案JSON

**响应**：
```json
{
  "code": 200,
  "message": "降水落区评分计算成功",
  "data": {
    "success": true,
    "finalScore": 21.5,
    "totalStations": 10,
    "studentTSScores": {
      "晴雨": 0.8,
      "小雨": 0.6,
      "中雨": 0.4,
      "大雨": 0.2,
      "暴雨": 0.0,
      "大暴雨": 0.0
    },
    "cmaMesoTSScores": {...},
    "skillScores": {...}
  }
}
```

### 2. 测试降水落区评分

**接口**：`GET /weather/scoring/precipitation/test`

**功能**：使用示例数据测试评分功能

## 使用示例

### 1. 基本使用

```java
@Autowired
private PrecipitationAreaScoringService precipitationAreaScoringService;

public void calculateScore() {
    String actualFilePath = "path/to/actual.000";
    String cmaMesoFilePath = "path/to/cma_meso.004";
    Map<String, Object> studentAnswer = getStudentAnswer();
    
    PrecipitationScoringResult result = precipitationAreaScoringService
        .calculatePrecipitationScore(actualFilePath, cmaMesoFilePath, studentAnswer);
    
    if (result.isSuccess()) {
        System.out.println("最终得分：" + result.getFinalScore());
        System.out.println("评分详情：" + result.getScoringSummary());
    }
}
```

### 2. 集成到评分引擎

降水落区评分已自动集成到`WeatherScoringEngine`中，在历史个例批卷时会自动调用。

### 3. 测试功能

```java
@Autowired
private PrecipitationScoringTestService testService;

public void runTest() {
    // 执行完整测试
    PrecipitationScoringResult result = testService.runFullTest();
    
    // 测试特定等级
    testService.testSpecificLevel("小雨");
    
    // 批量测试所有等级
    testService.testAllLevels();
}
```

## 配置说明

### 1. 文件路径配置

在题目的`scenarioData`中配置文件路径：
```json
{
  "actualPrecipitationFile": "path/to/actual.000",
  "cmaMesoFile": "path/to/cma_meso.004"
}
```

### 2. 评分权重配置

权重配置在`PrecipitationAreaScoringService`中：
```java
Map<String, Double> weights = new HashMap<>();
weights.put("晴雨", 0.1);
weights.put("小雨", 0.2);
weights.put("中雨", 0.2);
weights.put("大雨", 0.2);
weights.put("暴雨", 0.2);
weights.put("大暴雨", 0.1);
```

## 注意事项

### 1. 数据质量要求
- MICAPS文件格式必须正确
- 站点坐标必须在CMA-MESO格点范围内
- 考生答案必须包含有效的GeoJSON格式数据

### 2. 性能考虑
- 大量站点时插值计算可能较慢
- 复杂多边形的点在多边形内判断可能耗时
- 建议对大数据量进行分批处理

### 3. 错误处理
- 文件不存在或格式错误时返回错误信息
- 数据解析失败时使用默认值或跳过
- 异常情况下确保不影响其他评分项目

## 扩展功能

### 1. 支持更多降水等级
可以在`classifyPrecipitationLevel`方法中添加更细的等级划分

### 2. 支持不同的插值方法
可以在`MicapsType4Data`中实现更复杂的插值算法

### 3. 支持评分结果可视化
可以生成评分结果的地图可视化展示

## 故障排除

### 1. 常见错误

**文件解析失败**：
- 检查文件格式是否正确
- 检查文件编码是否为UTF-8
- 检查文件路径是否存在

**TS评分为0**：
- 检查实况数据是否有降水
- 检查考生答案是否覆盖了有降水的站点
- 检查降水等级分类是否正确

**最终评分异常**：
- 检查权重配置是否正确
- 检查基础分计算逻辑
- 检查CMA-MESO基准数据

### 2. 调试建议

1. 开启详细日志：`logging.level.com.yf.exam.modules.weather.scoring=DEBUG`
2. 使用测试服务验证各个环节
3. 检查中间结果的合理性
4. 对比手工计算结果验证算法正确性

---

**文档版本**：v1.0  
**最后更新**：2025-01-29  
**维护人员**：天气预报模块开发团队
