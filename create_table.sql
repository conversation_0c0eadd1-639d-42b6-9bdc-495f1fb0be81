CREATE TABLE IF NOT EXISTS el_weather_history_exam_answer (id varchar(50) NOT NULL COMMENT '主键', exam_id varchar(50) NOT NULL COMMENT '考试ID', question_id varchar(50) NOT NULL COMMENT '题目ID', user_id varchar(50) NOT NULL COMMENT '用户ID', precipitation_answer longtext COMMENT '第一部分：降水分级落区预报答案JSON', weather_answer longtext COMMENT '第二部分：灾害性天气预报答案JSON', overall_progress int(3) DEFAULT '0' COMMENT '整体进度百分比', answer_status tinyint(1) DEFAULT '0' COMMENT '答题状态：0-答题中，1-已提交', answer_time datetime DEFAULT NULL COMMENT '答题时间（最后保存时间）', time_spent int(11) DEFAULT '0' COMMENT '已用时间（秒）', submit_time datetime DEFAULT NULL COMMENT '提交时间', total_score decimal(5,2) DEFAULT NULL COMMENT '总得分', score_details longtext COMMENT '得分详情JSON', grading_status tinyint(1) DEFAULT '0' COMMENT '批改状态：0-未批改，1-已批改', grading_time datetime DEFAULT NULL COMMENT '批改时间', grader_id varchar(50) DEFAULT NULL COMMENT '批改人ID', grading_remark text COMMENT '批改备注', create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', PRIMARY KEY (id), UNIQUE KEY uk_exam_question_user (exam_id,question_id,user_id), KEY idx_exam_id (exam_id), KEY idx_question_id (question_id), KEY idx_user_id (user_id), KEY idx_answer_status (answer_status), KEY idx_grading_status (grading_status), KEY idx_create_time (create_time)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='历史个例考试答案表';
