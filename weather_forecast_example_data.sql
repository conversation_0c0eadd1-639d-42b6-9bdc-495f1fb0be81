-- 历史个例考试数据库设计

-- 1. 历史个例题目表
CREATE TABLE IF NOT EXISTS weather_case_question (
    id VARCHAR(32) PRIMARY KEY,
    title VARCHAR(200) NOT NULL COMMENT '题目标题',
    content TEXT COMMENT '题目内容描述',
    forecast_date VARCHAR(50) NOT NULL COMMENT '预报起报日期(如2035年9月9日)',
    forecast_time VARCHAR(20) NOT NULL COMMENT '预报时间(如08时)',
    data_file_path VARCHAR(500) COMMENT 'Micaps数据文件路径',
    data_file_name VARCHAR(200) COMMENT '数据文件名称',
    stations TEXT NOT NULL COMMENT '站点列表(JSON格式)',
    total_score DECIMAL(5,2) DEFAULT 100 COMMENT '总分',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 2. 历史个例标准答案表
CREATE TABLE IF NOT EXISTS weather_case_answer (
    id VARCHAR(32) PRIMARY KEY,
    question_id VARCHAR(32) NOT NULL COMMENT '题目ID',
    station_name VARCHAR(50) NOT NULL COMMENT '站点名称',
    station_order INT DEFAULT 0 COMMENT '站点排序',
    -- 08-08时段天气要素
    wind_force_0808 VARCHAR(10) COMMENT '08-08最大风力等级(一级到十二级)',
    wind_direction_0808 VARCHAR(20) COMMENT '08-08最大风力时的风向(八方位)',
    min_temperature VARCHAR(10) COMMENT '最低气温℃',
    max_temperature VARCHAR(10) COMMENT '最高气温℃',
    precipitation_0808 VARCHAR(20) COMMENT '08-08降水量级',
    disaster_weather VARCHAR(50) COMMENT '灾害性天气类型',
    -- 20-20时段天气要素(如果需要)
    wind_force_2020 VARCHAR(10) COMMENT '20-20最大风力等级',
    wind_direction_2020 VARCHAR(20) COMMENT '20-20最大风力时的风向',
    precipitation_2020 VARCHAR(20) COMMENT '20-20降水量级',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_question (question_id),
    INDEX idx_station (station_name)
);

-- 3. 考生答题记录表
CREATE TABLE IF NOT EXISTS weather_case_user_answer (
    id VARCHAR(32) PRIMARY KEY,
    paper_id VARCHAR(32) NOT NULL COMMENT '试卷ID',
    question_id VARCHAR(32) NOT NULL COMMENT '题目ID',
    user_id VARCHAR(32) NOT NULL COMMENT '考生ID',
    station_name VARCHAR(50) NOT NULL COMMENT '站点名称',
    -- 08-08时段考生答案
    wind_force_0808 VARCHAR(10) COMMENT '考生填写的08-08最大风力等级',
    wind_direction_0808 VARCHAR(20) COMMENT '考生填写的08-08风向',
    min_temperature VARCHAR(10) COMMENT '考生填写的最低气温',
    max_temperature VARCHAR(10) COMMENT '考生填写的最高气温',
    precipitation_0808 VARCHAR(20) COMMENT '考生填写的08-08降水量级',
    disaster_weather VARCHAR(50) COMMENT '考生填写的灾害性天气类型',
    -- 20-20时段考生答案(如果需要)
    wind_force_2020 VARCHAR(10) COMMENT '考生填写的20-20最大风力等级',
    wind_direction_2020 VARCHAR(20) COMMENT '考生填写的20-20风向',
    precipitation_2020 VARCHAR(20) COMMENT '考生填写的20-20降水量级',
    station_score DECIMAL(5,2) DEFAULT 0 COMMENT '该站点得分',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_paper_question (paper_id, question_id),
    INDEX idx_user (user_id)
);

-- 4. 插入示例题目数据
INSERT INTO weather_case_question (
    id,
    title,
    content,
    forecast_date,
    forecast_time,
    data_file_path,
    data_file_name,
    stations,
    total_score
) VALUES (
    'weather_case_001',
    '2035年9月9日08时历史个例天气预报',
    '根据提供的气象观测资料，对指定站点进行24小时天气预报。请仔细分析气象数据，预报各站点的风力、风向、气温、降水和灾害性天气情况。',
    '2035年9月9日',
    '08时',
    '/data/micaps/2035090908.dat',
    '2035090908.dat',
    '["站点1", "站点2", "站点3", "站点4", "站点5", "站点6"]',
    100.00
);
-- 5. 插入标准答案数据
INSERT INTO weather_case_answer (
    id, question_id, station_name, station_order,
    wind_force_0808, wind_direction_0808, min_temperature, max_temperature,
    precipitation_0808, disaster_weather,
    wind_force_2020, wind_direction_2020, precipitation_2020
) VALUES
('answer_001', 'weather_case_001', '站点1', 1, '三级', '东北', '15', '25', '小雨', '无', '二级', '东', '中雨'),
('answer_002', 'weather_case_001', '站点2', 2, '四级', '东', '18', '28', '中雨', '暴雨', '三级', '东南', '大雨'),
('answer_003', 'weather_case_001', '站点3', 3, '二级', '南', '20', '30', '大雨', '无', '一级', '南', '暴雨'),
('answer_004', 'weather_case_001', '站点4', 4, '五级', '西南', '16', '26', '大雨', '大风', '四级', '西', '小雨'),
('answer_005', 'weather_case_001', '站点5', 5, '三级', '西', '14', '24', '暴雨', '无', '二级', '西北', '无'),
('answer_006', 'weather_case_001', '站点6', 6, '六级', '西北', '12', '22', '无', '大雾', '五级', '北', '小雨');
-- 6. 查询验证数据
SELECT '=== 历史个例题目 ===' as info;
SELECT id, title, forecast_date, forecast_time, data_file_name FROM weather_case_question;

SELECT '=== 标准答案 ===' as info;
SELECT station_name, wind_force_0808, wind_direction_0808, min_temperature, max_temperature,
       precipitation_0808, disaster_weather, wind_force_2020, wind_direction_2020, precipitation_2020
FROM weather_case_answer
WHERE question_id = 'weather_case_001'
ORDER BY station_order;
