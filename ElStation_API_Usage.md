# ElStation 气象站点 API 使用说明

## 概述

根据 `el_station` 表结构生成的完整 Spring Boot + MyBatis-Plus 代码，支持气象站点的 CRUD 操作和查询功能。

## 生成的文件结构

```
src/main/java/com/yf/exam/modules/station/
├── entity/
│   └── ElStation.java                    # 实体类
├── dto/
│   ├── ElStationDTO.java                # 基础DTO
│   ├── request/
│   │   └── ElStationReqDTO.java         # 查询请求DTO
│   └── response/
│       └── ElStationRespDTO.java        # 响应DTO
├── mapper/
│   └── ElStationMapper.java             # MyBatis Mapper接口
├── service/
│   ├── ElStationService.java            # Service接口
│   └── impl/
│       └── ElStationServiceImpl.java    # Service实现类
└── controller/
    └── ElStationController.java         # REST控制器

src/main/resources/mapper/station/
└── ElStationMapper.xml                  # MyBatis XML映射文件
```

## API 接口说明

### 1. 分页查询站点
**接口地址**: `POST /exam/api/station/paging`

**请求参数**:
```json
{
  "current": 1,
  "size": 10,
  "params": {
    "adminCodeChn": "110000",     // 行政区划代码中文（模糊查询）
    "stationLevl": "国家站",       // 站点等级（精确查询）
    "stationName": "北京",         // 站点名称（模糊查询）
    "city": "北京",               // 城市（模糊查询）
    "cnty": "朝阳区",             // 县（模糊查询）
    "online": 1,                 // 是否在线（0-离线，1-在线）
    "drawTown": 1                // 是否绘制到地图（0-否，1-是）
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": "1234567890",
        "adminCodeChn": "110000",
        "alti": 50.0,
        "city": "北京",
        "cnty": "朝阳区",
        "lat": 39.9,
        "lon": 116.4,
        "stationIdC": "54511",
        "stationLevl": "国家站",
        "stationName": "北京站",
        "alias": "Beijing",
        "online": 1,
        "drawTown": 1,
        "sort": 1
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 2. 新增/修改站点
**接口地址**: `POST /exam/api/station/save`

**请求参数**:
```json
{
  "id": "1234567890",              // 修改时必填，新增时可为空
  "adminCodeChn": "110000",
  "alti": 50.0,
  "city": "北京",
  "cnty": "朝阳区",
  "lat": 39.9,
  "lon": 116.4,
  "stationIdC": "54511",
  "stationLevl": "国家站",
  "stationName": "北京站",
  "alias": "Beijing",
  "online": 1,
  "drawTown": 1,
  "sort": 1
}
```

### 3. 查询站点详情
**接口地址**: `POST /exam/api/station/detail`

**请求参数**:
```json
{
  "id": "1234567890"
}
```

### 4. 批量删除站点
**接口地址**: `POST /exam/api/station/delete`

**请求参数**:
```json
{
  "ids": ["1234567890", "1234567891"]
}
```

## 查询功能特点

### 支持的查询条件

1. **模糊查询字段**:
   - `adminCodeChn`: 行政区划代码中文
   - `stationName`: 站点名称
   - `city`: 城市
   - `cnty`: 县

2. **精确查询字段**:
   - `stationLevl`: 站点等级
   - `online`: 是否在线
   - `drawTown`: 是否绘制到地图

3. **排序规则**:
   - 按 `sort` 字段升序排列
   - 相同 `sort` 值按 `id` 升序排列

## 数据库表结构对应

| 数据库字段 | Java属性 | 类型 | 说明 |
|-----------|----------|------|------|
| id | id | String | 主键ID |
| admin_code_chn | adminCodeChn | String | 行政区划代码中文 |
| alti | alti | Float | 海拔高度 |
| city | city | String | 城市 |
| cnty | cnty | String | 县 |
| lat | lat | Float | 纬度 |
| lon | lon | Double | 经度 |
| station_id_c | stationIdC | String | 站点识别码 |
| station_levl | stationLevl | String | 站点等级 |
| station_name | stationName | String | 站点名称 |
| alias | alias | String | 别名 |
| online | online | Integer | 是否在线 |
| draw_town | drawTown | Integer | 是否绘制到地图 |
| sort | sort | Integer | 排序 |

## 使用示例

### 查询所有国家站
```json
{
  "current": 1,
  "size": 20,
  "params": {
    "stationLevl": "国家站"
  }
}
```

### 查询北京地区的在线站点
```json
{
  "current": 1,
  "size": 20,
  "params": {
    "adminCodeChn": "110000",
    "online": 1
  }
}
```

### 模糊查询站点名称包含"北京"的站点
```json
{
  "current": 1,
  "size": 20,
  "params": {
    "stationName": "北京"
  }
}
```

## 注意事项

1. 所有接口都使用 POST 方法
2. 分页查询支持多条件组合查询
3. 模糊查询使用 `LIKE '%keyword%'` 方式
4. 主键 ID 使用 MyBatis-Plus 的 `ASSIGN_ID` 策略自动生成
5. 查询结果按 `sort` 和 `id` 字段排序
