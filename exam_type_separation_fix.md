# 考试类型区分修复方案

## 🔍 问题分析

### 问题现象
历史个例考试管理页面显示的是理论考试，两种考试类型没有正确区分。

### 根本原因
1. **后端查询逻辑**：ExamMapper.xml的paging查询没有区分考试类型
2. **前端API调用**：各个考试管理页面调用相同的API，没有传递考试类型参数
3. **数据区分标识**：缺少明确的考试类型过滤条件

## 🛠️ 解决方案

### 1. 后端查询逻辑修复

**文件**: `src/main/resources/mapper/exam/ExamMapper.xml`

在paging查询中添加考试类型过滤条件：

```xml
<!-- 考试类型过滤：区分理论考试和历史个例考试 -->
<if test="query.examType!=null and query.examType=='weather'">
    AND ex.question_id IS NOT NULL
</if>
<if test="query.examType!=null and query.examType=='traditional'">
    AND ex.question_id IS NULL
</if>
```

**区分逻辑**：
- **历史个例考试**：`question_id IS NOT NULL`（有关联的题目ID）
- **理论考试**：`question_id IS NULL`（通过题库组卷，没有单独的题目ID）

### 2. 前端页面修复

#### 历史个例考试管理页面
**文件**: `exam-vue/src/views/weather/exam/index.vue`

```javascript
getList() {
  const params = {
    current: this.listQuery.page,
    size: this.listQuery.limit,
    params: {
      title: this.listQuery.title,
      state: this.listQuery.state,
      startTime: this.listQuery.startTime,
      endTime: this.listQuery.endTime,
      examType: 'weather' // 只查询历史个例考试
    }
  }
  // API调用...
}
```

#### 理论考试管理页面
**文件**: `exam-vue/src/views/exam/exam/index.vue`

```javascript
listQuery: {
  current: 1,
  size: 10,
  params: {
    title: '',
    examType: 'traditional' // 只查询理论考试
  }
}
```

#### 学生端理论考试页面
**文件**: `exam-vue/src/views/paper/exam/list.vue`

```javascript
listQuery: {
  current: 1,
  size: 10,
  params: {
    examType: 'traditional' // 只查询理论考试
  }
}
```

## 📋 修改文件列表

### 后端文件
1. `src/main/resources/mapper/exam/ExamMapper.xml` - 添加考试类型过滤条件

### 前端文件
1. `exam-vue/src/views/weather/exam/index.vue` - 历史个例考试管理页面
2. `exam-vue/src/views/exam/exam/index.vue` - 理论考试管理页面
3. `exam-vue/src/views/paper/exam/list.vue` - 学生端理论考试页面

## 🎯 实现效果

### 考试类型区分
1. **历史个例考试管理**：只显示有`question_id`的考试
2. **理论考试管理**：只显示没有`question_id`的考试
3. **学生端页面**：各自显示对应类型的考试

### 数据隔离
- 历史个例考试：基于单个题目创建，有明确的题目关联
- 理论考试：基于题库组卷创建，通过题库选择题目

## 🧪 测试步骤

### 1. 重启应用
```bash
# 重启后端应用（如果修改了XML文件）
# 重启前端应用
cd exam-vue
npm run dev
```

### 2. 验证历史个例考试管理
1. 访问"历史个例考试管理"页面
2. 确认只显示历史个例考试
3. 创建新的历史个例考试，验证显示正确

### 3. 验证理论考试管理
1. 访问"考试管理"页面
2. 确认只显示理论考试
3. 创建新的理论考试，验证显示正确

### 4. 验证学生端页面
1. 访问"理论考试"页面，确认只显示理论考试
2. 访问"历史个例"页面，确认只显示历史个例考试

## 🔧 数据库验证

可以通过以下SQL验证区分效果：

```sql
-- 查看所有考试及其类型
SELECT 
    id, 
    title, 
    question_id,
    CASE 
        WHEN question_id IS NOT NULL THEN '历史个例考试'
        ELSE '理论考试'
    END as exam_type
FROM el_exam 
ORDER BY create_time DESC;

-- 查看历史个例考试
SELECT id, title, question_id FROM el_exam WHERE question_id IS NOT NULL;

-- 查看理论考试
SELECT id, title, question_id FROM el_exam WHERE question_id IS NULL;
```

## 📝 技术要点

### 1. 数据区分标识
- 使用`question_id`字段作为考试类型的区分标识
- 历史个例考试创建时会设置`question_id`
- 理论考试通过题库组卷，不设置`question_id`

### 2. 查询过滤逻辑
- 在SQL查询中添加条件过滤
- 前端传递`examType`参数控制查询范围
- 后端根据参数动态构建查询条件

### 3. 页面隔离
- 不同类型的考试管理页面独立显示
- 学生端考试列表按类型分别展示
- 确保数据不会混淆显示

## ✅ 预期结果

修复后，各个页面将正确显示对应类型的考试：
- 历史个例考试管理页面：只显示历史个例考试
- 理论考试管理页面：只显示理论考试
- 学生端页面：按类型正确分类显示

这样确保了考试类型的清晰区分，提升了用户体验和系统的可维护性。
