# 历史个例评分系统 - 开发计划

## 项目背景

### 当前状态分析
基于代码研究发现，系统已具备以下基础设施：
- ✅ **数据库结构**：`el_weather_history_exam_answer`表已存在，支持JSON格式答案存储
- ✅ **实体模型**：`WeatherHistoryExamAnswer`实体类完整
- ✅ **基础服务**：`WeatherHistoryExamAnswerService`提供基本CRUD操作
- ✅ **前端组件**：`WeatherForecastTable`组件支持天气预报表格编辑
- ✅ **题目支持**：`el_qu`表的`scenario_data`字段存储标准答案
- ⏳ **评分功能**：仅有基础评分接口，缺少具体算法实现

### 待开发功能
- ❌ **核心评分引擎**：灾害性天气站点预报评分算法
- ❌ **批量评分处理**：支持批量计算多个考生得分
- ❌ **评分报告生成**：详细的评分分析和错误诊断
- ❌ **管理界面**：教师端评分管理和结果查看界面
- ❌ **统计分析**：成绩统计和可视化展示

## 开发阶段规划

### Phase 1: 核心评分引擎开发 (2周)

#### 第一周：算法设计与数据结构
**时间**：第1-3天
**任务**：设计评分算法核心逻辑

1. **评分算法研究**
   ```
   任务：深入研究和美鲸网站的评分标准
   输出：评分规则文档
   验收：评分规则清晰，算法流程明确
   ```

2. **数据结构设计**
   ```java
   // 评分配置类
   public class WeatherScoringConfig {
       private String configId;
       private String configName;
       private Integer totalScore;
       private Map<String, Integer> elementWeights;
       private Map<String, Object> toleranceConfig;
   }
   
   // 评分结果类
   public class WeatherScoringResult {
       private String resultId;
       private String answerId;
       private BigDecimal totalScore;
       private Map<String, Object> stationScores;
       private Map<String, Object> errorAnalysis;
   }
   ```

3. **数据库表创建**
   ```sql
   -- 评分配置表
   CREATE TABLE `el_weather_scoring_config` (
     `id` varchar(64) NOT NULL COMMENT 'ID',
     `config_name` varchar(100) NOT NULL COMMENT '配置名称',
     `total_score` int(11) NOT NULL COMMENT '总分',
     `element_weights` json NOT NULL COMMENT '要素权重配置',
     `tolerance_config` json DEFAULT NULL COMMENT '容差配置',
     `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
     PRIMARY KEY (`id`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评分配置表';
   
   -- 评分结果表
   CREATE TABLE `el_weather_scoring_result` (
     `id` varchar(64) NOT NULL COMMENT 'ID',
     `answer_id` varchar(64) NOT NULL COMMENT '答案ID',
     `exam_id` varchar(64) NOT NULL COMMENT '考试ID',
     `user_id` varchar(64) NOT NULL COMMENT '用户ID',
     `question_id` varchar(64) NOT NULL COMMENT '题目ID',
     `total_score` decimal(5,2) NOT NULL COMMENT '总得分',
     `station_scores` json NOT NULL COMMENT '分站得分详情',
     `scoring_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
     PRIMARY KEY (`id`),
     UNIQUE KEY `uk_answer_id` (`answer_id`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评分结果表';
   ```

**时间**：第4-7天
**任务**：实现核心评分算法

4. **评分算法实现**
   ```java
   @Service
   public class WeatherScoringAlgorithm {
       
       /**
        * 计算天气预报得分
        */
       public WeatherScoringResult calculateScore(
           Map<String, Object> studentAnswer,
           Map<String, Object> standardAnswer,
           WeatherScoringConfig config) {
           
           // 1. 数据预处理和验证
           // 2. 站点匹配和对比
           // 3. 要素评分计算
           // 4. 权重应用和总分计算
           // 5. 错误分析生成
       }
       
       /**
        * 计算单个站点得分
        */
       private StationScore calculateStationScore(
           Map<String, Object> studentStation,
           Map<String, Object> standardStation,
           Map<String, Integer> weights) {
           // 具体实现各要素评分逻辑
       }
   }
   ```

5. **评分服务层开发**
   ```java
   @Service
   public class WeatherScoringService {
       
       @Autowired
       private WeatherScoringAlgorithm algorithm;
       
       @Autowired
       private WeatherHistoryExamAnswerService answerService;
       
       /**
        * 计算单个答案得分
        */
       public WeatherScoringResult scoreAnswer(String answerId) {
           // 获取答案数据
           // 获取标准答案
           // 获取评分配置
           // 调用算法计算
           // 保存结果
       }
   }
   ```

#### 第二周：API开发与单元测试
**时间**：第8-10天
**任务**：开发RESTful API接口

6. **控制器开发**
   ```java
   @RestController
   @RequestMapping("/exam/api/weather/scoring")
   public class WeatherScoringController {
       
       @PostMapping("/calculate")
       public ApiRest<WeatherScoringResult> calculateScore(@RequestBody ScoringRequest request) {
           // 单个答案评分
       }
       
       @PostMapping("/batch")
       public ApiRest<List<WeatherScoringResult>> batchCalculate(@RequestBody BatchScoringRequest request) {
           // 批量评分
       }
       
       @GetMapping("/result/{answerId}")
       public ApiRest<WeatherScoringResult> getResult(@PathVariable String answerId) {
           // 获取评分结果
       }
   }
   ```

7. **配置管理API**
   ```java
   @RestController
   @RequestMapping("/exam/api/weather/scoring/config")
   public class ScoringConfigController {
       
       @GetMapping
       public ApiRest<List<WeatherScoringConfig>> getConfigs() {
           // 获取配置列表
       }
       
       @PostMapping
       public ApiRest saveConfig(@RequestBody WeatherScoringConfig config) {
           // 保存配置
       }
   }
   ```

**时间**：第11-14天
**任务**：单元测试与集成测试

8. **单元测试开发**
   ```java
   @SpringBootTest
   class WeatherScoringAlgorithmTest {
       
       @Test
       void testCalculateScore_ExactMatch() {
           // 测试完全匹配情况
       }
       
       @Test
       void testCalculateScore_PartialMatch() {
           // 测试部分匹配情况
       }
       
       @Test
       void testCalculateScore_Temperature_Tolerance() {
           // 测试温度容差范围
       }
       
       @Test
       void testCalculateScore_DisasterWeather_Multiple() {
           // 测试多个灾害天气评分
       }
   }
   ```

9. **集成测试**
   ```java
   @SpringBootTest
   @AutoConfigureTestDatabase
   class WeatherScoringIntegrationTest {
       
       @Test
       void testFullScoringWorkflow() {
           // 完整评分流程测试
       }
   }
   ```

### Phase 2: 批量处理功能 (1.5周)

#### 第三周前半段：异步处理架构
**时间**：第15-18天

10. **异步任务设计**
    ```java
    @Component
    public class BatchScoringTask {
        
        @Async("scoringExecutor")
        public CompletableFuture<BatchScoringResult> processBatch(List<String> answerIds) {
            // 异步批量处理逻辑
        }
    }
    ```

11. **批量评分服务**
    ```java
    @Service
    public class BatchScoringService {
        
        public String submitBatchJob(BatchScoringRequest request) {
            // 提交批量任务
            // 返回任务ID
        }
        
        public BatchJobStatus getJobStatus(String jobId) {
            // 获取任务状态
        }
    }
    ```

#### 第四周后半段：错误处理与监控
**时间**：第19-21天

12. **错误处理机制**
    ```java
    @Component
    public class ScoringErrorHandler {
        
        public void handleScoringError(String answerId, Exception error) {
            // 错误记录和通知
        }
        
        public void retryFailedScoring(String answerId) {
            // 重试机制
        }
    }
    ```

13. **任务监控**
    ```java
    @RestController
    @RequestMapping("/exam/api/weather/scoring/monitor")
    public class ScoringMonitorController {
        
        @GetMapping("/jobs")
        public ApiRest<List<BatchJobInfo>> getJobs() {
            // 获取任务列表
        }
        
        @PostMapping("/jobs/{jobId}/cancel")
        public ApiRest cancelJob(@PathVariable String jobId) {
            // 取消任务
        }
    }
    ```

### Phase 3: 前端界面开发 (2周)

#### 第五周：管理界面开发
**时间**：第22-25天

14. **评分管理页面**
    ```vue
    <template>
      <div class="scoring-management">
        <el-card class="filter-card">
          <!-- 筛选条件 -->
          <el-form :inline="true" :model="queryForm">
            <el-form-item label="考试名称">
              <el-input v-model="queryForm.examName" placeholder="请输入考试名称"></el-input>
            </el-form-item>
            <el-form-item label="评分状态">
              <el-select v-model="queryForm.scoringStatus" placeholder="请选择">
                <el-option label="未评分" value="0"></el-option>
                <el-option label="已评分" value="1"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button type="success" @click="handleBatchScoring">批量评分</el-button>
            </el-form-item>
          </el-form>
        </el-card>
        
        <el-card class="table-card">
          <!-- 答案列表表格 -->
          <el-table :data="answerList" @selection-change="handleSelectionChange">
            <el-table-column type="selection"></el-table-column>
            <el-table-column prop="examTitle" label="考试名称"></el-table-column>
            <el-table-column prop="userName" label="考生姓名"></el-table-column>
            <el-table-column prop="submitTime" label="提交时间"></el-table-column>
            <el-table-column prop="scoringStatus" label="评分状态">
              <template slot-scope="scope">
                <el-tag :type="scope.row.scoringStatus === 1 ? 'success' : 'warning'">
                  {{ scope.row.scoringStatus === 1 ? '已评分' : '未评分' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button size="mini" type="primary" @click="handleScoring(scope.row)">评分</el-button>
                <el-button size="mini" type="info" @click="handleViewResult(scope.row)">查看结果</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </template>
    ```

15. **批量评分对话框**
    ```vue
    <template>
      <el-dialog title="批量评分" :visible.sync="batchDialogVisible">
        <div class="batch-scoring-content">
          <el-alert type="info" title="评分说明" show-icon>
            <p>将对选中的 {{ selectedAnswers.length }} 个答案进行批量评分</p>
            <p>评分完成后将自动更新评分状态和得分信息</p>
          </el-alert>
          
          <el-form :model="batchForm" class="batch-form">
            <el-form-item label="评分配置">
              <el-select v-model="batchForm.configId" placeholder="请选择评分配置">
                <el-option 
                  v-for="config in scoringConfigs" 
                  :key="config.id" 
                  :label="config.configName" 
                  :value="config.id">
                </el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="处理方式">
              <el-radio-group v-model="batchForm.processMode">
                <el-radio label="sync">同步处理</el-radio>
                <el-radio label="async">异步处理</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
          
          <!-- 进度显示 -->
          <div v-if="processing" class="progress-section">
            <el-progress :percentage="progressPercentage" :status="progressStatus"></el-progress>
            <p class="progress-text">{{ progressText }}</p>
          </div>
        </div>
        
        <div slot="footer">
          <el-button @click="batchDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmBatchScoring" :loading="processing">确认评分</el-button>
        </div>
      </el-dialog>
    </template>
    ```

**时间**：第26-28天

16. **评分结果展示页面**
    ```vue
    <template>
      <div class="scoring-result">
        <el-card class="result-header">
          <h2>评分结果详情</h2>
          <div class="result-summary">
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="summary-item">
                  <span class="label">总得分</span>
                  <span class="value">{{ scoringResult.totalScore }}</span>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="summary-item">
                  <span class="label">满分</span>
                  <span class="value">{{ scoringResult.maxScore }}</span>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="summary-item">
                  <span class="label">得分率</span>
                  <span class="value">{{ scoringResult.scorePercentage }}%</span>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="summary-item">
                  <span class="label">评分时间</span>
                  <span class="value">{{ scoringResult.scoringTime }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
        
        <el-card class="station-scores">
          <h3>分站得分详情</h3>
          <el-table :data="stationScoreList">
            <el-table-column prop="stationName" label="站点名称"></el-table-column>
            <el-table-column prop="windForceScore" label="风力得分"></el-table-column>
            <el-table-column prop="windDirectionScore" label="风向得分"></el-table-column>
            <el-table-column prop="temperatureScore" label="温度得分"></el-table-column>
            <el-table-column prop="precipitationScore" label="降水得分"></el-table-column>
            <el-table-column prop="disasterWeatherScore" label="灾害天气得分"></el-table-column>
            <el-table-column prop="totalScore" label="小计"></el-table-column>
          </el-table>
        </el-card>
      </div>
    </template>
    ```

#### 第六周：统计分析界面
**时间**：第29-32天

17. **统计分析页面**
    ```vue
    <template>
      <div class="scoring-statistics">
        <el-card class="filter-card">
          <el-form :inline="true" :model="statisticsQuery">
            <el-form-item label="考试选择">
              <el-select v-model="statisticsQuery.examId" placeholder="请选择考试">
                <el-option 
                  v-for="exam in examList" 
                  :key="exam.id" 
                  :label="exam.title" 
                  :value="exam.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="statisticsQuery.dateRange"
                type="datetimerange"
                placeholder="选择时间范围">
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="loadStatistics">查询统计</el-button>
              <el-button type="success" @click="exportReport">导出报告</el-button>
            </el-form-item>
          </el-form>
        </el-card>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card title="得分分布">
              <div id="scoreDistributionChart" style="height: 400px;"></div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card title="要素得分对比">
              <div id="elementScoreChart" style="height: 400px;"></div>
            </el-card>
          </el-col>
        </el-row>
        
        <el-card class="statistics-table">
          <h3>详细统计</h3>
          <el-table :data="detailStatistics">
            <el-table-column prop="element" label="评分要素"></el-table-column>
            <el-table-column prop="avgScore" label="平均得分"></el-table-column>
            <el-table-column prop="maxScore" label="最高得分"></el-table-column>
            <el-table-column prop="minScore" label="最低得分"></el-table-column>
            <el-table-column prop="accuracy" label="准确率"></el-table-column>
          </el-table>
        </el-card>
      </div>
    </template>
    ```

### Phase 4: 系统集成测试 (1周)

#### 第七周：全面测试
**时间**：第33-35天

18. **功能测试**
    - API接口测试
    - 前端功能测试
    - 端到端测试
    - 用户场景测试

19. **性能测试**
    ```bash
    # 压力测试脚本
    #!/bin/bash
    # 测试批量评分性能
    for i in {1..100}; do
      curl -X POST "http://localhost:8080/exam/api/weather/scoring/batch" \
           -H "Content-Type: application/json" \
           -d '{"answerIds":["id1","id2","id3","id4","id5"]}'
    done
    ```

20. **数据一致性测试**
    ```java
    @Test
    void testDataConsistency() {
        // 测试评分结果的数据一致性
        // 验证分数计算的准确性
        // 检查并发访问下的数据完整性
    }
    ```

**时间**：第36-39天

21. **用户验收测试**
    - 教师端功能验收
    - 管理员端功能验收
    - 性能指标验收
    - 错误处理验收

### Phase 5: 部署上线 (0.5周)

#### 第八周前半段：生产部署
**时间**：第40-42天

22. **生产环境配置**
    ```yaml
    # application-prod.yml
    spring:
      datasource:
        url: ************************************************************************
        username: ${DB_USERNAME}
        password: ${DB_PASSWORD}
      
      task:
        execution:
          pool:
            core-size: 8
            max-size: 16
            queue-capacity: 100
    
    scoring:
      batch-size: 50
      timeout: 300000
    ```

23. **数据库迁移**
    ```sql
    -- 生产环境数据迁移脚本
    -- 1. 创建新表
    SOURCE create_scoring_tables.sql;
    
    -- 2. 迁移配置数据
    INSERT INTO el_weather_scoring_config (id, config_name, total_score, element_weights, is_active)
    SELECT 'default_config', '默认评分配置', 100, '{"windForce":1,"windDirection":1,"temperature":4,"precipitation":2,"disasterWeather":2}', 1;
    
    -- 3. 创建索引
    CREATE INDEX idx_answer_scoring_status ON el_weather_history_exam_answer(grading_status);
    ```

24. **监控配置**
    ```yaml
    # monitoring.yml
    management:
      endpoints:
        web:
          exposure:
            include: health,info,metrics,prometheus
      endpoint:
        health:
          show-details: always
      
    logging:
      level:
        com.yf.exam.modules.weather.scoring: INFO
      file:
        name: /logs/scoring-service.log
    ```

## 详细技术实现

### 核心评分算法伪代码

```java
public WeatherScoringResult calculateScore(StudentAnswer studentAnswer, StandardAnswer standardAnswer, ScoringConfig config) {
    WeatherScoringResult result = new WeatherScoringResult();
    BigDecimal totalScore = BigDecimal.ZERO;
    
    List<StationScore> stationScores = new ArrayList<>();
    
    // 遍历每个站点进行评分
    for (Station studentStation : studentAnswer.getStations()) {
        Station standardStation = findMatchingStation(standardAnswer.getStations(), studentStation.getStationId());
        
        if (standardStation == null) {
            continue; // 跳过无标准答案的站点
        }
        
        StationScore stationScore = new StationScore();
        stationScore.setStationId(studentStation.getStationId());
        stationScore.setStationName(studentStation.getStationName());
        
        // 1. 风力评分 (1.0分)
        stationScore.setWindForceScore(calculateWindForceScore(
            studentStation.getWindForce(), 
            standardStation.getWindForce()
        ));
        
        // 2. 风向评分 (1.0分)  
        stationScore.setWindDirectionScore(calculateWindDirectionScore(
            studentStation.getWindDirection(),
            standardStation.getWindDirectionAngleRange()
        ));
        
        // 3. 温度评分 (4.0分: 最低温2分 + 最高温2分)
        BigDecimal temperatureScore = BigDecimal.ZERO;
        // 最低温评分 (±2℃容差)
        temperatureScore = temperatureScore.add(calculateTemperatureScore(
            studentStation.getMinTemperature(), 
            standardStation.getMinTemperature(), 
            2.0
        ));
        // 最高温评分 (±2℃容差)
        temperatureScore = temperatureScore.add(calculateTemperatureScore(
            studentStation.getMaxTemperature(), 
            standardStation.getMaxTemperature(), 
            2.0
        ));
        stationScore.setTemperatureScore(temperatureScore);
        
        // 4. 降水评分 (2.0分) 
        BigDecimal precipitationScore = calculatePrecipitationScore(
            studentStation.getPrecipitation(),
            standardStation.getPrecipitation()
        );
        stationScore.setPrecipitationScore(precipitationScore);
        
        // 5. 灾害天气评分 (2.0分)
        BigDecimal disasterScore = calculateDisasterWeatherScore(
            studentStation.getDisasterWeather(),
            standardStation.getDisasterWeather()
        );
        stationScore.setDisasterWeatherScore(disasterScore);
        
        // 计算站点总分
        BigDecimal stationTotal = stationScore.getWindForceScore()
                                    .add(stationScore.getWindDirectionScore())
                                    .add(stationScore.getTemperatureScore())
                                    .add(stationScore.getPrecipitationScore())
                                    .add(stationScore.getDisasterWeatherScore());
        stationScore.setTotalScore(stationTotal);
        
        stationScores.add(stationScore);
        totalScore = totalScore.add(stationTotal);
    }
    
    result.setTotalScore(totalScore);
    result.setStationScores(stationScores);
    result.setScoringTime(new Date());
    
    return result;
}

// 风力评分方法
private BigDecimal calculateWindForceScore(String studentLevel, String standardLevel) {
    int student = parseWindForceLevel(studentLevel);
    int standard = parseWindForceLevel(standardLevel);
    
    // 根据考生预报等级判断得分
    switch (student) {
        case 0: // 静风
            if (standard == 0 || standard == 1) return new BigDecimal("1.0");
            return BigDecimal.ZERO;
            
        case 1: // 1级
            if (standard == 0 || standard == 1 || standard == 2) return new BigDecimal("1.0");
            return BigDecimal.ZERO;
            
        case 2: // 2级
            if (standard == 1 || standard == 2) return new BigDecimal("1.0");
            if (standard == 3) return new BigDecimal("0.8");
            return BigDecimal.ZERO;
            
        case 12: // 12级
            if (standard == 12 || standard == 13) return new BigDecimal("1.0"); // 13表示大于12级
            if (standard == 11) return new BigDecimal("0.8");
            return BigDecimal.ZERO;
            
        default: // 3-11级常规处理
            if (standard == student) return new BigDecimal("1.0"); // 完全匹配
            if (standard == student - 1 || standard == student + 1) return new BigDecimal("0.8"); // 相邻等级
            return BigDecimal.ZERO;
    }
}

// 风向评分方法
private BigDecimal calculateWindDirectionScore(String studentDirection, String standardAngleRange) {
    if ("无风".equals(studentDirection) || "无风".equals(standardAngleRange)) {
        return "无风".equals(studentDirection) && "无风".equals(standardAngleRange) ? 
               new BigDecimal("1.0") : BigDecimal.ZERO;
    }
    
    // 解析标准答案的角度范围
    double[] angleRange = parseAngleRange(standardAngleRange);
    if (angleRange == null) return BigDecimal.ZERO;
    
    double startAngle = angleRange[0];
    double endAngle = angleRange[1];
    
    // 根据考生填写的八方位判断得分
    switch (studentDirection) {
        case "东":
            if (isInAngleRange(startAngle, endAngle, 67.5, 90.0) ||
                isInAngleRange(startAngle, endAngle, 90.0, 112.5)) return new BigDecimal("1.0");
            if (isInAngleRange(startAngle, endAngle, 45.0, 67.5) || 
                isInAngleRange(startAngle, endAngle, 112.5, 135.0)) return new BigDecimal("0.6");
            break;
        // ... 其他方向的实现
    }
    
    return BigDecimal.ZERO;
}

// 温度容差评分方法
private BigDecimal calculateTemperatureScore(Integer studentTemp, Integer standardTemp, double maxScore) {
    if (studentTemp == null || standardTemp == null) {
        return BigDecimal.ZERO;
    }
    
    // 计算温度差的绝对值
    int tempDifference = Math.abs(studentTemp - standardTemp);
    
    // 容差范围：≤2℃为准确，>2℃为错误
    return tempDifference <= 2 ? new BigDecimal(maxScore) : BigDecimal.ZERO;
}

// 降水评分方法
private BigDecimal calculatePrecipitationScore(String studentType, String standardType) {
    if (studentType == null || standardType == null) {
        return BigDecimal.ZERO;
    }
    
    // 使用评分矩阵查表法
    return getPrecipitationScoreFromMatrix(studentType, standardType);
}

/**
 * 根据评分矩阵获取降水评分
 * 包含雨夹雪特殊规则和跨类型0分规则
 */
private BigDecimal getPrecipitationScoreFromMatrix(String student, String standard) {
    // 创建评分矩阵映射 - 简化版本，实际应从配置加载
    Map<String, Map<String, Double>> scoreMatrix = createPrecipitationScoreMatrix();
    
    Map<String, Double> studentRow = scoreMatrix.get(student);
    if (studentRow == null) return BigDecimal.ZERO;
    
    Double score = studentRow.get(standard);
    return score != null ? new BigDecimal(score) : BigDecimal.ZERO;
}

// 灾害天气评分方法
private BigDecimal calculateDisasterWeatherScore(List<String> studentForecast, List<String> standardActual) {
    // 处理空值情况
    Set<String> studentSet = new HashSet<>(studentForecast != null ? studentForecast : Collections.emptyList());
    Set<String> standardSet = new HashSet<>(standardActual != null ? standardActual : Collections.emptyList());
    
    // 处理"无"的特殊情况
    boolean studentHasNone = studentSet.contains("无") || studentSet.isEmpty();
    boolean standardHasNone = standardSet.contains("无") || standardSet.isEmpty();
    
    if (studentHasNone && standardHasNone) {
        return new BigDecimal("2.0"); // 都为"无"，完全匹配
    }
    
    if (studentHasNone || standardHasNone) {
        return BigDecimal.ZERO; // 一个为"无"，一个有灾害，不匹配
    }
    
    // 移除"无"选项，只处理具体的灾害天气
    studentSet.remove("无");
    standardSet.remove("无");
    
    // 计算匹配的灾害天气数量
    Set<String> correctMatches = new HashSet<>(studentSet);
    correctMatches.retainAll(standardSet); // 求交集，即正确预报的灾害天气
    
    // 按比例评分：(匹配数量 / 实况总数量) × 2.0
    if (standardSet.isEmpty()) {
        return new BigDecimal("2.0"); // 实况无灾害且预报也无灾害
    }
    
    double ratio = (double) correctMatches.size() / standardSet.size();
    return new BigDecimal(ratio * 2.0).setScale(2, RoundingMode.HALF_UP);
}
```

### 批量处理实现

```java
@Service
public class BatchScoringService {
    
    @Autowired
    private WeatherScoringService scoringService;
    
    @Async("scoringExecutor")
    public CompletableFuture<BatchScoringResult> processBatch(List<String> answerIds, String configId) {
        BatchScoringResult result = new BatchScoringResult();
        result.setTotalCount(answerIds.size());
        result.setStartTime(new Date());
        
        int successCount = 0;
        int failCount = 0;
        List<String> failedIds = new ArrayList<>();
        
        for (String answerId : answerIds) {
            try {
                WeatherScoringResult scoringResult = scoringService.scoreAnswer(answerId, configId);
                if (scoringResult != null) {
                    successCount++;
                } else {
                    failCount++;
                    failedIds.add(answerId);
                }
            } catch (Exception e) {
                log.error("评分失败，答案ID: {}, 错误: {}", answerId, e.getMessage());
                failCount++;
                failedIds.add(answerId);
            }
            
            // 更新进度
            result.setProcessedCount(successCount + failCount);
        }
        
        result.setSuccessCount(successCount);
        result.setFailCount(failCount);
        result.setFailedIds(failedIds);
        result.setEndTime(new Date());
        result.setStatus("completed");
        
        return CompletableFuture.completedFuture(result);
    }
}
```

## 质量保证措施

### 代码质量
1. **代码审查**：每个功能完成后进行peer review
2. **静态分析**：使用SonarQube进行代码质量检查
3. **编码规范**：遵循阿里巴巴Java编码规范

### 测试策略
1. **单元测试**：核心算法测试覆盖率≥90%
2. **集成测试**：API接口和数据库操作测试
3. **性能测试**：批量评分性能基准测试
4. **用户测试**：真实场景下的用户验收测试

### 部署策略
1. **灰度发布**：先在测试环境验证，再灰度发布到生产
2. **数据备份**：部署前完整备份数据库
3. **回滚方案**：准备快速回滚脚本和流程
4. **监控告警**：部署后持续监控系统状态

## 风险控制

### 技术风险
- **算法准确性风险**：建立完整测试用例，多轮验证
- **性能风险**：提前进行压力测试，优化批量处理性能
- **数据一致性风险**：使用事务控制，保证数据完整性

### 进度风险
- **需求变更风险**：与用户充分沟通，锁定核心需求
- **技术难点风险**：提前进行技术预研，制定备选方案
- **资源风险**：合理分配开发资源，关键路径重点保障

### 质量风险
- **测试不充分风险**：制定详细测试计划，多维度测试验证
- **用户接受度风险**：早期用户参与，及时收集反馈调整
- **系统稳定性风险**：完善错误处理，建立监控告警机制

## 成功标准

### 功能指标
- ✅ 支持历史个例考试自动评分
- ✅ 评分准确率达到95%以上  
- ✅ 支持1000+答案批量处理
- ✅ 系统响应时间≤3秒

### 性能指标
- ✅ 单次评分处理时间≤100ms
- ✅ 批量评分吞吐量≥200个/分钟
- ✅ 系统并发用户支持≥50人
- ✅ 数据库查询响应时间≤500ms

### 质量指标  
- ✅ 代码单元测试覆盖率≥85%
- ✅ 系统可用性≥99.5%
- ✅ 0级别bug数量为0
- ✅ 用户满意度≥90%

通过以上详细的开发计划，可以确保历史个例评分系统的高质量交付，满足用户对自动化评分的需求。 