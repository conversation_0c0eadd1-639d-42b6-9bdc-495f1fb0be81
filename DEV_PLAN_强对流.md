# 强对流天气临近预报考试模块 - 开发计划

## 开发概览

### 项目周期
**预计开发时间**：26-32天  
**开发团队**：1-2名全栈开发人员  
**技术栈**：Spring Boot 2.1.4 + Vue.js 2.6.10 + MySQL 8.0.11

### 核心里程碑
- **阶段一**：基础架构和数据库设计（3-4天）
- **阶段二**：后端API开发（8-10天）
- **阶段三**：前端界面开发（10-12天）
- **阶段四**：评分引擎集成（3-4天）
- **阶段五**：测试和优化（2-3天）

## 详细开发阶段

### 阶段一：基础架构和数据库设计（3-4天）

#### Day 1: 项目结构和数据库设计
**任务清单**：
- [ ] **上午任务**：创建强对流模块基础目录结构
  - 创建后端模块目录：`src/main/java/com/yf/exam/modules/convection/`
  - 创建前端页面目录：`exam-vue/src/views/convection/`
  - 创建API接口目录：`exam-vue/src/api/convection/`
  - 配置模块基础依赖和配置文件
- [ ] **下午任务**：数据库表设计和创建
  - 设计并创建`el_convection_exam_answer`答案表
  - 设计并创建`el_convection_grading_record`人工批卷记录表
  - 扩展`el_qu`表，添加`convection_standard_reasoning`字段
  - 创建数据库索引优化脚本
- [ ] **晚间任务**：配置文件设计
  - 创建评分配置文件`convection-scoring.yml`
  - 配置Spring Boot模块自动扫描
  - 初始化Git分支和版本管理

**详细执行步骤**：
1. **模块目录结构创建**：
   ```bash
   # 后端目录结构
   mkdir -p src/main/java/com/yf/exam/modules/convection/{controller,service,entity,dto,mapper,scoring}
   mkdir -p src/main/java/com/yf/exam/modules/convection/scoring/{algorithm,service,config}
   
   # 前端目录结构
   mkdir -p exam-vue/src/views/convection/{qu,exam,mark,grading}
   mkdir -p exam-vue/src/components/convection
   mkdir -p exam-vue/src/api/convection
   ```

2. **数据库性能优化索引**：
   ```sql
   -- 高频查询复合索引
   CREATE INDEX `idx_exam_user_status` ON `el_convection_exam_answer` (`exam_id`, `user_id`, `answer_status`);
   CREATE INDEX `idx_grading_status_time` ON `el_convection_grading_record` (`grading_status`, `grading_time`);
   
   -- JSON字段虚拟列索引（MySQL 8.0+）
   ALTER TABLE `el_convection_exam_answer` 
   ADD COLUMN `station_completion` tinyint GENERATED ALWAYS AS (
     CASE WHEN JSON_LENGTH(`station_answer`) > 0 THEN 1 ELSE 0 END
   ) VIRTUAL;
   CREATE INDEX `idx_station_completion` ON `el_convection_exam_answer` (`station_completion`);
   ```

**关键决策点**：
- 确定quType新值：设置强对流考试题型为quType=7
- JSON存储策略：使用MySQL 8.0 JSON类型存储复杂答案数据
- 索引策略：针对高频查询场景优化索引设计

**核心数据库建表SQL**：
```sql
-- 强对流考试答案表
CREATE TABLE `el_convection_exam_answer` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `exam_id` varchar(64) NOT NULL COMMENT '考试ID',
  `question_id` varchar(64) NOT NULL COMMENT '题目ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  
  -- 第一部分：站点预报答案 (JSON格式)
  `station_answer` json COMMENT '站点预报答案JSON',
  `station_progress` int DEFAULT 0 COMMENT '站点预报进度百分比',
  
  -- 第二部分：落区绘制答案 (JSON格式)
  `area_answer` json COMMENT '落区绘制答案JSON',
  `area_progress` int DEFAULT 0 COMMENT '落区绘制进度百分比',
  
  -- 预报依据阐述（考生输入）
  `forecast_reasoning` text COMMENT '考生预报依据阐述',
  `reasoning_word_count` int DEFAULT 0 COMMENT '预报依据字数',
  
  -- 整体状态
  `overall_progress` int DEFAULT 0 COMMENT '整体进度百分比',
  `answer_status` tinyint DEFAULT 0 COMMENT '答题状态：0-答题中，1-已提交',
  `submit_time` datetime COMMENT '提交时间',
  
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  KEY `idx_exam_user` (`exam_id`, `user_id`),
  KEY `idx_question` (`question_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='强对流考试答案表';

-- 强对流人工批卷记录表
CREATE TABLE `el_convection_grading_record` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `answer_id` varchar(64) NOT NULL COMMENT '答案ID',
  `exam_id` varchar(64) NOT NULL COMMENT '考试ID',
  `student_user_id` varchar(64) NOT NULL COMMENT '学生用户ID',
  `grader_user_id` varchar(64) NOT NULL COMMENT '批卷教师用户ID',
  
  -- 预报依据评分详情
  `reasoning_grading_basis_score` decimal(5,2) DEFAULT 0 COMMENT '分级依据阐述得分',
  `reasoning_extreme_score` decimal(5,2) DEFAULT 0 COMMENT '极端天气预报理由得分',
  `reasoning_total_score` decimal(5,2) DEFAULT 0 COMMENT '预报依据总得分',
  
  -- 批卷评语和建议
  `grading_comments` text COMMENT '批卷评语',
  `improvement_suggestions` text COMMENT '改进建议',
  
  -- 批卷状态
  `grading_status` tinyint DEFAULT 0 COMMENT '批卷状态：0-未批卷，1-已批卷',
  `grading_time` datetime COMMENT '批卷完成时间',
  
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  KEY `idx_answer` (`answer_id`),
  KEY `idx_grader` (`grader_user_id`),
  KEY `idx_student` (`student_user_id`),
  KEY `idx_status` (`grading_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='强对流人工批卷记录表';

-- 扩展现有题目表
ALTER TABLE `el_qu` ADD COLUMN `convection_standard_reasoning` text COMMENT '强对流标准预报依据（管理员录入）' AFTER `scenario_data`;
```

**评分配置文件**：
```yaml
# src/main/resources/config/convection-scoring.yml
convection:
  scoring:
    total_score: 100
    station_forecast_score: 68
    area_forecast_score: 32
    station_forecast:
      basic_station_score: 44
      extreme_weather_score: 4
      reasoning_score: 20
      per_station:
        total_score: 11
        rainfall:
          type_correct: 3
          intensity_correct: 1
        wind:
          type_correct: 3
          intensity_correct: 1
        hail:
          correct: 3
        reasoning_quality: 1
      extreme_weather:
        max_rainfall_station: 2
        max_wind_station: 2
      reasoning:
        grading_basis: 10
        extreme_reasoning: 10
    area_forecast:
      heavy_rainfall: 8
      thunderstorm_wind: 8
      hail: 8
      tornado: 8
```

#### Day 2: 实体类和数据访问层开发
**任务清单**：
- [ ] **上午任务**：核心实体类开发
  - 创建`ConvectionExamAnswer.java`实体类，包含JSON字段处理
  - 创建`ConvectionGradingRecord.java`实体类，支持评分记录
  - 配置自定义JSON类型处理器
- [ ] **下午任务**：数据访问层开发
  - 创建`ConvectionExamAnswerMapper.java`接口和XML映射
  - 创建`ConvectionGradingRecordMapper.java`接口和XML映射
  - 实现复杂查询方法（分页、条件查询等）
- [ ] **晚间任务**：基础配置和单元测试
  - 配置MyBatis扫描和事务管理
  - 编写数据访问层单元测试
  - 测试JSON字段序列化反序列化

**详细代码实现**：

1. **ConvectionExamAnswer实体类**：
```java
@Data
@TableName("el_convection_exam_answer")
@ApiModel("强对流考试答案实体")
public class ConvectionExamAnswer implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键ID")
    private String id;
    
    @ApiModelProperty("考试ID")
    private String examId;
    
    @ApiModelProperty("题目ID")
    private String questionId;
    
    @ApiModelProperty("用户ID")
    private String userId;
    
    @TableField(value = "station_answer", typeHandler = CustomJacksonTypeHandler.class)
    @ApiModelProperty("站点预报答案JSON")
    private Map<String, Object> stationAnswer;
    
    @TableField(value = "area_answer", typeHandler = CustomJacksonTypeHandler.class)
    @ApiModelProperty("落区绘制答案JSON")
    private Map<String, Object> areaAnswer;
    
    @ApiModelProperty("预报依据阐述")
    private String forecastReasoning;
    
    @ApiModelProperty("预报依据字数")
    private Integer reasoningWordCount;
    
    @ApiModelProperty("站点预报进度")
    private Integer stationProgress;
    
    @ApiModelProperty("落区绘制进度")
    private Integer areaProgress;
    
    @ApiModelProperty("总体进度")
    private Integer overallProgress;
    
    @ApiModelProperty("答题状态：0-答题中，1-已提交")
    private Integer answerStatus;
    
    @ApiModelProperty("提交时间")
    private LocalDateTime submitTime;
    
    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
    
    // 业务方法
    public boolean isSubmitted() {
        return this.answerStatus != null && this.answerStatus == 1;
    }
    
    public boolean hasStationAnswer() {
        return this.stationAnswer != null && !this.stationAnswer.isEmpty();
    }
    
    public boolean hasAreaAnswer() {
        return this.areaAnswer != null && !this.areaAnswer.isEmpty();
    }
}
```

2. **ConvectionExamAnswerMapper接口**：
```java
@Mapper
public interface ConvectionExamAnswerMapper extends BaseMapper<ConvectionExamAnswer> {
    
    /**
     * 根据考试ID和用户ID查询答案
     */
    @Select("SELECT * FROM el_convection_exam_answer WHERE exam_id = #{examId} AND user_id = #{userId}")
    ConvectionExamAnswer selectByExamAndUser(@Param("examId") String examId, @Param("userId") String userId);
    
    /**
     * 分页查询待批卷答案
     */
    IPage<ConvectionExamAnswer> selectPendingGradingAnswers(
        IPage<ConvectionExamAnswer> page,
        @Param("examId") String examId,
        @Param("answerStatus") Integer answerStatus
    );
    
    /**
     * 更新答案进度
     */
    @Update("UPDATE el_convection_exam_answer SET station_progress = #{stationProgress}, " +
            "area_progress = #{areaProgress}, overall_progress = #{overallProgress}, " +
            "update_time = NOW() WHERE id = #{id}")
    int updateProgress(@Param("id") String id, 
                      @Param("stationProgress") Integer stationProgress,
                      @Param("areaProgress") Integer areaProgress, 
                      @Param("overallProgress") Integer overallProgress);
    
    /**
     * 批量查询考试答案统计
     */
    List<Map<String, Object>> selectAnswerStatistics(@Param("examIds") List<String> examIds);
}
```

#### Day 3: DTO设计和服务接口定义
**任务清单**：
- [ ] **上午任务**：DTO类设计和实现
  - 创建`ConvectionStationAnswerDTO.java`站点答案传输对象
  - 创建`ConvectionAreaAnswerDTO.java`落区答案传输对象
  - 创建`ConvectionGradingDTO.java`批卷相关传输对象
  - 实现DTO与Entity的转换方法
- [ ] **下午任务**：服务接口定义
  - 定义`ConvectionAnswerService.java`接口
  - 定义`ConvectionGradingService.java`接口
  - 定义`ConvectionScoringService.java`接口
  - 设计服务方法签名和异常处理
- [ ] **晚间任务**：配置和工具类
  - 配置Jackson JSON序列化规则
  - 创建强对流模块工具类
  - 配置模块级别的异常处理

**详细代码实现**：

1. **ConvectionStationAnswerDTO传输对象**：
```java
@Data
@ApiModel("强对流站点预报答案DTO")
public class ConvectionStationAnswerDTO implements Serializable {
    
    @ApiModelProperty("站点预报答案数据")
    private Map<String, ConvectionStationData> stationAnswers;
    
    @ApiModelProperty("预报依据阐述")
    private String forecastReasoning;
    
    @ApiModelProperty("预报依据字数")
    private Integer reasoningWordCount;
    
    @ApiModelProperty("站点预报进度百分比")
    private Integer progress;
    
    @Data
    @ApiModel("单个站点强对流数据")
    public static class ConvectionStationData {
        
        @ApiModelProperty("短时强降水预报：level1/level2/level3")
        private String shortTimeRainfall;
        
        @ApiModelProperty("雷暴大风预报：moderate/severe/extreme")
        private String thunderstormWind;
        
        @ApiModelProperty("冰雹预报：large")
        private String hail;
        
        @ApiModelProperty("预报时间")
        private LocalDateTime forecastTime;
        
        @ApiModelProperty("预报信心度：1-5")
        private Integer confidence;
        
        // 业务验证方法
        public boolean hasAnyWeatherSelected() {
            return StringUtils.isNotBlank(shortTimeRainfall) ||
                   StringUtils.isNotBlank(thunderstormWind) ||
                   StringUtils.isNotBlank(hail);
        }
        
        public int getSelectedWeatherCount() {
            int count = 0;
            if (StringUtils.isNotBlank(shortTimeRainfall)) count++;
            if (StringUtils.isNotBlank(thunderstormWind)) count++;
            if (StringUtils.isNotBlank(hail)) count++;
            return count;
        }
    }
    
    // 业务计算方法
    public Integer calculateProgress() {
        if (stationAnswers == null || stationAnswers.isEmpty()) {
            return 0;
        }
        
        int totalStations = stationAnswers.size();
        int completedStations = 0;
        
        for (ConvectionStationData data : stationAnswers.values()) {
            if (data.hasAnyWeatherSelected()) {
                completedStations++;
            }
        }
        
        int selectionProgress = totalStations > 0 ? (completedStations * 80 / totalStations) : 0;
        int reasoningProgress = (reasoningWordCount != null && reasoningWordCount > 100) ? 20 : 0;
        
        return Math.min(100, selectionProgress + reasoningProgress);
    }
}
```

2. **ConvectionAnswerService接口定义**：
```java
public interface ConvectionAnswerService extends IService<ConvectionExamAnswer> {
    
    /**
     * 保存强对流考试答案
     * @param answerDTO 答案数据传输对象
     * @return 答案ID
     */
    String saveConvectionAnswer(ConvectionAnswerSaveDTO answerDTO);
    
    /**
     * 获取考生答案详情
     * @param examId 考试ID
     * @param userId 用户ID
     * @return 答案详情
     */
    ConvectionAnswerDetailDTO getStudentAnswer(String examId, String userId);
    
    /**
     * 提交强对流考试
     * @param examId 考试ID
     * @param userId 用户ID
     * @return 提交结果
     */
    ConvectionSubmitResultDTO submitConvectionExam(String examId, String userId);
    
    /**
     * 计算答题进度
     * @param answer 答案实体
     * @return 进度详情
     */
    ConvectionProgressDTO calculateProgress(ConvectionExamAnswer answer);
    
    /**
     * 验证答案数据完整性
     * @param answerDTO 答案数据
     * @return 验证结果
     */
    ValidationResult validateAnswerData(ConvectionAnswerSaveDTO answerDTO);
    
    /**
     * 获取考试答案统计
     * @param examId 考试ID
     * @return 统计信息
     */
    ConvectionAnswerStatisticsDTO getAnswerStatistics(String examId);
}
```

**核心实体类示例**：
```java
// ConvectionExamAnswer.java
@Data
@TableName("el_convection_exam_answer")
public class ConvectionExamAnswer {
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    
    private String examId;
    private String questionId;
    private String userId;
    
    @TableField(value = "station_answer", typeHandler = CustomJacksonTypeHandler.class)
    private Map<String, Object> stationAnswer;
    
    @TableField(value = "area_answer", typeHandler = CustomJacksonTypeHandler.class)
    private Map<String, Object> areaAnswer;
    
    private String forecastReasoning;
    private Integer reasoningWordCount;
    
    private Integer stationProgress;
    private Integer areaProgress;
    private Integer overallProgress;
    
    private Integer answerStatus;
    private LocalDateTime submitTime;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}

// ConvectionGradingRecord.java
@Data
@TableName("el_convection_grading_record")
public class ConvectionGradingRecord {
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    
    private String answerId;
    private String examId;
    private String studentUserId;
    private String graderUserId;
    
    private BigDecimal reasoningGradingBasisScore;
    private BigDecimal reasoningExtremeScore;
    private BigDecimal reasoningTotalScore;
    
    private String gradingComments;
    private String improvementSuggestions;
    
    private Integer gradingStatus;
    private LocalDateTime gradingTime;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}
```

#### Day 4: 路由和权限配置
**任务清单**：
- [ ] 配置前端路由文件`router/index.js`
- [ ] 设置权限控制规则
- [ ] 创建菜单配置
- [ ] 测试路由访问权限

**路由配置示例**：
```javascript
// exam-vue/src/router/index.js
// 学生端路由
{
  path: 'convection',
  component: () => import('@/views/user/convection/exam'),
  name: 'ConvectionExamOnline',
  meta: { title: '强对流考试', noCache: true, icon: 'thunder' }
},

// 管理端路由
{
  path: '/convection',
  component: Layout,
  redirect: '/convection/qu',
  name: 'ConvectionManage',
  meta: {
    title: '强对流',
    icon: 'thunder',
    roles: ['sa', 'teacher']
  },
  children: [
    {
      path: 'qu',
      component: () => import('@/views/convection/qu'),
      name: 'ConvectionListQu',
      meta: { title: '试题管理', noCache: true, icon: 'support', roles: ['sa'] }
    },
    {
      path: 'exam',
      component: () => import('@/views/convection/exam'),
      name: 'ConvectionListExam',
      meta: { title: '考试管理', noCache: true, icon: 'log' }
    },
    {
      path: 'grading',
      component: () => import('@/views/convection/grading'),
      name: 'ConvectionGrading',
      meta: { title: '人工批卷', noCache: true, icon: 'edit' }
    }
  ]
}
```

---

### 阶段二：后端API开发（8-10天）

#### Day 5-6: 强对流试题管理API
**任务清单**：
- [ ] 创建`ConvectionCaseController.java`
- [ ] 实现`ConvectionCaseService.java`和实现类
- [ ] 开发试题CRUD接口
- [ ] 实现预报依据标准答案管理接口
- [ ] 集成文件上传功能（MICAPS文件、强对流落区文件）
- [ ] 实现权限控制（强对流落区文件对考生不可见）

**核心Controller示例**：
```java
@RestController
@RequestMapping("/api/convection/case")
@ApiTags("强对流试题管理")
public class ConvectionCaseController extends BaseController {
    
    @Autowired
    private ConvectionCaseService convectionCaseService;
    
    @ApiOperation(value = "分页查询强对流试题")
    @PostMapping("/paging")
    public ApiRest<IPage<QuDTO>> paging(@RequestBody PagingReqDTO<QuQueryReqDTO> reqDTO) {
        // 限制查询强对流类型题目 (新增quType = 7)
        if (reqDTO.getParams() == null) {
            reqDTO.setParams(new QuQueryReqDTO());
        }
        reqDTO.getParams().setQuType(7); // 强对流天气预报题
        
        IPage<QuDTO> page = convectionCaseService.paging(reqDTO);
        return super.success(page);
    }
    
    @ApiOperation(value = "保存强对流试题")
    @PostMapping("/save")
    public ApiRest saveConvectionCase(@RequestBody ConvectionCaseDTO reqDTO) {
        // 强制设置为强对流天气预报题
        reqDTO.setQuType(7);
        
        // 验证标准预报依据
        if (StringUtils.isBlank(reqDTO.getConvectionStandardReasoning())) {
            return super.error("请输入标准预报依据");
        }
        
        convectionCaseService.save(reqDTO);
        return super.success();
    }
    
    @ApiOperation(value = "获取强对流试题详情")
    @GetMapping("/detail/{id}")
    public ApiRest<ConvectionCaseDTO> getDetail(@PathVariable String id) {
        ConvectionCaseDTO detail = convectionCaseService.getDetail(id);
        return super.success(detail);
    }
}
```

#### Day 7-8: 强对流考试管理API
**任务清单**：
- [ ] 创建`ConvectionExamController.java`
- [ ] 实现考试列表查询（学生端和管理端）
- [ ] 开发考试创建、编辑、删除接口
- [ ] 实现考试状态管理
- [ ] 集成部门权限控制

**核心Service示例**：
```java
@Service
public class ConvectionExamServiceImpl implements ConvectionExamService {
    
    @Autowired
    private ExamMapper examMapper;
    
    @Override
    public IPage<ExamDTO> onlinePaging(PagingReqDTO<ExamQueryReqDTO> reqDTO) {
        // 过滤强对流考试
        QueryWrapper<Exam> wrapper = new QueryWrapper<>();
        wrapper.eq("exam_type", "convection")
               .eq("state", 1)
               .isNotNull("question_id");
        
        // 部门权限过滤
        String userDepartId = UserUtils.getCurrentUser().getDepartId();
        if (StringUtils.isNotBlank(userDepartId)) {
            wrapper.eq("depart_id", userDepartId);
        }
        
        IPage<Exam> page = examMapper.selectPage(reqDTO.toPage(), wrapper);
        return page.convert(exam -> {
            ExamDTO dto = new ExamDTO();
            BeanUtils.copyProperties(exam, dto);
            dto.setExamType("convection");
            return dto;
        });
    }
}
```

#### Day 9-10: 强对流答案管理API
**任务清单**：
- [ ] 创建`ConvectionAnswerController.java`
- [ ] 实现答案保存接口（站点预报+落区绘制+预报依据）
- [ ] 开发答案查询接口
- [ ] 实现考试提交接口
- [ ] 集成进度计算逻辑

**核心答案保存逻辑**：
```java
@Service
public class ConvectionAnswerServiceImpl implements ConvectionAnswerService {
    
    @Override
    @Transactional
    public String saveAnswer(ConvectionAnswerDTO answerDTO) {
        ConvectionExamAnswer answer = new ConvectionExamAnswer();
        
        // 基础信息
        answer.setExamId(answerDTO.getExamId());
        answer.setQuestionId(answerDTO.getQuestionId());
        answer.setUserId(UserUtils.getCurrentUserId());
        
        // 第一部分：站点预报答案
        answer.setStationAnswer(answerDTO.getStationAnswer());
        answer.setStationProgress(calculateStationProgress(answerDTO.getStationAnswer()));
        
        // 第二部分：落区绘制答案
        answer.setAreaAnswer(answerDTO.getAreaAnswer());
        answer.setAreaProgress(calculateAreaProgress(answerDTO.getAreaAnswer()));
        
        // 预报依据阐述
        answer.setForecastReasoning(answerDTO.getForecastReasoning());
        answer.setReasoningWordCount(
            StringUtils.isNotBlank(answerDTO.getForecastReasoning()) ? 
            answerDTO.getForecastReasoning().length() : 0
        );
        
        // 计算整体进度
        int overallProgress = (int) Math.round(
            answer.getStationProgress() * 0.68 + answer.getAreaProgress() * 0.32
        );
        answer.setOverallProgress(overallProgress);
        
        // 保存或更新
        if (StringUtils.isNotBlank(answerDTO.getAnswerId())) {
            answer.setId(answerDTO.getAnswerId());
            convectionAnswerMapper.updateById(answer);
        } else {
            answer.setId(IdUtil.fastSimpleUUID());
            convectionAnswerMapper.insert(answer);
        }
        
        return answer.getId();
    }
    
    private Integer calculateStationProgress(Map<String, Object> stationAnswer) {
        if (stationAnswer == null || stationAnswer.isEmpty()) {
            return 0;
        }
        
        // 根据站点预报完成情况计算进度
        // 包括选择题部分和预报依据文本完成情况
        int totalElements = 0;
        int completedElements = 0;
        
        // 遍历4个站点的选择情况
        for (String stationKey : stationAnswer.keySet()) {
            Map<String, Object> stationData = (Map<String, Object>) stationAnswer.get(stationKey);
            if (stationData != null) {
                totalElements += 3; // 每站点3类天气现象
                if (stationData.get("shortTimeRainfall") != null) completedElements++;
                if (stationData.get("thunderstormWind") != null) completedElements++;
                if (stationData.get("hail") != null) completedElements++;
            }
        }
        
        return totalElements > 0 ? (completedElements * 100 / totalElements) : 0;
    }
}
```

#### Day 11-12: 人工批卷API开发
**任务清单**：
- [ ] 创建`ConvectionGradingController.java`
- [ ] 实现`ConvectionGradingService.java`和实现类
- [ ] 开发批卷任务列表接口
- [ ] 实现预报依据对比显示接口
- [ ] 开发评分提交接口
- [ ] 集成批卷记录管理

**人工批卷Controller示例**：
```java
@RestController
@RequestMapping("/api/convection/grading")
@ApiTags("强对流人工批卷")
public class ConvectionGradingController extends BaseController {
    
    @Autowired
    private ConvectionGradingService gradingService;
    
    @ApiOperation(value = "获取待批卷列表")
    @PostMapping("/pending-list")
    public ApiRest<IPage<ConvectionGradingTaskDTO>> getPendingList(
            @RequestBody PagingReqDTO<ConvectionGradingQueryDTO> reqDTO) {
        IPage<ConvectionGradingTaskDTO> page = gradingService.getPendingList(reqDTO);
        return super.success(page);
    }
    
    @ApiOperation(value = "获取批卷详情")
    @GetMapping("/detail/{answerId}")
    public ApiRest<ConvectionGradingDetailDTO> getGradingDetail(@PathVariable String answerId) {
        ConvectionGradingDetailDTO detail = gradingService.getGradingDetail(answerId);
        return super.success(detail);
    }
    
    @ApiOperation(value = "提交预报依据评分")
    @PostMapping("/submit-reasoning-score")
    public ApiRest submitReasoningScore(@RequestBody ConvectionGradingSubmitDTO submitDTO) {
        gradingService.submitReasoningScore(submitDTO);
        return super.success();
    }
}
```

---

### 阶段三：前端界面开发（10-12天）

#### Day 13-14: 试题管理界面
**任务清单**：
- [ ] 创建`/views/convection/qu/index.vue`试题列表页面
- [ ] 开发试题编辑表单，集成预报依据录入组件
- [ ] 创建`ForecastReasoningInput.vue`预报依据录入组件
- [ ] 实现文件上传功能（MICAPS文件、强对流落区文件）
- [ ] 集成富文本编辑器支持格式化输入
- [ ] 实现表单验证和数据保存

**预报依据录入组件示例**：
```vue
<!-- ForecastReasoningInput.vue -->
<template>
  <div class="forecast-reasoning-input">
    <div class="input-header">
      <h4>标准预报依据</h4>
      <div class="word-count">
        <span :class="{ 'over-limit': wordCount > maxWords }">
          {{ wordCount }}/{{ maxWords }}
        </span>
      </div>
    </div>
    
    <el-input
      type="textarea"
      v-model="reasoning"
      @input="handleInput"
      :rows="15"
      :placeholder="placeholder"
      maxlength="2000"
      show-word-limit
      resize="vertical"
      class="reasoning-textarea"
    />
    
    <div class="input-tips">
      <div class="tip-item">
        <i class="el-icon-info"></i>
        <span>请详细阐述各类强对流天气的分级依据和预报理由</span>
      </div>
      <div class="tip-item">
        <i class="el-icon-warning"></i>
        <span>建议字数：500-2000字，包含分级标准和极端天气预报要点</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ForecastReasoningInput',
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入标准预报依据，包括：\n1. 短时强降水分级依据（20≤R1＜40、40≤R1＜80、80≤R1mm/h）\n2. 雷暴大风分级依据（不同风力等级标准）\n3. 2cm以上大冰雹识别要点\n4. 极端天气预报的关键指标和判断依据'
    },
    maxWords: {
      type: Number,
      default: 2000
    }
  },
  data() {
    return {
      reasoning: this.value
    }
  },
  computed: {
    wordCount() {
      return this.reasoning ? this.reasoning.length : 0
    }
  },
  watch: {
    value(newVal) {
      this.reasoning = newVal
    }
  },
  methods: {
    handleInput() {
      this.$emit('input', this.reasoning)
      this.$emit('word-count-change', this.wordCount)
    }
  }
}
</script>

<style scoped>
.forecast-reasoning-input {
  margin-bottom: 20px;
}

.input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.input-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.word-count {
  color: #909399;
  font-size: 14px;
}

.word-count .over-limit {
  color: #f56c6c;
  font-weight: bold;
}

.reasoning-textarea {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.reasoning-textarea ::v-deep .el-textarea__inner {
  line-height: 1.8;
  font-size: 14px;
  border-radius: 8px;
  border: 2px solid #e0e6ed;
  transition: all 0.3s ease;
}

.reasoning-textarea ::v-deep .el-textarea__inner:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.input-tips {
  margin-top: 10px;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 5px;
  color: #606266;
  font-size: 13px;
}

.tip-item i {
  color: #909399;
}
</style>
```

#### Day 15-16: 强对流考试答题界面
**任务清单**：
- [ ] 创建`ConvectionExam.vue`主考试页面
- [ ] 开发`ConvectionStationTable.vue`站点预报表格组件
- [ ] 创建`ForecastReasoningTextarea.vue`考生预报依据输入组件
- [ ] 集成`ConvectionAreaDrawing.vue`落区绘制组件
- [ ] 实现考试进度计算和显示
- [ ] 开发答案自动保存功能

**站点预报表格组件示例**：
```vue
<!-- ConvectionStationTable.vue -->
<template>
  <div class="convection-station-table">
    <div class="table-header">
      <h4>第一部分：站点预报表格 (68分)</h4>
      <div class="progress-info">
        完成进度：{{ stationProgress }}%
      </div>
    </div>
    
    <!-- 强对流站点预报表格 -->
    <el-table
      :data="tableData"
      border
      stripe
      size="medium"
      class="station-table"
      @selection-change="handleSelectionChange"
    >
      <!-- 天气要素列 -->
      <el-table-column prop="element" label="天气要素" width="200" fixed="left">
        <template slot-scope="scope">
          <strong>{{ scope.row.elementLabel }}</strong>
        </template>
      </el-table-column>
      
      <!-- 动态站点列 -->
      <el-table-column
        v-for="station in stations"
        :key="station.code"
        :prop="station.code"
        :label="station.name"
        width="180"
        align="center"
      >
        <template slot-scope="scope">
          <div class="station-cell">
            <!-- 短时强降水选择 -->
            <div v-if="scope.row.element === 'rainfall'" class="weather-options">
              <div
                v-for="option in rainfallOptions"
                :key="option.value"
                class="weather-option"
                :class="{ 
                  'selected': isSelected(station.code, 'rainfall', option.value),
                  'unselected': !isSelected(station.code, 'rainfall', option.value)
                }"
                @click="selectWeatherOption(station.code, 'rainfall', option.value)"
              >
                <span class="option-icon">
                  {{ isSelected(station.code, 'rainfall', option.value) ? '○' : '×' }}
                </span>
                <span class="option-label">{{ option.label }}</span>
              </div>
            </div>
            
            <!-- 雷暴大风选择 -->
            <div v-else-if="scope.row.element === 'wind'" class="weather-options">
              <div
                v-for="option in windOptions"
                :key="option.value"
                class="weather-option"
                :class="{ 
                  'selected': isSelected(station.code, 'wind', option.value),
                  'unselected': !isSelected(station.code, 'wind', option.value)
                }"
                @click="selectWeatherOption(station.code, 'wind', option.value)"
              >
                <span class="option-icon">
                  {{ isSelected(station.code, 'wind', option.value) ? '○' : '×' }}
                </span>
                <span class="option-label">{{ option.label }}</span>
              </div>
            </div>
            
            <!-- 冰雹选择 -->
            <div v-else-if="scope.row.element === 'hail'" class="weather-options">
              <div
                class="weather-option"
                :class="{ 
                  'selected': isSelected(station.code, 'hail', 'large'),
                  'unselected': !isSelected(station.code, 'hail', 'large')
                }"
                @click="selectWeatherOption(station.code, 'hail', 'large')"
              >
                <span class="option-icon">
                  {{ isSelected(station.code, 'hail', 'large') ? '○' : '×' }}
                </span>
                <span class="option-label">2cm以上大冰雹</span>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 预报依据阐述 -->
    <div class="reasoning-section">
      <forecast-reasoning-textarea
        v-model="forecastReasoning"
        @input="handleReasoningChange"
        :max-words="1500"
        placeholder="请详细阐述预报依据，包括：&#10;1. 各类强对流天气的分级判断依据&#10;2. 极端天气预报的理由和关键指标&#10;3. 基于气象资料的分析结论"
      />
    </div>
  </div>
</template>

<script>
import ForecastReasoningTextarea from './ForecastReasoningTextarea'

export default {
  name: 'ConvectionStationTable',
  components: {
    ForecastReasoningTextarea
  },
  props: {
    stations: {
      type: Array,
      default: () => []
    },
    initialAnswers: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      stationAnswers: { ...this.initialAnswers },
      forecastReasoning: '',
      
      // 表格行数据
      tableData: [
        { element: 'rainfall', elementLabel: '短时强降水' },
        { element: 'wind', elementLabel: '雷暴大风' },
        { element: 'hail', elementLabel: '冰雹' }
      ],
      
      // 选项配置
      rainfallOptions: [
        { value: 'level1', label: '20≤R1＜40mm/h' },
        { value: 'level2', label: '40≤R1＜80mm/h' },
        { value: 'level3', label: '80≤R1mm/h以上' }
      ],
      windOptions: [
        { value: 'moderate', label: '8级≤Wg＜10级或6级≤W2＜8级' },
        { value: 'severe', label: '10级≤Wg＜12级或8级≤W2＜10级' },
        { value: 'extreme', label: '12级≤Wg或龙卷或10级≤W2' }
      ]
    }
  },
  computed: {
    stationProgress() {
      let totalElements = this.stations.length * 3 // 每站点3类天气现象
      let completedElements = 0
      
      this.stations.forEach(station => {
        if (this.stationAnswers[station.code]) {
          const answers = this.stationAnswers[station.code]
          if (answers.rainfall) completedElements++
          if (answers.wind) completedElements++
          if (answers.hail) completedElements++
        }
      })
      
      // 预报依据完成情况（字数超过100字认为基本完成）
      const reasoningComplete = this.forecastReasoning && this.forecastReasoning.length > 100
      if (reasoningComplete) {
        completedElements += Math.floor(totalElements * 0.2) // 预报依据占20%权重
      }
      
      return totalElements > 0 ? Math.round((completedElements / totalElements) * 100) : 0
    }
  },
  methods: {
    isSelected(stationCode, weatherType, optionValue) {
      return this.stationAnswers[stationCode] &&
             this.stationAnswers[stationCode][weatherType] === optionValue
    },
    
    selectWeatherOption(stationCode, weatherType, optionValue) {
      if (!this.stationAnswers[stationCode]) {
        this.$set(this.stationAnswers, stationCode, {})
      }
      
      // 单选约束：如果已选中相同选项，则取消选择；否则选择新选项
      if (this.stationAnswers[stationCode][weatherType] === optionValue) {
        this.$set(this.stationAnswers[stationCode], weatherType, null)
      } else {
        this.$set(this.stationAnswers[stationCode], weatherType, optionValue)
      }
      
      this.emitAnswerChange()
    },
    
    handleReasoningChange(reasoning) {
      this.forecastReasoning = reasoning
      this.emitAnswerChange()
    },
    
    emitAnswerChange() {
      this.$emit('answer-change', {
        stationAnswers: this.stationAnswers,
        forecastReasoning: this.forecastReasoning,
        progress: this.stationProgress
      })
    }
  }
}
</script>

<style scoped>
.convection-station-table {
  margin-bottom: 30px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-header h4 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.progress-info {
  color: #667eea;
  font-size: 14px;
  font-weight: 600;
  background: rgba(102, 126, 234, 0.1);
  padding: 5px 12px;
  border-radius: 12px;
}

.station-table {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
}

.station-cell {
  padding: 5px;
}

.weather-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.weather-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 10px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #e0e6ed;
  background: #ffffff;
  font-size: 13px;
}

.weather-option.selected {
  background: #f0f9ff;
  border-color: #667eea;
  color: #667eea;
}

.weather-option.selected .option-icon {
  color: #67c23a;
  font-weight: bold;
}

.weather-option.unselected .option-icon {
  color: #f56c6c;
  font-weight: bold;
}

.weather-option:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.option-label {
  flex: 1;
  line-height: 1.2;
}

.reasoning-section {
  margin-top: 25px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #e0e6ed;
}
</style>
```

#### Day 17-18: 落区绘制组件开发
**任务清单**：
- [ ] 创建`ConvectionAreaDrawing.vue`组件
- [ ] 基于现有`PrecipitationDrawing`组件进行扩展
- [ ] 实现四类强对流落区绘制工具
- [ ] 集成地图显示和交互功能
- [ ] 实现GeoJSON数据格式存储

#### Day 19-20: 人工批卷界面开发
**任务清单**：
- [ ] 创建`/views/convection/grading/GradingList.vue`批卷任务列表
- [ ] 开发`ReasoningGrading.vue`预报依据批卷页面
- [ ] 创建`ReasoningComparison.vue`答案对比显示组件
- [ ] 实现评分输入和提交功能
- [ ] 集成批卷进度跟踪

**预报依据批卷页面示例**：
```vue
<!-- ReasoningGrading.vue -->
<template>
  <div class="reasoning-grading">
    <div class="grading-header">
      <h3>预报依据人工批卷</h3>
      <div class="exam-info">
        <span>考试：{{ examTitle }}</span>
        <span>考生：{{ studentName }}</span>
        <span>提交时间：{{ submitTime }}</span>
      </div>
    </div>
    
    <!-- 答案对比显示 -->
    <div class="comparison-container">
      <reasoning-comparison
        :standard-answer="standardReasoning"
        :student-answer="studentReasoning"
        :grading-result="gradingResult"
      />
    </div>
    
    <!-- 评分面板 -->
    <div class="grading-panel">
      <el-card>
        <div slot="header">
          <span>预报依据评分 (总分20分)</span>
        </div>
        
        <el-form :model="gradingForm" label-width="150px">
          <el-form-item label="分级依据阐述">
            <div class="score-input-group">
              <el-input-number
                v-model="gradingForm.gradingBasisScore"
                :min="0"
                :max="10"
                :precision="1"
                :step="0.5"
                size="large"
              />
              <span class="score-suffix">/ 10分</span>
            </div>
            <div class="score-tips">
              评分要点：分级标准理解准确性、阐述完整性、专业术语使用
            </div>
          </el-form-item>
          
          <el-form-item label="极端天气预报理由">
            <div class="score-input-group">
              <el-input-number
                v-model="gradingForm.extremeReasoningScore"
                :min="0"
                :max="10"
                :precision="1"
                :step="0.5"
                size="large"
              />
              <span class="score-suffix">/ 10分</span>
            </div>
            <div class="score-tips">
              评分要点：预报依据科学性、逻辑性、关键指标识别
            </div>
          </el-form-item>
          
          <el-form-item label="总分">
            <div class="total-score">
              <strong>{{ totalReasoningScore }}</strong> / 20分
            </div>
          </el-form-item>
          
          <el-form-item label="批卷评语">
            <el-input
              v-model="gradingForm.gradingComments"
              type="textarea"
              :rows="4"
              placeholder="请输入批卷评语，指出优点和不足..."
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item label="改进建议">
            <el-input
              v-model="gradingForm.improvementSuggestions"
              type="textarea"
              :rows="3"
              placeholder="请提供改进建议..."
              maxlength="300"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </el-card>
    </div>
    
    <!-- 操作按钮 -->
    <div class="grading-actions">
      <el-button size="large" @click="saveDraft">保存草稿</el-button>
      <el-button
        type="primary"
        size="large"
        @click="submitGrading"
        :loading="submitting"
      >
        提交批卷结果
      </el-button>
    </div>
  </div>
</template>

<script>
import ReasoningComparison from '@/components/convection/ReasoningComparison'
import { submitReasoningScore } from '@/api/convection/grading'

export default {
  name: 'ReasoningGrading',
  components: {
    ReasoningComparison
  },
  data() {
    return {
      answerId: this.$route.params.answerId,
      examTitle: '',
      studentName: '',
      submitTime: '',
      standardReasoning: '',
      studentReasoning: '',
      gradingResult: null,
      submitting: false,
      
      gradingForm: {
        gradingBasisScore: 0,
        extremeReasoningScore: 0,
        gradingComments: '',
        improvementSuggestions: ''
      }
    }
  },
  computed: {
    totalReasoningScore() {
      return (this.gradingForm.gradingBasisScore || 0) + (this.gradingForm.extremeReasoningScore || 0)
    }
  },
  created() {
    this.loadGradingDetail()
  },
  methods: {
    async loadGradingDetail() {
      try {
        const response = await this.$api.convection.grading.getDetail(this.answerId)
        if (response.code === 0) {
          const detail = response.data
          this.examTitle = detail.examTitle
          this.studentName = detail.studentName
          this.submitTime = detail.submitTime
          this.standardReasoning = detail.standardReasoning
          this.studentReasoning = detail.studentReasoning
          
          // 如果已有批卷结果，则加载
          if (detail.gradingResult) {
            this.gradingForm = { ...detail.gradingResult }
            this.gradingResult = detail.gradingResult
          }
        }
      } catch (error) {
        this.$message.error('加载批卷详情失败')
      }
    },
    
    async saveDraft() {
      // 保存草稿逻辑
      this.$message.success('草稿已保存')
    },
    
    async submitGrading() {
      if (this.totalReasoningScore === 0) {
        this.$message.warning('请至少为其中一项评分项目给分')
        return
      }
      
      try {
        this.submitting = true
        
        const submitData = {
          answerId: this.answerId,
          gradingBasisScore: this.gradingForm.gradingBasisScore,
          extremeReasoningScore: this.gradingForm.extremeReasoningScore,
          totalScore: this.totalReasoningScore,
          gradingComments: this.gradingForm.gradingComments,
          improvementSuggestions: this.gradingForm.improvementSuggestions
        }
        
        const response = await submitReasoningScore(submitData)
        
        if (response.code === 0) {
          this.$message.success('批卷结果提交成功')
          this.$router.push('/convection/grading')
        } else {
          throw new Error(response.msg || '提交失败')
        }
      } catch (error) {
        this.$message.error('提交批卷结果失败：' + error.message)
      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

<style scoped>
.reasoning-grading {
  padding: 20px;
}

.grading-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e0e6ed;
}

.grading-header h3 {
  margin: 0;
  color: #303133;
}

.exam-info {
  display: flex;
  gap: 20px;
  color: #666;
  font-size: 14px;
}

.comparison-container {
  margin-bottom: 20px;
}

.grading-panel {
  margin-bottom: 20px;
}

.score-input-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.score-suffix {
  color: #666;
  font-size: 14px;
}

.score-tips {
  margin-top: 5px;
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}

.total-score {
  font-size: 18px;
  color: #667eea;
}

.grading-actions {
  text-align: center;
  padding: 20px 0;
}
</style>
```

#### Day 21-24: 其他前端页面和组件完善
**任务清单**：
- [ ] 完善考试管理页面
- [ ] 开发考试结果展示页面
- [ ] 创建学生端考试列表页面
- [ ] 实现组件间的数据传递和状态管理
- [ ] 优化UI样式和用户体验

---

### 阶段四：评分引擎集成（3-4天）

#### Day 25-26: 强对流评分算法开发
**任务清单**：
- [ ] 创建`ConvectionStationScoring.java`站点预报评分算法
- [ ] 开发`ConvectionAreaScoring.java`落区绘制评分算法
- [ ] 实现自动评分与人工批卷的结合逻辑
- [ ] 集成现有weather评分引擎框架

**核心评分算法示例**：
```java
@Component
public class ConvectionStationScoringAlgorithm {
    
    @Autowired
    private ConvectionScoringConfig scoringConfig;
    
    /**
     * 计算站点预报自动评分部分（48分：基础站点44分 + 极端天气4分）
     */
    public ConvectionStationScoreResult calculateStationScore(
            ConvectionStationAnswerDTO studentAnswer,
            ConvectionStationAnswerDTO standardAnswer) {
        
        ConvectionStationScoreResult result = new ConvectionStationScoreResult();
        
        // 1. 基础站点评分（44分）
        BigDecimal basicStationScore = calculateBasicStationScore(studentAnswer, standardAnswer);
        result.setBasicStationScore(basicStationScore);
        
        // 2. 过程极端天气预报（4分）
        BigDecimal extremeWeatherScore = calculateExtremeWeatherScore(studentAnswer, standardAnswer);
        result.setExtremeWeatherScore(extremeWeatherScore);
        
        // 3. 自动评分总分（不包括人工批卷的20分）
        BigDecimal autoScore = basicStationScore.add(extremeWeatherScore);
        result.setAutoScore(autoScore);
        
        return result;
    }
    
    private BigDecimal calculateBasicStationScore(
            ConvectionStationAnswerDTO studentAnswer,
            ConvectionStationAnswerDTO standardAnswer) {
        
        BigDecimal totalScore = BigDecimal.ZERO;
        List<String> stations = standardAnswer.getStations();
        
        for (String stationCode : stations) {
            BigDecimal stationScore = calculateSingleStationScore(
                studentAnswer.getStationAnswers().get(stationCode),
                standardAnswer.getStationAnswers().get(stationCode)
            );
            totalScore = totalScore.add(stationScore);
        }
        
        return totalScore;
    }
    
    private BigDecimal calculateSingleStationScore(
            Map<String, Object> studentStationAnswer,
            Map<String, Object> standardStationAnswer) {
        
        BigDecimal stationScore = BigDecimal.ZERO;
        
        // 短时强降水评分（最高4分）
        BigDecimal rainfallScore = calculateRainfallScore(
            (String) studentStationAnswer.get("rainfall"),
            (String) standardStationAnswer.get("rainfall")
        );
        stationScore = stationScore.add(rainfallScore);
        
        // 雷暴大风评分（最高4分）
        BigDecimal windScore = calculateWindScore(
            (String) studentStationAnswer.get("wind"),
            (String) standardStationAnswer.get("wind")
        );
        stationScore = stationScore.add(windScore);
        
        // 冰雹评分（最高3分）
        BigDecimal hailScore = calculateHailScore(
            (String) studentStationAnswer.get("hail"),
            (String) standardStationAnswer.get("hail")
        );
        stationScore = stationScore.add(hailScore);
        
        return stationScore;
    }
    
    private BigDecimal calculateRainfallScore(String studentAnswer, String standardAnswer) {
        if (StringUtils.isBlank(standardAnswer)) {
            // 标准答案为空，意味着该站点无强降水
            return StringUtils.isBlank(studentAnswer) ? 
                new BigDecimal("4.0") : BigDecimal.ZERO;
        }
        
        if (StringUtils.isBlank(studentAnswer)) {
            return BigDecimal.ZERO;
        }
        
        if (studentAnswer.equals(standardAnswer)) {
            // 类型和强度都正确
            return new BigDecimal("4.0");
        }
        
        // 检查是否类型正确但强度错误
        if (isSameRainfallType(studentAnswer, standardAnswer)) {
            return new BigDecimal("3.0"); // 类型正确得3分
        }
        
        return BigDecimal.ZERO;
    }
    
    private boolean isSameRainfallType(String answer1, String answer2) {
        // 只要都是强降水类型就算类型正确
        return (answer1.startsWith("level") && answer2.startsWith("level"));
    }
}
```

#### Day 27-28: 评分服务集成
**任务清单**：
- [ ] 创建`ConvectionScoringService.java`
- [ ] 集成自动评分和人工批卷工作流
- [ ] 实现最终成绩计算逻辑
- [ ] 测试评分算法准确性

---

### 阶段五：测试和优化（2-3天）

#### Day 29-30: 功能测试
**任务清单**：
- [ ] 单元测试：各个API接口功能
- [ ] 集成测试：完整考试流程
- [ ] 用户测试：试题录入→考试答题→人工批卷
- [ ] 性能测试：大数据量下的响应速度
- [ ] 兼容性测试：不同浏览器和设备

#### Day 31-32: 优化和部署
**任务清单**：
- [ ] 性能优化：SQL查询、前端渲染
- [ ] 安全加固：数据验证、权限检查
- [ ] 用户体验优化：交互细节、错误提示
- [ ] 文档编写：API文档、用户手册
- [ ] 部署准备：环境配置、数据迁移

## 详细测试策略

### 测试金字塔架构

#### 1. 单元测试（Unit Tests）- 70%
```java
// 示例：强对流评分算法单元测试
@SpringBootTest
class ConvectionScoringAlgorithmTest {
    
    @Autowired
    private ConvectionStationScoringAlgorithm scoringAlgorithm;
    
    @Test
    @DisplayName("测试单站点短时强降水评分计算")
    void testRainfallScoring() {
        // Given - 准备测试数据
        ConvectionStationAnswerDTO studentAnswer = createStudentAnswer("level2");
        ConvectionStationAnswerDTO standardAnswer = createStandardAnswer("level2");
        
        // When - 执行评分计算
        BigDecimal score = scoringAlgorithm.calculateRainfallScore(
            studentAnswer.getStationAnswers().get("station_001"),
            standardAnswer.getStationAnswers().get("station_001")
        );
        
        // Then - 验证结果
        assertThat(score).isEqualTo(new BigDecimal("4.0"));
    }
    
    @Test
    @DisplayName("测试预报依据字数统计功能")
    void testReasoningWordCount() {
        // Given
        String reasoning = "基于MICAPS资料分析，本次强对流天气过程主要特征如下...";
        
        // When
        ConvectionExamAnswer answer = new ConvectionExamAnswer();
        answer.setForecastReasoning(reasoning);
        answer.updateReasoningWordCount();
        
        // Then
        assertThat(answer.getReasoningWordCount()).isEqualTo(reasoning.length());
    }
    
    @ParameterizedTest
    @CsvSource({
        "level1, level1, 4.0",  // 完全正确
        "level1, level2, 3.0",  // 类型正确，强度错误
        "level1, , 0.0",        // 标准答案为空
        ", level1, 0.0"         // 学生答案为空
    })
    @DisplayName("测试各种降水评分场景")
    void testRainfallScoringScenarios(String studentLevel, String standardLevel, BigDecimal expectedScore) {
        // 参数化测试，覆盖各种评分场景
        BigDecimal actualScore = scoringAlgorithm.calculateRainfallScore(studentLevel, standardLevel);
        assertThat(actualScore).isEqualTo(expectedScore);
    }
}
```

#### 2. 集成测试（Integration Tests）- 20%
```java
// 示例：答案保存和查询集成测试
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Transactional
class ConvectionAnswerIntegrationTest {
    
    @Autowired
    private ConvectionAnswerService answerService;
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    @DisplayName("测试完整的答案保存和查询流程")
    void testCompleteAnswerFlow() {
        // Given - 准备考试数据
        String examId = createTestExam();
        String userId = createTestUser();
        ConvectionAnswerDTO answerDTO = createTestAnswer(examId, userId);
        
        // When - 保存答案
        String answerId = answerService.saveAnswer(answerDTO);
        
        // Then - 验证保存结果
        assertThat(answerId).isNotNull();
        
        // When - 查询答案
        ConvectionAnswerDetailDTO savedAnswer = answerService.getStudentAnswer(examId, userId);
        
        // Then - 验证查询结果
        assertThat(savedAnswer).isNotNull();
        assertThat(savedAnswer.getStationAnswer()).isEqualTo(answerDTO.getStationAnswer());
        assertThat(savedAnswer.getForecastReasoning()).isEqualTo(answerDTO.getForecastReasoning());
    }
    
    @Test
    @DisplayName("测试API端点完整调用链")
    void testApiEndpointChain() {
        // Given
        HttpHeaders headers = createAuthHeaders();
        ConvectionAnswerDTO request = createValidAnswerDTO();
        
        // When - 调用保存API
        ResponseEntity<ApiRest<String>> saveResponse = restTemplate.postForEntity(
            "/api/convection/answer/save",
            new HttpEntity<>(request, headers),
            new ParameterizedTypeReference<ApiRest<String>>() {}
        );
        
        // Then - 验证API响应
        assertThat(saveResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(saveResponse.getBody().getCode()).isEqualTo(0);
        
        String answerId = saveResponse.getBody().getData();
        
        // When - 调用查询API
        ResponseEntity<ApiRest<ConvectionAnswerDetailDTO>> getResponse = restTemplate.exchange(
            "/api/convection/answer/detail/" + answerId,
            HttpMethod.GET,
            new HttpEntity<>(headers),
            new ParameterizedTypeReference<ApiRest<ConvectionAnswerDetailDTO>>() {}
        );
        
        // Then - 验证查询结果
        assertThat(getResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(getResponse.getBody().getData()).isNotNull();
    }
}
```

#### 3. 端到端测试（E2E Tests）- 10%
```javascript
// 使用Cypress进行前端E2E测试
describe('强对流考试完整流程测试', () => {
  beforeEach(() => {
    cy.login('<EMAIL>', 'password123')
    cy.visit('/convection/exam')
  })

  it('应能完成完整的强对流考试流程', () => {
    // 选择考试
    cy.get('[data-testid="exam-list"]').should('be.visible')
    cy.get('[data-testid="start-exam-btn"]').first().click()
    
    // 第一部分：站点预报
    cy.get('[data-testid="station-table"]').should('be.visible')
    
    // 为第一个站点选择短时强降水
    cy.get('[data-testid="station-001-rainfall-level2"]').click()
    cy.get('[data-testid="station-001-rainfall-level2"]').should('have.class', 'selected')
    
    // 为第一个站点选择雷暴大风
    cy.get('[data-testid="station-001-wind-severe"]').click()
    
    // 输入预报依据
    cy.get('[data-testid="forecast-reasoning-textarea"]').type(
      '基于MICAPS资料分析，本次强对流天气过程具备以下特征：1. 大气不稳定度较高，CAPE值超过2000J/kg...'
    )
    
    // 检查进度更新
    cy.get('[data-testid="station-progress"]').should('contain', '25%')
    
    // 第二部分：落区绘制
    cy.get('[data-testid="area-drawing-container"]').should('be.visible')
    
    // 选择强降水绘制工具
    cy.get('[data-testid="draw-tool-rainfall"]').click()
    
    // 在地图上绘制区域（模拟鼠标操作）
    cy.get('[data-testid="map-container"]')
      .trigger('mousedown', { x: 100, y: 100 })
      .trigger('mousemove', { x: 200, y: 100 })
      .trigger('mousemove', { x: 200, y: 200 })
      .trigger('mousemove', { x: 100, y: 200 })
      .trigger('mouseup')
    
    // 检查绘制结果
    cy.get('[data-testid="drawn-areas-count"]').should('contain', '1')
    
    // 提交考试
    cy.get('[data-testid="submit-exam-btn"]').click()
    cy.get('[data-testid="confirm-submit-btn"]').click()
    
    // 验证提交成功
    cy.get('[data-testid="submit-success-message"]').should('be.visible')
    cy.url().should('include', '/exam/result')
  })

  it('应能正确处理表单验证错误', () => {
    cy.get('[data-testid="start-exam-btn"]').first().click()
    
    // 尝试直接提交空表单
    cy.get('[data-testid="submit-exam-btn"]').click()
    
    // 应显示验证错误
    cy.get('[data-testid="validation-error"]').should('contain', '请完成站点预报')
    
    // 填写部分数据后再次尝试
    cy.get('[data-testid="station-001-rainfall-level1"]').click()
    cy.get('[data-testid="submit-exam-btn"]').click()
    
    // 应显示需要完成更多内容的提示
    cy.get('[data-testid="validation-error"]').should('contain', '请输入预报依据')
  })
})
```

### 性能测试策略

#### 1. 负载测试配置
```yaml
# JMeter负载测试配置文件: load-test-plan.jmx
TestPlan:
  name: "强对流考试系统负载测试"
  scenarios:
    concurrent_exam_taking:
      thread_count: 100
      ramp_up_period: 60s
      duration: 300s
      requests:
        - name: "登录"
          endpoint: "/api/auth/login"
          method: POST
          expected_response_time: "<500ms"
        
        - name: "获取考试列表"
          endpoint: "/api/convection/exam/online-list"
          method: POST
          expected_response_time: "<1000ms"
        
        - name: "开始考试"
          endpoint: "/api/convection/exam/start"
          method: POST
          expected_response_time: "<2000ms"
        
        - name: "保存答案"
          endpoint: "/api/convection/answer/save"
          method: POST
          frequency: "every 30s"
          expected_response_time: "<1500ms"
        
        - name: "提交考试"
          endpoint: "/api/convection/answer/submit"
          method: POST
          expected_response_time: "<3000ms"

  performance_criteria:
    throughput: ">50 requests/second"
    error_rate: "<1%"
    response_time_95th: "<2000ms"
    concurrent_users: "100+"
```

#### 2. 数据库性能测试
```sql
-- 数据库压力测试脚本
-- 创建测试数据
DELIMITER //
CREATE PROCEDURE GenerateTestData()
BEGIN
    DECLARE i INT DEFAULT 1;
    DECLARE j INT DEFAULT 1;
    
    -- 插入1000个测试考试
    WHILE i <= 1000 DO
        INSERT INTO el_exam (id, title, exam_type, state, create_time)
        VALUES (CONCAT('test_exam_', i), CONCAT('测试考试', i), 'convection', 1, NOW());
        SET i = i + 1;
    END WHILE;
    
    SET i = 1;
    -- 为每个考试插入100个答案
    WHILE i <= 1000 DO
        SET j = 1;
        WHILE j <= 100 DO
            INSERT INTO el_convection_exam_answer (
                id, exam_id, user_id, station_answer, area_answer, 
                forecast_reasoning, answer_status, create_time
            ) VALUES (
                CONCAT('answer_', i, '_', j),
                CONCAT('test_exam_', i),
                CONCAT('user_', j),
                '{"station_001": {"rainfall": "level2", "wind": "severe"}}',
                '{"heavy_rainfall": [{"type": "Polygon", "coordinates": [[[116.3, 39.9], [116.4, 39.9], [116.4, 40.0], [116.3, 40.0], [116.3, 39.9]]]}]}',
                '测试预报依据内容...',
                1,
                NOW()
            );
            SET j = j + 1;
        END WHILE;
        SET i = i + 1;
    END WHILE;
END //
DELIMITER ;

-- 执行数据生成
CALL GenerateTestData();

-- 性能测试查询
-- 测试复杂查询性能
EXPLAIN ANALYZE 
SELECT 
    ea.exam_id,
    COUNT(*) as answer_count,
    AVG(ea.overall_progress) as avg_progress,
    SUM(CASE WHEN ea.answer_status = 1 THEN 1 ELSE 0 END) as submitted_count
FROM el_convection_exam_answer ea
WHERE ea.create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY ea.exam_id
HAVING answer_count > 50
ORDER BY avg_progress DESC
LIMIT 20;

-- 测试批卷查询性能
EXPLAIN ANALYZE
SELECT 
    ea.id as answer_id,
    ea.forecast_reasoning,
    qu.convection_standard_reasoning,
    gr.grading_status,
    u.real_name as student_name
FROM el_convection_exam_answer ea
LEFT JOIN el_convection_grading_record gr ON ea.id = gr.answer_id
JOIN el_exam e ON ea.exam_id = e.id
JOIN el_qu qu ON e.question_id = qu.id
JOIN el_user u ON ea.user_id = u.id
WHERE ea.answer_status = 1 
AND (gr.grading_status IS NULL OR gr.grading_status = 0)
ORDER BY ea.submit_time ASC
LIMIT 50;
```

### 代码质量保证体系

#### 1. 静态代码分析配置
```xml
<!-- SonarQube Maven插件配置 -->
<plugin>
    <groupId>org.sonarsource.scanner.maven</groupId>
    <artifactId>sonar-maven-plugin</artifactId>
    <version>3.9.1.2184</version>
    <configuration>
        <sonar.projectKey>convection-exam-module</sonar.projectKey>
        <sonar.projectName>强对流考试模块</sonar.projectName>
        <sonar.host.url>http://localhost:9000</sonar.host.url>
        <sonar.exclusions>
            **/target/**,
            **/test/**,
            **/*.js,
            **/*.css
        </sonar.exclusions>
        <sonar.coverage.jacoco.xmlReportPaths>
            target/site/jacoco/jacoco.xml
        </sonar.coverage.jacoco.xmlReportPaths>
        <sonar.java.codeCoveragePlugin>jacoco</sonar.java.codeCoveragePlugin>
    </configuration>
</plugin>

<!-- JaCoCo代码覆盖率插件 -->
<plugin>
    <groupId>org.jacoco</groupId>
    <artifactId>jacoco-maven-plugin</artifactId>
    <version>0.8.7</version>
    <executions>
        <execution>
            <goals>
                <goal>prepare-agent</goal>
            </goals>
        </execution>
        <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
                <goal>report</goal>
            </goals>
        </execution>
        <execution>
            <id>check</id>
            <goals>
                <goal>check</goal>
            </goals>
            <configuration>
                <rules>
                    <rule>
                        <element>BUNDLE</element>
                        <limits>
                            <limit>
                                <counter>LINE</counter>
                                <value>COVEREDRATIO</value>
                                <minimum>0.80</minimum>
                            </limit>
                        </limits>
                    </rule>
                </rules>
            </configuration>
        </execution>
    </executions>
</plugin>
```

#### 2. 前端代码质量配置
```json
// .eslintrc.js - ESLint配置
module.exports = {
  root: true,
  env: {
    node: true,
    browser: true,
    es2021: true
  },
  extends: [
    'plugin:vue/recommended',
    '@vue/standard'
  ],
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module'
  },
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'vue/max-attributes-per-line': [2, {
      'singleline': 3,
      'multiline': {
        'max': 1,
        'allowFirstLine': false
      }
    }],
    'vue/component-name-in-template-casing': ['error', 'kebab-case'],
    'vue/no-v-html': 'off',
    'complexity': ['error', 10],
    'max-lines-per-function': ['error', 50],
    'max-depth': ['error', 4]
  }
}

// jest.config.js - Jest测试配置
module.exports = {
  preset: '@vue/cli-plugin-unit-jest',
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.{js,vue}',
    '!src/main.js',
    '!src/router/index.js',
    '!**/node_modules/**'
  ],
  coverageReporters: ['html', 'text', 'lcov'],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js']
}
```

### 详细部署指南

#### 1. 容器化部署配置
```dockerfile
# Dockerfile - 后端服务容器化
FROM openjdk:8-jdk-alpine
VOLUME /tmp

# 安装字体和工具（支持中文）
RUN apk add --no-cache fontconfig ttf-dejavu

# 复制应用程序
COPY target/exam-api-1.0.0.jar app.jar

# 创建应用用户
RUN adduser -D -s /bin/sh examuser
USER examuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-jar", "/app.jar"]
EXPOSE 8080
```

```dockerfile
# Dockerfile.frontend - 前端应用容器化
FROM node:14-alpine as build-stage
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM nginx:alpine as production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:80/ || exit 1

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### 2. Docker Compose编排配置
```yaml
# docker-compose.yml - 完整的服务编排
version: '3.8'

services:
  mysql:
    image: mysql:8.0.11
    container_name: exam-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: exam_db
      MYSQL_USER: exam_user
      MYSQL_PASSWORD: exam_pass
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  redis:
    image: redis:6.2-alpine
    container_name: exam-redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  backend:
    build: .
    container_name: exam-backend
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: *********************************************************************************************
      SPRING_DATASOURCE_USERNAME: exam_user
      SPRING_DATASOURCE_PASSWORD: exam_pass
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
    ports:
      - "8080:8080"
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - app_logs:/app/logs
      - file_uploads:/app/uploads
    restart: unless-stopped

  frontend:
    build:
      context: ./exam-vue
      dockerfile: Dockerfile.frontend
    container_name: exam-frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    container_name: exam-nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    ports:
      - "443:443"
    depends_on:
      - frontend
      - backend
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
  app_logs:
  file_uploads:

networks:
  default:
    name: exam-network
```

#### 3. Kubernetes部署配置
```yaml
# k8s-deployment.yaml - Kubernetes部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: convection-exam-backend
  labels:
    app: convection-exam
    tier: backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: convection-exam
      tier: backend
  template:
    metadata:
      labels:
        app: convection-exam
        tier: backend
    spec:
      containers:
      - name: backend
        image: convection-exam:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        - name: SPRING_DATASOURCE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
        - name: SPRING_DATASOURCE_USERNAME
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: username
        - name: SPRING_DATASOURCE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: password
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        volumeMounts:
        - name: app-logs
          mountPath: /app/logs
        - name: file-uploads
          mountPath: /app/uploads
      volumes:
      - name: app-logs
        persistentVolumeClaim:
          claimName: app-logs-pvc
      - name: file-uploads
        persistentVolumeClaim:
          claimName: file-uploads-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: convection-exam-backend-service
spec:
  selector:
    app: convection-exam
    tier: backend
  ports:
  - protocol: TCP
    port: 8080
    targetPort: 8080
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: convection-exam-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - exam.yourdomain.com
    secretName: exam-tls-secret
  rules:
  - host: exam.yourdomain.com
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: convection-exam-backend-service
            port:
              number: 8080
      - path: /
        pathType: Prefix
        backend:
          service:
            name: convection-exam-frontend-service
            port:
              number: 80
```

### 监控与运维体系

#### 1. 应用监控配置
```yaml
# application-monitoring.yml - Spring Boot Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: convection-exam
      environment: ${SPRING_PROFILES_ACTIVE:dev}
  health:
    db:
      enabled: true
    redis:
      enabled: true

# 自定义健康指标
@Component
public class ConvectionExamHealthIndicator implements HealthIndicator {
    
    @Autowired
    private ConvectionAnswerService answerService;
    
    @Override
    public Health health() {
        try {
            // 检查数据库连接
            long count = answerService.count();
            
            // 检查关键服务状态
            if (count >= 0) {
                return Health.up()
                    .withDetail("database", "accessible")
                    .withDetail("answer_count", count)
                    .build();
            } else {
                return Health.down()
                    .withDetail("database", "inaccessible")
                    .build();
            }
        } catch (Exception e) {
            return Health.down()
                .withDetail("error", e.getMessage())
                .build();
        }
    }
}
```

#### 2. Prometheus监控配置
```yaml
# prometheus.yml - Prometheus配置
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "convection_exam_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'convection-exam-backend'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s
    
  - job_name: 'mysql-exporter'
    static_configs:
      - targets: ['localhost:9104']
    
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['localhost:9121']
```

```yaml
# convection_exam_rules.yml - 告警规则
groups:
  - name: convection-exam-alerts
    rules:
      - alert: HighMemoryUsage
        expr: (java_lang_Memory_HeapMemoryUsage_used / java_lang_Memory_HeapMemoryUsage_max) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "应用内存使用率过高"
          description: "内存使用率已达到 {{ $value }}%"
      
      - alert: DatabaseConnectionFailed
        expr: up{job="convection-exam-backend"} == 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "数据库连接失败"
          description: "强对流考试系统无法连接到数据库"
      
      - alert: HighResponseTime
        expr: http_server_requests_seconds{quantile="0.95"} > 2.0
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "API响应时间过长"
          description: "95%分位数响应时间超过2秒: {{ $value }}s"
      
      - alert: ExamSubmissionFailed
        expr: increase(http_server_requests_seconds_count{uri="/api/convection/answer/submit",status=~"5.."}[5m]) > 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "考试提交失败"
          description: "检测到考试提交API返回5xx错误"
```

#### 3. 日志管理配置
```xml
<!-- logback-spring.xml - 日志配置 -->
<configuration>
    <springProfile name="!prod">
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>

    <springProfile name="prod">
        <!-- 应用日志 -->
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>logs/convection-exam.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <fileNamePattern>logs/convection-exam.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
                <maxFileSize>100MB</maxFileSize>
                <maxHistory>30</maxHistory>
                <totalSizeCap>10GB</totalSizeCap>
            </rollingPolicy>
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <timestamp/>
                    <logLevel/>
                    <loggerName/>
                    <message/>
                    <mdc/>
                    <stackTrace/>
                </providers>
            </encoder>
        </appender>

        <!-- 错误日志 -->
        <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>logs/convection-exam-error.log</file>
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>ERROR</level>
            </filter>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <fileNamePattern>logs/convection-exam-error.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
                <maxFileSize>50MB</maxFileSize>
                <maxHistory>60</maxHistory>
            </rollingPolicy>
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <timestamp/>
                    <logLevel/>
                    <loggerName/>
                    <message/>
                    <mdc/>
                    <stackTrace/>
                </providers>
            </encoder>
        </appender>

        <!-- 业务日志 -->
        <appender name="BUSINESS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>logs/convection-exam-business.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <fileNamePattern>logs/convection-exam-business.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
                <maxFileSize>200MB</maxFileSize>
                <maxHistory>90</maxHistory>
            </rollingPolicy>
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <timestamp/>
                    <logLevel/>
                    <loggerName/>
                    <message/>
                    <mdc/>
                </providers>
            </encoder>
        </appender>

        <logger name="com.yf.exam.modules.convection" level="INFO" additivity="false">
            <appender-ref ref="BUSINESS_FILE"/>
            <appender-ref ref="ERROR_FILE"/>
        </logger>

        <root level="INFO">
            <appender-ref ref="FILE"/>
            <appender-ref ref="ERROR_FILE"/>
        </root>
    </springProfile>
</configuration>
```

### 团队协作与项目管理

#### 1. 开发工作流程
```mermaid
graph TD
    A[需求分析] --> B[技术设计]
    B --> C[任务分解]
    C --> D[功能开发]
    D --> E[单元测试]
    E --> F[代码审查]
    F --> G{审查通过?}
    G -->|否| D
    G -->|是| H[集成测试]
    H --> I[部署测试环境]
    I --> J[功能测试]
    J --> K{测试通过?}
    K -->|否| D
    K -->|是| L[部署生产环境]
    L --> M[发布验证]
    M --> N[项目完成]
```

#### 2. Git工作流规范
```bash
# 分支命名规范
feature/convection-station-table    # 功能分支
bugfix/scoring-calculation-error    # 修复分支
hotfix/critical-security-patch      # 热修复分支
release/v1.0.0                      # 发布分支

# 提交信息模板
git config commit.template .gitmessage

# .gitmessage 内容
# <type>(<scope>): <subject>
#
# <body>
#
# <footer>
#
# Type可以是:
# feat:     新功能
# fix:      bug修复
# docs:     文档更新
# style:    代码格式修改
# refactor: 代码重构
# test:     测试用例
# chore:    构建过程或辅助工具变动
# perf:     性能优化
# revert:   撤销之前的commit
#
# Scope可以是:
# convection: 强对流模块
# scoring:    评分引擎
# grading:    人工批卷
# database:   数据库相关
# config:     配置相关
#
# Subject:
# - 使用动词原形开头，首字母小写
# - 不超过50个字符
# - 结尾不加句号
#
# Body:
# - 详细描述更改的内容
# - 解释为什么进行这些更改
# - 每行不超过72个字符
#
# Footer:
# - 关联的Issue编号
# - Breaking changes说明
```

#### 3. 代码审查清单
```markdown
# 代码审查清单 (Code Review Checklist)

## 功能性检查
- [ ] 代码实现符合需求规格说明
- [ ] 边界条件处理正确
- [ ] 错误处理机制完善
- [ ] 用户输入验证充分
- [ ] 业务逻辑实现正确

## 代码质量检查
- [ ] 代码结构清晰，职责分离
- [ ] 命名规范，含义明确
- [ ] 注释充分，说明清楚
- [ ] 无重复代码，复用性好
- [ ] 复杂度控制在合理范围

## 性能检查
- [ ] 无明显性能瓶颈
- [ ] 数据库查询优化
- [ ] 内存使用合理
- [ ] 并发处理安全
- [ ] 缓存使用恰当

## 安全性检查
- [ ] 输入数据验证和过滤
- [ ] SQL注入防护
- [ ] XSS攻击防护
- [ ] 权限控制实现
- [ ] 敏感信息保护

## 测试检查
- [ ] 单元测试覆盖率达标
- [ ] 测试用例设计合理
- [ ] 集成测试通过
- [ ] 边界测试完备
- [ ] 异常场景测试

## 兼容性检查
- [ ] 浏览器兼容性
- [ ] 移动端适配
- [ ] 接口向下兼容
- [ ] 数据库版本兼容
- [ ] 第三方依赖兼容
```

#### 4. 项目进度管理
```yaml
# 项目里程碑配置
milestones:
  - name: "M1 - 基础架构完成"
    date: "2024-12-26"
    deliverables:
      - "数据库表设计和创建"
      - "基础实体类和DTO设计"
      - "项目目录结构搭建"
      - "开发环境配置完成"
    
  - name: "M2 - 后端API开发完成"
    date: "2025-01-05"
    deliverables:
      - "试题管理API完成"
      - "考试管理API完成"
      - "答案管理API完成"
      - "人工批卷API完成"
      - "单元测试通过率>80%"
    
  - name: "M3 - 前端界面开发完成"
    date: "2025-01-17"
    deliverables:
      - "试题管理界面完成"
      - "考试答题界面完成"
      - "人工批卷界面完成"
      - "界面集成测试通过"
    
  - name: "M4 - 评分引擎集成完成"
    date: "2025-01-21"
    deliverables:
      - "评分算法实现完成"
      - "评分配置文件完成"
      - "评分流程测试通过"
      - "性能测试达标"
    
  - name: "M5 - 项目交付"
    date: "2025-01-24"
    deliverables:
      - "系统功能测试完成"
      - "用户验收测试通过"
      - "部署文档完成"
      - "用户培训完成"

# 风险管理
risks:
  - risk: "数据库性能问题"
    probability: "中"
    impact: "高"
    mitigation: "提前进行性能测试，优化数据库索引"
    
  - risk: "前端地图组件集成困难"
    probability: "中"
    impact: "中"
    mitigation: "提前技术验证，准备备选方案"
    
  - risk: "人工批卷工作流复杂"
    probability: "高"
    impact: "中"
    mitigation: "分步实现，先完成基础功能"
    
  - risk: "团队成员技能不足"
    probability: "低"
    impact: "高"
    mitigation: "提前技能培训，外部技术支持"

# 质量门禁
quality_gates:
  - stage: "开发完成"
    criteria:
      - "代码审查通过率 = 100%"
      - "单元测试覆盖率 >= 80%"
      - "静态代码分析无严重问题"
      - "功能自测通过率 = 100%"
  
  - stage: "集成测试"
    criteria:
      - "集成测试通过率 >= 95%"
      - "API响应时间 < 1000ms"
      - "系统资源使用正常"
      - "安全扫描无高危漏洞"
  
  - stage: "用户验收"
    criteria:
      - "用户验收测试通过率 = 100%"
      - "性能指标达到要求"
      - "文档完整性检查通过"
      - "部署验证成功"
```

通过以上全面详细的开发计划，强对流天气临近预报考试模块将能够按照高质量标准完成开发，确保系统的稳定性、可维护性和用户体验。整个开发过程将严格按照测试驱动开发、持续集成、代码审查等最佳实践进行，为项目成功交付提供有力保障。 