# 气象站点区域编号映射查询功能

## 功能概述

在 `ElStationController` 中新增了通过区域编号（1-9）查询站点信息的功能。该功能支持：
1. 接收区域编号数组（如 `["1", "2", "9"]`）
2. 自动将区域编号映射为对应的省份行政区划代码
3. 将省份代码中的 `00` 替换为空并添加 `%` 进行模糊查询
4. 返回匹配的站点信息列表

## 区域编号映射规则

| 区域编号 | 区域名称 | 包含省份 | 对应行政区划代码 |
|---------|----------|----------|------------------|
| 1 | 华北区域 | 北京、天津、河北、山西、山东、河南 | 110000, 120000, 130000, 140000, 370000, 410000 |
| 2 | 东北区域 | 辽宁、吉林、黑龙江 | 210000, 220000, 230000 |
| 3 | 长江中下游区域 | 上海、江苏、浙江、安徽、湖北、湖南、江西 | 310000, 320000, 330000, 340000, 420000, 430000, 360000 |
| 4 | 华南区域 | 广东、广西、海南、福建 | 440000, 450000, 460000, 350000 |
| 5 | 西南地区东部 | 重庆、四川、贵州、云南 | 500000, 510000, 520000, 530000 |
| 6 | 青藏高原区域 | 青海、西藏 | 630000, 540000 |
| 7 | 新疆区域 | 新疆 | 650000 |
| 8 | 西北地区东部 | 陕西、甘肃、宁夏 | 610000, 620000, 640000 |
| 9 | 内蒙古区域 | 内蒙古 | 150000 |

## 新增/修改的文件

### 1. RegionCodeMapper.java (新增)
**路径**: `src/main/java/com/yf/exam/modules/station/utils/RegionCodeMapper.java`

区域编号映射工具类，提供以下功能：
- `convertRegionCodesToProvinceCodes()`: 将区域编号转换为省份行政区划代码
- `getSupportedRegionCodes()`: 获取所有支持的区域编号
- `isValidRegionCode()`: 检查区域编号是否有效
- `getProvinceNamesByRegionCode()`: 获取区域编号对应的省份名称列表

### 2. ElStationRegionReqDTO.java (修改)
修改 `regionCodes` 字段：
- 类型：`List<String>`
- 含义：区域编号数组（1-9）
- 示例：`["1", "2", "9"]`

### 3. ElStationServiceImpl.java (修改)
修改 `listByRegionCodes` 方法实现：
1. 使用 `RegionCodeMapper` 将区域编号转换为省份代码
2. 处理省份代码（去除00，添加%）
3. 调用Mapper进行数据库查询

## API 接口详情

### 接口地址
`POST /exam/api/station/listByRegionCodes`

### 请求参数
```json
{
  "regionCodes": ["1", "9"],
  "stationLevl": "国家站",
  "online": 1,
  "drawTown": 1
}
```

### 参数说明
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionCodes | Array[String] | 是 | 区域编号数组，取值范围：1-9 |
| stationLevl | String | 否 | 站点等级过滤条件 |
| online | Integer | 否 | 是否在线（0-离线，1-在线） |
| drawTown | Integer | 否 | 是否绘制到地图（0-否，1-是） |

### 响应示例
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": "1234567890",
      "adminCodeChn": "110100",
      "alti": 31.3,
      "city": "北京",
      "cnty": "朝阳区",
      "lat": 39.9,
      "lon": 116.4,
      "stationIdC": "54511",
      "stationLevl": "国家站",
      "stationName": "北京站",
      "alias": "Beijing",
      "online": 1,
      "drawTown": 1,
      "sort": 1
    },
    {
      "id": "1234567891",
      "adminCodeChn": "150100",
      "alti": 1063.0,
      "city": "呼和浩特",
      "cnty": "新城区",
      "lat": 40.8,
      "lon": 111.7,
      "stationIdC": "53463",
      "stationLevl": "国家站",
      "stationName": "呼和浩特站",
      "alias": "Hohhot",
      "online": 1,
      "drawTown": 1,
      "sort": 2
    }
  ]
}
```

## 数据转换流程

### 示例：查询华北区域和内蒙古区域的站点

1. **输入**: `regionCodes: ["1", "9"]`

2. **区域编号转换**:
   - "1" → ["110000", "120000", "130000", "140000", "370000", "410000"]
   - "9" → ["150000"]
   - 合并去重后：["110000", "120000", "130000", "140000", "370000", "410000", "150000"]

3. **省份代码处理**:
   - "110000" → "11%"
   - "120000" → "12%"
   - "130000" → "13%"
   - "140000" → "14%"
   - "370000" → "37%"
   - "410000" → "41%"
   - "150000" → "15%"

4. **SQL查询**:
   ```sql
   SELECT * FROM el_station station
   WHERE (
       station.admin_code_chn LIKE '11%' OR
       station.admin_code_chn LIKE '12%' OR
       station.admin_code_chn LIKE '13%' OR
       station.admin_code_chn LIKE '14%' OR
       station.admin_code_chn LIKE '37%' OR
       station.admin_code_chn LIKE '41%' OR
       station.admin_code_chn LIKE '15%'
   )
   ORDER BY station.sort ASC, station.id ASC
   ```

## 使用场景

### 1. 查询单个区域
```json
{
  "regionCodes": ["1"]  // 查询华北区域的所有站点
}
```

### 2. 查询多个区域
```json
{
  "regionCodes": ["1", "2", "9"]  // 查询华北、东北、内蒙古区域的站点
}
```

### 3. 带条件过滤
```json
{
  "regionCodes": ["1"],
  "stationLevl": "国家站",
  "online": 1
}
```

### 4. 查询所有区域
```json
{
  "regionCodes": ["1", "2", "3", "4", "5", "6", "7", "8", "9"]
}
```

## 工具类方法说明

### RegionCodeMapper.convertRegionCodesToProvinceCodes()
```java
// 输入：["1", "9"]
// 输出：["110000", "120000", "130000", "140000", "370000", "410000", "150000"]
List<String> provinceCodes = RegionCodeMapper.convertRegionCodesToProvinceCodes(regionCodes);
```

### RegionCodeMapper.getProvinceNamesByRegionCode()
```java
// 输入："1"
// 输出：["北京", "天津", "河北", "山西", "山东", "河南"]
List<String> provinceNames = RegionCodeMapper.getProvinceNamesByRegionCode("1");
```

### RegionCodeMapper.isValidRegionCode()
```java
// 检查区域编号是否有效
boolean isValid = RegionCodeMapper.isValidRegionCode("1"); // true
boolean isInvalid = RegionCodeMapper.isValidRegionCode("10"); // false
```

## 异常处理

### 1. 无效区域编号
- 当传入无效的区域编号时，该编号会被忽略
- 如果所有区域编号都无效，返回空列表

### 2. 空参数处理
- 当 `regionCodes` 为空或 null 时，返回空列表

### 3. 参数验证
- 使用 `@Valid` 和 `@NotEmpty` 进行参数验证

## 性能优化建议

### 1. 数据库索引
建议在 `admin_code_chn` 字段上创建索引：
```sql
CREATE INDEX idx_admin_code_chn ON el_station(admin_code_chn);
```

### 2. 缓存优化
可以考虑将区域编号映射关系缓存到Redis中，提高转换效率。

## 测试用例

### 1. 功能测试
- 测试单个区域编号查询
- 测试多个区域编号查询
- 测试无效区域编号处理
- 测试带过滤条件的查询

### 2. 边界测试
- 测试空数组输入
- 测试所有区域编号输入
- 测试重复区域编号输入
