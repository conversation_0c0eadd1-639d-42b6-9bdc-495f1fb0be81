// 强对流考试模块端到端测试
describe('强对流考试完整流程', () => {
  const mockExamData = {
    examId: 'exam_convection_001',
    questionId: 'qu_convection_001',
    stations: [
      { code: '54511', name: '北京', lon: 116.28, lat: 39.93 },
      { code: '54517', name: '密云', lon: 116.83, lat: 40.37 },
      { code: '54594', name: '天津', lon: 117.17, lat: 39.08 }
    ]
  }

  beforeEach(() => {
    // 模拟用户登录
    cy.login('student', 'password')
    
    // 模拟API响应
    cy.intercept('GET', '/api/convection/exam/*', {
      statusCode: 200,
      body: {
        code: 0,
        data: {
          id: mockExamData.examId,
          title: '强对流天气临近预报考试',
          description: '测试强对流天气预报能力',
          status: 1,
          duration: 60
        }
      }
    }).as('getExamDetail')

    cy.intercept('GET', '/api/convection/case/*', {
      statusCode: 200,
      body: {
        code: 0,
        data: {
          id: mockExamData.questionId,
          title: '强对流天气预报试题',
          content: '请根据MICAPS资料进行强对流天气预报',
          stations: mockExamData.stations
        }
      }
    }).as('getQuestionDetail')

    cy.intercept('POST', '/api/convection/answer/save', {
      statusCode: 200,
      body: {
        code: 0,
        data: 'answer_123'
      }
    }).as('saveAnswer')
  })

  describe('学生考试流程', () => {
    it('应该能够完成完整的考试答题流程', () => {
      // 1. 访问考试页面
      cy.visit(`/student/convection/exam/${mockExamData.examId}`)
      cy.wait('@getExamDetail')
      cy.wait('@getQuestionDetail')

      // 2. 验证页面加载
      cy.contains('强对流天气临近预报考试').should('be.visible')
      cy.contains('第一部分：站点预报表格').should('be.visible')
      cy.contains('第二部分：落区绘制').should('be.visible')

      // 3. 填写站点预报表格
      cy.get('[data-testid="station-table"]').should('be.visible')
      
      // 选择北京站的短时强降水级别
      cy.get('[data-testid="station-54511-rainfall-level1"]').click()
      cy.get('[data-testid="station-54511-rainfall-level1"]').should('have.class', 'selected')

      // 选择北京站的雷暴大风级别
      cy.get('[data-testid="station-54511-wind-moderate"]').click()
      cy.get('[data-testid="station-54511-wind-moderate"]').should('have.class', 'selected')

      // 选择密云站的天气现象
      cy.get('[data-testid="station-54517-rainfall-level2"]').click()
      cy.get('[data-testid="station-54517-hail-large"]').click()

      // 4. 填写预报依据
      const reasoningText = '根据MICAPS资料分析，北京地区受到强对流天气系统影响，预计出现短时强降水和雷暴大风。密云地区受冷空气和暖湿气流交汇影响，具备产生冰雹的条件。'
      
      cy.get('[data-testid="forecast-reasoning-input"]').type(reasoningText)
      cy.get('[data-testid="word-count"]').should('contain', reasoningText.length)

      // 5. 验证进度更新
      cy.get('[data-testid="station-progress"]').should('not.contain', '0%')
      cy.get('[data-testid="overall-progress"]').should('not.contain', '0%')

      // 6. 等待自动保存
      cy.wait('@saveAnswer', { timeout: 5000 })
      cy.get('[data-testid="auto-save-indicator"]').should('contain', '已自动保存')

      // 7. 进行落区绘制
      cy.get('[data-testid="area-drawing"]').should('be.visible')
      
      // 选择短时强降水类型
      cy.get('[data-testid="weather-type-heavy_rainfall"]').click()
      
      // 模拟在地图上绘制（这里简化为点击绘制按钮）
      cy.get('[data-testid="draw-polygon-btn"]').click()
      
      // 模拟绘制完成（实际E2E测试中需要更复杂的地图交互）
      cy.get('[data-testid="drawing-complete-btn"]').click()

      // 8. 验证绘制进度
      cy.get('[data-testid="area-progress"]').should('not.contain', '0%')

      // 9. 预览答案
      cy.get('[data-testid="preview-answer-btn"]').click()
      cy.get('[data-testid="answer-preview-dialog"]').should('be.visible')
      
      // 验证预览内容
      cy.get('[data-testid="preview-station-answer"]').should('contain', '北京')
      cy.get('[data-testid="preview-reasoning"]').should('contain', reasoningText.substring(0, 50))
      
      cy.get('[data-testid="close-preview-btn"]').click()

      // 10. 提交考试
      cy.intercept('POST', '/api/convection/answer/submit', {
        statusCode: 200,
        body: {
          code: 0,
          message: '提交成功'
        }
      }).as('submitExam')

      cy.get('[data-testid="submit-exam-btn"]').click()
      
      // 确认提交对话框
      cy.get('.el-message-box').should('be.visible')
      cy.get('.el-message-box__message').should('contain', '确定要提交考试吗')
      cy.get('.el-button--primary').contains('确定提交').click()

      cy.wait('@submitExam')

      // 11. 验证提交成功
      cy.get('.el-message--success').should('contain', '提交成功')
      
      // 12. 跳转到结果页面
      cy.url().should('include', '/student/convection/result')
    })

    it('应该正确处理网络错误', () => {
      // 模拟网络错误
      cy.intercept('POST', '/api/convection/answer/save', {
        statusCode: 500,
        body: { code: -1, message: '服务器错误' }
      }).as('saveAnswerError')

      cy.visit(`/student/convection/exam/${mockExamData.examId}`)
      
      // 尝试答题
      cy.get('[data-testid="station-54511-rainfall-level1"]').click()
      
      // 等待错误响应
      cy.wait('@saveAnswerError')
      
      // 验证错误提示
      cy.get('.el-message--error').should('contain', '自动保存失败')
    })

    it('应该支持考试时间倒计时', () => {
      cy.visit(`/student/convection/exam/${mockExamData.examId}`)
      
      // 验证计时器显示
      cy.get('[data-testid="exam-timer"]').should('be.visible')
      cy.get('[data-testid="remaining-time"]').should('match', /\d{2}:\d{2}:\d{2}/)
      
      // 模拟时间流逝（快进时间）
      cy.clock()
      cy.tick(60000) // 快进1分钟
      
      // 验证时间更新
      cy.get('[data-testid="remaining-time"]').should('not.contain', '01:00:00')
    })
  })

  describe('教师批卷流程', () => {
    beforeEach(() => {
      cy.login('teacher', 'password')
      
      // 模拟批卷API
      cy.intercept('GET', '/api/convection/grading/pending-list', {
        statusCode: 200,
        body: {
          code: 0,
          data: {
            records: [
              {
                id: 'answer_123',
                studentName: '张三',
                examTitle: '强对流天气临近预报考试',
                submitTime: new Date().toISOString(),
                gradingStatus: 0
              }
            ],
            total: 1
          }
        }
      }).as('getPendingList')

      cy.intercept('GET', '/api/convection/grading/detail/*', {
        statusCode: 200,
        body: {
          code: 0,
          data: {
            answer: {
              id: 'answer_123',
              stationAnswer: {
                '54511': { rainfall: 'level1', wind: 'moderate' }
              },
              forecastReasoning: '根据MICAPS资料分析...'
            },
            standardReasoning: '标准预报依据...',
            studentName: '张三'
          }
        }
      }).as('getGradingDetail')
    })

    it('应该能够完成人工批卷流程', () => {
      // 1. 访问批卷列表
      cy.visit('/convection/grading')
      cy.wait('@getPendingList')

      // 2. 验证列表显示
      cy.get('[data-testid="grading-list"]').should('be.visible')
      cy.contains('张三').should('be.visible')

      // 3. 进入批卷详情
      cy.get('[data-testid="grading-detail-btn"]').first().click()
      cy.wait('@getGradingDetail')

      // 4. 验证答案对比显示
      cy.get('[data-testid="answer-comparison"]').should('be.visible')
      cy.get('[data-testid="student-answer"]').should('contain', '根据MICAPS资料分析')
      cy.get('[data-testid="standard-reasoning"]').should('contain', '标准预报依据')

      // 5. 填写评分
      cy.get('[data-testid="reasoning-basis-score"]').clear().type('8')
      cy.get('[data-testid="reasoning-extreme-score"]').clear().type('7')

      // 6. 填写评语
      cy.get('[data-testid="grading-comments"]').type('预报依据分析较为准确，但在极端天气识别方面还需加强。')
      cy.get('[data-testid="improvement-suggestions"]').type('建议加强对MICAPS资料中关键指标的分析能力。')

      // 7. 提交批卷结果
      cy.intercept('POST', '/api/convection/grading/submit-reasoning-score', {
        statusCode: 200,
        body: { code: 0, message: '批卷提交成功' }
      }).as('submitGrading')

      cy.get('[data-testid="submit-grading-btn"]').click()
      cy.wait('@submitGrading')

      // 8. 验证提交成功
      cy.get('.el-message--success').should('contain', '批卷提交成功')
    })
  })

  describe('管理员管理流程', () => {
    beforeEach(() => {
      cy.login('admin', 'password')
    })

    it('应该能够创建强对流试题', () => {
      // 模拟试题创建API
      cy.intercept('POST', '/api/convection/case/save', {
        statusCode: 200,
        body: { code: 0, data: 'qu_new_123' }
      }).as('saveQuestion')

      // 1. 访问试题管理页面
      cy.visit('/convection/qu')

      // 2. 点击新建试题
      cy.get('[data-testid="add-question-btn"]').click()

      // 3. 填写试题信息
      cy.get('[data-testid="question-title"]').type('新的强对流天气预报试题')
      cy.get('[data-testid="question-content"]').type('请根据给定的MICAPS资料进行强对流天气预报分析')
      
      // 4. 设置标准答案
      cy.get('[data-testid="standard-reasoning"]').type('标准预报依据内容...')

      // 5. 提交保存
      cy.get('[data-testid="save-question-btn"]').click()
      cy.wait('@saveQuestion')

      // 6. 验证保存成功
      cy.get('.el-message--success').should('contain', '试题创建成功')
    })

    it('应该能够创建强对流考试', () => {
      // 模拟考试创建API
      cy.intercept('POST', '/api/convection/exam/save', {
        statusCode: 200,
        body: { code: 0, data: 'exam_new_123' }
      }).as('saveExam')

      // 1. 访问考试管理页面
      cy.visit('/convection/exam')

      // 2. 点击新建考试
      cy.get('[data-testid="add-exam-btn"]').click()

      // 3. 填写考试信息
      cy.get('[data-testid="exam-title"]').type('新的强对流天气考试')
      cy.get('[data-testid="exam-description"]').type('测试学生的强对流天气预报能力')
      cy.get('[data-testid="exam-duration"]').clear().type('90')

      // 4. 选择试题
      cy.get('[data-testid="question-selector"]').click()
      cy.get('.el-select-dropdown__item').first().click()

      // 5. 提交保存
      cy.get('[data-testid="save-exam-btn"]').click()
      cy.wait('@saveExam')

      // 6. 验证保存成功
      cy.get('.el-message--success').should('contain', '考试创建成功')
    })
  })

  describe('响应式兼容性测试', () => {
    const viewports = [
      { device: 'mobile', width: 375, height: 667 },
      { device: 'tablet', width: 768, height: 1024 },
      { device: 'desktop', width: 1920, height: 1080 }
    ]

    viewports.forEach(({ device, width, height }) => {
      it(`应该在${device}设备上正确显示`, () => {
        cy.viewport(width, height)
        cy.visit(`/student/convection/exam/${mockExamData.examId}`)

        // 验证关键元素可见性
        cy.get('[data-testid="station-table"]').should('be.visible')
        cy.get('[data-testid="area-drawing"]').should('be.visible')

        if (device === 'mobile') {
          // 移动端特定验证
          cy.get('.table-header').should('have.css', 'flex-direction', 'column')
        }
      })
    })
  })

  describe('性能测试', () => {
    it('页面加载时间应该在可接受范围内', () => {
      cy.visit(`/student/convection/exam/${mockExamData.examId}`, {
        onBeforeLoad: (win) => {
          win.performance.mark('page-start')
        }
      })

      cy.window().then((win) => {
        win.performance.mark('page-end')
        win.performance.measure('page-load', 'page-start', 'page-end')
        
        const measure = win.performance.getEntriesByName('page-load')[0]
        expect(measure.duration).to.be.lessThan(3000) // 3秒内加载完成
      })
    })

    it('大量数据渲染应该保持流畅', () => {
      // 模拟大量站点数据
      const largeStationList = Array.from({ length: 50 }, (_, i) => ({
        code: `station_${i}`,
        name: `站点${i}`,
        lon: 116 + i * 0.1,
        lat: 39 + i * 0.1
      }))

      cy.intercept('GET', '/api/convection/case/*', {
        statusCode: 200,
        body: {
          code: 0,
          data: {
            id: mockExamData.questionId,
            stations: largeStationList
          }
        }
      })

      cy.visit(`/student/convection/exam/${mockExamData.examId}`)

      // 验证渲染性能
      cy.get('[data-testid="station-table"]').should('be.visible')
      cy.get('[data-testid="station-table"] tbody tr').should('have.length.at.least', 3) // 确保行渲染正确
    })
  })
})

// 自定义命令
Cypress.Commands.add('login', (role, password) => {
  cy.visit('/login')
  cy.get('[data-testid="username"]').type(role)
  cy.get('[data-testid="password"]').type(password)
  cy.get('[data-testid="login-btn"]').click()
  cy.url().should('include', '/dashboard')
})

Cypress.Commands.add('mockConvectionAPIs', () => {
  // 通用API模拟
  cy.intercept('GET', '/api/convection/**', { fixture: 'convection/default.json' })
  cy.intercept('POST', '/api/convection/**', { statusCode: 200, body: { code: 0 } })
}) 