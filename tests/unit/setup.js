import Vue from 'vue'
import Element<PERSON> from 'element-ui'
import VueRouter from 'vue-router'
import Vuex from 'vuex'

// 安装Vue插件
Vue.use(ElementUI)
Vue.use(VueRouter)
Vue.use(Vuex)

// 全局配置
Vue.config.productionTip = false

// 模拟全局对象
global.console = {
  ...console,
  // 在测试中抑制某些日志
  warn: jest.fn(),
  error: jest.fn()
}

// 模拟window对象属性
Object.defineProperty(window, 'localStorage', {
  value: {
    store: {},
    getItem: jest.fn((key) => window.localStorage.store[key] || null),
    setItem: jest.fn((key, value) => {
      window.localStorage.store[key] = String(value)
    }),
    removeItem: jest.fn((key) => {
      delete window.localStorage.store[key]
    }),
    clear: jest.fn(() => {
      window.localStorage.store = {}
    })
  },
  writable: true
})

// 模拟sessionStorage
Object.defineProperty(window, 'sessionStorage', {
  value: {
    store: {},
    getItem: jest.fn((key) => window.sessionStorage.store[key] || null),
    setItem: jest.fn((key, value) => {
      window.sessionStorage.store[key] = String(value)
    }),
    removeItem: jest.fn((key) => {
      delete window.sessionStorage.store[key]
    }),
    clear: jest.fn(() => {
      window.sessionStorage.store = {}
    })
  },
  writable: true
})

// 模拟location对象
Object.defineProperty(window, 'location', {
  value: {
    href: 'http://localhost',
    pathname: '/',
    search: '',
    hash: '',
    reload: jest.fn()
  },
  writable: true
})

// 模拟navigator对象
Object.defineProperty(window, 'navigator', {
  value: {
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  },
  writable: true
})

// 模拟performance对象
Object.defineProperty(window, 'performance', {
  value: {
    now: jest.fn(() => Date.now()),
    timing: {
      navigationStart: Date.now() - 1000,
      domainLookupStart: Date.now() - 900,
      domainLookupEnd: Date.now() - 800,
      connectStart: Date.now() - 700,
      connectEnd: Date.now() - 600,
      requestStart: Date.now() - 500,
      responseStart: Date.now() - 400,
      responseEnd: Date.now() - 300,
      domLoading: Date.now() - 200,
      domComplete: Date.now() - 100,
      loadEventEnd: Date.now()
    }
  },
  writable: true
})

// 模拟IntersectionObserver（用于虚拟滚动）
global.IntersectionObserver = jest.fn((callback) => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
  callback
}))

// 模拟ResizeObserver
global.ResizeObserver = jest.fn((callback) => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
  callback
}))

// 模拟Element.scrollIntoView
Element.prototype.scrollIntoView = jest.fn()

// 模拟Element.getBoundingClientRect
Element.prototype.getBoundingClientRect = jest.fn(() => ({
  top: 0,
  left: 0,
  bottom: 0,
  right: 0,
  width: 0,
  height: 0
}))

// 模拟OpenLayers（如果使用地图组件）
jest.mock('ol/Map', () => {
  return jest.fn(() => ({
    addLayer: jest.fn(),
    removeLayer: jest.fn(),
    getView: jest.fn(() => ({
      setCenter: jest.fn(),
      setZoom: jest.fn()
    })),
    on: jest.fn(),
    un: jest.fn()
  }))
})

// 模拟axios
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    get: jest.fn(() => Promise.resolve({ data: {} })),
    post: jest.fn(() => Promise.resolve({ data: {} })),
    put: jest.fn(() => Promise.resolve({ data: {} })),
    delete: jest.fn(() => Promise.resolve({ data: {} })),
    interceptors: {
      request: {
        use: jest.fn()
      },
      response: {
        use: jest.fn()
      }
    }
  })),
  get: jest.fn(() => Promise.resolve({ data: {} })),
  post: jest.fn(() => Promise.resolve({ data: {} }))
}))

// 全局测试工具函数
global.createMockStore = (modules = {}) => {
  return new Vuex.Store({
    modules: {
      user: {
        namespaced: true,
        state: {
          userId: 'test_user_123',
          name: 'Test User',
          roles: ['student']
        },
        getters: {
          userId: state => state.userId
        }
      },
      convection: {
        namespaced: true,
        state: {
          currentExam: null,
          currentQuestion: null,
          examAnswer: {
            answerId: null,
            stationAnswer: {},
            areaAnswer: {},
            forecastReasoning: '',
            stationProgress: 0,
            areaProgress: 0,
            overallProgress: 0
          },
          ui: {
            examLoading: false,
            saveLoading: false,
            autoSaving: false
          }
        },
        mutations: {
          SET_CURRENT_EXAM: jest.fn(),
          UPDATE_STATION_ANSWER: jest.fn(),
          UPDATE_FORECAST_REASONING: jest.fn(),
          UPDATE_PROGRESS: jest.fn()
        },
        actions: {
          saveAnswer: jest.fn(),
          autoSave: jest.fn(),
          calculateProgress: jest.fn()
        }
      },
      ...modules
    }
  })
}

// 全局测试数据工厂
global.createMockExamData = () => ({
  id: 'exam_123',
  title: '强对流天气临近预报考试',
  description: '测试强对流天气预报能力',
  startTime: new Date().toISOString(),
  endTime: new Date(Date.now() + 3600000).toISOString(),
  duration: 60,
  status: 1
})

global.createMockQuestionData = () => ({
  id: 'qu_456',
  title: '强对流天气预报试题',
  content: '请根据MICAPS资料进行强对流天气预报',
  quType: 7,
  level: 2,
  convectionStandardReasoning: '标准预报依据...'
})

global.createMockStationData = () => [
  { code: '54511', name: '北京', lon: 116.28, lat: 39.93 },
  { code: '54517', name: '密云', lon: 116.83, lat: 40.37 },
  { code: '54594', name: '天津', lon: 117.17, lat: 39.08 }
]

// 清理函数
afterEach(() => {
  jest.clearAllMocks()
  // 清理localStorage
  window.localStorage.clear()
  window.sessionStorage.clear()
}) 