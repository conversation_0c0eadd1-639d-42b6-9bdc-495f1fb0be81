import { shallowMount, createLocalVue } from '@vue/test-utils'
import Vuex from 'vuex'
import ElementUI from 'element-ui'
import StationTable from '@/components/convection/StationTable.vue'
import ReasoningInput from '@/components/convection/ReasoningInput.vue'

const localVue = createLocalVue()
localVue.use(Vuex)
localVue.use(ElementUI)

describe('StationTable.vue', () => {
  let wrapper
  let store
  let mockMutations
  let mockActions

  const mockStations = [
    { code: '54511', name: '北京', lon: 116.28, lat: 39.93 },
    { code: '54517', name: '密云', lon: 116.83, lat: 40.37 },
    { code: '54594', name: '天津', lon: 117.17, lat: 39.08 }
  ]

  const mockExamAnswer = {
    stationAnswer: {
      '54511': {
        rainfall: 'level1',
        wind: 'moderate'
      }
    },
    forecastReasoning: '根据MICAPS资料分析，北京地区预计出现短时强降水...',
    stationProgress: 45
  }

  beforeEach(() => {
    mockMutations = {
      UPDATE_STATION_ANSWER: jest.fn(),
      UPDATE_FORECAST_REASONING: jest.fn(),
      UPDATE_PROGRESS: jest.fn()
    }

    mockActions = {
      autoSave: jest.fn(),
      calculateProgress: jest.fn()
    }

    store = new Vuex.Store({
      modules: {
        convection: {
          namespaced: true,
          state: {
            examAnswer: mockExamAnswer,
            ui: {
              autoSaving: false
            }
          },
          mutations: mockMutations,
          actions: mockActions
        }
      }
    })

    wrapper = shallowMount(StationTable, {
      localVue,
      store,
      propsData: {
        stations: mockStations,
        examId: 'exam_123',
        questionId: 'qu_456'
      },
      stubs: {
        ReasoningInput: true
      }
    })
  })

  afterEach(() => {
    wrapper.destroy()
    jest.clearAllMocks()
  })

  describe('组件初始化', () => {
    it('应该正确渲染基本结构', () => {
      expect(wrapper.find('.convection-station-table').exists()).toBe(true)
      expect(wrapper.find('.table-header').exists()).toBe(true)
      expect(wrapper.find('.station-prediction-table').exists()).toBe(true)
      expect(wrapper.find('.reasoning-section').exists()).toBe(true)
    })

    it('应该显示正确的表格头部信息', () => {
      const headerTitle = wrapper.find('.table-header h4')
      expect(headerTitle.text()).toBe('第一部分：站点预报表格')
      
      const scoreInfo = wrapper.find('.score-info')
      expect(scoreInfo.text()).toBe('(68分)')
    })

    it('应该渲染所有站点列', () => {
      // 注意：由于使用了 el-table，我们检查传入的数据是否正确
      expect(wrapper.vm.stations).toEqual(mockStations)
      expect(wrapper.vm.stations).toHaveLength(3)
    })

    it('应该初始化表格行数据', () => {
      const tableData = wrapper.vm.tableData
      expect(tableData).toHaveLength(3)
      expect(tableData[0].element).toBe('rainfall')
      expect(tableData[1].element).toBe('wind')
      expect(tableData[2].element).toBe('hail')
    })
  })

  describe('计算属性', () => {
    it('应该正确获取站点答案数据', () => {
      expect(wrapper.vm.stationAnswers).toEqual(mockExamAnswer.stationAnswer)
    })

    it('应该正确获取和设置预报依据', () => {
      expect(wrapper.vm.forecastReasoning).toBe(mockExamAnswer.forecastReasoning)
      
      wrapper.vm.forecastReasoning = '新的预报依据'
      expect(mockMutations.UPDATE_FORECAST_REASONING).toHaveBeenCalledWith(
        expect.any(Object),
        '新的预报依据'
      )
    })

    it('应该正确计算进度和颜色', () => {
      expect(wrapper.vm.stationProgress).toBe(45)
      expect(wrapper.vm.progressColor).toBe('#e6a23c') // 黄色，45%在30-70之间
    })
  })

  describe('天气选项交互', () => {
    it('应该正确判断选项是否被选中', () => {
      const isSelected = wrapper.vm.isSelected('54511', 'rainfall', 'level1')
      expect(isSelected).toBe(true)

      const isNotSelected = wrapper.vm.isSelected('54511', 'rainfall', 'level2')
      expect(isNotSelected).toBe(false)
    })

    it('应该正确生成选项样式类', () => {
      const selectedClass = wrapper.vm.getOptionClass('54511', 'rainfall', 'level1')
      expect(selectedClass.selected).toBe(true)
      expect(selectedClass.unselected).toBe(false)

      const unselectedClass = wrapper.vm.getOptionClass('54511', 'rainfall', 'level2')
      expect(unselectedClass.selected).toBe(false)
      expect(unselectedClass.unselected).toBe(true)
    })

    it('应该正确生成选项图标', () => {
      const selectedIcon = wrapper.vm.getOptionIcon('54511', 'rainfall', 'level1')
      expect(selectedIcon).toBe('el-icon-check')

      const unselectedIcon = wrapper.vm.getOptionIcon('54511', 'rainfall', 'level2')
      expect(unselectedIcon).toBe('el-icon-close')
    })
  })

  describe('天气选项选择功能', () => {
    it('应该能够选择新的天气选项', () => {
      wrapper.vm.selectWeatherOption('54517', 'rainfall', 'level2')

      expect(mockMutations.UPDATE_STATION_ANSWER).toHaveBeenCalledWith(
        expect.any(Object),
        {
          stationCode: '54517',
          weatherType: 'rainfall',
          value: 'level2'
        }
      )
    })

    it('应该能够取消已选择的选项', () => {
      // 点击已选择的选项应该取消选择
      wrapper.vm.selectWeatherOption('54511', 'rainfall', 'level1')

      expect(mockMutations.UPDATE_STATION_ANSWER).toHaveBeenCalledWith(
        expect.any(Object),
        {
          stationCode: '54511',
          weatherType: 'rainfall',
          value: null
        }
      )
    })

    it('在只读模式下应该禁用选择功能', async () => {
      await wrapper.setProps({ readonly: true })

      wrapper.vm.selectWeatherOption('54517', 'rainfall', 'level2')

      // 只读模式下不应该触发mutation
      expect(mockMutations.UPDATE_STATION_ANSWER).not.toHaveBeenCalled()
    })

    it('选择选项后应该计算进度并安排自动保存', () => {
      jest.useFakeTimers()
      
      wrapper.vm.selectWeatherOption('54517', 'rainfall', 'level2')

      // 应该调用进度计算
      expect(wrapper.vm.calculateStationProgress).toBeDefined()
      
      // 应该安排自动保存
      expect(wrapper.vm.scheduleAutoSave).toBeDefined()
      
      jest.useRealTimers()
    })
  })

  describe('进度计算', () => {
    it('应该正确计算站点进度', () => {
      const progress = wrapper.vm.getStationProgress('54511')
      // 54511站点有rainfall和wind两个选项，共3个选项，完成度为2/3 = 67%
      expect(progress).toBe(67)
    })

    it('应该正确计算总体站点进度', () => {
      wrapper.vm.calculateStationProgress()

      // 应该调用UPDATE_PROGRESS mutation
      expect(mockMutations.UPDATE_PROGRESS).toHaveBeenCalledWith(
        expect.any(Object),
        expect.objectContaining({
          type: 'station',
          progress: expect.any(Number)
        })
      )
    })
  })

  describe('预报依据处理', () => {
    it('应该正确处理预报依据变化', () => {
      const newReasoning = '更新的预报依据内容'
      wrapper.vm.handleReasoningChange(newReasoning)

      expect(mockMutations.UPDATE_FORECAST_REASONING).toHaveBeenCalledWith(
        expect.any(Object),
        newReasoning
      )
    })

    it('应该正确处理字数变化', () => {
      const wordCount = 150
      wrapper.vm.handleWordCountChange(wordCount)

      // 字数变化应该触发进度重新计算
      // 这个测试验证方法被调用且没有报错
      expect(() => wrapper.vm.handleWordCountChange(wordCount)).not.toThrow()
    })
  })

  describe('自动保存功能', () => {
    beforeEach(() => {
      jest.useFakeTimers()
    })

    afterEach(() => {
      jest.useRealTimers()
    })

    it('应该在规定时间后触发自动保存', () => {
      wrapper.vm.scheduleAutoSave()

      // 快进2秒
      jest.advanceTimersByTime(2000)

      expect(mockActions.autoSave).toHaveBeenCalled()
    })

    it('应该能够取消之前的自动保存定时器', () => {
      wrapper.vm.scheduleAutoSave()
      wrapper.vm.scheduleAutoSave() // 第二次调用应该取消第一次的定时器

      jest.advanceTimersByTime(2000)

      // 应该只调用一次
      expect(mockActions.autoSave).toHaveBeenCalledTimes(1)
    })

    it('组件销毁时应该清除定时器', () => {
      wrapper.vm.scheduleAutoSave()
      wrapper.destroy()

      jest.advanceTimersByTime(2000)

      // 组件销毁后不应该触发自动保存
      expect(mockActions.autoSave).not.toHaveBeenCalled()
    })
  })

  describe('响应式更新', () => {
    it('应该在预报依据变化时重新计算进度', async () => {
      const spy = jest.spyOn(wrapper.vm, 'calculateStationProgress')

      await wrapper.setData({
        forecastReasoning: '新的预报依据内容，包含更多详细信息...'
      })

      // 等待nextTick确保watcher触发
      await wrapper.vm.$nextTick()

      expect(spy).toHaveBeenCalled()
    })
  })

  describe('错误处理', () => {
    it('应该处理无效的站点数据', () => {
      const invalidStations = [
        { code: '', name: '', lon: null, lat: null }
      ]

      expect(() => {
        wrapper.setProps({ stations: invalidStations })
      }).not.toThrow()
    })

    it('应该处理空的答案数据', async () => {
      store.replaceState({
        convection: {
          examAnswer: {
            stationAnswer: {},
            forecastReasoning: '',
            stationProgress: 0
          }
        }
      })

      await wrapper.vm.$nextTick()

      expect(wrapper.vm.stationAnswers).toEqual({})
      expect(wrapper.vm.forecastReasoning).toBe('')
    })
  })

  describe('可访问性', () => {
    it('应该具有适当的ARIA属性', () => {
      // 检查表格是否有适当的可访问性属性
      const table = wrapper.find('.station-prediction-table')
      expect(table.exists()).toBe(true)
      
      // 检查进度条是否有标签
      const progressBars = wrapper.findAll('.el-progress')
      expect(progressBars.length).toBeGreaterThan(0)
    })

    it('应该支持键盘导航', () => {
      // 这个测试可以扩展以检查键盘事件处理
      expect(wrapper.find('.convection-station-table').exists()).toBe(true)
    })
  })

  describe('性能优化', () => {
    it('应该使用防抖进行自动保存', () => {
      jest.useFakeTimers()
      
      // 快速多次触发
      wrapper.vm.scheduleAutoSave()
      wrapper.vm.scheduleAutoSave()
      wrapper.vm.scheduleAutoSave()

      jest.advanceTimersByTime(2000)

      // 应该只执行一次自动保存
      expect(mockActions.autoSave).toHaveBeenCalledTimes(1)
      
      jest.useRealTimers()
    })
  })
}) 