-- =====================================================
-- 天气预报历史个例考试功能 - 数据库更新脚本
-- 请在您的数据库中手动执行以下SQL语句
-- =====================================================

-- 1. 创建天气预报表格配置表
CREATE TABLE IF NOT EXISTS `el_weather_table_config` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `name` varchar(255) NOT NULL COMMENT '配置名称',
  `template_type` varchar(64) NOT NULL COMMENT '模板类型',
  `title` varchar(255) DEFAULT NULL COMMENT '表格标题',
  `description` varchar(500) DEFAULT NULL COMMENT '表格描述',
  `table_schema` json DEFAULT NULL COMMENT '表格结构配置JSON',
  `validation_rules` json DEFAULT NULL COMMENT '数据验证规则JSON',
  `scoring_config` json DEFAULT NULL COMMENT '评分配置JSON',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_template_type` (`template_type`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='天气预报表格配置表';

-- 2. 创建天气预报答案表
CREATE TABLE IF NOT EXISTS `el_weather_answer` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `paper_qu_id` varchar(64) NOT NULL COMMENT '试卷题目ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `table_config_id` varchar(64) NOT NULL COMMENT '表格配置ID',
  `cell_data` json DEFAULT NULL COMMENT '表格单元格数据JSON',
  `answer_status` tinyint(1) DEFAULT '0' COMMENT '答题状态',
  `validation_result` json DEFAULT NULL COMMENT '数据验证结果JSON',
  `total_score` decimal(10,2) DEFAULT '0.00' COMMENT '总得分',
  `score_details` json DEFAULT NULL COMMENT '得分详情JSON',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_paper_qu_user` (`paper_qu_id`,`user_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_table_config_id` (`table_config_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='天气预报答案表';

-- 3. 修改题目表，添加天气预报表格配置ID字段
-- 注意：如果字段已存在，此语句会报错，可以忽略
ALTER TABLE `el_qu` 
ADD COLUMN `weather_config_id` varchar(64) DEFAULT NULL COMMENT '天气预报表格配置ID' AFTER `analysis`;

-- 4. 插入默认的天气预报表格配置
INSERT INTO `el_weather_table_config` 
(`id`, `name`, `template_type`, `title`, `description`, `table_schema`, `validation_rules`, `scoring_config`, `is_active`, `create_time`, `update_time`) 
VALUES (
  '1000000000000000001',
  '24小时站点灾害性天气预报',
  'STATION_FORECAST',
  '未来24小时内指定站点的灾害性天气和气象要素预报',
  '请根据给定的站点信息，预报未来24小时内的最大风力、风向、气温、降水和灾害性天气类型',
  '{
    "columns": [
      {"field": "forecastTime", "title": "预报起报日期及时间", "width": 180, "editable": false},
      {"field": "stationInfo", "title": "站名站号", "width": 120, "editable": false},
      {"field": "maxWindForce", "title": "08—08最大风力(级)", "width": 150, "editable": true, "type": "select", "options": ["0", "1", "2", "3-4", "5-6", "7-8", "9-10", "11-12"]},
      {"field": "windDirection", "title": "08—08最大风力时的风向", "width": 180, "editable": true, "type": "select", "options": ["北", "东北", "东", "东南", "南", "西南", "西", "西北", "无风"]},
      {"field": "minTemp", "title": "最低气温℃", "width": 120, "editable": true, "type": "number", "min": -50, "max": 50},
      {"field": "maxTemp", "title": "最高气温℃", "width": 120, "editable": true, "type": "number", "min": -50, "max": 50},
      {"field": "precipitation", "title": "08—08降水(雨、雪)量级", "width": 180, "editable": true, "type": "select", "options": ["无", "小雨", "中雨", "大雨", "暴雨", "大暴雨", "特大暴雨", "小雪", "中雪", "大雪", "暴雪"]},
      {"field": "disasterWeather", "title": "灾害性天气类型", "width": 200, "editable": true, "type": "multiSelect", "options": ["无", "雷暴", "冰雹", "大风", "龙卷风", "暴雨", "暴雪", "沙尘暴", "雾", "霾"]}
    ],
    "rows": 6,
    "timeInterval": 4,
    "title": "未来24小时内指定站点的灾害性天气和气象要素预报",
    "description": "请根据给定的站点信息，预报未来24小时内的最大风力、风向、气温、降水和灾害性天气类型"
  }',
  '{
    "rules": [
      {"field": "maxWindForce", "required": true, "message": "最大风力不能为空"},
      {"field": "windDirection", "required": true, "message": "风向不能为空"},
      {"field": "minTemp", "required": true, "message": "最低气温不能为空"},
      {"field": "maxTemp", "required": true, "message": "最高气温不能为空"},
      {"field": "precipitation", "required": true, "message": "降水量级不能为空"},
      {"field": "disasterWeather", "required": true, "message": "灾害性天气类型不能为空"}
    ]
  }',
  '{
    "weightConfig": {
      "exactMatch": 0.6,
      "dataConsistency": 0.2,
      "trendAnalysis": 0.1,
      "professionalJudgment": 0.1
    },
    "fieldConfigs": [
      {"field": "maxWindForce", "weight": 0.15, "scoreType": "exact"},
      {"field": "windDirection", "weight": 0.15, "scoreType": "exact"},
      {"field": "minTemp", "weight": 0.15, "scoreType": "range", "tolerance": 2},
      {"field": "maxTemp", "weight": 0.15, "scoreType": "range", "tolerance": 2},
      {"field": "precipitation", "weight": 0.2, "scoreType": "exact"},
      {"field": "disasterWeather", "weight": 0.2, "scoreType": "partial"}
    ],
    "totalScore": 100
  }',
  1,
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE `update_time` = NOW();

-- 5. 创建天气预报题库（如果不存在）
INSERT IGNORE INTO `el_repo` (`id`, `title`, `remark`, `create_time`, `update_time`)
VALUES (
  'weather_forecast_repo_001',
  '天气预报历史个例',
  '天气预报历史个例考试题库，包含24小时站点灾害性天气预报等题型',
  NOW(),
  NOW()
);

-- 6. 创建示例天气预报题目
INSERT INTO `el_qu` (
  `id`, `qu_type`, `level`, `content`, `weather_config_id`, `remark`, `analysis`, `create_time`, `update_time`
) VALUES (
  'weather_qu_sample_001',
  6,
  1,
  '根据以下气象资料，请预报2035年9月9日08时未来24小时内指定站点的灾害性天气和气象要素：

【背景资料】
时间：2035年9月9日08时
地点：华东地区某观测站

【当前天气实况】
- 气温：26℃
- 风向：东南风
- 风力：3-4级
- 天气：多云
- 气压：1012hPa
- 相对湿度：75%

【预报要点】
1. 受台风外围影响，风力将逐渐加大
2. 午后有雷阵雨天气过程
3. 气温变化不大
4. 需要关注强对流天气的发生可能

请根据上述资料，填写下表中各时段的预报要素：',
  '1000000000000000001',
  '天气预报历史个例示例题目',
  '【答题要点】
1. 风力预报：考虑台风外围影响，风力应呈递增趋势
2. 风向预报：受台风影响，风向可能从东南转为偏东或东北
3. 气温预报：受云量和降水影响，最高气温略有下降
4. 降水预报：午后雷阵雨，需要准确判断降水时段和强度
5. 灾害性天气：重点关注雷暴、大风等强对流天气

【评分标准】
- 风力风向预报准确性：30分
- 气温预报合理性：30分  
- 降水预报时效性：20分
- 灾害性天气识别：20分',
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE `update_time` = NOW();

-- 7. 将示例题目加入题库
INSERT IGNORE INTO `el_qu_repo` (`id`, `qu_id`, `repo_id`, `qu_type`, `sort`)
VALUES (
  'weather_qu_repo_rel_001',
  'weather_qu_sample_001',
  'weather_forecast_repo_001',
  6,
  1
);

-- 执行完成后，您可以：
-- 1. 在题目管理中看到新的"天气预报表格题"类型
-- 2. 在题库中找到"天气预报历史个例"题库
-- 3. 创建包含天气预报表格题的考试
-- 4. 在考试中体验天气预报表格答题功能
