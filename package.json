{"name": "yf-exam-frontend", "version": "2.1.0", "description": "YF考试系统前端 - 包含强对流天气模块", "author": "YF Development Team", "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "build:prod": "vue-cli-service build --mode production", "build:stage": "vue-cli-service build --mode staging", "build:analyze": "vue-cli-service build --analyze", "build:convection": "vue-cli-service build --mode production --modern --report", "preview": "node build/index.js", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --ext .js,.vue src", "lint:fix": "eslint --ext .js,.vue src --fix", "test:unit": "vue-cli-service test:unit", "test:unit:coverage": "vue-cli-service test:unit --coverage", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "test:convection": "vue-cli-service test:unit --testMatch='**/convection/**/*.spec.js'", "precommit": "lint-staged"}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "dependencies": {"axios": "0.18.1", "clipboard": "2.0.4", "codemirror": "5.45.0", "driver.js": "0.9.5", "dropzone": "5.5.1", "echarts": "4.2.1", "element-ui": "2.15.6", "file-saver": "2.0.1", "fuse.js": "3.4.4", "js-cookie": "2.2.0", "jsonlint": "1.6.3", "jszip": "3.2.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "screenfull": "4.2.0", "script-loader": "0.7.2", "sortablejs": "1.8.4", "tui-editor": "1.3.3", "vue": "2.6.10", "vue-count-to": "1.0.13", "vue-router": "3.0.2", "vue-splitpane": "1.0.4", "vuedraggable": "2.20.0", "vuex": "3.1.0", "xlsx": "0.14.1", "ol": "^6.14.1", "proj4": "^2.8.0", "geojson": "^0.5.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "24.8.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "chokidar": "2.1.5", "connect": "3.6.6", "cypress": "^9.7.0", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "jest-serializer-vue": "^2.0.2", "jest-sonar-reporter": "^2.0.0", "lint-staged": "8.1.5", "mockjs": "1.0.1-beta3", "plop": "2.3.0", "runjs": "4.3.2", "sass": "1.26.2", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "vue-jest": "^3.0.7", "vue-template-compiler": "2.6.10", "compression-webpack-plugin": "^6.1.1", "webpack-bundle-analyzer": "^4.5.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "engines": {"node": ">=12.0.0", "npm": ">= 6.0.0"}, "keywords": ["vue", "admin", "dashboard", "exam-system", "convection-weather", "meteorology"], "repository": {"type": "git", "url": "https://github.com/your-org/yf-exam-frontend.git"}, "license": "MIT"}