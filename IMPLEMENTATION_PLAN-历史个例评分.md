# 历史个例评分系统 - 详细实施计划

## 项目概述

基于`README-历史个例评分.md`和`DEV_PLAN-历史个例评分.md`的规范，本计划专注于后端核心评分功能的完整实现。

## 技术架构规范

### 后端技术栈
- **框架**：Spring Boot + MyBatis Plus
- **数据库**：MySQL 8.0+
- **语言**：Java 8+
- **JSON处理**：Jackson
- **精度处理**：BigDecimal

### 核心模块架构
```
src/main/java/com/yf/exam/modules/weather/
├── scoring/                    # 评分引擎模块（新增）
│   ├── algorithm/             # 评分算法实现
│   ├── service/               # 评分业务服务
│   ├── controller/            # 评分API控制器
│   └── config/                # 评分配置管理
├── entity/                    # 实体类（扩展）
├── mapper/                    # 数据访问层（扩展）
└── service/                   # 业务服务层（扩展）
```

## 数据库设计规范

### 核心表结构
1. **el_weather_scoring_config** - 评分配置表
2. **el_weather_scoring_result** - 评分结果表
3. 扩展现有的 **el_weather_history_exam_answer** 表
4. 关联现有的 **el_qu** 表

### 评分算法规范

#### 核心评分方法
需要实现6个核心评分方法：
- `calculateWindForceScore()` - 风力评分（1.0分，特殊规则处理）
- `calculateWindDirectionScore()` - 风向评分（1.0分，角度范围匹配）
- `calculateTemperatureScore()` - 温度评分（各2.0分，±2℃容差）
- `calculatePrecipitationScore()` - 降水评分（2.0分，矩阵查表）
- `calculateDisasterWeatherScore()` - 灾害天气评分（2.0分，比例计算）
- `calculateStationScore()` - 站点综合评分（10.0分总分）

#### 评分精度规范
- 所有分数计算使用 `BigDecimal` 类型
- 保留2位小数精度
- 使用 `RoundingMode.HALF_UP` 舍入模式

## 实施检查清单

### Phase 1: 数据库和基础架构 (项目1-2)
1. [创建数据库表结构和索引, review:true]
2. [实现评分配置管理Service层, review:true]

### Phase 2: 核心评分算法实现 (项目3-8)
3. [实现核心评分算法 - 风力评分方法, review:true]
4. [实现核心评分算法 - 风向评分方法, review:true]
5. [实现核心评分算法 - 温度评分方法, review:true]
6. [实现核心评分算法 - 降水评分方法, review:true]
7. [实现核心评分算法 - 灾害天气评分方法, review:true]
8. [实现站点综合评分算法, review:true]

### Phase 3: 业务服务层实现 (项目9-13)
9. [实现WeatherScoringService核心业务逻辑, review:true]
10. [实现批量评分处理服务, review:true]
11. [实现评分结果存储和查询, review:true]
12. [实现评分报告生成服务, review:true]
13. [实现统计分析服务, review:true]

### Phase 4: 控制器层实现 (项目14-15)
14. [开发评分相关Controller接口, review:true]
15. [开发配置管理Controller接口, review:true]

### Phase 5: 测试和优化 (项目16-18)
16. [集成测试 - 单个评分功能, review:true]
17. [集成测试 - 批量评分功能, review:true]
18. [性能测试和优化, review:true]

## 详细实施规范

### Phase 1: 数据库和基础架构

#### 项目1: 创建数据库表结构和索引
**目标文件**：
- `src/main/resources/db/migration/V20241221_2__Create_Weather_Scoring_Tables.sql`

**表结构规范**：
```sql
-- 评分配置表
CREATE TABLE `el_weather_scoring_config` (
  `id` varchar(64) NOT NULL COMMENT 'ID',
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `config_version` varchar(20) DEFAULT '1.0' COMMENT '配置版本',
  `total_score` int(11) NOT NULL DEFAULT 10 COMMENT '总分',
  `station_weight` int(11) NOT NULL DEFAULT 10 COMMENT '单站权重',
  `element_weights` json NOT NULL COMMENT '要素权重配置',
  `tolerance_config` json DEFAULT NULL COMMENT '容差配置',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_config_name` (`config_name`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='天气预报评分配置表';

-- 评分结果表
CREATE TABLE `el_weather_scoring_result` (
  `id` varchar(64) NOT NULL COMMENT 'ID',
  `answer_id` varchar(64) NOT NULL COMMENT '答案ID',
  `exam_id` varchar(64) NOT NULL COMMENT '考试ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `question_id` varchar(64) NOT NULL COMMENT '题目ID',
  `total_score` decimal(5,2) NOT NULL COMMENT '总得分',
  `max_score` decimal(5,2) NOT NULL COMMENT '满分',
  `score_percentage` decimal(5,2) NOT NULL COMMENT '得分率',
  `station_scores` json NOT NULL COMMENT '分站得分详情',
  `element_scores` json NOT NULL COMMENT '分要素得分详情',
  `error_analysis` json DEFAULT NULL COMMENT '错误分析',
  `scoring_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '评分时间',
  `config_version` varchar(20) DEFAULT NULL COMMENT '使用的配置版本',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_answer_id` (`answer_id`),
  KEY `idx_exam_user` (`exam_id`, `user_id`),
  KEY `idx_score_range` (`total_score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='天气预报评分结果表';
```

**实体类文件**：
- `src/main/java/com/yf/exam/modules/weather/entity/WeatherScoringConfig.java`
- `src/main/java/com/yf/exam/modules/weather/entity/WeatherScoringResult.java`

**Mapper接口文件**：
- `src/main/java/com/yf/exam/modules/weather/mapper/WeatherScoringConfigMapper.java`
- `src/main/java/com/yf/exam/modules/weather/mapper/WeatherScoringResultMapper.java`

**Mapper XML文件**：
- `src/main/resources/mapper/weather/WeatherScoringConfigMapper.xml`
- `src/main/resources/mapper/weather/WeatherScoringResultMapper.xml`

#### 项目2: 实现评分配置管理Service层
**目标文件**：
- `src/main/java/com/yf/exam/modules/weather/scoring/service/ScoringConfigService.java`
- `src/main/java/com/yf/exam/modules/weather/scoring/service/impl/ScoringConfigServiceImpl.java`

**核心方法**：
```java
public interface ScoringConfigService extends IService<WeatherScoringConfig> {
    WeatherScoringConfig getActiveConfig();
    WeatherScoringConfig getConfigByVersion(String version);
    boolean saveConfig(WeatherScoringConfig config);
    boolean activateConfig(String configId);
    List<WeatherScoringConfig> getConfigHistory();
}
```

### Phase 2: 核心评分算法实现

#### 项目3: 实现核心评分算法 - 风力评分方法
**目标文件**：
- `src/main/java/com/yf/exam/modules/weather/scoring/algorithm/WindForceScoring.java`

**核心方法签名**：
```java
public class WindForceScoring {
    public BigDecimal calculateWindForceScore(String studentLevel, String standardLevel);
    private int parseWindForceLevel(String windForceStr);
}
```

**特殊规则实现**：
- 静风(0级)：标准答案为静风或1级得1.0分
- 1级：标准答案为0级、1级、2级得1.0分
- 2-11级：完全匹配1.0分，相邻等级0.8分
- 12级：标准答案为12级或大于12级得1.0分，11级得0.8分

#### 项目4: 实现核心评分算法 - 风向评分方法
**目标文件**：
- `src/main/java/com/yf/exam/modules/weather/scoring/algorithm/WindDirectionScoring.java`

**核心方法签名**：
```java
public class WindDirectionScoring {
    public BigDecimal calculateWindDirectionScore(String studentDirection, String standardAngleRange);
    private double[] parseAngleRange(String angleRangeStr);
    private boolean isInAngleRange(double startAngle, double endAngle, double targetStart, double targetEnd);
}
```

**八方位与十六方位角度映射**：
- 北：337.5°～22.5°
- 东北：22.5°～67.5°
- 东：67.5°～112.5°（分为两个范围）
- 其他方向按规范实现

#### 项目5: 实现核心评分算法 - 温度评分方法
**目标文件**：
- `src/main/java/com/yf/exam/modules/weather/scoring/algorithm/TemperatureScoring.java`

**核心方法签名**：
```java
public class TemperatureScoring {
    public BigDecimal calculateTemperatureScore(Integer studentTemp, Integer standardTemp, double maxScore);
}
```

**容差规则**：
- |预报值-实况值|≤2℃：得满分
- |预报值-实况值|>2℃：得0分

#### 项目6: 实现核心评分算法 - 降水评分方法
**目标文件**：
- `src/main/java/com/yf/exam/modules/weather/scoring/algorithm/PrecipitationScoring.java`

**核心方法签名**：
```java
public class PrecipitationScoring {
    public BigDecimal calculatePrecipitationScore(String studentType, String standardType);
    private BigDecimal getPrecipitationScoreFromMatrix(String student, String standard);
    private Map<String, Map<String, Double>> createPrecipitationScoreMatrix();
}
```

**特殊规则**：
- 雨夹雪与小雨/小雪相互2.0分匹配
- 雨夹雪与中雨/中雪相互1.2分匹配
- 雨类与雪类之间（除雨夹雪）一律0分

#### 项目7: 实现核心评分算法 - 灾害天气评分方法
**目标文件**：
- `src/main/java/com/yf/exam/modules/weather/scoring/algorithm/DisasterWeatherScoring.java`

**核心方法签名**：
```java
public class DisasterWeatherScoring {
    public BigDecimal calculateDisasterWeatherScore(List<String> studentForecast, List<String> standardActual);
    private boolean isValidDisasterWeatherType(String weatherType);
    private List<String> filterValidDisasterWeather(List<String> weatherList);
}
```

**评分规则**：
- 得分 = (匹配数量 / 实况总数量) × 2.0
- "无"的特殊处理
- 不扣分原则

#### 项目8: 实现站点综合评分算法
**目标文件**：
- `src/main/java/com/yf/exam/modules/weather/scoring/algorithm/WeatherScoringAlgorithm.java`

**核心方法签名**：
```java
public class WeatherScoringAlgorithm {
    public BigDecimal calculateStationScore(StationAnswer student, StationAnswer standard);
    public Map<String, Object> calculateExamScore(WeatherAnswerDTO answerDTO, WeatherAnswerDTO standardAnswer);
}
```

### Phase 3: 业务服务层实现

#### 项目9: 实现WeatherScoringService核心业务逻辑
**目标文件**：
- `src/main/java/com/yf/exam/modules/weather/scoring/service/WeatherScoringService.java`
- `src/main/java/com/yf/exam/modules/weather/scoring/service/impl/WeatherScoringServiceImpl.java`

**核心方法**：
```java
public interface WeatherScoringService {
    Map<String, Object> calculateScore(String answerId);
    Map<String, Object> calculateScore(WeatherAnswerDTO answerDTO, WeatherAnswerDTO standardAnswer);
    boolean saveScoreResult(WeatherScoringResult result);
    WeatherScoringResult getScoreResult(String answerId);
}
```

#### 项目10: 实现批量评分处理服务
**目标文件**：
- `src/main/java/com/yf/exam/modules/weather/scoring/service/BatchScoringService.java`
- `src/main/java/com/yf/exam/modules/weather/scoring/service/impl/BatchScoringServiceImpl.java`

**核心方法**：
```java
public interface BatchScoringService {
    String submitBatchScoringTask(List<String> answerIds);
    BatchScoringResult getBatchScoringResult(String taskId);
    List<Map<String, Object>> batchCalculateScore(List<String> answerIds);
}
```

#### 项目11-13: 实现其他业务服务
**目标文件**：
- `src/main/java/com/yf/exam/modules/weather/scoring/service/ScoringReportService.java`
- `src/main/java/com/yf/exam/modules/weather/scoring/service/StatisticsService.java`

### Phase 4: 控制器层实现

#### 项目14: 开发评分相关Controller接口
**目标文件**：
- `src/main/java/com/yf/exam/modules/weather/scoring/controller/WeatherScoringController.java`

**API接口规范**：
```java
@RestController
@RequestMapping("/exam/api/weather/scoring")
public class WeatherScoringController {
    
    @PostMapping("/calculate")
    public R<Map<String, Object>> calculateScore(@RequestBody ScoringRequest request);
    
    @PostMapping("/batch")
    public R<String> batchCalculateScore(@RequestBody BatchScoringRequest request);
    
    @GetMapping("/result/{id}")
    public R<WeatherScoringResult> getScoreResult(@PathVariable String id);
    
    @PostMapping("/report")
    public R<Map<String, Object>> generateReport(@RequestBody ReportRequest request);
    
    @GetMapping("/statistics")
    public R<Map<String, Object>> getStatistics(@RequestParam Map<String, Object> params);
}
```

#### 项目15: 开发配置管理Controller接口
**目标文件**：
- `src/main/java/com/yf/exam/modules/weather/scoring/controller/ScoringConfigController.java`

### Phase 5: 测试和优化

#### 项目16-17: 集成测试
**目标文件**：
- `src/test/java/com/yf/exam/modules/weather/scoring/WeatherScoringServiceTest.java`
- `src/test/java/com/yf/exam/modules/weather/scoring/BatchScoringServiceTest.java`

**测试用例覆盖**：
- 所有评分算法的边界值测试
- 异常情况处理测试
- 性能基准测试
- API接口集成测试

#### 项目18: 性能测试和优化
**优化重点**：
- 批量处理异步化
- 评分算法性能优化
- 数据库查询优化
- 内存使用优化

## 错误处理策略

### 数据验证
- JSON格式验证
- 必填字段检查
- 数据类型验证
- 业务规则验证

### 异常处理
- `JsonProcessingException` - JSON解析异常
- `IllegalArgumentException` - 参数异常
- `DataAccessException` - 数据库异常
- `BusinessException` - 业务逻辑异常

## 性能要求

### 功能指标
- 评分准确率≥95%
- 批量处理能力≥1000个答案/小时
- 系统响应时间≤3秒

### 质量指标
- 代码测试覆盖率≥85%
- 系统可用性≥99%

## 部署配置

### 配置文件
- `src/main/resources/application-prod.yml`
- 数据库连接配置
- 评分算法配置
- 性能参数调优

### 监控告警
- 评分任务执行监控
- 系统性能监控
- 错误日志监控

---

**文档版本**：v1.0  
**创建时间**：2024-12-21  
**预计完成时间**：3-4周  
**责任人**：后端开发团队 