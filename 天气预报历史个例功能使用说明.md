# 天气预报历史个例考试功能使用说明

## 功能概述

本功能实现了天气预报历史个例考试中的"未来24小时内指定站点的灾害性天气和气象要素预报"题型。考生需要根据给定的气象背景资料，在表格中填写各时段的预报要素。

## 数据库更新

请先执行项目根目录下的 `database_update.sql` 文件来创建必要的数据库表和初始数据。

## 功能特点

### 1. 表格化答题界面
- 专业的天气预报表格布局
- 支持多种输入类型：下拉选择、数字输入、多选等
- 实时数据验证和提示

### 2. 智能数据验证
- 必填字段检查
- 数据逻辑性验证（如最低气温不应高于最高气温）
- 专业一致性检查（如暴雨降水应对应暴雨灾害）

### 3. 灵活的评分机制
- 支持精确匹配、范围匹配、部分匹配等多种评分方式
- 可配置的权重分配
- 详细的评分报告

## 使用流程

### 1. 创建天气预报题目
1. 进入题目管理页面
2. 选择题目类型为"天气预报表格题"
3. 填写题目内容（包括背景资料和预报要点）
4. 选择表格配置（默认提供24小时站点预报配置）
5. 设置难度等级和分值
6. 保存题目

### 2. 创建考试
1. 在考试管理中创建新考试
2. 选择包含天气预报题目的题库
3. 配置考试规则（可以混合其他题型）
4. 发布考试

### 3. 考生答题
1. 考生进入考试页面
2. 遇到天气预报表格题时，会显示专业的表格界面
3. 根据题目要求填写各个预报要素
4. 系统提供实时验证和提示
5. 完成后提交答案

### 4. 评分和查看结果
1. 系统自动评分（基于配置的评分规则）
2. 支持人工复评
3. 提供详细的评分报告
4. 可查看答题统计和分析

## 表格字段说明

当前默认配置包含以下预报要素：

| 字段 | 说明 | 输入类型 | 选项 |
|------|------|----------|------|
| 预报起报日期及时间 | 预报的起始时间 | 只读 | 系统生成 |
| 站名站号 | 观测站点信息 | 只读 | 题目给定 |
| 08—08最大风力(级) | 24小时内最大风力等级 | 下拉选择 | 0, 1, 2, 3-4, 5-6, 7-8, 9-10, 11-12 |
| 08—08最大风力时的风向 | 最大风力时的风向 | 下拉选择 | 北, 东北, 东, 东南, 南, 西南, 西, 西北, 无风 |
| 最低气温℃ | 24小时内最低气温 | 数字输入 | -50 到 50 |
| 最高气温℃ | 24小时内最高气温 | 数字输入 | -50 到 50 |
| 08—08降水(雨、雪)量级 | 降水强度等级 | 下拉选择 | 无, 小雨, 中雨, 大雨, 暴雨, 大暴雨, 特大暴雨, 小雪, 中雪, 大雪, 暴雪 |
| 灾害性天气类型 | 可能出现的灾害性天气 | 多选 | 无, 雷暴, 冰雹, 大风, 龙卷风, 暴雨, 暴雪, 沙尘暴, 雾, 霾 |

## 评分规则

### 默认评分配置
- **精确匹配权重**: 60%（风力、风向、降水等级等）
- **数据一致性权重**: 20%（各要素间的逻辑一致性）
- **趋势分析权重**: 10%（时间序列的合理性）
- **专业判断权重**: 10%（灾害性天气的识别）

### 字段权重分配
- 最大风力: 15%
- 风向: 15%
- 最低气温: 15%
- 最高气温: 15%
- 降水量级: 20%
- 灾害性天气: 20%

## 技术架构

### 后端模块
- `weather` 模块：天气预报相关业务逻辑
- `WeatherTableConfig`：表格配置管理
- `WeatherAnswer`：答案数据存储
- `WeatherScoringService`：评分算法

### 前端组件
- `WeatherTableEditor`：天气预报表格编辑器
- 集成到现有的考试答题页面
- 支持数据验证和实时提示

### 数据库表
- `el_weather_table_config`：表格配置表
- `el_weather_answer`：答案数据表
- `el_qu`：扩展支持天气预报题型

## 扩展说明

### 添加新的表格配置
1. 在 `el_weather_table_config` 表中添加新配置
2. 定义表格结构、验证规则和评分配置
3. 创建题目时选择对应配置

### 自定义评分算法
1. 实现 `WeatherScoringService` 接口
2. 在配置中指定评分算法类型
3. 支持多种评分策略并存

### 添加新的预报要素
1. 在表格配置的 `columns` 中添加新字段
2. 更新前端组件支持新的输入类型
3. 在验证和评分逻辑中处理新字段

## 注意事项

1. **数据格式**：表格数据以JSON格式存储，确保数据结构的一致性
2. **性能考虑**：大量答题数据可能影响查询性能，建议定期归档
3. **扩展性**：当前实现主要针对站点预报，后续可扩展支持区域预报等
4. **兼容性**：新功能与现有考试系统完全兼容，不影响其他题型

## 故障排除

### 常见问题
1. **表格不显示**：检查题目的 `weather_config_id` 是否正确设置
2. **数据保存失败**：检查JSON数据格式是否正确
3. **评分异常**：检查评分配置是否完整

### 日志查看
- 后端日志：查看 `WeatherAnswerServiceImpl` 和 `WeatherScoringServiceImpl` 的日志
- 前端调试：在浏览器控制台查看组件交互日志

## 联系支持

如有问题或建议，请联系开发团队或查看项目文档。
