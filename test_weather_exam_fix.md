# 历史个例考试修复测试指南

## 问题描述
历史个例考试保存后，点击"历史个例考试"菜单报404错误：`/exam/paging`接口不存在。

## 修复内容

### 1. 前端API调用修复
- 修改了`exam-vue/src/views/user/weather/exam.vue`中的API调用
- 从`/exam/paging`改为`/exam/api/weather/exam/online-paging`
- 修复了响应码判断（从200改为0）

### 2. 后端查询修复
- 修复了`ExamMapper.online`方法的用户ID参数传递问题
- 添加了对历史个例考试的过滤支持（通过`question_id`字段）
- 添加了状态过滤支持

### 3. 数据模型扩展
- 在`ExamDTO`和`Exam`实体类中添加了`questionId`和`examType`字段
- 更新了`ExamMapper.xml`的字段映射

## 测试步骤

### 前置条件
1. 执行SQL脚本：`sql/add_question_id_to_exam.sql`
2. 重启应用程序

### 测试流程
1. **创建历史个例题目**
   - 访问"历史个例题目管理"
   - 创建一个新的历史个例题目
   - 确保题目保存成功

2. **创建历史个例考试**
   - 访问"历史个例考试管理"
   - 创建新考试，选择刚创建的题目
   - 设置考试时间和参数
   - 保存考试

3. **学生端测试**
   - 以学生身份登录
   - 点击"历史个例考试"菜单
   - 应该能看到考试列表，不再报404错误

### 预期结果
- 学生端能正常显示历史个例考试列表
- 只显示已发布状态的历史个例考试
- 不显示传统题库组卷的考试

## 技术细节

### 查询逻辑
```sql
-- 历史个例考试过滤条件
AND ex.question_id IS NOT NULL  -- 有关联题目的是历史个例考试

-- 状态过滤
AND ex.state = 1  -- 1=已发布，0=未发布，2=已结束
```

### 前端参数
```javascript
params: {
  state: 1,           // 显示已发布的考试
  examType: 'weather' // 过滤历史个例考试
}
```

## 故障排除

### 如果仍然报404错误
1. 检查`WeatherExamController`是否存在`/online-paging`接口
2. 确认应用程序已重启
3. 检查路由配置

### 如果查询结果为空
1. 确认数据库中存在`question_id`不为空的考试记录
2. 检查考试状态是否为已发布（state=1）
3. 确认用户权限设置正确

### 如果数据库错误
1. 确认已执行SQL脚本添加`question_id`字段
2. 检查字段类型和索引是否正确创建
