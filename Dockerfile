# 多阶段构建：构建阶段
FROM node:14-alpine as build-stage

# 设置工作目录
WORKDIR /app

# 设置npm源（国内加速）
RUN npm config set registry https://registry.npm.taobao.org

# 复制package文件
COPY package*.json ./

# 安装依赖（仅生产环境依赖）
RUN npm ci --only=production --silent && npm cache clean --force

# 复制源代码
COPY . .

# 构建应用
RUN npm run build:prod

# 生产阶段
FROM nginx:1.21-alpine as production-stage

# 安装必要工具
RUN apk add --no-cache curl

# 复制自定义nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 复制构建产物
COPY --from=build-stage /app/dist /usr/share/nginx/html

# 创建非root用户
RUN addgroup -g 1001 -S nginx-user && \
    adduser -S -D -H -u 1001 -h /var/cache/nginx -s /sbin/nologin -G nginx-user -g nginx-user nginx-user

# 设置正确的权限
RUN chown -R nginx-user:nginx-user /usr/share/nginx/html && \
    chown -R nginx-user:nginx-user /var/cache/nginx && \
    chown -R nginx-user:nginx-user /var/log/nginx && \
    chown -R nginx-user:nginx-user /etc/nginx/conf.d

# 创建pid文件目录
RUN mkdir -p /var/run/nginx && \
    chown -R nginx-user:nginx-user /var/run/nginx

# 切换到非root用户
USER nginx-user

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

# 启动命令
CMD ["nginx", "-g", "daemon off;"] 