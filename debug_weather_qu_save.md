# 历史个例试题保存问题调试

## 问题描述
用户修改历史个例试题后，接口返回成功，但数据没有保存到数据库中。

## 问题分析

### 1. 原始问题
- 前端调用 `exam/api/weather/case/save` 接口
- 接口返回 `{"code": 0, "msg": "请求成功！", "success": true}`
- 但刷新页面后，数据没有变化

### 2. 根本原因
- 前端发送的数据包含历史个例特有的字段（stations、forecastDate、forecastTime等）
- 这些字段在Qu实体类中不存在，导致数据没有被保存
- QuService.save方法使用BeanMapper.copy，只复制匹配的字段

## 解决方案

### 1. 扩展Qu实体类
添加了以下字段：
- `tableConfigId` - 关联的天气预报表格配置ID
- `weatherScenario` - 天气情景描述和背景材料  
- `scenarioData` - 情景相关数据（JSON格式）

### 2. 数据序列化策略
将历史个例特有的数据序列化到`scenarioData`字段中：
```json
{
  "stations": ["站点1", "站点2"],
  "forecastDate": "2024-01-15",
  "forecastTime": "08",
  "dataFileName": "data.dat",
  "answers": {...}
}
```

### 3. 前端修改
- 修改提交数据格式，将特殊字段序列化到scenarioData
- 修改编辑时的数据解析，从scenarioData中恢复数据
- 添加兼容性处理，支持旧数据格式

### 4. 后端修改
- 扩展Qu实体类和QuDTO
- 更新Mapper XML文件，包含新字段
- 确保数据库表结构支持新字段

## 测试步骤

1. **保存测试**
   - 创建新的历史个例试题
   - 填写完整信息（包括站点、答案等）
   - 提交保存
   - 检查数据库中scenarioData字段是否有数据

2. **编辑测试**
   - 打开已保存的试题进行编辑
   - 检查是否正确显示之前保存的数据
   - 修改部分内容后保存
   - 再次打开检查修改是否生效

3. **兼容性测试**
   - 测试旧格式数据的显示
   - 测试新旧数据格式的转换

## 关键文件修改

### 后端
- `src/main/java/com/yf/exam/modules/qu/entity/Qu.java` - 添加新字段
- `src/main/java/com/yf/exam/modules/qu/dto/QuDTO.java` - 添加新字段
- `src/main/resources/mapper/qu/QuMapper.xml` - 更新映射

### 前端
- `exam-vue/src/views/weather/qu/index.vue` - 修改数据提交和解析逻辑

## 预期结果
- 用户修改历史个例试题后，数据能正确保存到数据库
- 再次打开编辑时，显示最新的修改内容
- 支持完整的历史个例功能（站点配置、答案设置等）
