# 历史个例考试管理问题修复总结

## 🔍 问题描述

### 问题1：关联了试题但列表显示未关联
**现象**：在历史个例考试管理中，虽然创建考试时关联了试题，但是列表中显示"未关联"。

### 问题2：发布考试的含义不清楚
**现象**：用户不理解"发布考试"功能的作用和考试状态的含义。

## 🛠️ 解决方案

### 问题1解决方案：修复题目关联显示

#### 根本原因
前端列表显示逻辑依赖`questionTitle`字段，但后端查询只返回了`questionId`，没有关联查询题目标题。

#### 修复内容

**1. 后端数据库查询修复**
- **文件**: `src/main/resources/mapper/exam/ExamMapper.xml`
- **修改**: 在paging查询中添加题目表关联查询

```xml
<!-- 修改前 -->
SELECT * FROM el_exam ex

<!-- 修改后 -->
SELECT ex.*, qu.title as questionTitle 
FROM el_exam ex
LEFT JOIN el_qu qu ON ex.question_id = qu.id
```

**2. 结果映射扩展**
```xml
<resultMap id="ListResultMap" type="com.yf.exam.modules.exam.dto.ExamDTO" extends="BaseResultMap">
    <result column="questionTitle" property="questionTitle" />
</resultMap>
```

**3. DTO字段扩展**
- **文件**: `src/main/java/com/yf/exam/modules/exam/dto/ExamDTO.java`
- **添加字段**: `questionTitle`

```java
@ApiModelProperty(value = "关联题目标题（历史个例考试使用）", required=false)
private String questionTitle;
```

#### 前端显示逻辑
```vue
<el-table-column label="关联题目" width="150" align="center">
  <template v-slot="scope">
    <span v-if="scope.row.questionTitle">{{ scope.row.questionTitle }}</span>
    <el-tag v-else type="warning" size="mini">未关联</el-tag>
  </template>
</el-table-column>
```

### 问题2解决方案：实现考试状态管理

#### 考试状态说明

| 状态值 | 状态名称 | 含义 | 学生端显示 |
|--------|----------|------|------------|
| 0 | 未发布 | 考试创建完成，但未对学生开放 | 不显示 |
| 1 | 已发布 | 考试已发布，学生可以参加 | 显示并可参加 |
| 2 | 已结束 | 考试已结束，学生无法参加 | 显示但无法参加 |

#### 发布考试的含义
- **发布前（状态=0）**：考试处于草稿状态，学生看不到这个考试
- **发布后（状态=1）**：考试对学生开放，学生可以在考试列表中看到并参加
- **结束后（状态=2）**：考试关闭，学生无法继续参加，但可以查看成绩

#### 后端接口实现

**1. 添加状态更新接口**
- **文件**: `src/main/java/com/yf/exam/modules/weather/controller/WeatherExamController.java`

```java
@PostMapping("/state")
public ApiRest updateState(@RequestBody BaseStateReqDTO reqDTO) {
    QueryWrapper<Exam> wrapper = new QueryWrapper<>();
    wrapper.lambda().in(Exam::getId, reqDTO.getIds());
    Exam exam = new Exam();
    exam.setState(reqDTO.getState());
    exam.setUpdateTime(new Date());
    
    examService.update(exam, wrapper);
    return super.success();
}
```

**2. 前端API接口**
- **文件**: `exam-vue/src/api/weather/weather.js`

```javascript
export function updateWeatherExamState(data) {
  return post('/exam/api/weather/exam/state', data)
}
```

#### 前端功能实现

**1. 发布功能**
```javascript
handlePublish(row) {
  this.$confirm('确定要发布这个考试吗？发布后学生就可以参加考试了。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const data = {
      ids: [row.id],
      state: 1 // 1=已发布
    }
    updateWeatherExamState(data).then(() => {
      this.$message.success('考试发布成功')
      this.getList()
    })
  })
}
```

**2. 结束功能**
```javascript
handleEnd(row) {
  this.$confirm('确定要结束这个考试吗？结束后学生将无法继续参加考试。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const data = {
      ids: [row.id],
      state: 2 // 2=已结束
    }
    updateWeatherExamState(data).then(() => {
      this.$message.success('考试已结束')
      this.getList()
    })
  })
}
```

**3. 状态显示**
```javascript
getStatusText(state) {
  const texts = {
    0: '未发布',
    1: '已发布', 
    2: '已结束'
  }
  return texts[state] || '未知'
}

getStatusType(state) {
  const types = {
    0: 'info',     // 灰色
    1: 'success',  // 绿色
    2: 'warning'   // 橙色
  }
  return types[state] || 'info'
}
```

## 📋 修改文件列表

### 后端文件
1. `src/main/resources/mapper/exam/ExamMapper.xml` - 添加题目关联查询
2. `src/main/java/com/yf/exam/modules/exam/dto/ExamDTO.java` - 添加questionTitle字段
3. `src/main/java/com/yf/exam/modules/weather/controller/WeatherExamController.java` - 添加状态更新接口

### 前端文件
1. `exam-vue/src/api/weather/weather.js` - 添加状态更新API
2. `exam-vue/src/views/weather/exam/index.vue` - 实现发布和结束功能

## ✅ 预期效果

### 题目关联显示
- ✅ 列表中正确显示关联的题目标题
- ✅ 未关联题目时显示"未关联"标签
- ✅ 关联查询不影响性能

### 考试状态管理
- ✅ 发布功能：将考试状态从"未发布"改为"已发布"
- ✅ 结束功能：将考试状态从"已发布"改为"已结束"
- ✅ 状态显示：清晰的状态标签和颜色区分
- ✅ 操作确认：发布和结束前有明确的确认提示

## 🧪 测试步骤

### 测试题目关联显示
1. 创建历史个例考试并关联题目
2. 保存后查看列表，确认显示题目标题
3. 创建不关联题目的考试，确认显示"未关联"

### 测试考试状态管理
1. **创建考试**：新建考试，状态为"未发布"
2. **发布考试**：点击"发布"按钮，状态变为"已发布"
3. **结束考试**：点击"结束"按钮，状态变为"已结束"
4. **学生端验证**：确认不同状态下学生端的显示和权限

## 📝 使用说明

### 考试管理流程
1. **创建考试**：填写考试信息，关联题目，设置部门权限
2. **发布考试**：确认考试信息无误后，点击"发布"让学生可以参加
3. **监控考试**：查看参与人数和考试进度
4. **结束考试**：考试时间到或需要提前结束时，点击"结束"
5. **查看成绩**：考试结束后查看学生成绩和统计

### 注意事项
- 发布后的考试信息不建议大幅修改
- 结束考试前确认所有学生已完成答题
- 考试状态变更会立即生效，请谨慎操作
