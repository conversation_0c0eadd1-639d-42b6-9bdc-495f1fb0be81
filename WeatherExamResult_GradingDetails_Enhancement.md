# 历史个例考试结果接口增强 - 批卷详细信息

## 修改概述

在 `exam/api/weather/exam/result/detail` 接口中新增了获取批卷详细信息的功能，从 `el_weather_scoring_result` 表中获取 `detail_result` 等批卷相关信息。

## 修改的文件

### 1. WeatherHistoryExamResultServiceImpl.java

**文件路径**: `src/main/java/com/yf/exam/modules/weather/service/impl/WeatherHistoryExamResultServiceImpl.java`

**主要修改**:
1. 添加了 `WeatherScoringResult` 实体类的导入
2. 在 `getExamResult` 方法中新增获取批卷详细信息的逻辑
3. 新增 `getGradingDetails` 私有方法用于获取批卷详细信息

## 新增功能详情

### 1. 批卷详细信息获取

在原有的考试结果基础上，新增了 `gradingDetails` 字段，包含以下信息：

#### 基本批卷信息
- `hasGrading`: 是否有批卷信息
- `resultId`: 评分结果ID
- `isSuccess`: 评分是否成功
- `finalScore`: 最终得分
- `totalScore`: 总得分
- `maxScore`: 满分
- `scorePercentage`: 得分率
- `scoringTime`: 评分时间
- `configVersion`: 使用的配置版本
- `scoringDuration`: 评分耗时（毫秒）

#### 详细评分信息
- `detailResults`: 详细结果信息（来自 `detail_results` 字段）
- `stationScores`: 分站得分详情
- `elementScores`: 分要素得分详情
- `errorAnalysis`: 错误分析

#### 评分等级信息
- `grade`: 评分等级（优秀、良好、及格、不及格）
- `isPassed`: 是否通过（得分率>=60%）

## API 响应示例

### 修改前的响应结构
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "examId": "exam123",
    "examTitle": "历史个例考试",
    "questionId": "question123",
    "questionTitle": "2023年7月暴雨过程",
    "submitTime": "2024-01-15T10:30:00",
    "totalScore": 85.5,
    "maxScore": 100,
    "userAnswer": {
      "precipitationAnswer": {...},
      "weatherAnswer": {...}
    },
    "standardAnswer": {
      "precipitationAnswer": {...},
      "weatherAnswer": {...}
    },
    "scoringDetails": {...},
    "scenarioData": {...}
  }
}
```

### 修改后的响应结构
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "examId": "exam123",
    "examTitle": "历史个例考试",
    "questionId": "question123",
    "questionTitle": "2023年7月暴雨过程",
    "submitTime": "2024-01-15T10:30:00",
    "totalScore": 85.5,
    "maxScore": 100,
    "userAnswer": {
      "precipitationAnswer": {...},
      "weatherAnswer": {...}
    },
    "standardAnswer": {
      "precipitationAnswer": {...},
      "weatherAnswer": {...}
    },
    "scoringDetails": {...},
    "gradingDetails": {
      "hasGrading": true,
      "resultId": "result123",
      "isSuccess": true,
      "finalScore": 85.5,
      "totalScore": 85.5,
      "maxScore": 100.0,
      "scorePercentage": 85.5,
      "scoringTime": "2024-01-15T10:35:00",
      "configVersion": "v1.0",
      "scoringDuration": 5000,
      "detailResults": {
        "precipitationScoring": {...},
        "weatherScoring": {...},
        "overallAnalysis": {...}
      },
      "stationScores": {
        "station1": 90.0,
        "station2": 85.0,
        "station3": 80.0
      },
      "elementScores": {
        "precipitation": 88.0,
        "temperature": 85.0,
        "wind": 83.0
      },
      "errorAnalysis": {
        "commonErrors": [...],
        "suggestions": [...]
      },
      "grade": "良好",
      "isPassed": true
    },
    "scenarioData": {...}
  }
}
```

## 异常处理

### 1. 无批卷信息的情况
当用户答案没有对应的批卷结果时，返回：
```json
"gradingDetails": {
  "hasGrading": false,
  "message": "暂无批卷信息"
}
```

### 2. 获取批卷信息失败的情况
当获取批卷信息过程中发生异常时，返回：
```json
"gradingDetails": {
  "hasGrading": false,
  "error": "获取批卷信息失败: 具体错误信息"
}
```

## 数据来源

批卷详细信息主要来源于 `el_weather_scoring_result` 表的以下字段：

| 数据库字段 | 响应字段 | 说明 |
|-----------|----------|------|
| id | resultId | 评分结果ID |
| is_success | isSuccess | 评分是否成功 |
| final_score | finalScore | 最终得分 |
| total_score | totalScore | 总得分 |
| max_score | maxScore | 满分 |
| score_percentage | scorePercentage | 得分率 |
| scoring_time | scoringTime | 评分时间 |
| config_version | configVersion | 配置版本 |
| scoring_duration | scoringDuration | 评分耗时 |
| detail_results | detailResults | 详细结果（JSON） |
| station_scores | stationScores | 分站得分（JSON） |
| element_scores | elementScores | 分要素得分（JSON） |
| error_analysis | errorAnalysis | 错误分析（JSON） |

## 使用说明

1. **前端调用**: 无需修改前端调用方式，接口地址和请求参数保持不变
2. **向后兼容**: 新增的 `gradingDetails` 字段不会影响现有功能
3. **数据获取**: 通过答案ID关联查询评分结果表获取批卷信息
4. **性能影响**: 增加了一次数据库查询，但对整体性能影响较小

## 测试建议

1. 测试有批卷信息的考试结果
2. 测试无批卷信息的考试结果
3. 测试批卷信息获取异常的情况
4. 验证各个批卷字段的数据完整性
5. 确认评分等级计算的正确性
