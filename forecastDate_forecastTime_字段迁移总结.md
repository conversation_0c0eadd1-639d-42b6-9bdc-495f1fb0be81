# forecastDate和forecastTime字段迁移总结

## 🎯 迁移目标

将`forecastDate`和`forecastTime`从scenarioData JSON字段中迁移到el_qu表的独立字段，提高查询性能和数据规范性。

## 📊 数据结构变化

### 迁移前（旧格式）
```json
// el_qu表
{
  "id": "weather_qu_sample_001",
  "title": "题目标题",
  "content": "题目内容...",
  "scenarioData": "{\"stations\":[...], \"forecastDate\":\"2025-07-02\", \"forecastTime\":\"08\", \"answers\":{...}}"
}
```

### 迁移后（新格式）
```json
// el_qu表
{
  "id": "weather_qu_sample_001", 
  "title": "题目标题",
  "content": "题目内容...",
  "forecastDate": "2025-07-02",     // 独立字段
  "forecastTime": "08",             // 独立字段
  "scenarioData": "{\"stations\":[...], \"answers\":{...}}"  // 不再包含forecastDate和forecastTime
}
```

## 🛠️ 实施的修改

### 1. 数据库层面
- **迁移脚本**: `V20241221_2__Add_Forecast_Fields_To_Qu.sql`
- **新增字段**: 
  - `forecast_date` varchar(50) - 预报起报日期
  - `forecast_time` varchar(20) - 预报起报时次
- **索引优化**: 添加查询索引提高性能

### 2. 后端实体层面
- **Qu.java**: 添加`forecastDate`和`forecastTime`字段
- **QuDTO.java**: 添加对应的DTO字段
- **QuMapper.xml**: 更新ResultMap和Column_List

### 3. 前端数据处理
- **提交数据**: forecastDate和forecastTime作为根级别字段提交
- **scenarioData**: 只包含stations、answers、文件信息等复杂数据
- **编辑回显**: 优先从根级别读取，兼容从scenarioData读取（向后兼容）

## 📋 修改的文件列表

### 后端文件
1. `src/main/resources/db/migration/V20241221_2__Add_Forecast_Fields_To_Qu.sql`
2. `src/main/java/com/yf/exam/modules/qu/entity/Qu.java`
3. `src/main/java/com/yf/exam/modules/qu/dto/QuDTO.java`
4. `src/main/resources/mapper/qu/QuMapper.xml`

### 前端文件
1. `exam-vue/src/views/weather/qu/index.vue`

## 🔄 兼容性处理

### 向后兼容策略
前端编辑时的数据读取优先级：
1. **优先**: 从根级别字段读取（新格式）
2. **备选**: 从scenarioData中读取（旧格式兼容）
3. **默认**: 使用默认值

```javascript
// 优先从根级别获取（新格式）
if (row.forecastDate) {
  this.questionForm.forecastDate = row.forecastDate
}

// 如果根级别没有，从scenarioData中获取（兼容旧数据）
if (!row.forecastDate && scenarioData.forecastDate) {
  this.questionForm.forecastDate = scenarioData.forecastDate
}
```

## ✅ 预期效果

### 数据存储
- ✅ forecastDate和forecastTime作为独立字段存储
- ✅ scenarioData只包含复杂的嵌套数据
- ✅ 数据库查询性能提升

### 功能表现
- ✅ 新增题目时，forecastDate和forecastTime正确保存
- ✅ 编辑题目时，能正确回显这两个字段
- ✅ 列表显示中能正确显示起报日期和时次
- ✅ 支持按日期和时次查询（后续可扩展）

### 兼容性
- ✅ 新格式数据正常处理
- ✅ 旧格式数据兼容处理
- ✅ 数据迁移平滑过渡

## 🧪 测试建议

### 1. 新增测试
- 创建新的历史个例题目
- 设置不同的起报日期和时次
- 验证数据库中字段正确保存

### 2. 编辑测试
- 编辑现有题目的起报信息
- 验证修改后能正确保存和显示

### 3. 兼容性测试
- 测试旧格式数据的编辑功能
- 验证数据迁移的正确性

### 4. 查询测试
- 验证列表显示正确
- 测试按日期筛选功能（如果实现）

## 🚀 后续优化建议

1. **查询功能增强**: 可以添加按起报日期和时次的筛选功能
2. **数据验证**: 添加日期格式和时次范围的验证
3. **索引优化**: 根据实际查询需求调整索引策略
4. **数据清理**: 定期清理scenarioData中的冗余字段

现在forecastDate和forecastTime已经成功迁移到根级别字段，数据结构更加规范，查询性能也会有所提升！
