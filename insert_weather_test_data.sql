-- 插入天气预报表格题测试数据

-- 1. 插入天气预报表格题目
INSERT INTO el_qu (id, qu_type, level, content, create_time, update_time, remark, analysis, weather_config_id) VALUES
('weather_qu_001', 6, 2, '请根据气象观测数据，填写24小时天气预报表格', NOW(), NOW(), '天气预报表格题示例1', '根据温度、湿度、风向等要素进行综合分析', 'weather_config_001'),
('weather_qu_002', 6, 3, '根据卫星云图和地面观测资料，完成降水预报表格', NOW(), NOW(), '天气预报表格题示例2', '重点关注降水概率和降水量的预报准确性', 'weather_config_002'),
('weather_qu_003', 6, 1, '填写基础天气要素预报表格（温度、风向、风速）', NOW(), NOW(), '天气预报表格题示例3', '基础天气要素的预报方法和技巧', 'weather_config_001');

-- 2. 确保有默认题库存在
INSERT IGNORE INTO el_repo (id, title, remark, create_time, update_time, group_name) VALUES
('default_weather_repo', '天气预报题库', '专门用于天气预报历史个例考试的题库', NOW(), NOW(), 'default');

-- 3. 将天气预报题目加入题库
INSERT INTO el_qu_repo (id, qu_id, repo_id) VALUES
('weather_repo_001', 'weather_qu_001', 'default_weather_repo'),
('weather_repo_002', 'weather_qu_002', 'default_weather_repo'),
('weather_repo_003', 'weather_qu_003', 'default_weather_repo');

-- 4. 插入天气预报表格配置（如果不存在）
INSERT IGNORE INTO el_weather_table_config (id, name, template_type, title, description, table_schema, validation_rules, scoring_config, is_active, create_time, update_time, create_by, update_by) VALUES
('weather_config_001', '基础天气预报表格', 'basic_forecast', '24小时天气预报表', '基础天气要素预报表格', 
'{"columns": [{"key": "time", "label": "时间", "type": "time"}, {"key": "temperature", "label": "温度(℃)", "type": "number"}, {"key": "humidity", "label": "湿度(%)", "type": "number"}, {"key": "wind_direction", "label": "风向", "type": "select"}, {"key": "wind_speed", "label": "风速(m/s)", "type": "number"}], "rows": 24}',
'{"temperature": {"min": -50, "max": 50}, "humidity": {"min": 0, "max": 100}, "wind_speed": {"min": 0, "max": 30}}',
'{"total": 100, "temperature": 30, "humidity": 20, "wind_direction": 25, "wind_speed": 25}',
1, NOW(), NOW(), 'system', 'system'),

('weather_config_002', '降水预报表格', 'precipitation_forecast', '降水预报表', '降水概率和降水量预报表格',
'{"columns": [{"key": "time", "label": "时间", "type": "time"}, {"key": "precipitation_probability", "label": "降水概率(%)", "type": "number"}, {"key": "precipitation_amount", "label": "降水量(mm)", "type": "number"}, {"key": "precipitation_type", "label": "降水类型", "type": "select"}], "rows": 12}',
'{"precipitation_probability": {"min": 0, "max": 100}, "precipitation_amount": {"min": 0, "max": 200}}',
'{"total": 100, "precipitation_probability": 40, "precipitation_amount": 35, "precipitation_type": 25}',
1, NOW(), NOW(), 'system', 'system');

-- 5. 查询验证数据
SELECT '=== 天气预报题目 ===' as info;
SELECT id, qu_type, level, content, weather_config_id FROM el_qu WHERE qu_type = 6;

SELECT '=== 题库关联 ===' as info;
SELECT qr.qu_id, qr.repo_id, r.title as repo_title, q.content 
FROM el_qu_repo qr 
LEFT JOIN el_repo r ON qr.repo_id = r.id 
LEFT JOIN el_qu q ON qr.qu_id = q.id 
WHERE q.qu_type = 6;

SELECT '=== 天气配置 ===' as info;
SELECT id, name, template_type, title FROM el_weather_table_config WHERE is_active = 1;
