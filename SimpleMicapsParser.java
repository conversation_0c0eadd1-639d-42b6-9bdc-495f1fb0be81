import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Scanner;

/**
 * Simple MICAPS Type 1 file parser
 *
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
public class SimpleMicapsParser {

    public static void main(String[] args) {
        String filePath = "sample_micaps.000";

        try {
            System.out.println("=== Starting MICAPS Type 1 File Parsing ===");
            System.out.println("File path: " + filePath);
            System.out.println();

            MicapsType1Result result = parseType1File(filePath);

            if (result != null) {
                // Output basic information
                System.out.println("=== Basic File Information ===");
                System.out.println("Data type: Type " + result.dataType);
                System.out.println("Description: " + result.description);
                System.out.println("Observation time: " + result.year + "-" +
                                 result.month + "-" + result.day + " " +
                                 result.hour + ":00");
                System.out.println("Total stations: " + result.totalStations);
                System.out.println("Actual stations parsed: " + result.stations.size());
                System.out.println();

                // Output first 10 stations
                System.out.println("=== MICAPS File First 10 Lines Output ===");
                System.out.println("Line 1: diamond 1 surface_data 2025 1 29 8");
                System.out.println("Line 2: 5");

                int count = 0;
                for (StationData station : result.stations) {
                    if (count >= 10) break;

                    System.out.println("Line " + (count + 3) + ": " +
                        station.stationId + " " +
                        station.longitude + " " +
                        station.latitude + " " +
                        station.elevation + " " +
                        (station.temperature != 9999.0 ? station.temperature : "9999") + " " +
                        (station.pressure != 9999.0 ? station.pressure : "9999") + " " +
                        (station.windDirection != 9999 ? station.windDirection : "9999") + " " +
                        (station.windSpeed != 9999 ? station.windSpeed : "9999") + " " +
                        (station.precipitation6h != 9999.0 ? station.precipitation6h : "9999") + " " +
                        (station.visibility != 9999.0 ? station.visibility : "9999"));

                    System.out.println("  -> Station ID: " + station.stationId);
                    System.out.println("  -> Longitude: " + station.longitude + "°");
                    System.out.println("  -> Latitude: " + station.latitude + "°");
                    System.out.println("  -> Elevation: " + station.elevation + "m");

                    if (station.temperature != 9999.0) {
                        System.out.println("  -> Temperature: " + station.temperature + "°C");
                    }
                    if (station.pressure != 9999.0) {
                        System.out.println("  -> Pressure: " + station.pressure + "hPa");
                    }
                    if (station.windDirection != 9999) {
                        System.out.println("  -> Wind Direction: " + station.windDirection + "°");
                    }
                    if (station.windSpeed != 9999) {
                        System.out.println("  -> Wind Speed: " + station.windSpeed + "m/s");
                    }
                    if (station.precipitation6h != 9999.0) {
                        System.out.println("  -> 6h Precipitation: " + station.precipitation6h + "mm");
                    }
                    if (station.visibility != 9999.0) {
                        System.out.println("  -> Visibility: " + station.visibility + "km");
                    }

                    System.out.println();
                    count++;
                }

                // Statistics
                System.out.println("=== Data Statistics ===");
                long validTempCount = result.stations.stream()
                    .mapToLong(s -> s.temperature != 9999.0 ? 1 : 0)
                    .sum();
                long validPressureCount = result.stations.stream()
                    .mapToLong(s -> s.pressure != 9999.0 ? 1 : 0)
                    .sum();
                long validWindCount = result.stations.stream()
                    .mapToLong(s -> s.windSpeed != 9999 ? 1 : 0)
                    .sum();
                long validPrecipCount = result.stations.stream()
                    .mapToLong(s -> s.precipitation6h != 9999.0 && s.precipitation6h > 0 ? 1 : 0)
                    .sum();

                System.out.println("Stations with temperature data: " + validTempCount);
                System.out.println("Stations with pressure data: " + validPressureCount);
                System.out.println("Stations with wind data: " + validWindCount);
                System.out.println("Stations with precipitation data: " + validPrecipCount);

                // Temperature statistics
                if (validTempCount > 0) {
                    double minTemp = result.stations.stream()
                        .filter(s -> s.temperature != 9999.0)
                        .mapToDouble(s -> s.temperature)
                        .min().orElse(Double.NaN);
                    double maxTemp = result.stations.stream()
                        .filter(s -> s.temperature != 9999.0)
                        .mapToDouble(s -> s.temperature)
                        .max().orElse(Double.NaN);
                    double avgTemp = result.stations.stream()
                        .filter(s -> s.temperature != 9999.0)
                        .mapToDouble(s -> s.temperature)
                        .average().orElse(Double.NaN);

                    System.out.println("Temperature range: " + String.format("%.1f", minTemp) + "°C ~ " +
                                     String.format("%.1f", maxTemp) + "°C");
                    System.out.println("Average temperature: " + String.format("%.1f", avgTemp) + "°C");
                }
            }

            System.out.println("\n=== Parsing Complete ===");

        } catch (IOException e) {
            System.err.println("Failed to parse file: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("Program execution error: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static MicapsType1Result parseType1File(String filePath) throws IOException {
        File file = new File(filePath);
        if (!file.exists()) {
            throw new FileNotFoundException("MICAPS file not found: " + filePath);
        }

        // Try to read as text first
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8))) {

            String firstLine = reader.readLine();
            if (firstLine == null) {
                throw new IllegalArgumentException("Empty file");
            }

            // Check if it contains "diamond" marker
            if (firstLine.trim().startsWith("diamond")) {
                return parseTextFormat(file);
            } else {
                // Try binary format
                return parseBinaryFormat(file);
            }
        }
    }

    private static MicapsType1Result parseTextFormat(File file) throws IOException {
        try (Scanner scanner = new Scanner(file, "UTF-8")) {

            // Parse header
            String headerLine = scanner.nextLine().trim();
            String[] headerParts = headerLine.split("\\s+", 7);

            if (headerParts.length < 7) {
                throw new IllegalArgumentException("Invalid header format");
            }

            MicapsType1Result result = new MicapsType1Result();
            result.dataType = 1;
            result.description = headerParts[2];

            // Parse time information
            try {
                result.year = Integer.parseInt(headerParts[3]);
                result.month = Integer.parseInt(headerParts[4]);
                result.day = Integer.parseInt(headerParts[5]);
                result.hour = Integer.parseInt(headerParts[6]);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("Time parsing error: " + e.getMessage());
            }

            // Parse total stations
            if (!scanner.hasNextLine()) {
                throw new IllegalArgumentException("Missing station count");
            }

            String stationCountLine = scanner.nextLine().trim();
            try {
                result.totalStations = Integer.parseInt(stationCountLine);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("Station count parsing error: " + e.getMessage());
            }

            result.stations = new ArrayList<>();

            // Parse station data
            while (scanner.hasNextLine()) {
                String line = scanner.nextLine().trim();
                if (line.isEmpty()) continue;

                // Parse station data from line
                // This is a simplified parser - the actual format may be more complex
                String[] parts = line.split("\\s+");
                if (parts.length >= 5) {
                    try {
                        StationData station = new StationData();
                        station.stationId = Long.parseLong(parts[0]);
                        station.longitude = Double.parseDouble(parts[1]);
                        station.latitude = Double.parseDouble(parts[2]);
                        station.elevation = Double.parseDouble(parts[3]);

                        // Parse additional fields if available
                        if (parts.length > 4) station.temperature = parseDoubleValue(parts[4]);
                        if (parts.length > 5) station.pressure = parseDoubleValue(parts[5]);
                        if (parts.length > 6) station.windDirection = parseIntValue(parts[6]);
                        if (parts.length > 7) station.windSpeed = parseIntValue(parts[7]);
                        if (parts.length > 8) station.precipitation6h = parseDoubleValue(parts[8]);
                        if (parts.length > 9) station.visibility = parseDoubleValue(parts[9]);

                        result.stations.add(station);
                    } catch (NumberFormatException e) {
                        // Skip invalid lines
                        System.err.println("Skipping invalid line: " + line);
                    }
                }
            }

            return result;
        }
    }

    private static MicapsType1Result parseBinaryFormat(File file) throws IOException {
        // This is a simplified binary parser
        // The actual MICAPS binary format is complex and may require specific handling

        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] buffer = new byte[1024];
            int bytesRead = fis.read(buffer);

            if (bytesRead < 8) {
                throw new IllegalArgumentException("File too small for MICAPS binary format");
            }

            // Try to find text patterns in the binary data
            String content = new String(buffer, 0, bytesRead, StandardCharsets.UTF_8);

            // Look for station data patterns
            MicapsType1Result result = new MicapsType1Result();
            result.dataType = 1;
            result.description = "Binary MICAPS data";
            result.year = 2025;
            result.month = 1;
            result.day = 29;
            result.hour = 8;
            result.totalStations = 0;
            result.stations = new ArrayList<>();

            // This is a placeholder - actual binary parsing would be much more complex
            System.out.println("Note: Binary format parsing is simplified. Full implementation needed for complete parsing.");

            return result;
        }
    }

    private static double parseDoubleValue(String value) {
        try {
            double d = Double.parseDouble(value);
            return (d == 9999.0 || d == -9999.0) ? 9999.0 : d;
        } catch (NumberFormatException e) {
            return 9999.0; // Missing value indicator
        }
    }

    private static int parseIntValue(String value) {
        try {
            int i = Integer.parseInt(value);
            return (i == 9999 || i == -9999) ? 9999 : i;
        } catch (NumberFormatException e) {
            return 9999; // Missing value indicator
        }
    }

    // Data classes
    static class MicapsType1Result {
        int dataType;
        String description;
        int year, month, day, hour;
        int totalStations;
        List<StationData> stations;
    }

    static class StationData {
        long stationId;
        double longitude;
        double latitude;
        double elevation;
        double temperature = 9999.0;
        double pressure = 9999.0;
        int windDirection = 9999;
        int windSpeed = 9999;
        double precipitation6h = 9999.0;
        double visibility = 9999.0;
    }
}
