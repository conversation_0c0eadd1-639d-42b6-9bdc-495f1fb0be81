# 历史个例考试部门设置功能实现

## 🎯 功能概述

参考理论考试的部门设置逻辑，为历史个例考试实现了完整的部门权限控制功能，包括：

1. **开放类型选择**：完全公开 / 部门开放
2. **部门树选择**：支持多部门选择和搜索过滤
3. **权限验证**：确保部门开放时必须选择部门
4. **数据持久化**：正确保存和加载部门信息

## 🛠️ 实现细节

### 1. 前端界面增强

**文件**: `exam-vue/src/views/weather/exam/index.vue`

#### 新增表单字段
```vue
<!-- 权限配置 -->
<el-form-item label="开放类型" prop="openType">
  <el-radio-group v-model="examForm.openType">
    <el-radio :label="1" v-if="isSaRole">完全公开</el-radio>
    <el-radio :label="2">部门开放</el-radio>
  </el-radio-group>
</el-form-item>

<el-form-item v-if="examForm.openType === 2" label="选择部门" prop="departIds">
  <el-input v-model="filterText" placeholder="输入关键字进行过滤" />
  <el-tree
    ref="tree"
    :data="treeData"
    :default-checked-keys="examForm.departIds"
    :props="defaultProps"
    :filter-node-method="filterNode"
    show-checkbox
    node-key="id"
    @check-change="handleCheckChange"
  />
</el-form-item>
```

#### 数据结构扩展
```javascript
data() {
  return {
    treeData: [],           // 部门树数据
    filterText: '',         // 部门搜索关键字
    examForm: {
      openType: 2,          // 默认部门开放
      departIds: []         // 选中的部门ID列表
    },
    defaultProps: {
      children: 'children',
      label: 'deptName'
    }
  }
}
```

### 2. 权限控制逻辑

#### 角色权限检查
```javascript
computed: {
  ...mapGetters(['roles']),
  isSaRole() {
    return this.roles.includes('sa')  // 只有超级管理员能选择完全公开
  }
}
```

#### 部门选择处理
```javascript
handleCheckChange() {
  this.examForm.departIds = []
  const nodes = this.$refs.tree.getCheckedNodes()
  nodes.forEach(item => {
    this.examForm.departIds.push(item.id)
  })
}
```

### 3. 数据验证和保存

#### 表单验证
```javascript
submitForm() {
  this.$refs.examForm.validate((valid) => {
    if (valid) {
      // 验证部门选择
      if (this.examForm.openType === 2 && 
          (!this.examForm.departIds || this.examForm.departIds.length === 0)) {
        this.$message.error('请选择至少一个部门')
        return
      }
      // 保存逻辑...
    }
  })
}
```

#### 编辑时数据加载
```javascript
handleEdit(row) {
  getWeatherExamDetail({ id: row.id }).then(response => {
    this.examForm = { ...response.data }
    // 确保departIds是数组格式
    if (!Array.isArray(this.examForm.departIds)) {
      this.examForm.departIds = this.examForm.departIds ? [this.examForm.departIds] : []
    }
    this.dialogVisible = true
  })
}
```

### 4. 后端支持

后端ExamServiceImpl.save方法已经支持部门处理：

```java
// 开放的部门
if(OpenType.DEPT_OPEN.equals(reqDTO.getOpenType())){
    examDepartService.saveAll(id, reqDTO.getDepartIds());
}
```

## 📋 功能特性

### ✅ 已实现功能

1. **开放类型选择**
   - 完全公开（仅超级管理员可选）
   - 部门开放（默认选项）

2. **部门树管理**
   - 树形结构显示部门层级
   - 支持关键字搜索过滤
   - 多选支持（复选框）
   - 默认展开所有节点

3. **权限验证**
   - 部门开放时必须选择部门
   - 角色权限控制（sa角色才能完全公开）
   - 表单验证提示

4. **数据持久化**
   - 新增时保存部门关联
   - 编辑时正确加载部门信息
   - 支持部门信息修改

### 🔄 交互流程

1. **新增考试**
   - 默认选择"部门开放"
   - 显示部门选择树
   - 必须选择至少一个部门

2. **编辑考试**
   - 加载已选择的部门信息
   - 在树中显示已选中状态
   - 支持修改部门选择

3. **权限控制**
   - 根据用户角色显示可选项
   - 部门树根据用户权限过滤
   - 保存时验证部门选择

## 🧪 测试步骤

### 1. 基础功能测试
```bash
# 启动前端应用
cd exam-vue
npm run dev
```

### 2. 新增考试测试
1. 访问"历史个例考试管理"
2. 点击"新增"按钮
3. 填写基本信息
4. 选择"部门开放"
5. 在部门树中选择部门
6. 保存并验证

### 3. 编辑考试测试
1. 点击已有考试的"编辑"按钮
2. 验证部门信息正确加载
3. 修改部门选择
4. 保存并验证更新

### 4. 权限测试
1. 使用不同角色用户登录
2. 验证"完全公开"选项的显示
3. 验证部门树的权限过滤

## 📝 注意事项

1. **角色权限**：只有sa角色用户才能选择"完全公开"
2. **部门验证**：选择"部门开放"时必须选择至少一个部门
3. **数据格式**：departIds字段在前后端都使用数组格式
4. **树形结构**：部门树支持搜索和多选，提升用户体验

## 🔗 相关文件

- `exam-vue/src/views/weather/exam/index.vue` - 主要实现文件
- `exam-vue/src/api/sys/depart/depart.js` - 部门API
- `src/main/java/com/yf/exam/modules/exam/service/impl/ExamServiceImpl.java` - 后端保存逻辑
- `src/main/java/com/yf/exam/modules/exam/service/impl/ExamDepartServiceImpl.java` - 部门关联服务
