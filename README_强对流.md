# 强对流天气临近预报考试模块 - 项目蓝图

## 项目概述

### 目标
在现有天气考试系统基础上，新增强对流天气临近预报考试模块，实现0-1小时强对流天气的预报能力考核，包括站点预报和落区绘制两个核心功能。

### 核心功能特点
- **双重考核**：站点预报表格 + 落区绘制地图
- **专业分类**：短时强降水、雷暴大风、冰雹、龙卷四类强对流天气
- **精确评分**：基于现有评分引擎，实现68分详细评分规则
- **文件管理**：支持MICAPS数据文件和强对流落区文件管理
- **权限控制**：考生只能查看部分文件，确保考试公平性
- **预报依据阐述**：管理员录入标准答案和考生答题都需要详细的预报理由说明

### 业务场景
- 气象专业人员强对流天气预报技能考核
- 临近预报能力培训和认证
- 极端天气预警业务能力评估

## 功能架构设计

### 系统整体架构
```
强对流考试模块 (convection)
├── 前端路由菜单                    # 参考历史个例考试结构
│   ├── 学生端：/my/convection     # 强对流考试列表和答题
│   └── 管理端：/convection        # 试题管理、考试管理、阅卷管理
├── 第一部分：站点预报 (68分)        # 4个指定站点的强对流天气预报
│   ├── 横向表格布局               # 站点为列，要素为行
│   ├── 三类天气预报               # 短时强降水、雷暴大风、冰雹
│   ├── 预报依据阐述               # 管理员录入标准答案和考生答题都需要
│   │   ├── 管理员端：大文本框录入标准预报依据
│   │   ├── 考生端：大文本框输入预报理由
│   │   └── 人工批卷：对比显示标准答案与考生答案
│   └── 交互式选择器               # 绿色圈(选中)/红色×(未选)，单选约束
├── 第二部分：落区绘制 (32分)        # 0-1小时强对流落区预报
│   ├── WebGIS地图组件             # 基于OpenLayers，参考PrecipitationDrawing
│   ├── 四类落区绘制               # 强降水、雷暴大风、冰雹、龙卷
│   ├── JSON格式存储               # 地理数据以GeoJSON格式保存
│   └── 无需预报依据               # 纯落区绘制，不要求文字说明
├── 人工批卷系统                    # 专门针对预报依据的人工评分
│   ├── 标准答案展示               # 显示管理员录入的标准预报依据
│   ├── 考生答案展示               # 显示考生输入的预报理由
│   ├── 对比界面                   # 并排显示便于批阅
│   └── 评分工具                   # 管理员/教练根据情况给分
└── 文件管理系统
    ├── MICAPS数据文件             # 考生可下载的气象资料
    ├── 强对流落区文件             # 考生不可见的标准答案文件
    └── 权限控制机制               # 确保文件访问安全性
```

### 系统数据流架构设计

#### 考试答题数据流
```mermaid
graph TD
    A[考生进入考试] --> B[加载题目数据]
    B --> C[渲染站点预报表格]
    B --> D[渲染落区绘制地图]
    
    C --> E[选择天气现象]
    C --> F[输入预报依据]
    E --> G[实时数据验证]
    F --> H[字数统计检查]
    
    D --> I[绘制强对流落区]
    I --> J[GeoJSON数据转换]
    
    G --> K[答案实时保存]
    H --> K
    J --> K
    
    K --> L[进度计算更新]
    L --> M[前端界面刷新]
    
    N[提交考试] --> O[数据完整性验证]
    O --> P[保存最终答案]
    P --> Q[触发评分流程]
```

#### 人工批卷数据流
```mermaid
graph TD
    A[批卷任务分配] --> B[加载考生答案]
    B --> C[加载标准答案]
    C --> D[答案对比界面]
    
    D --> E[分级依据评分]
    D --> F[极端天气理由评分]
    
    E --> G[评分记录保存]
    F --> G
    
    G --> H[批卷评语输入]
    H --> I[提交批卷结果]
    I --> J[更新考试总分]
    J --> K[通知考生成绩]
```

### 组件依赖关系设计

#### 前端组件依赖图
```
ConvectionExam.vue (主考试页面)
├── ConvectionStationTable.vue (站点预报表格)
│   ├── ForecastReasoningTextarea.vue (预报依据输入)
│   └── WeatherOptionSelector.vue (天气现象选择器)
├── ConvectionAreaDrawing.vue (落区绘制)
│   ├── OpenLayers Map Engine
│   └── DrawingToolbar.vue (绘制工具栏)
└── ExamTimer.vue (考试计时器)

ReasoningGrading.vue (人工批卷页面)
├── ReasoningComparison.vue (答案对比显示)
├── ScoreInput.vue (评分输入组件)
└── GradingComments.vue (批卷评语组件)
```

#### 后端服务依赖图
```
ConvectionExamController
├── ConvectionExamService
│   ├── ConvectionAnswerService
│   └── ConvectionScoringService
├── ConvectionCaseService
└── FileManagementService

ConvectionGradingController
├── ConvectionGradingService
│   ├── ConvectionAnswerService
│   └── UserService
└── ConvectionScoringService
```

### 第一部分详细设计：站点预报表格

#### 表格布局（与历史个例相反）
- **横向表头**：站点名称（4个指定站点）
- **纵向表头**：天气要素名称
- **数据结构**：每个站点-要素组合一个数据单元

#### 数据交互流程
```javascript
// 站点预报答题数据结构
const stationAnswerStructure = {
  "station_001": {
    "shortTimeRainfall": "level2", // 40≤R1＜80mm/h
    "thunderstormWind": "severe",   // 10级≤Wg＜12级
    "hail": null                    // 未选择
  },
  "station_002": {
    "shortTimeRainfall": null,
    "thunderstormWind": "extreme",  // 12级≤Wg或龙卷
    "hail": "large"                 // 2cm以上大冰雹
  },
  "forecastReasoning": "详细的预报依据阐述文本...",
  "reasoningWordCount": 856,
  "lastModified": "2024-12-22T14:30:00Z"
}
```

#### 三类强对流天气分类
```javascript
// 短时强降水（每站点最多选1个）
shortTimeRainfall: [
  { value: 'level1', label: '20≤R1＜40mm/h', score: 3 },
  { value: 'level2', label: '40≤R1＜80mm/h', score: 3 },
  { value: 'level3', label: '80≤R1mm/h以上', score: 3 }
]

// 雷暴大风（每站点最多选1个）
thunderstormWind: [
  { value: 'moderate', label: '极大风8级≤Wg＜10级或平均风6级≤W2＜8级', score: 3 },
  { value: 'severe', label: '极大风10级≤Wg＜12级或平均风8级≤W2＜10级', score: 3 },
  { value: 'extreme', label: '极大风12级≤Wg或龙卷或平均风10级≤W2', score: 3 }
]

// 冰雹（每站点最多选1个）
hail: [
  { value: 'large', label: '2cm以上大冰雹', score: 3 }
]
```

#### 交互设计规则
- **单选约束**：每个站点的每类天气现象最多只能选择一个
- **视觉反馈**：选中显示绿色圆圈(○)，未选显示红色叉号(×)
- **实时验证**：选择新选项时自动取消同类其他选项

#### 预报依据阐述设计
##### 管理员录入界面
- **标准答案文本框**：大型富文本编辑器，支持格式化输入
- **内容要求**：包含分级依据说明和极端天气预报理由
- **字数限制**：建议500-2000字
- **保存验证**：确保预报依据内容完整性

##### 考生答题界面  
- **预报理由文本框**：大型文本域，便于详细阐述
- **内容提示**：明确要求阐述分级和极端天气预报理由
- **字数统计**：实时显示当前字数，建议300-1500字
- **自动保存**：防止答题内容丢失

##### 人工批卷界面
- **对比显示**：左右分栏显示标准答案和考生答案
- **评分工具**：支持分项评分（分级依据10分，极端天气理由10分）
- **评分记录**：记录批卷教师和评分时间
- **评语功能**：支持添加批阅评语和改进建议

### 第二部分详细设计：落区绘制

#### 基于现有PrecipitationDrawing组件扩展
```vue
<convection-area-drawing
  ref="convectionDrawing"
  :initial-data="convectionForecastData"
  :region="questionData.forecastRegion"
  :readonly="false"
  @data-change="onConvectionDataChange"
>
  <!-- 四类强对流落区绘制工具 -->
  <drawing-toolbar>
    <tool-button type="heavy-rainfall" color="#0000FE" label="强降水" />
    <tool-button type="thunderstorm-wind" color="#FF6B00" label="雷暴大风" />
    <tool-button type="hail" color="#8B0000" label="冰雹" />
    <tool-button type="tornado" color="#4B0082" label="龙卷" />
  </drawing-toolbar>
</convection-area-drawing>
```

#### 数据存储格式
```json
{
  "convectionAreas": {
    "heavy-rainfall": [
      {
        "id": "rainfall_001",
        "geometry": {
          "type": "Polygon",
          "coordinates": [[[lng, lat], [lng, lat], ...]]
        },
        "properties": {
          "intensity": "heavy",
          "createTime": "2024-12-22T10:30:00Z"
        }
      }
    ],
    "thunderstorm-wind": [...],
    "hail": [...],
    "tornado": [...]
  },
  "totalAreaCount": 15,
  "lastModified": "2024-12-22T10:35:00Z"
}
```

## 技术架构规范

### 系统集成架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    强对流考试模块技术架构                          │
├─────────────────────────────────────────────────────────────┤
│  前端层 (Vue.js 2.6.10)                                      │
│  ├── 路由管理 (Vue Router)                                   │
│  ├── 状态管理 (Vuex)                                        │  
│  ├── UI组件库 (Element UI 2.15.7)                           │
│  └── 地图引擎 (OpenLayers 6.x)                              │
├─────────────────────────────────────────────────────────────┤
│  API网关层                                                   │
│  ├── 权限验证 (Shiro)                                       │
│  ├── 请求路由                                              │
│  └── 响应处理                                              │
├─────────────────────────────────────────────────────────────┤
│  业务服务层 (Spring Boot 2.1.4)                             │
│  ├── 强对流考试服务 (ConvectionExamService)                  │
│  ├── 答案管理服务 (ConvectionAnswerService)                 │
│  ├── 人工批卷服务 (ConvectionGradingService)                │
│  └── 评分引擎服务 (ConvectionScoringService)                │
├─────────────────────────────────────────────────────────────┤
│  数据持久层                                                  │
│  ├── MyBatis Plus 3.4.1                                   │
│  ├── 数据库连接池 (HikariCP)                                │
│  └── 事务管理 (Spring Transaction)                          │
├─────────────────────────────────────────────────────────────┤
│  数据存储层 (MySQL 8.0.11)                                  │
│  ├── 强对流考试答案表 (el_convection_exam_answer)             │
│  ├── 人工批卷记录表 (el_convection_grading_record)           │
│  └── 配置文件存储 (convection-scoring.yml)                   │
└─────────────────────────────────────────────────────────────┘
```

### 后端模块结构
```
src/main/java/com/yf/exam/modules/
├── convection/                     # 强对流考试主模块
│   ├── controller/                # API控制器层
│   │   ├── ConvectionCaseController.java        # 试题管理
│   │   ├── ConvectionExamController.java        # 考试管理
│   │   ├── ConvectionAnswerController.java      # 答案管理
│   │   └── ConvectionGradingController.java     # 人工批卷管理
│   ├── service/                   # 业务服务层
│   │   ├── ConvectionCaseService.java           # 试题业务逻辑
│   │   ├── ConvectionExamService.java           # 考试业务逻辑
│   │   ├── ConvectionAnswerService.java         # 答案处理逻辑
│   │   └── ConvectionGradingService.java        # 批卷业务逻辑
│   ├── entity/                    # 实体类
│   │   ├── ConvectionExamAnswer.java            # 考试答案实体
│   │   └── ConvectionGradingRecord.java         # 批卷记录实体
│   ├── dto/                       # 数据传输对象
│   │   ├── ConvectionStationAnswerDTO.java      # 站点答案DTO
│   │   ├── ConvectionAreaAnswerDTO.java         # 落区答案DTO
│   │   └── ConvectionGradingDTO.java            # 批卷DTO
│   ├── scoring/                   # 评分引擎（扩展weather.scoring）
│   │   ├── algorithm/             # 强对流专用评分算法
│   │   │   ├── ConvectionStationScoring.java   # 站点预报评分
│   │   │   └── ConvectionAreaScoring.java       # 落区绘制评分
│   │   ├── service/               # 评分服务
│   │   │   └── ConvectionScoringService.java    # 强对流评分服务
│   │   └── config/                # 评分配置文件
│   │       └── convection-scoring.yml           # 强对流评分规则配置
│   └── mapper/                    # 数据访问层
└── weather/                       # 现有天气模块（需扩展）
    └── scoring/                   # 评分引擎基础框架
        ├── engine/                # 评分引擎核心
        └── algorithm/             # 基础评分算法（需扩展）
```

### 前端组件结构
```
exam-vue/src/
├── views/convection/              # 强对流考试页面
│   ├── qu/index.vue              # 试题管理页面
│   │   └── components/           # 试题管理组件
│   │       └── ForecastReasoningInput.vue      # 预报依据录入组件
│   ├── exam/index.vue            # 考试管理页面
│   ├── exam/ConvectionExam.vue   # 强对流考试答题页面
│   │   └── components/           # 答题页面组件
│   │       └── ForecastReasoningTextarea.vue   # 预报依据答题组件
│   ├── mark/index.vue            # 阅卷管理页面
│   └── grading/                  # 人工批卷专用页面
│       ├── GradingList.vue       # 批卷任务列表
│       └── ReasoningGrading.vue  # 预报依据批卷页面
├── components/convection/         # 强对流专用组件
│   ├── ConvectionStationTable.vue          # 站点预报表格组件
│   ├── ConvectionAreaDrawing.vue           # 落区绘制组件
│   ├── ConvectionResultDisplay.vue         # 成绩展示组件
│   └── ReasoningComparison.vue             # 预报依据对比显示组件
└── api/convection/               # API接口
    ├── convection.js             # 强对流考试API
    └── grading.js                # 批卷相关API
```

### 评分配置文件设计

#### 完整配置文件模板
```yaml
# convection-scoring.yml - 强对流天气考试评分配置
# 配置版本：v1.0
# 更新时间：2024-12-22
# 适用范围：强对流天气临近预报考试模块

convection:
  # 全局配置
  global:
    config_version: "1.0"
    last_updated: "2024-12-22T10:00:00Z"
    description: "强对流天气临近预报考试评分规则配置"
    
  # 评分总体配置
  scoring:
    # 总分配置
    total_score: 100
    station_forecast_score: 68    # 第一部分：站点预报
    area_forecast_score: 32       # 第二部分：落区绘制
    
    # 精度配置
    precision:
      decimal_places: 2           # 分数保留2位小数
      rounding_mode: "HALF_UP"    # 四舍五入规则
      
    # 评分策略
    strategy:
      enable_partial_scoring: true        # 启用部分得分
      enable_bonus_scoring: false         # 禁用奖励得分
      strict_validation: true             # 严格验证模式
      
    # 第一部分详细评分配置
    station_forecast:
      basic_station_score: 44     # 基础站点评分
      extreme_weather_score: 4    # 过程极端天气预报
      reasoning_score: 20         # 预报理由简述（人工批卷）
      
      # 站点数量配置
      station_config:
        required_stations: 4      # 必需的站点数量
        station_weight: "equal"   # 站点权重：equal（等权重）| weighted（加权）
        
      # 单站点评分配置
      per_station:
        total_score: 11
        
        # 短时强降水评分
        rainfall:
          type_correct: 3         # 类型正确基础分
          intensity_correct: 1    # 强度等级正确附加分
          no_rainfall_correct: 4  # 无强降水预报正确
          
          # 分级标准映射
          levels:
            level1:
              label: "20≤R1＜40mm/h"
              range: [20, 40]
              score_weight: 1.0
            level2:
              label: "40≤R1＜80mm/h" 
              range: [40, 80]
              score_weight: 1.0
            level3:
              label: "80≤R1mm/h以上"
              range: [80, 9999]
              score_weight: 1.0
              
        # 雷暴大风评分
        wind:
          type_correct: 3         # 类型正确基础分
          intensity_correct: 1    # 强度等级正确附加分
          no_wind_correct: 4      # 无雷暴大风预报正确
          
          # 分级标准映射
          levels:
            moderate:
              label: "8级≤Wg＜10级或6级≤W2＜8级"
              gust_range: [8, 10]
              sustained_range: [6, 8]
              score_weight: 1.0
            severe:
              label: "10级≤Wg＜12级或8级≤W2＜10级"
              gust_range: [10, 12]
              sustained_range: [8, 10]
              score_weight: 1.0
            extreme:
              label: "12级≤Wg或龙卷或10级≤W2"
              gust_range: [12, 99]
              sustained_range: [10, 99]
              includes_tornado: true
              score_weight: 1.0
              
        # 冰雹评分
        hail:
          correct: 3              # 2cm以上大冰雹正确
          no_hail_correct: 3      # 无冰雹预报正确
          
          # 冰雹分级
          criteria:
            large_hail:
              label: "2cm以上大冰雹"
              diameter_threshold: 2.0  # 直径阈值（厘米）
              score_weight: 1.0
              
        # 预报依据质量评分（自动评分部分）
        reasoning_quality: 1      # 基于字数和关键词的质量评分
        
      # 极端天气预报评分
      extreme_weather:
        max_rainfall_station: 2   # 最强短时强降水预报正确
        max_wind_station: 2       # 最强雷暴大风预报正确
        
        # 极值识别规则
        identification_rules:
          rainfall_priority: ["level3", "level2", "level1"]
          wind_priority: ["extreme", "severe", "moderate"]
          tie_breaking: "first_station"  # 相同强度时选择规则
          
      # 预报理由评分（人工批卷）
      reasoning:
        grading_basis: 10         # 分级依据阐述
        extreme_reasoning: 10     # 极端天气预报理由
        
        # 人工批卷标准
        grading_criteria:
          grading_basis:
            max_score: 10.0
            passing_score: 6.0
            key_points:
              - "短时强降水分级标准理解"
              - "雷暴大风分级标准掌握" 
              - "冰雹识别要点说明"
            scoring_weights:
              accuracy: 0.4       # 准确性权重
              completeness: 0.3   # 完整性权重
              professionalism: 0.3 # 专业性权重
              
          extreme_reasoning:
            max_score: 10.0
            passing_score: 6.0
            key_points:
              - "极端天气形成机制"
              - "预报关键指标"
              - "预警发布依据"
            scoring_weights:
              scientific: 0.5     # 科学性权重
              logical: 0.3        # 逻辑性权重
              practical: 0.2      # 实用性权重
              
        # 评分辅助工具配置
        grading_assistance:
          enable_keyword_matching: true
          enable_similarity_analysis: true
          enable_scoring_suggestions: true
          
          # 关键词词典
          keywords:
            convection_terms:
              - "短时强降水"
              - "雷暴大风"
              - "冰雹"
              - "龙卷"
              - "对流云团"
              - "风切变"
              - "不稳定度"
              - "水汽条件"
              - "抬升机制"
              - "温度梯度"
              - "CAPE"
              - "风暴移动"
              - "超级单体"
              - "多单体"
              - "飑线"
              
          # 相似度计算配置
          similarity:
            algorithm: "cosine"    # 相似度算法：cosine | jaccard | levenshtein
            threshold: 0.6         # 相似度阈值
            weight: 0.3           # 相似度在评分建议中的权重
    
    # 第二部分详细评分配置  
    area_forecast:
      heavy_rainfall: 8           # 强降水落区准确性
      thunderstorm_wind: 8        # 雷暴大风落区准确性
      hail: 8                     # 冰雹落区准确性
      tornado: 8                  # 龙卷落区准确性
      
      # 落区评分方法配置
      scoring_method:
        algorithm: "iou"          # 评分算法：iou（交并比）| hausdorff | area_overlap
        iou_threshold: 0.5        # IoU阈值
        penalty_for_overforecast: 0.1  # 过度预报惩罚系数
        penalty_for_underforecast: 0.2 # 预报不足惩罚系数
        
      # 地理精度配置
      geographic_precision:
        coordinate_system: "WGS84"
        precision_level: 0.01     # 坐标精度（度）
        min_area_threshold: 1.0   # 最小面积阈值（平方公里）
        max_area_threshold: 10000.0 # 最大面积阈值（平方公里）
        
      # 落区类型权重
      area_weights:
        heavy_rainfall: 1.0
        thunderstorm_wind: 1.0
        hail: 1.0
        tornado: 1.5              # 龙卷权重稍高，因为预报难度大
        
  # 系统集成配置
  integration:
    # 与现有评分引擎的集成
    weather_scoring_engine:
      enabled: true
      version: "2.1.0"
      compatibility_mode: true
      
    # 批卷系统集成
    grading_system:
      enabled: true
      auto_assignment: true       # 自动分配批卷任务
      workload_balancing: true    # 工作负载均衡
      quality_control: true       # 质量控制
      
    # 通知系统集成  
    notification:
      enabled: true
      grading_completion: true    # 批卷完成通知
      score_release: true         # 成绩发布通知
      
  # 质量保证配置
  quality_assurance:
    # 评分一致性检查
    consistency_check:
      enabled: true
      threshold: 0.1            # 一致性阈值
      auto_flag_outliers: true  # 自动标记异常评分
      
    # 批卷质量监控
    grading_quality:
      inter_rater_reliability: true  # 评分者间信度检查
      completion_time_monitoring: true # 完成时间监控
      score_distribution_analysis: true # 分数分布分析
      
  # 性能优化配置
  performance:
    # 缓存配置
    cache:
      enabled: true
      scoring_rules_cache_ttl: 3600    # 评分规则缓存时长（秒）
      answer_cache_ttl: 1800           # 答案缓存时长（秒）
      
    # 并发配置
    concurrency:
      max_concurrent_scoring: 10       # 最大并发评分数
      batch_size: 50                   # 批处理大小
      
  # 日志和审计配置
  logging:
    # 评分日志
    scoring_log:
      enabled: true
      level: "INFO"
      include_debug_info: false
      
    # 审计日志
    audit_log:
      enabled: true
      record_all_operations: true
      retention_days: 365
```

#### 配置文件使用示例
```java
// 配置文件加载和使用示例
@Configuration
@ConfigurationProperties(prefix = "convection")
@Data
public class ConvectionScoringConfig {
    
    private Global global;
    private Scoring scoring;
    private Integration integration;
    private QualityAssurance qualityAssurance;
    private Performance performance;
    private Logging logging;
    
    @Data
    public static class Scoring {
        private Integer totalScore;
        private Integer stationForecastScore;
        private Integer areaForecastScore;
        private Precision precision;
        private Strategy strategy;
        private StationForecast stationForecast;
        private AreaForecast areaForecast;
    }
    
    // 使用配置进行评分计算
    @Service
    public class ConvectionScoringServiceImpl {
        
        @Autowired
        private ConvectionScoringConfig config;
        
        public BigDecimal calculateStationScore(ConvectionStationAnswer answer) {
            BigDecimal totalScore = BigDecimal.ZERO;
            
            // 获取配置中的评分规则
            StationForecast stationConfig = config.getScoring().getStationForecast();
            
            // 计算基础站点评分
            BigDecimal basicScore = calculateBasicStationScore(answer, stationConfig);
            totalScore = totalScore.add(basicScore);
            
            // 计算极端天气评分
            BigDecimal extremeScore = calculateExtremeWeatherScore(answer, stationConfig);
            totalScore = totalScore.add(extremeScore);
            
            return totalScore;
        }
    }
}
```

#### 配置文件版本管理
```yaml
# 配置文件版本控制示例
convection:
  global:
    config_version: "1.1"
    migration_info:
      from_version: "1.0"
      migration_date: "2024-12-25T09:00:00Z"
      changes:
        - "新增tornado权重配置"
        - "优化相似度算法参数"
        - "增加质量保证配置项"
        
    backward_compatibility:
      support_v1_0: true
      deprecation_warnings: true
      migration_guide_url: "https://docs.example.com/convection/migration/v1.0-to-v1.1"
```

### 数据库表设计

#### 核心表结构设计原则
- **规范化设计**：避免数据冗余，确保数据一致性
- **性能优化**：合理设计索引，支持高并发查询
- **扩展性考虑**：预留扩展字段，支持功能迭代
- **JSON字段应用**：复杂数据结构使用JSON存储，提高灵活性

```sql
-- 强对流考试答案表
CREATE TABLE `el_convection_exam_answer` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `exam_id` varchar(64) NOT NULL COMMENT '考试ID',
  `question_id` varchar(64) NOT NULL COMMENT '题目ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  
  -- 第一部分：站点预报答案 (JSON格式)
  `station_answer` json COMMENT '站点预报答案JSON',
  `station_progress` int DEFAULT 0 COMMENT '站点预报进度百分比',
  
  -- 第二部分：落区绘制答案 (JSON格式)
  `area_answer` json COMMENT '落区绘制答案JSON',
  `area_progress` int DEFAULT 0 COMMENT '落区绘制进度百分比',
  
  -- 预报依据阐述（考生输入）
  `forecast_reasoning` text COMMENT '考生预报依据阐述',
  `reasoning_word_count` int DEFAULT 0 COMMENT '预报依据字数',
  
  -- 整体状态
  `overall_progress` int DEFAULT 0 COMMENT '整体进度百分比',
  `answer_status` tinyint DEFAULT 0 COMMENT '答题状态：0-答题中，1-已提交',
  `submit_time` datetime COMMENT '提交时间',
  
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  KEY `idx_exam_user` (`exam_id`, `user_id`),
  KEY `idx_question` (`question_id`),
  KEY `idx_status` (`answer_status`),
  KEY `idx_submit_time` (`submit_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='强对流考试答案表';

-- 强对流人工批卷记录表
CREATE TABLE `el_convection_grading_record` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `answer_id` varchar(64) NOT NULL COMMENT '答案ID',
  `exam_id` varchar(64) NOT NULL COMMENT '考试ID',
  `student_user_id` varchar(64) NOT NULL COMMENT '学生用户ID',
  `grader_user_id` varchar(64) NOT NULL COMMENT '批卷教师用户ID',
  
  -- 预报依据评分详情
  `reasoning_grading_basis_score` decimal(5,2) DEFAULT 0 COMMENT '分级依据阐述得分',
  `reasoning_extreme_score` decimal(5,2) DEFAULT 0 COMMENT '极端天气预报理由得分',
  `reasoning_total_score` decimal(5,2) DEFAULT 0 COMMENT '预报依据总得分',
  
  -- 批卷评语和建议
  `grading_comments` text COMMENT '批卷评语',
  `improvement_suggestions` text COMMENT '改进建议',
  
  -- 批卷状态
  `grading_status` tinyint DEFAULT 0 COMMENT '批卷状态：0-未批卷，1-已批卷',
  `grading_time` datetime COMMENT '批卷完成时间',
  
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_answer_grader` (`answer_id`, `grader_user_id`),
  KEY `idx_answer` (`answer_id`),
  KEY `idx_grader` (`grader_user_id`),
  KEY `idx_student` (`student_user_id`),
  KEY `idx_status` (`grading_status`),
  KEY `idx_grading_time` (`grading_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='强对流人工批卷记录表';

-- 扩展现有题目表，添加强对流专用字段
ALTER TABLE `el_qu` ADD COLUMN `convection_standard_reasoning` text COMMENT '强对流标准预报依据（管理员录入）' AFTER `scenario_data`;
ALTER TABLE `el_qu` ADD INDEX `idx_qu_type` (`qu_type`);
```

#### 数据库性能优化策略
```sql
-- 针对高频查询的复合索引
CREATE INDEX `idx_exam_user_status` ON `el_convection_exam_answer` (`exam_id`, `user_id`, `answer_status`);
CREATE INDEX `idx_grading_status_time` ON `el_convection_grading_record` (`grading_status`, `grading_time`);

-- JSON字段的虚拟列索引（MySQL 8.0+）
ALTER TABLE `el_convection_exam_answer` 
ADD COLUMN `station_completion` tinyint GENERATED ALWAYS AS (
  CASE WHEN JSON_LENGTH(`station_answer`) > 0 THEN 1 ELSE 0 END
) VIRTUAL;
CREATE INDEX `idx_station_completion` ON `el_convection_exam_answer` (`station_completion`);

-- 分区策略（按时间分区，提高查询性能）
ALTER TABLE `el_convection_exam_answer` 
PARTITION BY RANGE (YEAR(create_time)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p2026 VALUES LESS THAN (2027),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 批卷记录表时间分区
ALTER TABLE `el_convection_grading_record`
PARTITION BY RANGE (YEAR(create_time)) (
    PARTITION pg2024 VALUES LESS THAN (2025),
    PARTITION pg2025 VALUES LESS THAN (2026),
    PARTITION pg2026 VALUES LESS THAN (2027),
    PARTITION pg_future VALUES LESS THAN MAXVALUE
);
```

#### 数据库监控和维护策略
```sql
-- 创建监控视图：考试答题统计
CREATE VIEW `v_convection_exam_statistics` AS
SELECT 
    DATE(create_time) as exam_date,
    COUNT(*) as total_answers,
    SUM(CASE WHEN answer_status = 1 THEN 1 ELSE 0 END) as submitted_count,
    AVG(overall_progress) as avg_progress,
    AVG(reasoning_word_count) as avg_reasoning_words
FROM `el_convection_exam_answer`
GROUP BY DATE(create_time)
ORDER BY exam_date DESC;

-- 创建监控视图：批卷进度统计
CREATE VIEW `v_convection_grading_statistics` AS
SELECT 
    DATE(create_time) as grading_date,
    COUNT(*) as total_grading,
    SUM(CASE WHEN grading_status = 1 THEN 1 ELSE 0 END) as completed_grading,
    AVG(reasoning_total_score) as avg_reasoning_score,
    COUNT(DISTINCT grader_user_id) as active_graders
FROM `el_convection_grading_record`
GROUP BY DATE(create_time)
ORDER BY grading_date DESC;

-- 定期维护脚本：清理过期数据
DELIMITER //
CREATE PROCEDURE `CleanupOldConvectionData`()
BEGIN
    -- 清理1年前的草稿答案（未提交的答案）
    DELETE FROM `el_convection_exam_answer` 
    WHERE answer_status = 0 
    AND create_time < DATE_SUB(NOW(), INTERVAL 1 YEAR);
    
    -- 清理2年前的批卷记录
    DELETE FROM `el_convection_grading_record`
    WHERE create_time < DATE_SUB(NOW(), INTERVAL 2 YEAR);
END //
DELIMITER ;

-- 创建定期执行任务
CREATE EVENT `cleanup_convection_data`
ON SCHEDULE EVERY 1 MONTH
STARTS CURRENT_TIMESTAMP
DO CALL CleanupOldConvectionData();
```

#### 数据安全和备份策略
```sql
-- 创建备份表结构（用于重要数据备份）
CREATE TABLE `el_convection_exam_answer_backup` LIKE `el_convection_exam_answer`;
CREATE TABLE `el_convection_grading_record_backup` LIKE `el_convection_grading_record`;

-- 敏感数据脱敏视图（用于数据分析，不暴露个人信息）
CREATE VIEW `v_convection_answer_analysis` AS
SELECT 
    CONCAT('user_', SUBSTR(MD5(user_id), 1, 8)) as anonymous_user_id,
    exam_id,
    station_progress,
    area_progress,
    overall_progress,
    reasoning_word_count,
    answer_status,
    DATE(create_time) as answer_date,
    DATE(submit_time) as submit_date
FROM `el_convection_exam_answer`;

-- 权限控制：限制普通用户对敏感字段的访问
CREATE ROLE 'convection_reader'@'%';
GRANT SELECT ON exam.v_convection_answer_analysis TO 'convection_reader'@'%';
GRANT SELECT ON exam.v_convection_exam_statistics TO 'convection_reader'@'%';
GRANT SELECT ON exam.v_convection_grading_statistics TO 'convection_reader'@'%';
```

## 评分规则设计

### 第一部分评分规则（68分）

#### 基础站点评分（44分）
- **4个站点** × **11分/站点** = **44分**
- 每站点分值分配：
  - 短时强降水类型正确：3分，强度等级正确：+1分
  - 雷暴大风类型正确：3分，强度等级正确：+1分
  - 2cm以上大冰雹正确：3分
  - 预报依据质量：1分（自动评分部分）

#### 过程极端天气预报（4分）
- 4个站点中最强短时强降水预报正确：2分
- 4个站点中最强雷暴大风预报正确：2分

#### 预报理由简述（20分）- **人工批卷**
- **分级依据阐述：10分**
  - 管理员/教练对比标准答案和考生答案
  - 评估考生对各类强对流天气分级标准的理解程度
  - 根据阐述的准确性、完整性和专业性给分
- **极端天气预报理由：10分**
  - 评估考生对极端天气形成机制和预报要点的掌握
  - 重点关注预报依据的科学性和逻辑性
  - 支持部分给分，如理由正确但表述不完整可给部分分数

#### 人工批卷流程
1. **系统自动评分**：完成基础站点评分（44分）和过程极端天气预报（4分）
2. **分配批卷任务**：系统将预报理由部分分配给指定的管理员/教练
3. **对比批阅**：批卷界面显示标准答案和考生答案，便于对比
4. **分项评分**：分别对分级依据（10分）和极端天气理由（10分）给分
5. **记录评语**：可添加批阅评语和改进建议
6. **确认提交**：批卷完成后提交，系统计算最终总分

### 第二部分评分规则（32分）
- 强降水落区准确性：8分
- 雷暴大风落区准确性：8分
- 冰雹落区准确性：8分
- 龙卷落区准确性：8分

### 评分算法设计思路

#### 自动评分算法逻辑
```java
// 站点预报自动评分伪代码
public BigDecimal calculateStationAutoScore(StudentAnswer student, StandardAnswer standard) {
    BigDecimal totalScore = BigDecimal.ZERO;
    
    // 1. 基础站点评分（44分）
    for (Station station : getStations()) {
        BigDecimal stationScore = BigDecimal.ZERO;
        
        // 短时强降水评分
        if (isCorrectRainfallType(student.getRainfall(station), standard.getRainfall(station))) {
            stationScore = stationScore.add(new BigDecimal("3.0")); // 类型正确3分
            if (isCorrectRainfallIntensity(student.getRainfall(station), standard.getRainfall(station))) {
                stationScore = stationScore.add(new BigDecimal("1.0")); // 强度正确额外1分
            }
        }
        
        // 雷暴大风评分（同上逻辑）
        // 冰雹评分（同上逻辑）
        
        totalScore = totalScore.add(stationScore);
    }
    
    // 2. 过程极端天气预报（4分）
    totalScore = totalScore.add(calculateExtremeWeatherScore(student, standard));
    
    return totalScore;
}
```

## 关键技术特点

### 与历史个例考试的差异
1. **表格布局**：横纵关系与历史个例相反
2. **交互方式**：单选约束 + 视觉反馈
3. **评分复杂度**：更详细的68分评分规则 + 人工批卷
4. **文件权限**：强对流落区文件对考生不可见
5. **预报依据**：管理员和考生都需要输入大篇幅的预报理由文本

### 技术创新点
1. **智能单选约束**：JavaScript实现同类天气现象的互斥选择
2. **实时进度计算**：基于选择状态和绘制区域自动计算进度
3. **混合评分引擎**：自动评分 + 人工批卷的综合评分体系
4. **权限文件管理**：不同角色对数据文件的差异化访问
5. **富文本预报依据**：支持格式化的预报理由录入和显示
6. **配置文件管理**：评分规则通过YAML配置文件管理，便于维护

## 人工批卷系统架构设计

### 批卷工作流程架构
```mermaid
graph TD
    A[考生提交考试] --> B[系统自动评分]
    B --> C{评分完成?}
    C -->|自动评分部分| D[基础站点评分44分]
    C -->|自动评分部分| E[极端天气预报4分]
    C -->|人工批卷部分| F[预报依据20分待批]
    
    F --> G[创建批卷任务]
    G --> H[分配给教师批卷]
    H --> I[教师登录批卷系统]
    
    I --> J[加载答案对比界面]
    J --> K[显示标准答案]
    J --> L[显示考生答案]
    
    K --> M[分项评分]
    L --> M
    M --> N[分级依据评分10分]
    M --> O[极端天气理由评分10分]
    
    N --> P[输入评语和建议]
    O --> P
    P --> Q[提交批卷结果]
    Q --> R[更新考试总分]
    R --> S[通知考生成绩]
```

### 批卷系统核心组件

#### 1. 批卷任务管理器
```java
@Component
public class ConvectionGradingTaskManager {
    
    /**
     * 创建批卷任务
     */
    public ConvectionGradingTask createGradingTask(String answerId, String examId) {
        ConvectionGradingTask task = new ConvectionGradingTask();
        task.setAnswerId(answerId);
        task.setExamId(examId);
        task.setStatus(GradingStatus.PENDING);
        task.setPriority(calculateTaskPriority(examId));
        task.setCreateTime(LocalDateTime.now());
        
        // 自动分配批卷教师
        String assignedGraderId = autoAssignGrader(examId);
        task.setAssignedGraderId(assignedGraderId);
        
        return gradingTaskRepository.save(task);
    }
    
    /**
     * 批卷任务自动分配算法
     */
    private String autoAssignGrader(String examId) {
        // 1. 获取有批卷权限的教师列表
        List<String> availableGraders = getAvailableGraders(examId);
        
        // 2. 计算每个教师的当前工作负载
        Map<String, Integer> workloadMap = calculateGraderWorkload(availableGraders);
        
        // 3. 选择工作负载最轻的教师
        return workloadMap.entrySet().stream()
            .min(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse(getDefaultGrader());
    }
    
    /**
     * 获取批卷任务列表（支持多种过滤条件）
     */
    public IPage<ConvectionGradingTaskDTO> getGradingTasks(
            String graderId, 
            GradingStatus status, 
            String examId,
            IPage<ConvectionGradingTaskDTO> page) {
        
        QueryWrapper<ConvectionGradingTask> wrapper = new QueryWrapper<>();
        
        if (StringUtils.isNotBlank(graderId)) {
            wrapper.eq("assigned_grader_id", graderId);
        }
        if (status != null) {
            wrapper.eq("status", status.getCode());
        }
        if (StringUtils.isNotBlank(examId)) {
            wrapper.eq("exam_id", examId);
        }
        
        // 按优先级和创建时间排序
        wrapper.orderByDesc("priority", "create_time");
        
        return gradingTaskMapper.selectPage(page, wrapper);
    }
}
```

#### 2. 答案对比显示引擎
```java
@Service
public class AnswerComparisonService {
    
    /**
     * 生成答案对比数据
     */
    public ConvectionAnswerComparisonDTO generateComparison(String answerId) {
        // 1. 获取考生答案
        ConvectionExamAnswer studentAnswer = answerService.getById(answerId);
        
        // 2. 获取标准答案
        ConvectionStandardAnswer standardAnswer = getStandardAnswer(studentAnswer.getQuestionId());
        
        // 3. 生成对比结果
        ConvectionAnswerComparisonDTO comparison = new ConvectionAnswerComparisonDTO();
        
        // 预报依据对比
        ReasoningComparisonDTO reasoningComparison = compareReasoning(
            studentAnswer.getForecastReasoning(),
            standardAnswer.getStandardReasoning()
        );
        comparison.setReasoningComparison(reasoningComparison);
        
        // 站点预报对比
        StationComparisonDTO stationComparison = compareStationAnswers(
            studentAnswer.getStationAnswer(),
            standardAnswer.getStationAnswer()
        );
        comparison.setStationComparison(stationComparison);
        
        return comparison;
    }
    
    /**
     * 预报依据文本对比分析
     */
    private ReasoningComparisonDTO compareReasoning(String studentText, String standardText) {
        ReasoningComparisonDTO comparison = new ReasoningComparisonDTO();
        
        // 基础统计
        comparison.setStudentWordCount(studentText != null ? studentText.length() : 0);
        comparison.setStandardWordCount(standardText != null ? standardText.length() : 0);
        
        // 关键词匹配分析
        List<String> keyWords = extractKeyWords(standardText);
        Map<String, Boolean> keyWordMatches = new HashMap<>();
        
        for (String keyWord : keyWords) {
            boolean matched = studentText != null && studentText.contains(keyWord);
            keyWordMatches.put(keyWord, matched);
        }
        comparison.setKeyWordMatches(keyWordMatches);
        
        // 相似度计算（基于编辑距离或其他算法）
        double similarity = calculateTextSimilarity(studentText, standardText);
        comparison.setSimilarityScore(similarity);
        
        // 生成评分建议
        GradingSuggestion suggestion = generateGradingSuggestion(comparison);
        comparison.setGradingSuggestion(suggestion);
        
        return comparison;
    }
    
    /**
     * 关键词提取算法
     */
    private List<String> extractKeyWords(String text) {
        if (StringUtils.isBlank(text)) {
            return Collections.emptyList();
        }
        
        // 强对流天气关键词词典
        Set<String> convectionKeywords = Set.of(
            "短时强降水", "雷暴大风", "冰雹", "龙卷", "对流云团",
            "风切变", "不稳定度", "水汽条件", "抬升机制", "温度梯度",
            "CAPE", "风暴移动", "超级单体", "多单体", "飑线"
        );
        
        return convectionKeywords.stream()
            .filter(text::contains)
            .collect(Collectors.toList());
    }
}
```

#### 3. 评分规则引擎
```java
@Component
public class ConvectionGradingRuleEngine {
    
    /**
     * 预报依据评分规则配置
     */
    private static final Map<String, GradingCriteria> REASONING_GRADING_CRITERIA = Map.of(
        "分级依据", GradingCriteria.builder()
            .maxScore(10.0)
            .passingScore(6.0)
            .keyPoints(Arrays.asList(
                "短时强降水分级标准理解", 
                "雷暴大风分级标准掌握", 
                "冰雹识别要点说明"
            ))
            .scoringWeights(Map.of(
                "准确性", 0.4,
                "完整性", 0.3,
                "专业性", 0.3
            ))
            .build(),
            
        "极端天气理由", GradingCriteria.builder()
            .maxScore(10.0)
            .passingScore(6.0)
            .keyPoints(Arrays.asList(
                "极端天气形成机制", 
                "预报关键指标", 
                "预警发布依据"
            ))
            .scoringWeights(Map.of(
                "科学性", 0.5,
                "逻辑性", 0.3,
                "实用性", 0.2
            ))
            .build()
    );
    
    /**
     * 生成评分建议
     */
    public GradingSuggestionDTO generateGradingSuggestion(
            String category, 
            ReasoningComparisonDTO comparison) {
        
        GradingCriteria criteria = REASONING_GRADING_CRITERIA.get(category);
        GradingSuggestionDTO suggestion = new GradingSuggestionDTO();
        
        // 基于关键词匹配计算建议分数
        double keywordScore = calculateKeywordScore(comparison, criteria);
        
        // 基于文本相似度调整分数
        double similarityBonus = comparison.getSimilarityScore() * 2.0;
        
        // 计算建议分数
        double suggestedScore = Math.min(criteria.getMaxScore(), 
            keywordScore + similarityBonus);
        
        suggestion.setSuggestedScore(suggestedScore);
        suggestion.setConfidenceLevel(calculateConfidence(comparison));
        
        // 生成评分要点提示
        List<String> gradingTips = generateGradingTips(comparison, criteria);
        suggestion.setGradingTips(gradingTips);
        
        return suggestion;
    }
    
    /**
     * 生成评分要点提示
     */
    private List<String> generateGradingTips(
            ReasoningComparisonDTO comparison, 
            GradingCriteria criteria) {
        
        List<String> tips = new ArrayList<>();
        
        // 检查关键词覆盖情况
        Map<String, Boolean> keywordMatches = comparison.getKeyWordMatches();
        long matchedCount = keywordMatches.values().stream()
            .mapToLong(matched -> matched ? 1L : 0L)
            .sum();
        
        if (matchedCount >= criteria.getKeyPoints().size() * 0.8) {
            tips.add("✓ 关键概念覆盖较为全面，建议给予基础分");
        } else {
            tips.add("⚠ 关键概念覆盖不足，请重点检查专业术语使用");
        }
        
        // 检查文本长度
        int wordCount = comparison.getStudentWordCount();
        if (wordCount >= 200) {
            tips.add("✓ 答案详细程度适中，内容较为充实");
        } else if (wordCount >= 100) {
            tips.add("△ 答案基本完整，但可能缺少部分要点");
        } else {
            tips.add("⚠ 答案过于简略，请检查是否遗漏重要内容");
        }
        
        // 相似度评估
        double similarity = comparison.getSimilarityScore();
        if (similarity >= 0.7) {
            tips.add("✓ 与标准答案高度一致，理解准确");
        } else if (similarity >= 0.4) {
            tips.add("△ 与标准答案部分一致，存在理解偏差");
        } else {
            tips.add("⚠ 与标准答案差异较大，请仔细核实");
        }
        
        return tips;
    }
}
```

### 批卷界面设计规范

#### 1. 答案对比界面布局
```javascript
// 批卷界面布局配置
const gradingLayoutConfig = {
  // 左右分栏布局
  layout: 'split-pane',
  
  // 左侧：标准答案显示
  leftPane: {
    title: '标准答案',
    components: [
      {
        type: 'reasoning-display',
        props: {
          content: 'standardReasoning',
          editable: false,
          highlighted: true
        }
      },
      {
        type: 'keyword-highlights',
        props: {
          keywords: 'extractedKeywords',
          color: '#4CAF50'
        }
      }
    ]
  },
  
  // 右侧：考生答案显示
  rightPane: {
    title: '考生答案',
    components: [
      {
        type: 'reasoning-display',
        props: {
          content: 'studentReasoning',
          editable: false,
          showStats: true
        }
      },
      {
        type: 'similarity-indicator',
        props: {
          score: 'similarityScore',
          threshold: 0.6
        }
      }
    ]
  },
  
  // 底部：评分面板
  bottomPane: {
    title: '评分面板',
    components: [
      {
        type: 'score-input-group',
        items: [
          {
            label: '分级依据阐述',
            field: 'gradingBasisScore',
            max: 10,
            step: 0.5,
            suggestions: 'gradingSuggestions.basis'
          },
          {
            label: '极端天气预报理由',
            field: 'extremeReasoningScore', 
            max: 10,
            step: 0.5,
            suggestions: 'gradingSuggestions.extreme'
          }
        ]
      },
      {
        type: 'grading-comments',
        props: {
          placeholder: '请输入批卷评语...',
          maxLength: 500,
          showTips: true
        }
      }
    ]
  }
}
```

#### 2. 批卷工作流状态管理
```javascript
// 批卷工作流状态机
const gradingWorkflowStates = {
  PENDING: {
    name: '待批卷',
    color: '#FFA726',
    actions: ['start_grading'],
    next: ['IN_PROGRESS']
  },
  
  IN_PROGRESS: {
    name: '批卷中',
    color: '#42A5F5',
    actions: ['save_draft', 'submit_grading', 'pause_grading'],
    next: ['COMPLETED', 'PAUSED', 'PENDING']
  },
  
  PAUSED: {
    name: '暂停',
    color: '#FF7043',
    actions: ['resume_grading', 'reassign'],
    next: ['IN_PROGRESS', 'PENDING']
  },
  
  COMPLETED: {
    name: '已完成',
    color: '#66BB6A',
    actions: ['view_result', 'modify_score'],
    next: ['IN_PROGRESS'] // 允许修改
  },
  
  REVIEWED: {
    name: '已复核',
    color: '#26A69A',
    actions: ['view_result'],
    next: [] // 终态
  }
}
```

### 安全性设计考虑
1. **文件访问控制**：强对流落区文件仅管理员可见
2. **答案数据加密**：敏感考试数据传输加密
3. **权限细粒度控制**：基于角色的功能访问限制
4. **批卷权限管理**：只有指定教师可以进行人工批卷
5. **批卷日志审计**：记录所有批卷操作，支持溯源和复核
6. **数据完整性保护**：批卷过程中的数据一致性检查和事务保护

### 兼容性保障
- 复用现有weather模块的评分引擎框架
- 继承现有的权限管理和菜单路由机制
- 保持与历史个例考试相同的答题和提交流程
- 使用相同的数据库连接池和事务管理
- 配置文件与现有系统配置管理方式一致

## 项目价值

### 业务价值
- 提升气象人员强对流天气预报技能
- 建立标准化的临近预报能力评估体系
- 支持极端天气预警业务培训
- 引入人工批卷，提高评分的专业性和准确性

### 技术价值
- 扩展现有考试系统的题型覆盖范围
- 完善地理信息系统在考试中的应用
- 建立复杂评分规则的技术范例
- 实现自动评分与人工批卷的有机结合

### 系统价值
- 丰富考试系统的专业化功能
- 提高系统在气象领域的适用性
- 为后续专业考试模块提供技术参考
- 建立完善的人工批卷工作流程

## 项目风险分析

### 技术风险与应对
1. **复杂评分逻辑实现风险**
   - 风险：68分详细评分规则复杂，容易出错
   - 应对：分步实现，充分测试，建立评分算法单元测试

2. **人工批卷系统集成风险**
   - 风险：批卷工作流与现有系统集成复杂
   - 应对：参考现有模块设计模式，保持架构一致性

3. **地图组件性能风险**
   - 风险：大量地理数据渲染可能影响性能
   - 应对：实现分层加载，优化地图组件性能

### 业务风险与应对
1. **用户接受度风险**
   - 风险：新的交互方式用户可能不适应
   - 应对：提供详细操作指南，设置新手引导

2. **评分标准争议风险**
   - 风险：人工批卷可能存在主观性差异
   - 应对：制定详细的评分标准，提供培训材料

### 项目进度风险与应对
1. **开发时间超期风险**
   - 风险：功能复杂度可能导致开发延期
   - 应对：合理分解任务，设置里程碑检查点

## 快速开始指南

### 环境要求

#### 系统要求
- **操作系统**：Windows 10+ / Linux / macOS
- **数据库**：MySQL 8.0.11+
- **JDK版本**：OpenJDK 8 或 Oracle JDK 8+
- **Node.js版本**：Node.js 14.x+
- **NPM版本**：NPM 6.x+

#### 技术栈版本
- **后端框架**：Spring Boot 2.1.4.RELEASE
- **前端框架**：Vue.js 2.6.10
- **UI组件库**：Element UI 2.15.7
- **地图引擎**：OpenLayers 6.x
- **数据库ORM**：MyBatis Plus 3.4.1
- **构建工具**：Maven 3.6+

#### 硬件建议
- **CPU**：4核心以上
- **内存**：8GB以上
- **磁盘空间**：至少50GB可用空间
- **网络**：稳定的互联网连接

### 安装部署

#### 1. 获取项目代码
```bash
# 克隆项目到本地
git clone [项目仓库地址]
cd exam-api

# 切换到强对流开发分支
git checkout feature/convection-exam
```

#### 2. 数据库初始化
```bash
# 连接MySQL数据库
mysql -u root -p

# 创建数据库
CREATE DATABASE exam_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 使用数据库
USE exam_db;

# 执行初始化脚本
source sql/convection_init.sql;
source sql/convection_test_data.sql;
```

#### 3. 后端服务启动
```bash
# 进入后端项目目录
cd exam-api

# 安装依赖
mvn clean install -DskipTests

# 配置application.yml文件
cp src/main/resources/application-dev.yml.example src/main/resources/application-dev.yml
# 编辑配置文件，修改数据库连接等配置

# 启动Spring Boot应用
mvn spring-boot:run

# 或者构建JAR包运行
mvn clean package -DskipTests
java -jar target/exam-api-1.0.0.jar
```

#### 4. 前端项目启动
```bash
# 进入前端项目目录
cd exam-vue

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

#### 5. 访问系统
- **前端开发地址**：http://localhost:9528
- **后端API地址**：http://localhost:8080
- **API文档地址**：http://localhost:8080/swagger-ui.html

### 开发环境配置

#### IDE推荐配置
- **后端开发**：IntelliJ IDEA 2020.3+
- **前端开发**：Visual Studio Code + Vue.js扩展
- **数据库工具**：Navicat Premium 或 DBeaver

#### 代码格式化配置
```json
// .vscode/settings.json
{
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "eslint.autoFixOnSave": true,
  "vetur.format.defaultFormatter.html": "js-beautify-html",
  "vetur.format.defaultFormatter.js": "prettier",
  "vetur.format.defaultFormatter.ts": "prettier"
}
```

#### Git Hook配置
```bash
# 安装代码提交前检查
npm install --save-dev husky lint-staged

# 配置package.json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged"
    }
  },
  "lint-staged": {
    "*.{js,vue}": ["eslint --fix", "git add"],
    "*.{css,scss,less}": ["stylelint --fix", "git add"]
  }
}
```

## 使用指南

### 管理员使用指南

#### 1. 试题管理
```markdown
1. 登录系统，进入"强对流" -> "试题管理"
2. 点击"新增试题"，填写基本信息
3. 上传MICAPS数据文件（考生可见）
4. 上传强对流落区标准答案文件（考生不可见）
5. 在"标准预报依据"文本框中详细录入预报理由
6. 保存试题，系统自动设置quType=7
```

#### 2. 考试管理
```markdown
1. 进入"强对流" -> "考试管理"
2. 创建新考试，选择强对流试题
3. 设置考试时间、参考部门、考试时长
4. 配置评分权重和批卷教师
5. 发布考试，通知考生参加
```

#### 3. 人工批卷
```markdown
1. 进入"强对流" -> "人工批卷"
2. 查看待批卷任务列表
3. 点击"开始批卷"，进入批卷界面
4. 对比标准答案和考生答案
5. 分别为"分级依据阐述"(10分)和"极端天气预报理由"(10分)评分
6. 输入批卷评语和改进建议
7. 提交批卷结果
```

#### 4. 评分配置管理
```yaml
# 修改评分配置文件：src/main/resources/config/convection-scoring.yml
convection:
  scoring:
    total_score: 100
    station_forecast_score: 68
    area_forecast_score: 32
    # 可根据需要调整各项评分权重
```

### 考生使用指南

#### 1. 参加考试
```markdown
1. 登录系统，进入"我的考试" -> "强对流考试"
2. 选择可参加的考试，点击"开始考试"
3. 系统加载考试题目，显示MICAPS资料文件
```

#### 2. 第一部分：站点预报（68分）
```markdown
1. 查看4个指定站点的横向表格
2. 为每个站点选择对应的强对流天气现象：
   - 短时强降水：选择合适的降水强度等级
   - 雷暴大风：选择合适的风力等级
   - 冰雹：是否有2cm以上大冰雹
3. 在预报依据文本框中详细阐述：
   - 各类强对流天气的分级判断依据
   - 极端天气预报的理由和关键指标
   - 基于气象资料的分析结论
4. 注意文字输入建议300-1500字
```

#### 3. 第二部分：落区绘制（32分）
```markdown
1. 使用地图绘制工具绘制四类落区：
   - 强降水落区（蓝色）
   - 雷暴大风落区（橙色）
   - 冰雹落区（红色）
   - 龙卷落区（紫色）
2. 可以绘制多个不连续区域
3. 支持编辑、删除已绘制区域
4. 实时显示绘制进度
```

#### 4. 提交考试
```markdown
1. 确认两部分内容都已完成
2. 检查答题进度达到100%
3. 点击"提交考试"
4. 确认提交后无法修改
5. 等待系统自动评分和教师人工批卷
```

### 教师使用指南

#### 1. 批卷权限
```markdown
- 需要管理员分配批卷权限
- 具有"teacher"或"sa"角色才能进行人工批卷
- 支持按部门分配批卷任务
```

#### 2. 批卷操作流程
```markdown
1. 进入批卷系统，查看分配给自己的批卷任务
2. 选择待批卷答案，进入批卷界面
3. 仔细阅读标准答案和考生答案
4. 根据评分标准进行打分：
   - 分级依据阐述(10分)：检查对强对流分级标准的理解
   - 极端天气理由(10分)：评估预报依据的科学性
5. 输入具体的批卷评语
6. 提供改进建议
7. 提交批卷结果
```

#### 3. 批卷质量标准
```markdown
- 准确性：评分应基于标准答案客观评判
- 一致性：同类问题应保持评分标准一致
- 建设性：提供有价值的改进建议
- 及时性：在规定时间内完成批卷任务
```

## 故障排除

### 常见问题解答(FAQ)

#### Q1: 启动后端服务时报"数据库连接失败"错误
**A1**: 
```bash
# 检查MySQL服务是否启动
systemctl status mysql  # Linux
# 或
net start mysql  # Windows

# 检查数据库配置
# 编辑 src/main/resources/application-dev.yml
spring:
  datasource:
    url: *************************************************************************************************
    username: your_username
    password: your_password
```

#### Q2: 前端页面显示空白或报错
**A2**: 
```bash
# 清除npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules
npm install

# 检查Node.js版本
node --version  # 应该是14.x以上

# 如果仍有问题，尝试使用yarn
npm install -g yarn
yarn install
yarn dev
```

#### Q3: 地图组件无法正常显示
**A3**: 
```javascript
// 检查OpenLayers依赖是否正确安装
npm list ol

// 如果缺失，手动安装
npm install ol@6.14.1

// 检查地图API配置
// 在 src/components/convection/ConvectionAreaDrawing.vue 中
// 确保地图服务URL配置正确
```

#### Q4: 考试提交后评分结果异常
**A4**: 
```bash
# 检查评分配置文件
cat src/main/resources/config/convection-scoring.yml

# 查看评分服务日志
tail -f logs/yf-exam-lite/spring.log | grep ConvectionScoring

# 检查数据库表数据完整性
mysql -u root -p
use exam_db;
select * from el_convection_exam_answer where user_id = 'your_user_id';
```

#### Q5: 文件上传失败
**A5**: 
```bash
# 检查文件上传目录权限
ls -la uploads/
chmod 755 uploads/

# 检查文件大小限制配置
# 在application.yml中调整
spring:
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 100MB
```

#### Q6: 人工批卷界面加载慢
**A6**: 
```sql
-- 检查数据库索引
SHOW INDEX FROM el_convection_exam_answer;

-- 如果缺少索引，添加优化索引
CREATE INDEX idx_exam_user_status ON el_convection_exam_answer (exam_id, user_id, answer_status);
CREATE INDEX idx_grading_status_time ON el_convection_grading_record (grading_status, grading_time);
```

### 性能优化建议

#### 1. 数据库优化
```sql
-- 定期清理过期数据
DELETE FROM el_convection_exam_answer 
WHERE answer_status = 0 AND create_time < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 定期执行表优化
OPTIMIZE TABLE el_convection_exam_answer;
OPTIMIZE TABLE el_convection_grading_record;

-- 监控慢查询
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;
```

#### 2. 前端性能优化
```javascript
// 路由懒加载
const ConvectionExam = () => import('@/views/convection/exam/ConvectionExam.vue')

// 组件懒加载
components: {
  ConvectionAreaDrawing: () => import('@/components/convection/ConvectionAreaDrawing.vue')
}

// 图片懒加载
<img v-lazy="imageUrl" alt="地图">
```

#### 3. 缓存策略
```java
// Redis缓存配置
@Cacheable(value = "convection:scoring", key = "#examId")
public ConvectionScoringConfig getScoringConfig(String examId) {
    return scoringConfigService.getByExamId(examId);
}

// 静态资源缓存
# nginx配置
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 监控与运维

#### 系统监控指标
```yaml
# 关键性能指标(KPI)
performance:
  response_time:
    api_average: "<1000ms"
    page_load: "<3000ms"
  throughput:
    concurrent_users: "100+"
    exam_submissions_per_hour: "500+"
  availability:
    uptime: ">99.5%"
    error_rate: "<0.1%"
```

#### 日志监控
```bash
# 应用日志监控
tail -f logs/yf-exam-lite/spring.log | grep ERROR

# 数据库监控
mysqladmin -u root -p processlist

# 系统资源监控
top -p $(pgrep java)
df -h
free -m
```

#### 备份策略
```bash
# 数据库每日备份
#!/bin/bash
BACKUP_DIR="/var/backups/exam_db"
DATE=$(date +%Y%m%d_%H%M%S)

mysqldump -u root -p exam_db > $BACKUP_DIR/exam_db_$DATE.sql
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete

# 应用配置备份
tar -czf /var/backups/app_config_$DATE.tar.gz src/main/resources/
```

## 贡献指南

### 开发规范

#### 代码提交规范
```bash
# 提交信息格式
<type>(<scope>): <subject>

# 类型说明
feat: 新功能
fix: bug修复
docs: 文档更新
style: 代码格式修改
refactor: 代码重构
test: 测试用例
chore: 构建过程或辅助工具变动

# 示例
feat(convection): 添加强对流考试答题页面
fix(scoring): 修复评分算法计算错误
docs(readme): 更新部署文档
```

#### 分支管理规范
```bash
# 主分支
master          # 生产环境分支
develop         # 开发环境分支

# 功能分支
feature/convection-exam          # 强对流考试功能
feature/convection-scoring       # 强对流评分引擎
feature/convection-grading       # 人工批卷系统

# 修复分支
hotfix/scoring-bug-fix          # 紧急修复分支
```

#### 测试覆盖率要求
```bash
# 单元测试覆盖率要求
Backend (Java): >= 80%
Frontend (Vue.js): >= 70%

# 运行测试
mvn test                        # 后端测试
npm run test                    # 前端测试
```

### 技术支持

#### 联系方式
- **技术负责人**：[开发团队负责人邮箱]
- **项目经理**：[项目经理邮箱]
- **问题反馈**：[GitHub Issues 或内部bug跟踪系统]

#### 文档资源
- **API文档**：http://localhost:8080/swagger-ui.html
- **技术架构文档**：docs/architecture.md
- **数据库设计文档**：docs/database-design.md
- **前端组件文档**：docs/frontend-components.md

#### 开发者社区
- **内部技术讨论群**：[企业微信群或钉钉群]
- **代码review平台**：[GitLab MR 或 GitHub PR]
- **技术分享会**：每双周五下午进行技术分享

## 更新日志

### v1.0.0 (2024-12-22)
#### 新增功能
- ✨ 强对流天气预报考试模块
- ✨ 站点预报表格交互组件
- ✨ 落区绘制地图组件
- ✨ 人工批卷系统
- ✨ 68分详细评分规则
- ✨ 预报依据文本编辑器

#### 技术改进
- 🔧 扩展现有weather评分引擎
- 🔧 优化数据库索引性能
- 🔧 增加JSON字段处理能力
- 🔧 集成OpenLayers地图引擎

#### 已知问题
- ⚠️ 地图组件在低版本浏览器兼容性问题
- ⚠️ 大文件上传时可能超时

### 未来版本规划

#### v1.1.0 (计划2025年Q1)
- 🎯 增加考试统计分析功能
- 🎯 支持批量批卷操作
- 🎯 优化移动端适配
- 🎯 增加考试录像回放功能

#### v1.2.0 (计划2025年Q2)
- 🎯 集成AI评分辅助系统
- 🎯 支持多语言界面
- 🎯 增加实时协作批卷
- 🎯 优化大数据量处理性能

---

**© 2024 强对流天气临近预报考试系统. 保留所有权利.**