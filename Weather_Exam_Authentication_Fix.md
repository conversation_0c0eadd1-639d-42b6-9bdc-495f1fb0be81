# 历史个例考试认证问题修复方案

## 问题描述

在历史个例考试过程中，调用 `/exam/api/weather/case/exam-detail` 接口时出现认证错误，导致无法获取题目详情。

## 问题原因分析

### 1. 接口需要认证
`/exam/api/weather/case/exam-detail` 接口需要JWT Token认证，不在匿名访问列表中。

### 2. 可能的认证失败原因
- **Token过期**: JWT Token有24小时有效期，长时间考试可能导致过期
- **Token丢失**: 浏览器清除了Token或存储出现问题  
- **网络问题**: 请求头中Token传递失败
- **用户登出**: 用户在其他标签页登出导致Token失效

## 解决方案

### 方案1: 增强前端认证处理（推荐）

#### 1.1 创建Token辅助工具类
**文件**: `src/utils/tokenHelper.js`

提供以下功能：
- `checkToken()`: 检查Token是否存在且有效
- `handleAuthError()`: 统一处理认证错误
- `redirectToLogin()`: 重定向到登录页面
- `checkBeforeApiCall()`: API调用前检查Token

#### 1.2 修改考试页面
**文件**: `src/views/weather/exam/WeatherHistoryExam.vue`

**修改内容**:
```javascript
// 导入Token辅助工具
import tokenHelper from '@/utils/tokenHelper'

// 在API调用前检查Token
if (!tokenHelper.checkBeforeApiCall(this.$router, this.$message)) {
  return
}

// 统一的认证错误处理
if (tokenHelper.handleAuthError(error, this.$router, this.$message)) {
  return
}
```

### 方案2: 创建匿名访问接口（备选）

如果需要完全避免认证问题，可以创建匿名访问的考试专用接口：

#### 2.1 修改Shiro配置
在 `ShiroConfig.java` 中添加：
```java
// 考试专用接口不需要校验
map.put("/exam/api/weather/case/exam-detail-anonymous", "anon");
```

#### 2.2 创建新的控制器方法
```java
@ApiOperation(value = "获取历史个例题目详情（匿名考试专用）")
@PostMapping("/exam-detail-anonymous")
public ApiRest<QuExamDTO> getExamDetailAnonymous(@RequestBody BaseIdReqDTO reqDTO) {
    // 可以添加额外的验证逻辑，如考试ID验证
    QuExamDTO detail = quService.examDetail(reqDTO.getId());
    return super.success(detail);
}
```

## 当前实现的解决方案

### 已修改的文件

#### 1. `src/utils/tokenHelper.js` (新增)
Token辅助工具类，提供统一的认证检查和错误处理。

#### 2. `src/views/weather/exam/WeatherHistoryExam.vue` (修改)
- 导入tokenHelper工具
- 在API调用前检查Token状态
- 使用统一的认证错误处理逻辑

### 功能特点

#### 1. 预防性检查
在调用API前检查Token是否存在，避免无效请求。

#### 2. 统一错误处理
识别各种认证错误类型：
- HTTP 401状态码
- Token相关错误消息
- 特定错误码（如10010002）

#### 3. 用户友好提示
- 明确的错误提示信息
- 自动重定向到登录页面
- 保存当前页面路径用于登录后返回

#### 4. 降级处理
当获取题目详情失败时，使用默认配置继续考试，避免完全阻塞。

## 使用说明

### 正常流程
1. 用户进入考试页面
2. 系统检查Token有效性
3. 调用API获取题目详情
4. 正常显示考试内容

### 认证失败流程
1. 检测到Token无效或过期
2. 显示友好的错误提示
3. 自动重定向到登录页面
4. 用户重新登录后返回考试页面

### 降级流程
1. API调用失败但非认证问题
2. 显示警告信息
3. 使用默认配置继续考试
4. 确保考试不被完全阻塞

## 测试建议

### 1. 正常场景测试
- 正常登录状态下进入考试
- 验证题目详情正确加载

### 2. Token过期测试
- 手动清除浏览器中的Token
- 进入考试页面验证重定向

### 3. 网络异常测试
- 模拟网络中断
- 验证降级处理是否正常

### 4. 长时间考试测试
- 模拟长时间考试场景
- 验证Token刷新机制

## 进一步优化建议

### 1. Token自动刷新
可以添加Token自动刷新机制，在Token即将过期时自动续期。

### 2. 离线缓存
可以考虑将题目详情缓存到本地存储，减少网络依赖。

### 3. 心跳检测
定期检查用户登录状态，提前发现认证问题。

### 4. 考试状态保存
在认证失败时保存当前考试进度，登录后可以恢复。

## 总结

通过增强前端的认证处理机制，可以有效解决历史个例考试中的认证问题。该方案既保证了安全性，又提供了良好的用户体验，是推荐的解决方案。

如果仍然遇到问题，建议：
1. 检查浏览器控制台的具体错误信息
2. 确认Token是否正确存储和传递
3. 验证后端接口是否正常响应
4. 考虑使用方案2创建匿名访问接口
